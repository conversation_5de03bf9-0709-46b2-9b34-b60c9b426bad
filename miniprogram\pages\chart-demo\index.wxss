.container {
  padding: 20rpx;
  box-sizing: border-box;
}

/* 图表区块样式 */
.chart-section {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 图表类型选择器 */
.chart-type-selector {
  margin-bottom: 30rpx;
  width: 100%;
}

.type-selector-scroll {
  white-space: nowrap;
  width: 100%;
}

.selector-buttons {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 10rpx 0;
}

.type-btn {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.type-btn.active {
  color: #fff;
  background-color: #1890ff;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
}

/* 图表容器 */
.chart-wrapper {
  width: 100%;
  height: 500rpx;
  position: relative;
  margin-bottom: 30rpx;
  transition: height 0.3s ease;
}

/* 雷达图特殊样式 */
.chart-wrapper-radar {
  height: 900rpx;
  padding: 30rpx 0;
}

/* 按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.action-btn {
  background-color: #1890ff;
  color: white;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
  margin: 0 20rpx;
  width: 40%;
}

.action-btn:active {
  background-color: #096dd9;
}

/* 多图表布局 */
.multi-chart-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.chart-item {
  width: 48%;
  background-color: #fafafa;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.chart-item.full-width {
  width: 100%;
}

.chart-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.chart-item-wrapper {
  width: 100%;
  height: 300rpx;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}

.legend-color {
  width: 30rpx;
  height: 30rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.legend-text {
  font-size: 28rpx;
  color: #333;
} 
{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar path = require('path');\nvar childProcess = require('child_process');\nvar objectAssign = require('object-assign');\nvar Promise = require('pinkie-promise');\n\nmodule.exports = function (target, opts) {\n\tif (typeof target !== 'string') {\n\t\treturn Promise.reject(new Error('Expected a `target`'));\n\t}\n\n\topts = objectAssign({wait: true}, opts);\n\n\tvar cmd;\n\tvar appArgs = [];\n\tvar args = [];\n\tvar cpOpts = {};\n\n\tif (Array.isArray(opts.app)) {\n\t\tappArgs = opts.app.slice(1);\n\t\topts.app = opts.app[0];\n\t}\n\n\tif (process.platform === 'darwin') {\n\t\tcmd = 'open';\n\n\t\tif (opts.wait) {\n\t\t\targs.push('-W');\n\t\t}\n\n\t\tif (opts.app) {\n\t\t\targs.push('-a', opts.app);\n\t\t}\n\t} else if (process.platform === 'win32') {\n\t\tcmd = 'cmd';\n\t\targs.push('/c', 'start', '\"\"');\n\t\ttarget = target.replace(/&/g, '^&');\n\n\t\tif (opts.wait) {\n\t\t\targs.push('/wait');\n\t\t}\n\n\t\tif (opts.app) {\n\t\t\targs.push(opts.app);\n\t\t}\n\n\t\tif (appArgs.length > 0) {\n\t\t\targs = args.concat(appArgs);\n\t\t}\n\t} else {\n\t\tif (opts.app) {\n\t\t\tcmd = opts.app;\n\t\t} else {\n\t\t\tcmd = path.join(__dirname, 'xdg-open');\n\t\t}\n\n\t\tif (appArgs.length > 0) {\n\t\t\targs = args.concat(appArgs);\n\t\t}\n\n\t\tif (!opts.wait) {\n\t\t\t// xdg-open will block the process unless\n\t\t\t// stdio is ignored even if it's unref'd\n\t\t\tcpOpts.stdio = 'ignore';\n\t\t}\n\t}\n\n\targs.push(target);\n\n\tif (process.platform === 'darwin' && appArgs.length > 0) {\n\t\targs.push('--args');\n\t\targs = args.concat(appArgs);\n\t}\n\n\tvar cp = childProcess.spawn(cmd, args, cpOpts);\n\n\tif (opts.wait) {\n\t\treturn new Promise(function (resolve, reject) {\n\t\t\tcp.once('error', reject);\n\n\t\t\tcp.once('close', function (code) {\n\t\t\t\tif (code > 0) {\n\t\t\t\t\treject(new Error('Exited with code ' + code));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tresolve(cp);\n\t\t\t});\n\t\t});\n\t}\n\n\tcp.unref();\n\n\treturn Promise.resolve(cp);\n};\n"]}
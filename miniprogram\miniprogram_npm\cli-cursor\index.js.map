{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nconst restoreCursor = require('restore-cursor');\n\nlet hidden = false;\n\nexports.show = stream => {\n\tconst s = stream || process.stderr;\n\n\tif (!s.isTTY) {\n\t\treturn;\n\t}\n\n\thidden = false;\n\ts.write('\\u001b[?25h');\n};\n\nexports.hide = stream => {\n\tconst s = stream || process.stderr;\n\n\tif (!s.isTTY) {\n\t\treturn;\n\t}\n\n\trestoreCursor();\n\thidden = true;\n\ts.write('\\u001b[?25l');\n};\n\nexports.toggle = (force, stream) => {\n\tif (force !== undefined) {\n\t\thidden = force;\n\t}\n\n\tif (hidden) {\n\t\texports.show(stream);\n\t} else {\n\t\texports.hide(stream);\n\t}\n};\n"]}
// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api, {} from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    navMenus: [],
    tools: [],
    loading: true,
    isEmpty: false
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '工具箱',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '工具箱',
        path: '/pages/tool/test/test'
      };
    }
  }
});

{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar PENDING = 'pending';\nvar SETTLED = 'settled';\nvar FULFILLED = 'fulfilled';\nvar REJECTED = 'rejected';\nvar NOOP = function () {};\nvar isNode = typeof global !== 'undefined' && typeof global.process !== 'undefined' && typeof global.process.emit === 'function';\n\nvar asyncSetTimer = typeof setImmediate === 'undefined' ? setTimeout : setImmediate;\nvar asyncQueue = [];\nvar asyncTimer;\n\nfunction asyncFlush() {\n\t// run promise callbacks\n\tfor (var i = 0; i < asyncQueue.length; i++) {\n\t\tasyncQueue[i][0](asyncQueue[i][1]);\n\t}\n\n\t// reset async asyncQueue\n\tasyncQueue = [];\n\tasyncTimer = false;\n}\n\nfunction asyncCall(callback, arg) {\n\tasyncQueue.push([callback, arg]);\n\n\tif (!asyncTimer) {\n\t\tasyncTimer = true;\n\t\tasyncSetTimer(asyncFlush, 0);\n\t}\n}\n\nfunction invokeResolver(resolver, promise) {\n\tfunction resolvePromise(value) {\n\t\tresolve(promise, value);\n\t}\n\n\tfunction rejectPromise(reason) {\n\t\treject(promise, reason);\n\t}\n\n\ttry {\n\t\tresolver(resolvePromise, rejectPromise);\n\t} catch (e) {\n\t\trejectPromise(e);\n\t}\n}\n\nfunction invokeCallback(subscriber) {\n\tvar owner = subscriber.owner;\n\tvar settled = owner._state;\n\tvar value = owner._data;\n\tvar callback = subscriber[settled];\n\tvar promise = subscriber.then;\n\n\tif (typeof callback === 'function') {\n\t\tsettled = FULFILLED;\n\t\ttry {\n\t\t\tvalue = callback(value);\n\t\t} catch (e) {\n\t\t\treject(promise, e);\n\t\t}\n\t}\n\n\tif (!handleThenable(promise, value)) {\n\t\tif (settled === FULFILLED) {\n\t\t\tresolve(promise, value);\n\t\t}\n\n\t\tif (settled === REJECTED) {\n\t\t\treject(promise, value);\n\t\t}\n\t}\n}\n\nfunction handleThenable(promise, value) {\n\tvar resolved;\n\n\ttry {\n\t\tif (promise === value) {\n\t\t\tthrow new TypeError('A promises callback cannot return that same promise.');\n\t\t}\n\n\t\tif (value && (typeof value === 'function' || typeof value === 'object')) {\n\t\t\t// then should be retrieved only once\n\t\t\tvar then = value.then;\n\n\t\t\tif (typeof then === 'function') {\n\t\t\t\tthen.call(value, function (val) {\n\t\t\t\t\tif (!resolved) {\n\t\t\t\t\t\tresolved = true;\n\n\t\t\t\t\t\tif (value === val) {\n\t\t\t\t\t\t\tfulfill(promise, val);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresolve(promise, val);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}, function (reason) {\n\t\t\t\t\tif (!resolved) {\n\t\t\t\t\t\tresolved = true;\n\n\t\t\t\t\t\treject(promise, reason);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t} catch (e) {\n\t\tif (!resolved) {\n\t\t\treject(promise, e);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\nfunction resolve(promise, value) {\n\tif (promise === value || !handleThenable(promise, value)) {\n\t\tfulfill(promise, value);\n\t}\n}\n\nfunction fulfill(promise, value) {\n\tif (promise._state === PENDING) {\n\t\tpromise._state = SETTLED;\n\t\tpromise._data = value;\n\n\t\tasyncCall(publishFulfillment, promise);\n\t}\n}\n\nfunction reject(promise, reason) {\n\tif (promise._state === PENDING) {\n\t\tpromise._state = SETTLED;\n\t\tpromise._data = reason;\n\n\t\tasyncCall(publishRejection, promise);\n\t}\n}\n\nfunction publish(promise) {\n\tpromise._then = promise._then.forEach(invokeCallback);\n}\n\nfunction publishFulfillment(promise) {\n\tpromise._state = FULFILLED;\n\tpublish(promise);\n}\n\nfunction publishRejection(promise) {\n\tpromise._state = REJECTED;\n\tpublish(promise);\n\tif (!promise._handled && isNode) {\n\t\tglobal.process.emit('unhandledRejection', promise._data, promise);\n\t}\n}\n\nfunction notifyRejectionHandled(promise) {\n\tglobal.process.emit('rejectionHandled', promise);\n}\n\n/**\n * @class\n */\nfunction Promise(resolver) {\n\tif (typeof resolver !== 'function') {\n\t\tthrow new TypeError('Promise resolver ' + resolver + ' is not a function');\n\t}\n\n\tif (this instanceof Promise === false) {\n\t\tthrow new TypeError('Failed to construct \\'Promise\\': Please use the \\'new\\' operator, this object constructor cannot be called as a function.');\n\t}\n\n\tthis._then = [];\n\n\tinvokeResolver(resolver, this);\n}\n\nPromise.prototype = {\n\tconstructor: Promise,\n\n\t_state: PENDING,\n\t_then: null,\n\t_data: undefined,\n\t_handled: false,\n\n\tthen: function (onFulfillment, onRejection) {\n\t\tvar subscriber = {\n\t\t\towner: this,\n\t\t\tthen: new this.constructor(NOOP),\n\t\t\tfulfilled: onFulfillment,\n\t\t\trejected: onRejection\n\t\t};\n\n\t\tif ((onRejection || onFulfillment) && !this._handled) {\n\t\t\tthis._handled = true;\n\t\t\tif (this._state === REJECTED && isNode) {\n\t\t\t\tasyncCall(notifyRejectionHandled, this);\n\t\t\t}\n\t\t}\n\n\t\tif (this._state === FULFILLED || this._state === REJECTED) {\n\t\t\t// already resolved, call callback async\n\t\t\tasyncCall(invokeCallback, subscriber);\n\t\t} else {\n\t\t\t// subscribe\n\t\t\tthis._then.push(subscriber);\n\t\t}\n\n\t\treturn subscriber.then;\n\t},\n\n\tcatch: function (onRejection) {\n\t\treturn this.then(null, onRejection);\n\t}\n};\n\nPromise.all = function (promises) {\n\tif (!Array.isArray(promises)) {\n\t\tthrow new TypeError('You must pass an array to Promise.all().');\n\t}\n\n\treturn new Promise(function (resolve, reject) {\n\t\tvar results = [];\n\t\tvar remaining = 0;\n\n\t\tfunction resolver(index) {\n\t\t\tremaining++;\n\t\t\treturn function (value) {\n\t\t\t\tresults[index] = value;\n\t\t\t\tif (!--remaining) {\n\t\t\t\t\tresolve(results);\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\tfor (var i = 0, promise; i < promises.length; i++) {\n\t\t\tpromise = promises[i];\n\n\t\t\tif (promise && typeof promise.then === 'function') {\n\t\t\t\tpromise.then(resolver(i), reject);\n\t\t\t} else {\n\t\t\t\tresults[i] = promise;\n\t\t\t}\n\t\t}\n\n\t\tif (!remaining) {\n\t\t\tresolve(results);\n\t\t}\n\t});\n};\n\nPromise.race = function (promises) {\n\tif (!Array.isArray(promises)) {\n\t\tthrow new TypeError('You must pass an array to Promise.race().');\n\t}\n\n\treturn new Promise(function (resolve, reject) {\n\t\tfor (var i = 0, promise; i < promises.length; i++) {\n\t\t\tpromise = promises[i];\n\n\t\t\tif (promise && typeof promise.then === 'function') {\n\t\t\t\tpromise.then(resolve, reject);\n\t\t\t} else {\n\t\t\t\tresolve(promise);\n\t\t\t}\n\t\t}\n\t});\n};\n\nPromise.resolve = function (value) {\n\tif (value && typeof value === 'object' && value.constructor === Promise) {\n\t\treturn value;\n\t}\n\n\treturn new Promise(function (resolve) {\n\t\tresolve(value);\n\t});\n};\n\nPromise.reject = function (reason) {\n\treturn new Promise(function (resolve, reject) {\n\t\treject(reason);\n\t});\n};\n\nmodule.exports = Promise;\n"]}
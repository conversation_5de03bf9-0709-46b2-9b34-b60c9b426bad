// 定义订阅消息接口
import { API_ENDPOINTS } from './constants';

interface SubscribeMessageData {
  [key: string]: {
    value: string;
  };
}

interface SubscribeMessageParams {
  template_id: string;
  page?: string;
  data: SubscribeMessageData;
  send_time?: string;
  openid?: string;
}

class SubscribeMessageService {
  // 添加定时订阅消息
  static async addScheduledMessage(params: SubscribeMessageParams): Promise<any> {
    const openid = params.openid || wx.getStorageSync('openid');
    if (!openid) {
      throw new Error('未找到用户openid');
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: API_ENDPOINTS.SCHEDULED_MESSAGE,
        method: 'POST',
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        data: {
          openid,
          template_id: params.template_id,
          page: params.page || 'pages/index/index',
          send_time: params.send_time || this.formatDate(new Date()),
          data: params.data
        },
        success: (res: any) => {
          console.log('添加定时消息结果:', res.data);
          if (res.data && res.data.code === 1) {
            resolve({
              success: true,
              data: res.data
            });
          } else {
            const message = res.data ? res.data.msg : '添加定时消息失败';
            resolve({
              success: false,
              message: message
            });
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '请求失败'));
        }
      });
    });
  }

  // 请求订阅授权
  static async requestSubscribe(templateIds: string[]): Promise<Record<string, string>> {
    return new Promise((resolve, reject) => {
      wx.requestSubscribeMessage({
        tmplIds: templateIds,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  // 获取默认发送时间（当前时间）
  private static getDefaultSendTime(): string {
    const now = new Date();
    return this.formatDate(now);
  }

  // 格式化日期
  static formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  // 获取未来某个时间点
  static getFutureTime(minutes: number): string {
    const future = new Date(Date.now() + minutes * 60 * 1000);
    return this.formatDate(future);
  }
}

export default SubscribeMessageService; 
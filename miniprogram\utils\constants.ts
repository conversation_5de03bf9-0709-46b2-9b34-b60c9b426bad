export const CACHE_NONE = 'none';
export const CACHE_MEMORY = 'memory';
export const CACHE_STORAGE = 'storage';
export const CACHE_BOTH = 'both';

// 基础 URL
export const DOMAIN = 'https://tool.feifan919.com';
export const BASE_URL = `${DOMAIN}/api/`;

// 静态资源 URL
export const STATIC_URL = {
  ICON: `${DOMAIN}/wx/icon/`,
  IMAGE: `${DOMAIN}/assets/img/`,
  FONT: `${DOMAIN}/wx/font/`,
  IMG_STYLE: `${DOMAIN}/wx/img_style/`
};

// 常用资源
export const COMMON_ASSETS = {
  DEFAULT_AVATAR: `${STATIC_URL.IMAGE}avatar.png`
};

// API 特定端点
export const API_ENDPOINTS = {
  UPLOAD: `${BASE_URL}common/upload`,
  WX_LOGIN: `${BASE_URL}user/wxLogin`,
  WXACODE: `${BASE_URL}studio/wxacode`,
  UPLOAD_AVATAR: `${BASE_URL}user/uploadAvatar`,
  ENCRYPT: `${BASE_URL}studio/encrypt`,
  SEND_MESSAGE: `${BASE_URL}wechat/sendSubscribeMessage`,
  SCHEDULED_MESSAGE: `${BASE_URL}wechat/addScheduledMessage`,
  UPLOAD_PORTFOLIO: `${BASE_URL}series/uploadPortfolio`
};

export const STORAGE_PREFIX = 'request_cache_'; 
// 引入图表数据生成工具
import ChartHelper from '../../utils/chart-helper';

interface ChartType {
  type: string;
  name: string;
}

interface PageData {
  chartTypes: ChartType[];
  currentChartType: string;
  currentPreset: string;
  chartData: any;
  chartOpts: any;
  loading: boolean;
  pieChartData: any;
  columnChartData: any;
  lineChartData: any;
  presets: string[];
}

interface PageInstance {
  data: PageData;
  initChartData: () => void;
  initMultiCharts: () => void;
  generateChartData: (chartType: string) => void;
  switchChartType: (e: WechatMiniprogram.TouchEvent) => void;
  updateData: () => void;
  switchPreset: () => void;
  onChartClick: (e: any) => void;
  onChartInited: (e: any) => void;
  onPieClick: (e: any) => void;
  onColumnClick: (e: any) => void;
  onLineClick: (e: any) => void;
}

/**
 * 预生成不同图表类型的数据，避免切换时的延迟
 */
function generateChartDataByType(chartType: string): any {
  switch(chartType) {
    case 'column':
      return ChartHelper.createMultiSeriesData(
        ['一月', '二月', '三月', '四月', '五月', '六月'],
        [
          [18, 25, 30, 35, 28, 40],
          [12, 20, 25, 30, 22, 35]
        ],
        ['今年', '去年']
      );
    case 'line':
      return ChartHelper.createMultiSeriesData(
        ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        [
          [15, 20, 25, 30, 35, 40, 45],
          [10, 15, 20, 25, 30, 35, 40]
        ],
        ['本周', '上周']
      );
    case 'area':
      return ChartHelper.createColumnData(
        ['一季度', '二季度', '三季度', '四季度'],
        [35, 45, 38, 50],
        '销售趋势'
      );
    case 'bar':
      return ChartHelper.createMultiSeriesData(
        ['北京', '上海', '广州', '深圳', '杭州'],
        [
          [55, 60, 45, 50, 42],
          [65, 55, 50, 45, 38]
        ],
        ['去年', '今年']
      );
    case 'pie':
      return ChartHelper.createPieData(
        ['研发部', '市场部', '销售部', '客服部', '行政部'],
        [35, 25, 20, 15, 5]
      );
    case 'ring':
      return ChartHelper.createPieData(
        ['完成', '进行中', '未开始'],
        [60, 30, 10]
      );
    case 'radar':
      // 使用增强型雷达图
      // 创建6个维度的指标配置
      const indicators = [
        { text: '产品力', max: 100, color: '#5B8FF9' },
        { text: '技术力', max: 100, color: '#5AD8A6' },
        { text: '服务力', max: 100, color: '#5D7092' },
        { text: '创新力', max: 100, color: '#F6BD16' },
        { text: '团队力', max: 100, color: '#6DC8EC' },
        { text: '资金力', max: 100, color: '#9270CA' }
      ];
      
      // 配置雷达图的显示参数，使其更大更清晰
      const radarConfig = {
        gridType: 'polygon',
        gridColor: '#CCCCCC',
        gridCount: 5,
        opacity: 0.3,
        labelColor: '#333333',
        border: true,
        borderWidth: 3,
        fontSize: 18,         // 增大字体大小
        shape: 'polygon',
        radius: 220,          // 大幅增大雷达图半径
        splitNumber: 5,       // 分割数量
        point: true,          // 显示数据点
        pointSize: 5,         // 增大数据点大小
        labelShow: true,      // 显示标签
        animation: true,      // 启用动画
        labelPadding: 15      // 增大标签与图表的距离
      };
      
      return ChartHelper.createEnhancedRadarData(
        indicators,
        [
          [95, 85, 90, 80, 85, 75],
          [65, 70, 75, 90, 65, 60]
        ],
        ['我司', '行业平均'],
        radarConfig
      );
    case 'gauge':
      return ChartHelper.createGaugeData(0.75, '完成率');
    case 'funnel':
      return ChartHelper.createFunnelData(
        ['访问', '浏览', '加购', '下单', '支付'],
        [100, 80, 50, 30, 20]
      );
    case 'mount':
      return ChartHelper.createMountData(
        ['一月', '二月', '三月', '四月', '五月'],
        [25, 35, 30, 45, 40],
        '销售趋势'
      );
    case 'word':
      return ChartHelper.createWordCloudData(
        ['微信小程序', 'uCharts', '图表', '可视化', '数据分析', '前端开发', 'Canvas', 'JavaScript', '组件化', '通用组件'],
        [80, 70, 65, 60, 55, 50, 45, 40, 35, 30],
        [20, 18, 16, 16, 14, 14, 12, 12, 10, 10]
      );
    case 'mix':
      return ChartHelper.createMixData(
        ['一月', '二月', '三月', '四月', '五月', '六月'],
        [25, 35, 30, 45, 40, 50],
        '销售额',
        [65, 55, 70, 60, 75, 80],
        '增长率'
      );
    default:
      return {}; 
  }
}

// 提前准备多图表数据
const pieChartData = ChartHelper.createPieData(
  ['产品A', '产品B', '产品C', '产品D', '产品E'],
  [30, 25, 18, 15, 12]
);

const columnChartData = ChartHelper.createColumnData(
  ['一月', '二月', '三月', '四月', '五月'],
  [12, 19, 8, 23, 16],
  '销售额'
);

const lineChartData = ChartHelper.createMultiSeriesData(
  ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
  [
    [10, 15, 20, 25, 22, 28, 30],
    [5, 10, 12, 15, 20, 25, 18]
  ],
  ['本周', '上周']
);

Page<PageData, PageInstance>({
  data: {
    // 图表类型列表
    chartTypes: [
      { type: 'column', name: '柱状图' },
      { type: 'line', name: '折线图' },
      { type: 'area', name: '区域图' },
      { type: 'bar', name: '条形图' },
      { type: 'pie', name: '饼图' },
      { type: 'ring', name: '环形图' },
      { type: 'radar', name: '雷达图' },
      { type: 'gauge', name: '仪表盘' },
      { type: 'funnel', name: '漏斗图' },
      { type: 'mount', name: '山峰图' },
      { type: 'word', name: '词云图' },
      { type: 'mix', name: '混合图' }
    ],
    currentChartType: 'column',  // 当前图表类型
    currentPreset: 'default',    // 当前样式预设
    // 直接初始化图表数据，避免先显示默认数据
    chartData: generateChartDataByType('column'),
    // 为雷达图添加额外样式配置
    chartOpts: {
      radar: {
        radius: 220,           // 增大雷达图半径
        splitNumber: 5,        // 分割数量
        point: true,           // 显示数据点
        pointSize: 5,          // 增大数据点大小
        labelShow: true,       // 显示标签
        labelPadding: 15,      // 增大标签与图表的距离
        fontSize: 18,          // 增大标签字体大小
        labelColor: '#333333'  // 标签颜色
      }
    },
    loading: false,              // 加载状态
    
    // 多图表数据 - 直接使用预先生成的数据
    pieChartData,
    columnChartData,
    lineChartData,
    
    // 预设样式切换顺序
    presets: ['default', 'dark', 'businessBlue', 'colorful']
  },

  onLoad() {
    // 不再需要立即初始化数据
    // 如果需要后续更新数据，可以添加更新逻辑
  },
  
  /**
   * 初始化所有图表数据
   */
  initChartData() {
    // 用于完全重置所有图表数据的方法
    this.setData({
      chartData: generateChartDataByType(this.data.currentChartType),
      pieChartData,
      columnChartData,
      lineChartData
    });
  },
  
  /**
   * 初始化多图表示例数据
   */
  initMultiCharts() {
    // 只用于刷新多图表区域
    this.setData({
      pieChartData,
      columnChartData,
      lineChartData
    });
  },
  
  /**
   * 根据图表类型生成数据
   */
  generateChartData(chartType: string) {
    // 设置加载状态
    this.setData({ loading: true });
    
    // 使用定时器模拟请求过程
    setTimeout(() => {
      const chartData = generateChartDataByType(chartType);
      
      // 更新图表数据
      this.setData({
        chartData,
        loading: false
      });
    }, 500);
  },
  
  /**
   * 切换图表类型
   */
  switchChartType(e: WechatMiniprogram.TouchEvent) {
    const type = e.currentTarget.dataset.type;
    if (type === this.data.currentChartType) {
      return;
    }
    
    this.setData({
      currentChartType: type
    });
    
    // 生成新图表数据
    this.generateChartData(type);
  },
  
  /**
   * 更新图表数据
   */
  updateData() {
    // 保存当前图表类型
    const currentType = this.data.currentChartType;
    
    // 设置加载状态
    this.setData({ loading: true });
    
    // 使用定时器模拟请求过程
    setTimeout(() => {
      // 特殊处理雷达图数据更新 - 确保指标配置保持不变
      let chartData;
      
      if (currentType === 'radar') {
        // 雷达图特殊处理 - 保持指标配置，只更新数据值
        const indicators = [
          { text: '产品力', max: 100, color: '#5B8FF9' },
          { text: '技术力', max: 100, color: '#5AD8A6' },
          { text: '服务力', max: 100, color: '#5D7092' },
          { text: '创新力', max: 100, color: '#F6BD16' },
          { text: '团队力', max: 100, color: '#6DC8EC' },
          { text: '资金力', max: 100, color: '#9270CA' }
        ];
        
        // 生成随机数据但保持相同的配置结构
        const radarConfig = {
          gridType: 'polygon',
          gridColor: '#CCCCCC',
          gridCount: 5,
          opacity: 0.3,
          labelColor: '#333333',
          border: true,
          borderWidth: 3,
          fontSize: 18,         
          shape: 'polygon',
          radius: 220,          
          splitNumber: 5,       
          point: true,          
          pointSize: 5,         
          labelShow: true,      
          animation: true,      
          labelPadding: 15      
        };
        
        // 生成新的随机数据
        const getRandomValue = () => Math.floor(Math.random() * 40) + 60; // 60-100之间的随机数
        
        chartData = ChartHelper.createEnhancedRadarData(
          indicators,
          [
            [getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue()],
            [getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue(), getRandomValue()]
          ],
          ['我司', '行业平均'],
          radarConfig
        );
      } else {
        // 其他图表类型正常更新
        chartData = generateChartDataByType(currentType);
      }
      
      // 更新图表数据
      this.setData({
        chartData,
        loading: false
      });
    }, 500);
  },
  
  /**
   * 切换图表样式预设
   */
  switchPreset() {
    const presets = this.data.presets;
    const currentIndex = presets.indexOf(this.data.currentPreset);
    const nextIndex = (currentIndex + 1) % presets.length;
    
    this.setData({
      currentPreset: presets[nextIndex]
    });
  },
  
  /**
   * 图表点击事件
   */
  onChartClick(e: any) {
    const { index, item } = e.detail;
    
    wx.showToast({
      title: `点击了: ${JSON.stringify(item)}`,
      icon: 'none'
    });
  },
  
  /**
   * 图表初始化完成事件
   */
  onChartInited(e: any) {
    console.log('图表初始化完成', e.detail);
  },
  
  /**
   * 饼图点击事件
   */
  onPieClick(e: any) {
    const { index, item } = e.detail;
    if (item) {
      wx.showToast({
        title: `饼图: ${item.name} - ${item.value}`,
        icon: 'none'
      });
    }
  },
  
  /**
   * 柱状图点击事件
   */
  onColumnClick(e: any) {
    const { index, item } = e.detail;
    if (item) {
      wx.showToast({
        title: `柱图: ${JSON.stringify(item)}`,
        icon: 'none'
      });
    }
  },
  
  /**
   * 折线图点击事件
   */
  onLineClick(e: any) {
    const { index, item } = e.detail;
    if (item) {
      wx.showToast({
        title: `折线图: ${JSON.stringify(item)}`,
        icon: 'none'
      });
    }
  }
}); 
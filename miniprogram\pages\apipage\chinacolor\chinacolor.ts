// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api from '../../../utils/api';
import eventBus from '../../../utils/eventBus';

// 优化类型定义
interface Canvas {
  createImage(): {
    onload: (value: unknown) => void;
    onerror: (err: any) => void;
    src: string;
    width: number;
    height: number;
  };
  getContext(type: string): any;
  width: number;
  height: number;
}

// 优化颜色数据接口定义
interface ColorData {
  id: number;
  color_name: string[];  // 修改为字符串数组类型
  color_pinyin: string;
  color_name_en: string;
  color_alias: string[] | string;  // 支持数组或字符串类型
  color_description: string[] | string;  // 支持数组或字符串类型
  hex_value: string;
  rgb: string;
  cmyk: string;
  color_category: string;
  color_subcategory: string;
  dynasty_first_seen: string;
  historical_reference: string;
  cultural_meaning: string;
  usage_scenarios: string;
  psychological_effect?: string;
  similar_colors?: string[];
  complementary_colors?: string[];
  seasonal_usage?: Record<string, string>;
  modern_mix_formula?: {
    mineral_formula?: {
      formula: string;
      origins: Record<string, string>;
      materials: Record<string, number>;
      craft_method: string;
      processing_steps: string[];
    };
    original_formula?: {
      formula: string;
      percentages: Record<string, number>;
    };
    basic_colors_formula?: {
      formula: string;
      description: string;
      percentages: Record<string, number>;
    };
  };
  metadata?: Record<string, any>;
}

// 优化朝代顺序映射
const DYNASTY_ORDER: Record<string, number> = {
  '先秦': 1,
  '秦代': 2,
  '汉代': 3,
  '三国': 4,
  '晋代': 5,
  '南北朝': 6,
  '隋代': 7,
  '唐代': 8,
  '五代': 9,
  '宋代': 10,
  '元代': 11,
  '明代': 12,
  '清代': 13,
  '近代': 14,
  '现代': 15
};

// 添加缓存相关常量
const CACHE_KEY = {
  COLOR_DATA: 'colorData',
  FONT_LOADED: 'fontLoaded',
  CATEGORY_ORDER: 'categoryOrder'
};

// 添加防抖延迟常量
const DEBOUNCE_DELAY = 100;

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    loading: true,
    isEmpty: false,
    colorList: [] as ColorData[],
    displayColorList: [] as ColorData[],
    selectedColor: null as ColorData | null,
    tabBarHeight: 0,
    categories: [] as string[],
    currentCategory: '',
    fontLoaded: false,
    showColorDetail: false,
    categoryOrderMap: null as Map<string, number> | null,
    debounceTimer: null as number | null,
    shareImage: ''
  },

  lifetimes: {
    attached: function() {
      // 优化事件监听
      this._bindEvents();
      this.loadColorData();
    },
    
    detached: function() {
      // 优化事件解绑和清理
      this._unbindEvents();
      this._clearDebounceTimer();
    }    
  },

  methods: {
    // 优化事件绑定方法
    _bindEvents() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },

    // 优化事件解绑方法
    _unbindEvents() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },

    // 优化定时器清理方法
    _clearDebounceTimer() {
      if (this.data.debounceTimer) {
        clearTimeout(this.data.debounceTimer);
        this.setData({ debounceTimer: null });
      }
    },

    handleTabBarChange: function(data: { 
      isCollapsed: boolean;
      expandedHeight: number;
      collapsedHeight: number;
      currentHeight: number;
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    // 优化数据加载方法
    async loadColorData() {
      this.setData({ loading: true });
      try {
        // 尝试从缓存加载数据
        const cachedData = wx.getStorageSync(CACHE_KEY.COLOR_DATA);
        if (cachedData && cachedData.list && cachedData.list.length > 0 && cachedData.timestamp && 
            Date.now() - cachedData.timestamp < 24 * 60 * 60 * 1000) { // 缓存24小时
          console.log('从缓存加载数据：', cachedData.list.length);
          this.processColorData(cachedData.list);
          return;
        }

        const res = await Api.color.getAllColors('', '');
        console.log('从API获取数据：', res);
        
        if (res && res.list && res.list.length > 0) {
          // 保存到缓存，添加时间戳
          wx.setStorageSync(CACHE_KEY.COLOR_DATA, {
            list: res.list,
            timestamp: Date.now()
          });
          this.processColorData(res.list);
        } else {
          throw new Error('获取数据为空');
        }

      } catch (error) {
        console.error('加载颜色数据失败：', error);
        wx.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
        this.setData({
          loading: false,
          isEmpty: true
        });
      }
    },

    // 优化颜色转换方法
    hexToHSL(hexColor: string): { h: number; s: number; l: number } {
      const hex = hexColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16) / 255;
      const g = parseInt(hex.substr(2, 2), 16) / 255;
      const b = parseInt(hex.substr(4, 2), 16) / 255;
      
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0;
      let s = 0;
      const l = (max + min) / 2;

      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
      }

      return { 
        h: Math.round(h * 360), 
        s: Math.round(s * 100), 
        l: Math.round(l * 100) 
      };
    },

    // 优化亮度计算方法
    calculateBrightness(hexColor: string): number {
      const hex = hexColor.replace('#', '');
      const [r, g, b] = [
        parseInt(hex.substr(0, 2), 16),
        parseInt(hex.substr(2, 2), 16),
        parseInt(hex.substr(4, 2), 16)
      ];
      
      return Math.sqrt(
        0.299 * (r * r) +
        0.587 * (g * g) +
        0.114 * (b * b)
      );
    },

    // 优化防抖实现
    debounce<T extends (...args: any[]) => void>(func: T, wait: number = DEBOUNCE_DELAY) {
      this._clearDebounceTimer();
      const timer = setTimeout(() => {
        func.call(this);
      }, wait);
      this.setData({ debounceTimer: timer });
    },

    // 优化分类切换
    switchCategory(e: WechatMiniprogram.CustomEvent) {
      const category = e.currentTarget.dataset.category;
      this.debounce(() => {
        if (this.data.currentCategory !== category) {
          this.setData({ currentCategory: category }, () => {
            this.filterColors();
          });
        }
      });
    },

    // 优化颜色选择
    onColorSelect(e: WechatMiniprogram.CustomEvent<{ color: ColorData }>) {
      const color = e.currentTarget.dataset.color;
      if (color) {
        this.setData({ selectedColor: color });
      }
    },

    // 优化数据处理
    processColorData(list: ColorData[]) {
      try {
        // 创建类别顺序映射
        if (!this.data.categoryOrderMap) {
          const categoryOrder = new Map<string, number>();
          list.forEach((color, index) => {
            if (!categoryOrder.has(color.color_category)) {
              categoryOrder.set(color.color_category, index);
            }
          });
          this.setData({ categoryOrderMap: categoryOrder });
        }
        
        // 提取并排序类别
        const categories = Array.from(new Set(list.map(item => item.color_category)))
          .sort((a, b) => {
            const orderA = this.data.categoryOrderMap && this.data.categoryOrderMap.get(a) || 0;
            const orderB = this.data.categoryOrderMap && this.data.categoryOrderMap.get(b) || 0;
            return orderA - orderB;
          });

        // 处理颜色数据
        const processedList = this.sortColorsById(list).map(color => {
          try {
            // 优化数据解析
            ['similar_colors', 'complementary_colors', 'seasonal_usage', 
             'modern_mix_formula', 'metadata'].forEach(key => {
              if (typeof color[key] === 'string') {
                try {
                  color[key] = JSON.parse(color[key]);
                } catch (e) {
                  console.warn(`解析${key}失败:`, e);
                }
              }
            });

            // 处理字符串数组
            ['color_description', 'color_name'].forEach(key => {
              if (typeof color[key] === 'string') {
                color[key] = color[key].split('');
              }
            });
          } catch (error) {
            console.error('处理颜色数据失败:', color.id, error);
          }
          return color;
        });
        
        this.setData({
          colorList: processedList,
          displayColorList: processedList,
          categories,
          loading: false,
          selectedColor: processedList[0]
        }, () => {
          this.filterColors();
          wx.nextTick(() => {
            this.generateFontPreview();
          });
        });
      } catch (error) {
        console.error('处理数据失败:', error);
        this.setData({ loading: false, isEmpty: true });
      }
    },

    // 获取颜色的排序权重
    getColorWeight(hexColor: string): number {
      const hsl = this.hexToHSL(hexColor);
      // 调整色相使红色在开始位置（原本红色色相为0或360）
      let hue = hsl.h;
      // 将色相值标准化到0-360度
      if (hue < 0) hue += 360;
      if (hue >= 360) hue -= 360;
      return hue;
    },

    // 排序颜色列表
    sortColorsById(colors: ColorData[]): ColorData[] {
      return [...colors].sort((a, b) => a.id - b.id);
    },

    filterColors() {
      let filteredList = [...this.data.colorList];
      
      if (this.data.currentCategory) {
        filteredList = filteredList.filter(color => color.color_category === this.data.currentCategory);
      }

      // 保持ID排序
      filteredList = this.sortColorsById(filteredList);

      this.setData({ 
        displayColorList: filteredList,
        selectedColor: filteredList.find(color => this.data.selectedColor && color.id === this.data.selectedColor.id) || filteredList[0]
      });
    },

    // 滚动到指定分类
    scrollToCategory(category: string) {
      const query = wx.createSelectorQuery().in(this);
      query.select(`.filter-item[data-category="${category}"]`).boundingClientRect();
      query.select('.category-scroll').boundingClientRect();
      query.exec((res) => {
        if (res[0] && res[1]) {
          const itemRect = res[0];
          const scrollRect = res[1];
          const scrollLeft = itemRect.left - scrollRect.left - (scrollRect.width - itemRect.width) / 2;
          wx.createSelectorQuery().in(this).select('.category-scroll').node().exec((res) => {
            const scrollView = res[0].node;
            scrollView.scrollTo({ left: scrollLeft, duration: 300 });
          });
        }
      });
    },

    // 显示颜色详情弹出层
    showColorDetail() {
      if (this.data.selectedColor) {
        this.setData({ showColorDetail: true });
      }
    },

    // 隐藏颜色详情弹出层
    hideColorDetail() {
      this.setData({ showColorDetail: false });
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 空函数，用于阻止事件冒泡
    },

    onShareTimeline() {
      return new Promise((resolve) => {
        this.captureMainContent().then(imageUrl => {
          resolve({ title: '中国传统色', imageUrl });
        });
      });
    },

    onShareAppMessage() {
      return new Promise((resolve) => {
        this.captureMainContent().then(imageUrl => {
          resolve({
            title: '中国传统色',
            path: '/pages/apipage/chinacolor/chinacolor',
            imageUrl
          });
        });
      });
    },

    async captureMainContent() {
      try {
        const canvas = (await new Promise(resolve => {
          wx.createSelectorQuery()
            .in(this)
            .select('#shareCanvas')
            .fields({ node: true })
            .exec(res => resolve(res[0].node));
        })) as WechatMiniprogram.Canvas;

        if (!canvas) return '/assets/share-default.jpg';

        const ctx = canvas.getContext('2d');
        const dpr = wx.getSystemInfoSync().pixelRatio;
        const width = 500;
        const height = 400;
        
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);

        // 绘制背景和文字
        if (this.data.selectedColor) {
          // 背景色
          ctx.fillStyle = this.data.selectedColor.hex_value;
          ctx.fillRect(0, 0, width, height);

          // 文字样式
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillStyle = '#FFFFFF';
          ctx.shadowColor = 'rgba(0,0,0,0.5)';
          ctx.shadowBlur = 10;
          ctx.shadowOffsetX = 2;
          ctx.shadowOffsetY = 2;

          // 绘制文字
          ctx.font = 'bold 48px CustomFont';
          ctx.fillText(this.data.selectedColor.color_name.join(''), width/2, height/2 - 30);
          
          ctx.font = '24px sans-serif';
          ctx.fillText(this.data.selectedColor.color_pinyin, width/2, height/2 + 30);
          
          ctx.font = '20px monospace';
          ctx.fillText(this.data.selectedColor.hex_value, width/2, height - 40);
        }

        // 生成图片
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas,
            success: res => resolve(res.tempFilePath),
            fail: () => resolve('/assets/share-default.jpg')
          });
        });

        this.setData({ shareImage: tempFilePath });
        return tempFilePath;
      } catch (error) {
        return '/assets/share-default.jpg';
      }
    },

    // 文字转hex
    textToHex(text: string) {
      return text.split('').map(char => char.charCodeAt(0).toString(16)).join(',');
    },

    // 优化字体加载
    async generateFontPreview() {
      if (this.data.fontLoaded) return;
      
      try {
        if (!this.data.colorList || this.data.colorList.length === 0) {
          return;
        }

        const allColorNames = Array.from(new Set(this.data.colorList.map(color => color.color_name).join(''))).join('');
        const hexWords = this.textToHex(allColorNames);
        
        const purl = 'https://font.chinaz.com/api/fonts/FontPreview.ashx?format=ttf&hex=1&font_file=/font3/bb5095.ttf&t=ttf&words=' + hexWords;

        const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
          wx.request({
            url: purl,
            method: 'GET',
            responseType: 'arraybuffer',
            header: {
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Origin': 'https://font.chinaz.com',
              'Referer': 'https://font.chinaz.com/'
            },
            success: resolve,
            fail: reject
          });
        });

        if (res.statusCode === 200 && res.data) {
          const buffer = res.data as ArrayBuffer;
          const base64 = wx.arrayBufferToBase64(buffer);

          await wx.loadFontFace({
            family: 'CustomFont',
            source: `data:font/ttf;base64,${base64}`,
            success: () => {
              this.setData({ fontLoaded: true });
            },
            fail: (error) => {
              console.error('字体加载失败:', error);
            }
          });
        }
      } catch (error) {
        console.error('字体加载过程出错:', error);
      }
    }
  }
});





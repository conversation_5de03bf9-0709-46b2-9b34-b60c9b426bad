{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nfunction isTrue(value) {\n  return !!value && value !== \"0\" && value !== \"false\"\n}\n\nvar envDisable = isTrue(process.env.DISABLE_OPENCOLLECTIVE) || isTrue(process.env.OPEN_SOURCE_CONTRIBUTOR) || isTrue(process.env.CI);\nvar logLevel = process.env.npm_config_loglevel;\nvar logLevelDisplay = ['silent', 'error', 'warn'].indexOf(logLevel) > -1;\n\nif (!envDisable && !logLevelDisplay) {\n  var pkg = require(require('path').resolve('./package.json'));\n  if (pkg.collective) {\n    console.log(`\\u001b[96m\\u001b[1mThank you for using ${pkg.name}!\\u001b[96m\\u001b[1m`);\n    console.log(`\\u001b[0m\\u001b[96mIf you rely on this package, please consider supporting our open collective:\\u001b[22m\\u001b[39m`);\n    console.log(`> \\u001b[94m${pkg.collective.url}/donate\\u001b[0m\\n`);\n  }\n}\n"]}
{"version": 3, "sources": ["mute.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var Stream = require('stream')\n\nmodule.exports = MuteStream\n\n// var out = new MuteStream(process.stdout)\n// argument auto-pipes\nfunction MuteStream (opts) {\n  Stream.apply(this)\n  opts = opts || {}\n  this.writable = this.readable = true\n  this.muted = false\n  this.on('pipe', this._onpipe)\n  this.replace = opts.replace\n\n  // For readline-type situations\n  // This much at the start of a line being redrawn after a ctrl char\n  // is seen (such as backspace) won't be redrawn as the replacement\n  this._prompt = opts.prompt || null\n  this._hadControl = false\n}\n\nMuteStream.prototype = Object.create(Stream.prototype)\n\nObject.defineProperty(MuteStream.prototype, 'constructor', {\n  value: MuteStream,\n  enumerable: false\n})\n\nMuteStream.prototype.mute = function () {\n  this.muted = true\n}\n\nMuteStream.prototype.unmute = function () {\n  this.muted = false\n}\n\nObject.defineProperty(MuteStream.prototype, '_onpipe', {\n  value: onPipe,\n  enumerable: false,\n  writable: true,\n  configurable: true\n})\n\nfunction onPipe (src) {\n  this._src = src\n}\n\nObject.defineProperty(MuteStream.prototype, 'isTTY', {\n  get: getIsTTY,\n  set: setIsTTY,\n  enumerable: true,\n  configurable: true\n})\n\nfunction getIsTTY () {\n  return( (this._dest) ? this._dest.isTTY\n        : (this._src) ? this._src.isTTY\n        : false\n        )\n}\n\n// basically just get replace the getter/setter with a regular value\nfunction setIsTTY (isTTY) {\n  Object.defineProperty(this, 'isTTY', {\n    value: isTTY,\n    enumerable: true,\n    writable: true,\n    configurable: true\n  })\n}\n\nObject.defineProperty(MuteStream.prototype, 'rows', {\n  get: function () {\n    return( this._dest ? this._dest.rows\n          : this._src ? this._src.rows\n          : undefined )\n  }, enumerable: true, configurable: true })\n\nObject.defineProperty(MuteStream.prototype, 'columns', {\n  get: function () {\n    return( this._dest ? this._dest.columns\n          : this._src ? this._src.columns\n          : undefined )\n  }, enumerable: true, configurable: true })\n\n\nMuteStream.prototype.pipe = function (dest, options) {\n  this._dest = dest\n  return Stream.prototype.pipe.call(this, dest, options)\n}\n\nMuteStream.prototype.pause = function () {\n  if (this._src) return this._src.pause()\n}\n\nMuteStream.prototype.resume = function () {\n  if (this._src) return this._src.resume()\n}\n\nMuteStream.prototype.write = function (c) {\n  if (this.muted) {\n    if (!this.replace) return true\n    if (c.match(/^\\u001b/)) {\n      if(c.indexOf(this._prompt) === 0) {\n        c = c.substr(this._prompt.length);\n        c = c.replace(/./g, this.replace);\n        c = this._prompt + c;\n      }\n      this._hadControl = true\n      return this.emit('data', c)\n    } else {\n      if (this._prompt && this._hadControl &&\n          c.indexOf(this._prompt) === 0) {\n        this._hadControl = false\n        this.emit('data', this._prompt)\n        c = c.substr(this._prompt.length)\n      }\n      c = c.toString().replace(/./g, this.replace)\n    }\n  }\n  this.emit('data', c)\n}\n\nMuteStream.prototype.end = function (c) {\n  if (this.muted) {\n    if (c && this.replace) {\n      c = c.toString().replace(/./g, this.replace)\n    } else {\n      c = null\n    }\n  }\n  if (c) this.emit('data', c)\n  this.emit('end')\n}\n\nfunction proxy (fn) { return function () {\n  var d = this._dest\n  var s = this._src\n  if (d && d[fn]) d[fn].apply(d, arguments)\n  if (s && s[fn]) s[fn].apply(s, arguments)\n}}\n\nMuteStream.prototype.destroy = proxy('destroy')\nMuteStream.prototype.destroySoon = proxy('destroySoon')\nMuteStream.prototype.close = proxy('close')\n"]}
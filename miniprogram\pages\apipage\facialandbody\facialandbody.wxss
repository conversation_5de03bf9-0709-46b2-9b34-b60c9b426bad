.refresh-button-container {
  padding: 30rpx 20rpx;
  display: flex;
  justify-content: center;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a90e2;
  color: white;
  border-radius: 40rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.refresh-button.counting {
  background: #a0a0a0;
  min-width: 120rpx;
}

.countdown {
  font-size: 32rpx;
  font-weight: bold;
}

.refresh-button[disabled] {
  background: #a0a0a0;
  box-shadow: none;
}

.refresh-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.refresh-button:not([disabled]):active {
  transform: scale(0.95);
}

.image-container {
  padding: 20rpx;
  margin-top: 60px;
}

.image-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: visible;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.image-wrapper {
  position: relative;
  width: 100%;
  min-height: 400rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.image-number {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(74, 144, 226, 0.5);
  color: white;
  width: 50rpx;
  height: 50rpx;
  border-radius: 0 0 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.main-image {
  padding: 20rpx;
  width: 100%;
  height: auto;
  display: block;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(245, 245, 245, 0.9);
  backdrop-filter: blur(5px);
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  width: 80%;
  max-width: 500rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.loading-step {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  background: rgba(74, 144, 226, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background: #4a90e2;
  border-radius: 3rpx;
  transition: width 0.3s ease-in-out;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.loading-message {
  width: 100%;
  text-align: center;
  background: rgba(74, 144, 226, 0.05);
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.image-info {
  padding: 20rpx;
}

.artist-info {
  margin-bottom: 20rpx;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.image-stats {
  display: flex;
  margin-bottom: 20rpx;
}

.stat-item {
  margin-right: 30rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  gap: 10rpx;
}

.tag {
  background: #f0f0f0;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
}

.links {
  display: flex;
  gap: 20rpx;
}

.link-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  padding: 12rpx 0;
  background: #4a90e2;
  color: white;
  border-radius: 10rpx;
  text-align: center;
  line-height: 1.5;
}

.link-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .image-card {
    background: rgba(30, 30, 30, 0.9);
  }
  
  .image-wrapper {
    background: #333;
  }
  
  .image-loading {
    background: rgba(51, 51, 51, 0.9);
  }
  
  .loading-text {
    color: #999;
  }
  
  .label {
    color: #999;
  }
  
  .value {
    color: #fff;
  }
  
  .tag {
    background: #333;
    color: #fff;
  }

  .refresh-button {
    background: #2d5a8e;
  }

  .refresh-button[disabled],
  .refresh-button.counting {
    background: #4a4a4a;
  }

  .refresh-button-container {
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .image-number {
    background: #2d5a8e;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.4);
  }

  .progress-bar {
    background: rgba(255, 255, 255, 0.1);
  }

  .progress-inner {
    background: #6ba4e5;
  }

  .progress-text {
    color: #999;
  }

  .loading-progress {
    background: rgba(30, 30, 30, 0.9);
  }

  .loading-step {
    color: #999;
    background: rgba(74, 144, 226, 0.2);
  }

  .loading-message {
    background: rgba(74, 144, 226, 0.1);
  }

  .loading-text {
    color: #999;
  }
}

.control-panel {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.8);
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
  z-index: 1;
}

.picker-item {
  flex: 1;
  margin: 0 10rpx;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 12rpx;
  border: 2rpx solid rgba(74, 144, 226, 0.2);
}

.picker-label {
  color: #666;
  font-size: 28rpx;
  margin-right: 16rpx;
  white-space: nowrap;
}

.picker-value {
  color: #4a90e2;
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 分类选择浮窗 */
.category-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-popup.show {
  visibility: visible;
  opacity: 1;
}

.popup-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.category-popup.show .popup-content {
  transform: scale(1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
  cursor: pointer;
}

.category-lists {
  display: flex;
  height: 600rpx;
}

.category-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 2rpx solid #f0f0f0;
}

.category-column:last-child {
  border-right: none;
}

.column-title {
  padding: 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background: #f8f8f8;
  font-weight: 500;
}

.category-scroll {
  flex: 1;
  height: 0;
}

.category-item {
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 2rpx solid #f8f8f8;
  transition: all 0.2s ease;
}

.category-item.active {
  color: #4a90e2;
  background: rgba(74, 144, 226, 0.1);
  font-weight: 500;
}

.category-item:active {
  background: rgba(74, 144, 226, 0.05);
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .popup-content {
    background: #222;
  }

  .popup-header {
    border-bottom-color: #333;
  }

  .popup-title {
    color: #fff;
  }

  .category-column {
    border-right-color: #333;
  }

  .column-title {
    background: #2a2a2a;
    color: #999;
  }

  .category-item {
    color: #fff;
    border-bottom-color: #333;
  }

  .category-item.active {
    color: #6ba4e5;
    background: rgba(74, 144, 226, 0.2);
  }

  .category-item:active {
    background: rgba(74, 144, 226, 0.15);
  }
}

.picker-wrapper {
  flex: 1;
  margin: 0 10rpx;
}

.picker-arrow {
  margin-left: 8rpx;
  color: #4a90e2;
  font-size: 24rpx;
}

@media (prefers-color-scheme: dark) {
  .picker-arrow {
    color: #6ba4e5;
  }
}

/* 选择器样式 */
.selector-wrapper {
  flex: 1;
  margin: 0 10rpx;
}

.selector-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 12rpx;
  border: 2rpx solid rgba(74, 144, 226, 0.2);
}

.selector-label {
  color: #666;
  font-size: 28rpx;
  margin-right: 16rpx;
  white-space: nowrap;
}

.selector-value {
  color: #4a90e2;
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

.selector-arrow {
  margin-left: 8rpx;
  color: #4a90e2;
  font-size: 24rpx;
}






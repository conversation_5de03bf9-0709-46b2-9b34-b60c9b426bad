import { layoutUtil } from '../../utils/layout';
import eventBus from '../../utils/eventBus';
interface IDragData {
  CUT_L: number | null;
  CUT_T: number | null;
  CUT_R: number | null;
  CUT_B: number | null;
  CUT_W: number | null;
  CUT_H: number | null;
  IS_TOUCH_CONTENT: boolean;
  IS_TOUCH_SIDE: boolean;
  IS_NO_DRAG: boolean;
  TOUCH_OFFSET_X: number | null;
  TOUCH_OFFSET_Y: number | null;
  TOUCH_MAX_MOVE_SECTION_X: number | null;
  TOUCH_MAX_MOVE_SECTION_Y: number | null;
  MOVE_PAGE_X: number | null;
  MOVE_PAGE_Y: number | null;
  SPACE_TOP_POSITION: number | null;
  SPACE_LEFT_POSITION: number | null;
  SPACE_RIGHT_POSITION: number | null;
  SPACE_BOTTOM_POSITION: number | null;
}

interface IConfData {
  IMG_RATIO: number | null;
  IMG_REAL_W: number | null;
  IMG_REAL_H: number | null;
  CROPPER_HEIGHT: number | null;
  CROPPER_WIDTH: number | null;
  CUT_MIN_W: number | null;
  CUT_MIN_H: number | null;
  DRAG_MOVE_RATIO: number;
  INIT_DRAG_POSITION: number;
  DRAW_IMAGE_W: number | null;
  MAX_QW: number;
  MIN_CROPPER_DIS: number;
}

interface IComponentData {
  showImg: boolean;
  cropperW: number;
  cropperH: number;
  cutL: number;
  cutT: number;
  cropperBoxWidth: number;
  cropperBoxHeight: number;
  filePath: string | null;
  imageWidth: number;
  imageHeight: number;
  imageLeft: number;
  imageTop: number;
  imageInfo: {
    width: number;
    height: number;
    path: string;
  } | null;
  isDragging: boolean;
  startX: number;
  startY: number;
  oldCutL: number;
  oldCutT: number;
  currentPaperSize: null;
  paperSizeList: Array<{
    name: string;
    value: string;
    width: number;
    height: number;
  }>;
  paperSizeIndex: number;
  isScaling: boolean;
  scaleStartX: number;
  scaleStartY: number;
  scaleStartWidth: number;
  scaleStartHeight: number;
  safeAreaInsets: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  systemInfo: WechatMiniprogram.SystemInfo;
  contentHeight: number;
  cropperConfig: {
    width: number;
    height: number;
    maxScale: number;
    minScale: number;
    quality: number;
  };
  navHeight: number;
  tabBarHeight: number;
}


Component({
  properties: {
    imageSrc: {
      type: String,
      value: ''
    }
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    contentStyle: '',
    showImg: false,
    cropperW: 0,
    cropperH: 0,
    cutL: 0,
    cutT: 0,
    cropperBoxWidth: 0,
    cropperBoxHeight: 0,
    filePath: null,
    imageWidth: 0,
    imageHeight: 0,
    imageLeft: 0,
    imageTop: 0,
    imageInfo: null,
    isDragging: false,
    startX: 0,
    startY: 0,
    oldCutL: 0,
    oldCutT: 0,
    currentPaperSize: null,
    cropperConfig: {
      width: 750,
      height: 750,
      maxScale: 2.5,
      minScale: 1,
      quality: 1
    },
    paperSizeList: [
      { name: '手绘卡 (6.4cm x 8.8cm)', value: 'hand-drawn-card', width: 64, height: 88 },
      { name: 'A6 (10.5cm x 14.8cm)', value: 'A6', width: 105, height: 148 },
      { name: 'A5 (14.8cm x 21cm)', value: 'A5', width: 148, height: 210 },
      { name: 'A4 (21cm x 29.7cm)', value: 'A4', width: 210, height: 297 }
    ],
    paperSizeIndex: 0,
    isScaling: false,
    scaleStartX: 0,
    scaleStartY: 0,
    scaleStartWidth: 0,
    scaleStartHeight: 0,
    lastMoveTime: 0,
    moveThrottleDelay: 16,
    safeAreaInsets: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    systemInfo: null,
    contentHeight: 0,
    navHeight: 0,
    tabBarHeight: 0
  } as IComponentData,

  lifetimes: {
    attached() {
      // 初始化系统信息和布局
      const systemInfo = wx.getSystemInfoSync();
      const layoutInfo = layoutUtil.getLayoutInfo();
      const navHeight = layoutInfo.statusBarHeight + layoutInfo.navBarHeight;
      const tabBarHeight = layoutInfo.tabBarHeight;
      
      this.setData({
        navHeight,
        tabBarHeight,
        contentHeight: systemInfo.windowHeight - navHeight - tabBarHeight,
        systemInfo
      });

      // 使用 requestAnimationFrame 优化的节流函数
      this.throttledContentDragMove = this.rafThrottle(this.contentDragMove);
      this.throttledScaleMove = this.rafThrottle(this.scaleMove);

      if (this.properties.imageSrc) {
        this.loadImage(this.properties.imageSrc)
      }
      // 注册 tab-bar 状态变化监听
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // 清理可能存在的定时器
      if (this.throttledContentDragMove && this.throttledContentDragMove.timer) {
        clearTimeout(this.throttledContentDragMove.timer);
      }
      if (this.throttledScaleMove && this.throttledScaleMove.timer) {
        clearTimeout(this.throttledScaleMove.timer);
      }
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = wx.getSystemInfoSync();
      const safeArea = systemInfo.safeArea || {
        top: 0,
        right: systemInfo.windowWidth,
        bottom: systemInfo.windowHeight,
        left: 0
      };

      const safeAreaInsets = {
        top: safeArea.top,
        right: systemInfo.windowWidth - safeArea.right,
        bottom: systemInfo.windowHeight - safeArea.bottom,
        left: safeArea.left
      };

      // 计算内容区域高度
      const contentHeight = systemInfo.windowHeight - 
                          this.data.navHeight - 
                          this.data.tabBarHeight -
                          safeAreaInsets.bottom;

      this.setData({
        systemInfo,
        safeAreaInsets,
        contentHeight
      });
    },

    // 使用 requestAnimationFrame 实现的节流函数
    rafThrottle(fn: Function) {
      let isRunning = false;
      return function(this: any, ...args: any[]) {
        if (isRunning) return;
        isRunning = true;
        
        wx.nextTick(() => {
          fn.apply(this, args);
          isRunning = false;
        });
      };
    },

    // 计算图片最佳显示尺寸
    calculateImageSize(imgWidth: number, imgHeight: number) {
      const { windowWidth } = this.data.systemInfo;
      const contentHeight = this.data.contentHeight;
      // 计算实际可用空间
      const topReserved = 120; // 纸张选择器高度
      const bottomReserved = 120; // 底部按钮区域高度
      const horizontalPadding = 10; // 左右边距
      
      const availableWidth = windowWidth - (horizontalPadding * 2);
      const availableHeight = contentHeight - topReserved - bottomReserved;
      
      // 计算图片比例
      const imageRatio = imgWidth / imgHeight;
      const screenRatio = availableWidth / availableHeight;
      
      let displayWidth, displayHeight;
      
      if (imageRatio > screenRatio) {
        // 如果图片比例更宽，以可用宽度为基准
        displayWidth = availableWidth;
        displayHeight = displayWidth / imageRatio;
      } else {
        // 如果图片比例更高，以可用高度为基准
        displayHeight = availableHeight;
        displayWidth = displayHeight * imageRatio;
      }
      
      // 计算居中位置
      const imageLeft = (windowWidth - displayWidth) / 2;
      const imageTop = topReserved + (availableHeight - displayHeight) / 2;
      
      return {
        width: displayWidth,
        height: displayHeight,
        left: imageLeft,
        top: imageTop
      };
    },

    // 处理纸张尺寸变化
    onPaperSizeChange(e: WechatMiniprogram.PickerChange) {
      const index = Number(e.detail.value);
      const selectedSize = this.data.paperSizeList[index];
      const ratio = selectedSize.width / selectedSize.height;

      this.setData({ paperSizeIndex: index })
      this.updateCropperBox(ratio)
    },

    // 处理纸张尺寸选择
    onPaperSizeSelect(e: any) {
      const { size } = e.detail;
      this.setData({ currentPaperSize: size });
      
      // 计算新的裁剪框比例并更新
      const ratio = parseFloat(size.width) / parseFloat(size.height);
      this.updateCropperBox(ratio);
    },

    // 更新裁剪框尺寸
    updateCropperBox(ratio: number) {
      // 如果没有传入比例，使用当前选择的纸张尺寸计算比例
      if (!ratio && this.data.currentPaperSize) {
        ratio = parseFloat(this.data.currentPaperSize.width) / parseFloat(this.data.currentPaperSize.height);
      }
      // 如果仍然没有比例，使用默认的 1:1
      ratio = ratio || 1;

      let cropperBoxWidth = this.data.imageWidth * 0.8;
      let cropperBoxHeight = cropperBoxWidth / ratio;

      // 如果高度超出图片高度，重新计算
      if (cropperBoxHeight > this.data.imageHeight * 0.8) {
        cropperBoxHeight = this.data.imageHeight * 0.8;
        cropperBoxWidth = cropperBoxHeight * ratio;
      }

      // 确保裁剪框不超过图片宽度
      if (cropperBoxWidth > this.data.imageWidth * 0.8) {
        cropperBoxWidth = this.data.imageWidth * 0.8;
        cropperBoxHeight = cropperBoxWidth / ratio;
      }

      // 计算新的位置（居中）
      const cutL = this.data.imageLeft + (this.data.imageWidth - cropperBoxWidth) / 2;
      const cutT = this.data.imageTop + (this.data.imageHeight - cropperBoxHeight) / 2;

      this.setData({
        cropperBoxWidth,
        cropperBoxHeight,
        cutL,
        cutT
      });
    },

    // 修改加载图片方法
    loadImage(src: string) {
      const _this = this;
      wx.showLoading({ title: '图片加载中...' });
      
      wx.getImageInfo({
        src: src || this.properties.imageSrc,
        success(res) {

          // 获取系统信息和内容区域高度
          const systemInfo = wx.getSystemInfoSync();
          const windowWidth = systemInfo.windowWidth;
          const contentHeight = _this.data.contentHeight;
          
          // 计算图片显示尺寸
          const imgWidth = res.width;
          const imgHeight = res.height;
          const imgRatio = imgWidth / imgHeight;
          
          
          // 计算可用空间
          const topPadding = 20;
          const bottomPadding = 120;
          const availableHeight = contentHeight - topPadding - bottomPadding;
          const availableWidth = windowWidth * 0.9;
          
          
          let displayWidth, displayHeight;
          
          // 计算适合的显示尺寸
          if (imgRatio > availableWidth / availableHeight) {
            displayWidth = availableWidth;
            displayHeight = displayWidth / imgRatio;
          } else {
            displayHeight = availableHeight;
            displayWidth = displayHeight * imgRatio;
          }
          
          // 确保不超过最大限制
          if (displayHeight > availableHeight) {
            displayHeight = availableHeight;
            displayWidth = displayHeight * imgRatio;
          }
          if (displayWidth > availableWidth) {
            displayWidth = availableWidth;
            displayHeight = displayWidth / imgRatio;
          }
          
          // 计算图片居中位置
          const imageLeft = (windowWidth - displayWidth) / 2;
          const imageTop = topPadding + (availableHeight - displayHeight) / 2;
          
          

          // 根据当前选择的纸张尺寸设置裁剪框
          let cropperRatio;
          if (_this.data.currentPaperSize) {
            cropperRatio = parseFloat(_this.data.currentPaperSize.width) / 
                          parseFloat(_this.data.currentPaperSize.height);
          } else {
            // 默认使用第一个纸张尺寸
            const defaultPaper = _this.data.paperSizeList[0];
            cropperRatio = defaultPaper.width / defaultPaper.height;
          }

          // 计算裁剪框尺寸
          let cropperWidth = displayWidth * 0.8;
          let cropperHeight = cropperWidth / cropperRatio;

          // 如果高度超出，重新计算
          if (cropperHeight > displayHeight * 0.8) {
            cropperHeight = displayHeight * 0.8;
            cropperWidth = cropperHeight * cropperRatio;
          }

          // 计算裁剪框居中位置
          const cutL = imageLeft + (displayWidth - cropperWidth) / 2;
          const cutT = imageTop + (displayHeight - cropperHeight) / 2;
          
          
          _this.setData({
            imageInfo: res,
            imageWidth: displayWidth,
            imageHeight: displayHeight,
            imageLeft,
            imageTop,
            cropperBoxWidth: cropperWidth,
            cropperBoxHeight: cropperHeight,
            cutL,
            cutT,
            filePath: res.path,
            showImg: true
          });
          
          wx.hideLoading();
        },
        fail(error) {
          console.error('图片加载失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '图片加载失败',
            icon: 'none'
          });
        }
      });
    },

    // 简化拖动处理
    contentDragStart(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0];
      this.setData({
        isDragging: true,
        startX: touch.pageX,
        startY: touch.pageY,
        oldCutL: this.data.cutL,
        oldCutT: this.data.cutT
      });
    },

    // 优化拖动处理函数
    contentDragMove(e: WechatMiniprogram.TouchEvent) {
      if (!this.data.isDragging) return;
      
      const touch = e.touches[0];
      const moveX = touch.pageX - this.data.startX;
      const moveY = touch.pageY - this.data.startY;
      
      // 计算新位置
      const newCutL = Math.min(
        Math.max(
          this.data.oldCutL + moveX,
          this.data.imageLeft
        ),
        this.data.imageLeft + this.data.imageWidth - this.data.cropperBoxWidth
      );
      
      const newCutT = Math.min(
        Math.max(
          this.data.oldCutT + moveY,
          this.data.imageTop
        ),
        this.data.imageTop + this.data.imageHeight - this.data.cropperBoxHeight
      );
      
      // 批量更新数据，减少重绘
      this.setData({
        cutL: newCutL,
        cutT: newCutT
      });
    },

    // 选择图片
    getImage() {
      const _this = this
      wx.chooseImage({
        count: 1,
        sizeType: ['original'], // 只使用原图
        sourceType: ['album', 'camera'],
        success(res) {
          _this.setData({
            showImg: false,
            filePath: res.tempFilePaths[0]
          })
          _this.loadImage(res.tempFilePaths[0])
        }
      })
    },

    // 修改获取裁剪后图片的方法
    getImageInfo() {
      const _this = this;
      wx.showLoading({ title: '图片生成中...' });

      const query = wx.createSelectorQuery().in(this);
      query.select('#wxCropperCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 获取当前纸张比例
          let ratio;
          if (_this.data.currentPaperSize) {
            ratio = parseFloat(_this.data.currentPaperSize.width) / 
                    parseFloat(_this.data.currentPaperSize.height);
          } else {
            const defaultPaper = _this.data.paperSizeList[0];
            ratio = defaultPaper.width / defaultPaper.height;
          }

          // 设置固定输出尺寸
          const outputWidth = 1200;
          const outputHeight = outputWidth / ratio;
          
          canvas.width = outputWidth;
          canvas.height = outputHeight;

          const img = canvas.createImage();
          img.src = _this.data.filePath as string;
          img.onload = () => {
            // 计算实际裁剪参数
            const scaleX = img.width / _this.data.imageWidth;
            const scaleY = img.height / _this.data.imageHeight;
            
            const sx = (_this.data.cutL - _this.data.imageLeft) * scaleX;
            const sy = (_this.data.cutT - _this.data.imageTop) * scaleY;
            const sWidth = _this.data.cropperBoxWidth * scaleX;
            const sHeight = _this.data.cropperBoxHeight * scaleY;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(
              img,
              sx, sy, sWidth, sHeight,
              0, 0, canvas.width, canvas.height
            );

            wx.canvasToTempFilePath({
              canvas,
              width: canvas.width,
              height: canvas.height,
              destWidth: canvas.width,
              destHeight: canvas.height,
              fileType: 'jpg',
              quality: 1,
              success(res) {
                const result = {
                  path: res.tempFilePath,
                  width: outputWidth,
                  height: outputHeight
                };
                _this.triggerEvent('cropped', result);
                wx.hideLoading();
              },
              fail(error) {
                console.error('Canvas to image failed:', error);
                wx.showToast({
                  title: '图片生成失败',
                  icon: 'none'
                });
                wx.hideLoading();
              }
            });
          };
        });
    },

    // 关闭裁剪
    close() {
      this.triggerEvent('close')
    },

    // 简化缩放开始
    scaleStart(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0];
      this.setData({
        isScaling: true,
        scaleStartX: touch.pageX,
        scaleStartY: touch.pageY,
        scaleStartWidth: this.data.cropperBoxWidth,
        scaleStartHeight: this.data.cropperBoxHeight,
        scaleStartCutL: this.data.cutL,
        scaleStartCutT: this.data.cutT
      });
    },

    // 优化缩放处理函数
    scaleMove(e: WechatMiniprogram.TouchEvent) {
      if (!this.data.isScaling) return;
      
      const touch = e.touches[0];
      const moveX = touch.pageX - this.data.scaleStartX;
      
      // 获取当前纸张比例
      let ratio;
      if (this.data.currentPaperSize) {
        ratio = parseFloat(this.data.currentPaperSize.width) / parseFloat(this.data.currentPaperSize.height);
      } else {
        const selectedSize = this.data.paperSizeList[this.data.paperSizeIndex];
        ratio = selectedSize.width / selectedSize.height;
      }
      
      // 使用更高效的缩放计算
      const scaleFactor = 1 + (moveX * 0.005); // 降低缩放灵敏度
      
      // 计算新尺寸，保持比例
      let newWidth = this.data.scaleStartWidth * scaleFactor;
      let newHeight = newWidth / ratio;
      
      // 使用常量限制最小/最大尺寸
      const MIN_WIDTH = 100;
      const maxWidth = this.data.imageWidth;
      const maxHeight = this.data.imageHeight;
      
      // 高效的边界检查
      if (newWidth < MIN_WIDTH) {
        newWidth = MIN_WIDTH;
        newHeight = newWidth / ratio;
      }
      
      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = newWidth / ratio;
      }
      
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = newHeight * ratio;
      }
      
      // 计算新位置，保持中心点不变
      const centerX = this.data.scaleStartCutL + this.data.scaleStartWidth / 2;
      const centerY = this.data.scaleStartCutT + this.data.scaleStartHeight / 2;
      
      let newCutL = centerX - newWidth / 2;
      let newCutT = centerY - newHeight / 2;
      
      // 边界约束
      newCutL = Math.min(
        Math.max(newCutL, this.data.imageLeft),
        this.data.imageLeft + this.data.imageWidth - newWidth
      );
      
      newCutT = Math.min(
        Math.max(newCutT, this.data.imageTop),
        this.data.imageTop + this.data.imageHeight - newHeight
      );
      
      // 批量更新状态
      this.setData({
        cropperBoxWidth: newWidth,
        cropperBoxHeight: newHeight,
        cutL: newCutL,
        cutT: newCutT
      });
    },

    // 结束拖动
    contentDragEnd() {
      this.setData({
        isDragging: false
      });
    },

    // 触摸结束处理
    contentTouchEnd() {
      if (this.data.isDragging) {
        this.contentDragEnd();
      } else if (this.data.isScaling) {
        this.scaleEnd();
      }
    },

    // 结束缩放
    scaleEnd() {
      this.setData({
        isScaling: false
      });
    }
  },

  attached() {

    if (this.properties.imageSrc) {
      this.loadImage(this.properties.imageSrc)
    }
  }
})
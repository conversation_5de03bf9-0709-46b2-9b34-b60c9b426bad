<view class="weui-search-bar {{searchState ? 'weui-search-bar_focusing' : ''}} {{extClass}}">
    <view class="weui-search-bar__form" aria-role="combobox" aria-haspopup aria-expanded="{{searchState}}" aria-owns="searchResult">
        <view class="weui-search-bar__box">
            <icon class="weui-icon-search" type="search" size="12"/>
            <input aria-controls="searchResult" type="text" class="weui-search-bar__input" placeholder="{{placeholder}}" value="{{value}}" focus="{{focus}}" bindblur="inputBlur" bindfocus="inputFocus" bindinput="inputChange"/>
            <view class="weui-icon-clear" hover-class="weui-hover-active" wx:if="{{value.length > 0}}" bindtap="clearInput" aria-role="button" aria-label="清除"/>
        </view>
        <label id="searchText" class="weui-search-bar__label" bindtap="showInput">
            <icon class="weui-icon-search" type="search" size="12"/>
            <text aria-hidden class="weui-search-bar__text">{{placeholder}}</text>
        </label>
    </view>
    <view wx:if="{{cancel && searchState}}" class="weui-search-bar__cancel-btn" bindtap="hideInput" aria-role="button">{{cancelText}}</view>
</view>
<mp-cells id="searchResult" aria-role="listbox" ext-class=" {{'searchbar-result ' + extClass}}" wx:if="{{searchState && result.length > 0}}">
    <mp-cell class="result" bindtap="selectResult" body-class="weui-cell_primary" data-index="{{index}}" wx:for="{{result}}" wx:key="index" hover aria-role="option">
        <view>{{item.text}}</view>
    </mp-cell>
</mp-cells>

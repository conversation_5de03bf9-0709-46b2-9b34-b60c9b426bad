/* pages/redirect/index.wxss */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: #333;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 调试模式样式 */
.debug-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 20rpx;
}

.debug-content {
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.debug-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
  margin-bottom: 20rpx;
}

.debug-params {
  max-height: 800rpx;
  overflow-y: auto;
}

.param-item {
  padding: 10rpx 0;
  font-size: 28rpx;
  border-bottom: 1px dashed #f0f0f0;
}

.param-key {
  color: #3498db;
  font-weight: bold;
}

.param-value {
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 新增样式 */
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 10rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
}

.no-params {
  padding: 20rpx 0;
  color: #999;
  font-style: italic;
  text-align: center;
}

.error-message {
  color: #e74c3c;
  font-size: 28rpx;
  padding: 20rpx 0;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #666;
  font-size: 28rpx;
}

.mini-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-scroll {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 表单样式 */
.qrcode-form {
  margin: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 2rpx solid #eee;
}

.form-slider {
  margin: 20rpx 0;
}

.picker {
  width: 100%;
  height: 80rpx;
  background: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  box-sizing: border-box;
  border: 2rpx solid #eee;
}

.color-picker {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}

.color-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  border: 2rpx solid #eee;
}

.color-sliders {
  flex: 1;
}

.color-slider {
  margin: 10rpx 0;
}

.generate-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 40rpx;
  font-weight: 500;
}

.generate-btn:active {
  opacity: 0.8;
}

/* 预览区域 */
.qrcode-preview {
  margin: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

.preview-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  margin: 20rpx auto;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
  width: 300rpx;
  height: 80rpx;
  background: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 20rpx auto 0;
  font-weight: 500;
}

/* 说明区域 */
.tool-intro {
  margin: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.intro-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
}

.intro-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.intro-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
}

.section-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.feature-list {
  margin-top: 16rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.feature-dot {
  color: #07c160;
  margin-right: 8rpx;
  font-weight: bold;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  line-height: 1.6;
}

.scan-result {
  margin-top: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}

.scan-result-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: block;
}

.scan-result-content {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  display: block;
  margin-bottom: 10rpx;
}

/* 在文件开头添加新的样式 */
.scan-params {
  margin: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.scan-params-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.scan-params-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.scan-params-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.scan-params-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
  background: rgba(255, 255, 255, 0.8);
  padding: 16rpx;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}

.scan-result {
  margin: 20rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.scan-result-text {
  color: #333;
  font-size: 28rpx;
  word-break: break-all;
}

.expandable-item {
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.expandable-header {
  display: flex;
  align-items: center;
  background: #f8f8f8;
}

.expand-icon {
  margin: 0 10rpx;
  color: #666;
  font-size: 24rpx;
}

.expandable-content {
  padding: 16rpx;
  background: #fff;
}

.object-item {
  display: flex;
  padding: 8rpx 0;
  font-size: 24rpx;
  line-height: 1.4;
}

.item-key {
  color: #333;
  font-weight: 500;
  margin-right: 10rpx;
}

.item-value {
  color: #666;
  flex: 1;
  word-break: break-all;
}
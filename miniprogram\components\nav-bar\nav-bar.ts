import { configManager } from '../../utils/configManager';

Component({
  options: {
    styleIsolation: 'shared'
  },

  properties: {
    showBack: {
      type: Boolean,
      value: false
    },
    showMore: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '',
      observer: function(newVal) {
        if (newVal) {
          this.setData({ currentTitle: newVal });
        }
      }
    }
  },

  data: {
    statusBarHeight: 0,
    navBarHeight: 0,
    menuButtonHeight: 0,
    menuButtonTop: 0,
    currentTitle: '',
    _unsubscribe: null
  },

  lifetimes: {
    attached: function() {
      const self = this;
      try {
        // 获取系统信息
        const systemInfo = wx.getSystemInfoSync();
        const menuButton = wx.getMenuButtonBoundingClientRect();
        
        self.setData({
          statusBarHeight: systemInfo.statusBarHeight,
          menuButtonHeight: menuButton.height,
          menuButtonTop: menuButton.top,
          navBarHeight: (menuButton.top - systemInfo.statusBarHeight) * 2 + menuButton.height,
          currentTitle: this.properties.title || '' // 确保初始标题设置
        });

        // 订阅配置更新
        const unsubscribe = configManager.subscribe(function() {
          self.updateTitle();
        });

        self.setData({ _unsubscribe: unsubscribe });
      } catch (error) {
        console.error('导航栏初始化失败:', error);
      }
    },

    detached: function() {
      if (this.data._unsubscribe) {
        this.data._unsubscribe();
      }
    }
  },

  methods: {
    updateTitle: function() {
      const self = this;
      try {
        const pages = getCurrentPages();
        if (!pages || pages.length === 0) {
          self.setData({ currentTitle: self.properties.title || '' });
          return;
        }
        
        const currentPage = pages[pages.length - 1];
        const route = '/' + currentPage.route;

        const config = configManager.getConfig();
        if (!config) {
          self.setData({ currentTitle: self.properties.title || '' });
          return;
        }

        const tabList = config.tabList || [];
        const currentTab = tabList.find(function(tab) {
          return tab.url === route;
        });
        
        const titleText = currentTab ? currentTab.text : (self.properties.title || '');
        self.setData({ currentTitle: titleText });

        // console.log('当前标题:', titleText);
      } catch (error) {
        console.error('更新标题失败:', error);
        self.setData({ currentTitle: self.properties.title || '' });
      }
    },

    goBack: function() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack({ delta: 1 });
      } else {
        wx.reLaunch({ url: '/pages/index/index' });
      }
    },

    showMoreMenu: function() {
      wx.showActionSheet({
        itemList: ['设置', '关于'],
        success: function(res) {
          switch (res.tapIndex) {
            case 0:
              // 设置
              break;
            case 1:
              // 关于
              break;
          }
        }
      });
    }
  }
}); 
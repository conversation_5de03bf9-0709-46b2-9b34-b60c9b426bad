<!-- 引入顶部导航栏 -->
<nav-bar title="图表组件示例" showBack="{{true}}" showHome="{{true}}"></nav-bar>

<view class="container">
  <!-- 单图表展示区域 -->
  <view class="chart-section">
    <view class="section-title">图表组件示例</view>
    
    <!-- 图表类型选择器 -->
    <view class="chart-type-selector">
      <scroll-view scroll-x="true" class="type-selector-scroll">
        <view class="selector-buttons">
          <block wx:for="{{chartTypes}}" wx:key="type">
            <view class="type-btn {{currentChartType === item.type ? 'active' : ''}}" bindtap="switchChartType" data-type="{{item.type}}">{{item.name}}</view>
          </block>
        </view>
      </scroll-view>
    </view>
    
    <!-- 图表容器 -->
    <view class="chart-wrapper {{currentChartType === 'radar' ? 'chart-wrapper-radar' : ''}}">
      <u-chart 
        type="{{currentChartType}}" 
        chart-data="{{chartData}}" 
        opts="{{chartOpts}}"
        height="{{currentChartType === 'radar' ? 800 : 400}}"
        preset="{{currentPreset}}"
        loading="{{loading}}"
        bind:click="onChartClick"
        bind:inited="onChartInited">
      </u-chart>
      
      <!-- 雷达图图例说明 -->
      <view class="chart-legend" wx:if="{{currentChartType === 'radar'}}">
        <view class="legend-item">
          <view class="legend-color" style="background-color: #5B8FF9;"></view>
          <view class="legend-text">我司</view>
        </view>
        <view class="legend-item">
          <view class="legend-color" style="background-color: #5AD8A6;"></view>
          <view class="legend-text">行业平均</view>
        </view>
      </view>
    </view>
    
    <!-- 控制按钮区域 -->
    <view class="action-buttons">
      <button class="action-btn" bindtap="updateData">更新数据</button>
      <button class="action-btn" bindtap="switchPreset">切换样式</button>
    </view>
  </view>
  
  <!-- 多图表展示区域 -->
  <view class="chart-section">
    <view class="section-title">多图表示例</view>
    
    <!-- 上部多图表区域 -->
    <view class="multi-chart-row">
      <view class="chart-item">
        <view class="chart-item-title">饼图</view>
        <view class="chart-item-wrapper">
          <u-chart 
            type="pie" 
            chart-data="{{pieChartData}}" 
            height="300"
            bind:click="onPieClick">
          </u-chart>
        </view>
      </view>
      
      <view class="chart-item">
        <view class="chart-item-title">柱状图</view>
        <view class="chart-item-wrapper">
          <u-chart 
            type="column" 
            chart-data="{{columnChartData}}" 
            height="300"
            bind:click="onColumnClick">
          </u-chart>
        </view>
      </view>
    </view>
    
    <!-- 下部单图表区域 -->
    <view class="chart-item full-width">
      <view class="chart-item-title">折线图</view>
      <view class="chart-item-wrapper">
        <u-chart 
          type="line" 
          chart-data="{{lineChartData}}" 
          height="300"
          bind:click="onLineClick">
        </u-chart>
      </view>
    </view>
  </view>
</view>

<!-- 引入底部菜单栏 -->
<tab-bar currentTab="0"></tab-bar> 
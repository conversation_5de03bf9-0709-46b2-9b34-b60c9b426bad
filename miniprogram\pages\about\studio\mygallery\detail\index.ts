import { layoutUtil } from '../../../../../utils/layout';
import Api, {} from '../../../../../utils/api';
import eventBus from '../../../../../utils/eventBus';
import pageHelper from '../../../../../utils/pageHelper';
import { DOMAIN } from '../../../../../utils/constants';

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    loading: true,
    galleryInfo: null,
    selectedImages: [],
    baseUrl: DOMAIN, // 使用常量作为图片前缀
    showStealProtection: false, // 是否显示防盗水印
    downloadProgress: 0, // 下载进度
    isDownloadingAll: false, // 是否正在批量下载
    totalImages: 0, // 总图片数
    downloadedImages: 0, // 已下载图片数
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      eventBus.on('pageInfoUpdate', this.handlePageInfo.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      eventBus.off('pageInfoUpdate', this.handlePageInfo.bind(this));
      // 离开页面时关闭禁止截屏
      this.setScreenShotProtection(false);
    },
    // 页面加载时读取参数
    ready: function() {
      // 尝试从页面栈中获取当前页面实例和参数
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.options && currentPage.options.gallery_id) {
          const galleryId = currentPage.options.gallery_id;
          console.log('从页面栈获取到的gallery_id:', galleryId);
          this.loadGalleryDetail(galleryId);
        }
      } catch (error) {
        console.error('获取页面参数失败:', error);
      }
    }
  },

  pageLifetimes: {
    // 页面显示时尝试加载数据
    show: function() {
      // 尝试获取页面参数
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.options && currentPage.options.gallery_id) {
          const galleryId = currentPage.options.gallery_id;
          console.log('页面显示时获取gallery_id:', galleryId);
          this.loadGalleryDetail(galleryId);
        }
      } catch (error) {
        console.error('获取页面参数失败:', error);
      }
    }
  },

  methods: {
    // 设置截屏保护
    setScreenShotProtection: function(enable) {
      if (wx.setVisualEffectOnCapture) {
        wx.setVisualEffectOnCapture({
          visualEffect: enable ? 'hidden' : 'none',
          success: () => {
            console.log('设置截屏保护:', enable ? '开启' : '关闭');
          },
          fail: (err) => {
            console.error('设置截屏保护失败:', err);
          }
        });
      }
    },

    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    // 处理页面参数
    handlePageInfo: async function(data: {path: string, params: Record<string, any>}) {
      const params = data.params;
      
      if (!params || !params.gallery_id) {
        console.warn('缺少必要的参数gallery_id');
        this.setData({ loading: false });
        return;
      }
      
      this.loadGalleryDetail(params.gallery_id);
    },
    
    // 加载图库详情
    loadGalleryDetail: async function(galleryId: number) {
      try {
        console.log('开始加载图库详情, ID:', galleryId);
        this.setData({ loading: true });
        
        // 获取图库状态信息
        const galleryStatusResponse = await Api.Status.getUserGalleryStatus();
        console.log('获取到的图库状态数据:', galleryStatusResponse);
        
        if (!galleryStatusResponse) {
          throw new Error('获取图库状态失败');
        }
        
        // 在返回的列表中找到当前画廊
        const galleryInfo = galleryStatusResponse.find((item: any) => {
          console.log('比较:', item.gallery_id, galleryId, item.gallery_id == galleryId);
          return item.gallery_id == galleryId || item.id == galleryId;
        });
        
        console.log('找到的图库信息:', galleryInfo);
        
        if (!galleryInfo) {
          throw new Error('未找到该图库信息');
        }
        
        // 直接使用已选图片数据
        const selectedImages = galleryInfo.selected_images || [];
        console.log('已选图片数量:', selectedImages.length);
        
        // 更新页面数据
        this.setData({
          loading: false,
          galleryInfo: galleryInfo,
          selectedImages: selectedImages
        });
        
        // 根据审核状态设置防盗保护
        const isConfirmed = galleryInfo.selection_status === 'confirmed';
        this.setScreenShotProtection(!isConfirmed);
        this.setData({
          showStealProtection: !isConfirmed
        });
        
        console.log('图库详情加载完成');
        
      } catch (error) {
        console.error('加载图库详情失败:', error);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    },
    
    // 预览图片 - 根据审核状态决定是否允许预览
    previewImage: function(e) {
      // 如果未通过审核，不允许预览原图
      if (this.data.galleryInfo && this.data.galleryInfo.selection_status !== 'confirmed') {
        wx.showToast({
          title: '需要审核通过后才能预览原图',
          icon: 'none'
        });
        return;
      }
      
      const currentUrl = e.currentTarget.dataset.url;
      const index = e.currentTarget.dataset.index;
      
      if (!this.data.selectedImages || this.data.selectedImages.length === 0) {
        return;
      }
      
      // 获取所有图片的URL
      const urls = this.data.selectedImages.map((image: any) => this.data.baseUrl + image.url);
      
      wx.previewImage({
        current: currentUrl,
        urls: urls
      });
    },
    
    // 下载图片
    downloadImage: function(e) {
      const url = e.currentTarget.dataset.url;
      const filename = e.currentTarget.dataset.filename;
      
      // 检查是否已通过状态
      if (this.data.galleryInfo && this.data.galleryInfo.selection_status !== 'confirmed') {
        wx.showToast({
          title: '需要审核通过后才能下载',
          icon: 'none'
        });
        return;
      }
      
      wx.showLoading({
        title: '正在下载...',
      });
      
      // 对URL进行编码处理，避免特殊字符导致下载失败
      const encodedUrl = encodeURI(url);
      console.log('下载图片URL:', encodedUrl);
      
      // 下载图片
      wx.downloadFile({
        url: encodedUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存到相册
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.hideLoading();
                wx.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                });
              },
              fail: (err) => {
                wx.hideLoading();
                console.error('保存图片到相册失败:', err);
                
                // 如果是用户拒绝授权
                if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0) {
                  wx.showModal({
                    title: '提示',
                    content: '需要您授权保存图片到相册',
                    confirmText: '去授权',
                    success: (res) => {
                      if (res.confirm) {
                        wx.openSetting({
                          success: (settingRes) => {
                            console.log('设置结果', settingRes);
                          }
                        });
                      }
                    }
                  });
                } else {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              }
            });
          } else {
            wx.hideLoading();
            console.error('下载图片状态码错误:', res.statusCode);
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('下载图片失败:', err);
          
          // 显示更详细的错误信息
          wx.showModal({
            title: '下载失败',
            content: '图片下载失败，请检查网络连接或联系客服',
            showCancel: false
          });
        }
      });
    },
    
    // 下载所有图片
    downloadAllImages: function() {
      // 检查是否已通过状态
      if (this.data.galleryInfo && this.data.galleryInfo.selection_status !== 'confirmed') {
        wx.showToast({
          title: '需要审核通过后才能下载',
          icon: 'none'
        });
        return;
      }
      
      // 检查是否有图片可下载
      if (!this.data.selectedImages || this.data.selectedImages.length === 0) {
        wx.showToast({
          title: '没有可下载的图片',
          icon: 'none'
        });
        return;
      }
      
      // 确认用户是否要批量下载
      wx.showModal({
        title: '批量下载',
        content: `确定要下载全部 ${this.data.selectedImages.length} 张图片到相册吗？`,
        success: (res) => {
          if (res.confirm) {
            this.startBatchDownload();
          }
        }
      });
    },
    
    // 开始批量下载图片
    startBatchDownload: function() {
      const totalImages = this.data.selectedImages.length;
      
      // 初始化下载状态
      this.setData({
        isDownloadingAll: true,
        downloadProgress: 0,
        totalImages: totalImages,
        downloadedImages: 0
      });
      
      // 获取相册权限
      wx.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          // 开始逐个下载图片
          this.downloadImageByIndex(0);
        },
        fail: (err) => {
          // 处理授权失败
          console.error('获取相册权限失败:', err);
          this.setData({ isDownloadingAll: false });
          
          // 如果是用户拒绝授权
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    console.log('设置结果', settingRes);
                    // 如果用户在设置页面授权了，则重新开始下载
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      this.startBatchDownload();
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    
    // 按索引下载单张图片
    downloadImageByIndex: function(index) {
      // 检查是否已完成所有下载
      if (index >= this.data.totalImages) {
        this.setData({ isDownloadingAll: false });
        wx.showToast({
          title: '全部下载完成',
          icon: 'success'
        });
        return;
      }
      
      const image = this.data.selectedImages[index];
      const imageUrl = this.data.baseUrl + image.url;
      
      // 对URL进行编码处理，避免特殊字符导致下载失败
      const encodedUrl = encodeURI(imageUrl);
      console.log(`下载第${index+1}张图片:`, encodedUrl);
      
      // 下载单张图片
      wx.downloadFile({
        url: encodedUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存到相册
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                // 更新进度
                const downloadedImages = this.data.downloadedImages + 1;
                const progress = (downloadedImages / this.data.totalImages) * 100;
                
                this.setData({
                  downloadedImages: downloadedImages,
                  downloadProgress: progress
                });
                
                // 继续下载下一张
                setTimeout(() => {
                  this.downloadImageByIndex(index + 1);
                }, 300); // 添加短暂延迟，避免请求过于频繁
              },
              fail: (err) => {
                console.error('保存图片到相册失败:', err);
                // 更新计数，但标记为失败
                const downloadedImages = this.data.downloadedImages + 1;
                const progress = (downloadedImages / this.data.totalImages) * 100;
                
                this.setData({
                  downloadedImages: downloadedImages,
                  downloadProgress: progress
                });
                
                // 继续下载下一张
                setTimeout(() => {
                  this.downloadImageByIndex(index + 1);
                }, 300);
              }
            });
          } else {
            console.error('下载图片状态码错误:', res.statusCode);
            // 更新计数，但标记为失败
            const downloadedImages = this.data.downloadedImages + 1;
            const progress = (downloadedImages / this.data.totalImages) * 100;
            
            this.setData({
              downloadedImages: downloadedImages,
              downloadProgress: progress
            });
            
            // 继续下载下一张
            setTimeout(() => {
              this.downloadImageByIndex(index + 1);
            }, 300);
          }
        },
        fail: (err) => {
          console.error(`下载第${index+1}张图片失败:`, err);
          
          // 更新计数，但标记为失败
          const downloadedImages = this.data.downloadedImages + 1;
          const progress = (downloadedImages / this.data.totalImages) * 100;
          
          this.setData({
            downloadedImages: downloadedImages,
            downloadProgress: progress
          });
          
          // 继续下载下一张
          setTimeout(() => {
            this.downloadImageByIndex(index + 1);
          }, 300);
        }
      });
    },
  
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '我的图库',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '我的图库',
        path: '/pages/about/studio/mygallery/index'
      };
    }
  }
}); 
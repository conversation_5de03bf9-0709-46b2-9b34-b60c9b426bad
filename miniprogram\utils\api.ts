/**
 * API 接口封装模块
 * 
 * @description
 * 本模块专注于业务接口的封装，主要包括：
 * 1. 用户管理：登录、注册、个人信息等
 * 2. 导航管理：菜单列表、工具列表
 * 3. 资源管理：字体、纸张尺寸等
 * 4. 通用功能：短信、验证码、文件上传等
 * 
 * 特点：
 * - 业务逻辑集中管理
 * - 类型安全（TypeScript支持）
 * - 统一的接口规范
 * 
 * @example
 * // 1. 用户登录
 * const userInfo = await Api.user.login('username', 'password');
 * 
 * // 2. 获取导航菜单
 * const menuList = await Api.nav.getMenuList();
 * 
 * // 3. 发送验证码
 * await Api.common.sendSms('13800138000', EventType.LOGIN);
 * 
 * // 4. 上传文件
 * const fileInfo = await Api.common.upload('/path/to/file');
 */

import { request, CACHE_NONE, CACHE_MEMORY, CACHE_STORAGE, CACHE_BOTH, RequestOptions, StandardResponse } from './requestManager';
import { API_ENDPOINTS } from './constants';

// 定义 CacheStrategy 常量
const CacheStrategy = {
  NONE: CACHE_NONE,
  MEMORY: CACHE_MEMORY,
  STORAGE: CACHE_STORAGE,
  BOTH: CACHE_BOTH
};

/**
 * 缓存时间常量（毫秒）
 */
const CACHE_TIME = {
  NONE: 0,
  MINUTE_5: 5 * 60 * 1000,
  HOUR_1: 60 * 60 * 1000,
  HOUR_24: 24 * 60 * 60 * 1000,
  WEEK_1: 7 * 24 * 60 * 60 * 1000
};

/**
 * API响应数据通用类型
 * @template T 响应数据类型
 * @property {number} code 响应状态码 1=成功
 * @property {string} msg 响应消息
 * @property {string} time 服务器时间戳
 * @property {T} data 响应数据
 */
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  time: string;
  data: T;
}

/**
 * 用户信息类型
 * @property {number} id 用户ID
 * @property {string} username 用户名
 * @property {string} nickname 昵称
 * @property {string} avatar 头像URL
 * @property {string} mobile 手机号
 * @property {string} email 邮箱
 * @property {string} bio 个人简介
 */
export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  mobile: string;
  email: string;
  bio: string;
}

/**
 * 字体信息类型
 * @property {number} id 字体ID
 * @property {string} name 字体名称
 * @property {string} category 字体分类
 * @property {string} preview 预览图URL
 * @property {string} download_url 下载地址
 */
export interface FontInfo {
  id: number;
  name: string;
  category: string;
  preview: string;
  download_url: string;
}

/**
 * 纸张尺寸信息类型
 * @property {number} id 尺寸ID
 * @property {string} name 尺寸名称
 * @property {number} width 宽度(mm)
 * @property {number} height 高度(mm)
 * @property {string} category 分类
 */
export interface PaperSize {
  id: number;
  name: string;
  width: number;
  height: number;
  category: string;
}

/**
 * 事件类型常量
 * @description 用于发送验证码等场景的事件类型
 */
const EventType = {
  LOGIN: 'login',
  REGISTER: 'register',
  RESET_PASSWORD: 'reset_password',
  CHANGE_MOBILE: 'change_mobile',
  CHANGE_EMAIL: 'change_email'
};

type EventTypeKeys = keyof typeof EventType;
type EventTypeValues = typeof EventType[EventTypeKeys];

/**
 * 导航菜单信息类型
 */
export interface NavMenuItem {
  id: number;
  text: string;
  name: string | null;
  icon: string;
  url: string | null;
  visible: number;
  show_in_home: number;
  sort_order: number;
  submenus?: NavMenuItem[];
}

/**
 * 工具信息类型
 */
export interface ToolItem {
  id: number;
  text: string;
  icon: string;
  url: string;
  description: string;
  features: string[];
  category: string;
}

/**
 * API请求类 - 业务接口封装
 */
const Api = {
  /**
   * 用户相关接口
   */
  user: {
    /**
     * 会员登录
     */
    login: (username: string, password: string) => {
      return request<UserInfo>('/user/login', { username, password }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 手机验证码登录
     */
    mobileLogin: (mobile: string, captcha: string) => {
      return request<UserInfo>('/user/mobilelogin', { mobile, captcha }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     *  获取艺术家设置信息
     */
    getArtistProfile: (id: number) => {
      return request<UserInfo>('user/getArtistProfile', { id: id }, {
        cache: {
          strategy: CacheStrategy.NONE,
        }
      });
    },
    /**
     *  更新画师信息
     */
    artistProfile: (data: any) => {
      return request<UserInfo>('user/artistProfile', data, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.NONE,
        }
      });
    },

    /**
     * 获取用户统计信息
     */
    userStats: (id: number) => {
      return request('/user/userStats', {id:id}, {
        cache: { strategy: CacheStrategy.NONE }
      });
    }
  },
  /**
   * API相关接口
   */
  api: {
    /**
     * 获取api外链
     */
    getapi: (api_key: string,params:{}) => {
      return request('allapi/proxy',  { api_key: api_key,params: params }, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    }
  },
  /**
   * 获取中国传统颜色
   */
  color: {
    /**
     * 获取api外链
     */
    getAllColors: (include: string,exclude:string) => {
      return request('allapi/getAllColors',  { include: include,exclude: exclude }, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    }
  },

  /**
   * 字体相关接口
   */
  font: {
    /**
     * 获取字体分类列表
     */
    getCategories: () => {
      return request('/font/categories', null, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },

    /**
     * 获取字体详情
     */
    getDetail: (fontId: string) => {
      return request<FontInfo>('/font/detail', { font_id: fontId }, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },

    /**
     * 搜索字体
     */
    search: (keyword: string) => {
      return request<FontInfo[]>('/font/search', { keyword }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    }
  },

  /**
   * 纸张尺寸相关接口
   */
  paper: {
    /**
     * 获取纸张分类列表
     */
    getCategories: (options?: { forceRefresh?: boolean }) => {
      return request('/paper/categories', null, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24,
          forceRefresh: options && options.forceRefresh
        }
      });
    },

    /**
     * 获取纸张尺寸详情
     */
    getDetail: (sizeId: number) => {
      return request<PaperSize>('/paper/detail', { size_id: sizeId }, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },

    /**
     * 搜索纸张尺寸
     */
    search: (keyword: string) => {
      return request<PaperSize[]>('/paper/search', { keyword }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    }
  },

  /**
   * 通用功能接口
   */
  common: {
    /**
     * 登录测试接口，返回用户ID
     * @returns {Promise<{welcome: string, id: number}>} 返回欢迎信息和用户ID
     */
    logintest: () => {
      return request('user/index', null, {
        method: 'GET',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 获取公告列表
     */
    getAnnouncementList: (type?: string, target_type?: string, target_id?: string) => {
      return request('allapi/getAnnouncementList', { type:type, target_type:target_type, target_id:target_id }, {
        method: 'GET',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 获取登陆状态
     */
    debug: (debug: string) => {
      return request('allapi/getwxconfig', { 'name':debug }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 解密
     */
    decrypt: (encrypted: string) => {
      return request('allapi/decrypt', { encrypted }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 发送短信验证码
     */
    sendSms: (mobile: string, event: string) => {
      return request('/sms/send', { mobile, event }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 校验短信验证码
     */
    checkSms: (mobile: string, event: string, captcha: string) => {
      return request('/sms/check', { mobile, event, captcha }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 上传文件
     */
    upload: async (filePath: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: API_ENDPOINTS.UPLOAD,
          filePath,
          name: 'file',
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data && data.data) {
                resolve(data.data);
              } else {
                reject(new Error('Upload failed: Invalid response format'));
              }
            } catch (e) {
              reject(new Error('Upload failed: Invalid JSON response'));
            }
          },
          fail: (error) => {
            reject(new Error(`Upload failed: ${error.errMsg}`));
          }
        });
      });
    },

    /**
     * 获取图形验证码
     */
    getCaptcha: () => {
      return request('/common/captcha', null, {
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 生成小程序码
     * @param params 生成参数
     * @param params.scene 场景值，最大32个可见字符,只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~
     * @param params.page 跳转页面路径,例如 pages/index/index，根路径前不要填加 /，不能携带参数（参数请放在 scene 字段里）
     * @param params.check_path 检查 page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面
     * @param params.env_version 要打开的小程序版本。正式版为 release，体验版为 trial，开发版为 develop
     * @param params.width 二维码的宽度，单位 px，最小 280px，最大 1280px
     * @param params.auto_color 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false
     * @param params.line_color auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {'r':'xxx','g':'xxx','b':'xxx'} 十进制表示
     * @param params.is_hyaline 是否需要透明底色，为 true 时，生成透明底色的小程序
     */
    generateWXACode: (params: {
      scene: string;
      page?: string;
      check_path?: boolean;
      env_version?: 'develop' | 'trial' | 'release';
      width?: number;
      auto_color?: boolean;
      line_color?: {
        r: number;
        g: number;
        b: number;
      };
      is_hyaline?: boolean;
    }) => {
      // 参数验证
      console.log("参数",params);
      if (!params.scene) {
        throw new Error('场景值不能为空');
      }

      if (params.width && (params.width < 280 || params.width > 1280)) {
        throw new Error('二维码宽度必须在280px到1280px之间');
      }

      // 确保页面路径格式正确
      if (params.page && params.page.startsWith('/')) {
        params.page = params.page.substring(1);
      }

      // 构建请求数据
      const requestData = {
        scene: params.scene,
        page: params.page || 'pages/index/index',
        check_path: params.check_path === undefined ? false : params.check_path,
        env_version: params.env_version || 'release',
        width: params.width || 430,
        auto_color: params.auto_color === undefined ? false : params.auto_color,
        is_hyaline: params.is_hyaline === undefined ? false : params.is_hyaline
      };

      // 如果不是自动配色且有指定颜色，则添加颜色参数
      if (!requestData.auto_color && params.line_color) {
        Object.assign(requestData, {
          line_color: {
            r: params.line_color.r || 0,
            g: params.line_color.g || 0,
            b: params.line_color.b || 0
          }
        });
      }

      // 打印请求参数，方便调试
      console.log('生成小程序码参数:', requestData);

      return new Promise((resolve, reject) => {
        wx.request({
          url: API_ENDPOINTS.WXACODE,
          method: 'POST',
          header: {
            'content-type': 'application/json'
          },
          data: requestData,
          success: (res: any) => {
            console.log('回传值:', res);
            console.log('生成小程序码响应:', res.data);
            
            if (res.statusCode !== 200) {
              reject(new Error(`请求失败: ${res.statusCode}`));
              return;
            }

            if (res.data && res.data.code === 1 && res.data.data && res.data.data.url) {
              resolve({
                code: 1,
                msg: '成功',
                data: res.data.data.url
              });
            } else {
              const errorMsg = res.data && res.data.msg ? res.data.msg : '生成小程序码失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            console.error('生成小程序码请求失败:', error);
            reject(new Error('请求失败: ' + error.errMsg));
          }
        });
      });
    }
  },

  /**
   * 导航相关接口
   */
  nav: {
    /**
     * 获取导航菜单列表
     */
    getMenuList: () => {
      return request<NavMenuItem[]>('menu/getMenus', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_1
        }
      });
    },

    /**
     * 获取工具列表
     */
    getToolList: () => {
      return request<ToolItem[]>('menu/getHomeTools', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_1
        }
      });
    }

  },
  /**
   * 招募功能
   */
  recruitment: {
    /**
     * 获取画种列表
     *
     * @ApiTitle    (获取所有画风和画种列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/series/getArtworkTypes)
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "time": "1234567890",
     *   "data": [{
     *     "id": "画种ID",
     *     "name": "画种名称",
     *     "code": "画种代码",
     *     "description": "描述"
     *   }]
     * })
     */
    getStylesAndTypes: () => {
      return request('allapi/getStylesAndTypes', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },
    /**
     * 获取招募列表
     *
     * @ApiTitle    (获取招募列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/series/getRecruitmentList)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams   (name="status", type="string", required=false, description="状态筛选")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "time": "1234567890",
     *   "data": {
     *     "total": "总数",
     *     "list": [{
     *       "id": "招募ID",
     *       "series_id": "系列ID",
     *       "title": "招募标题",
     *       "description": "招募描述",
     *       "requirements": "招募要求",
     *       "artwork_types": ["画种1", "画种2"],
     *       "min_cards": "最少卡牌数",
     *       "max_cards": "最多卡牌数",
     *       "recruitment_start_date": "开始日期",
     *       "recruitment_end_date": "结束日期",
     *       "status": "状态",
     *       "series_name": "系列名称",
     *       "min_price": "最低价格",
     *       "max_price": "最高价格"
     *     }]
     *   }
     * })
     */
    getRecruitmentList: (studio_id?: number,series_id?: number,recruitment_id?:number) => {
      const params: any = {};
      if(studio_id) params.studio_id = studio_id;
      if(series_id) params.series_id = series_id; 
      if(recruitment_id) params.recruitment_id = recruitment_id;
      
      return request('series/getRecruitmentList', params, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_1
        }
      });
    },

    /**
     * 获取招募详情
     *
     * @ApiTitle    (获取招募详情)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/series/getRecruitmentDetail)
     * @ApiParams   (name="id", type="integer", required=true, description="招募ID")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "time": "1234567890",
     *   "data": {
     *     "id": "招募ID",
     *     "series_id": "系列ID",
     *     "title": "招募标题",
     *     "description": "招募描述",
     *     "requirements": "招募要求",
     *     "artwork_types": ["画种1", "画种2"],
     *     "min_cards": "最少卡牌数",
     *     "max_cards": "最多卡牌数",
     *     "recruitment_start_date": "开始日期",
     *     "recruitment_end_date": "结束日期",
     *     "status": "状态",
     *     "series_name": "系列名称",
     *     "series_description": "系列描述",
     *     "min_price": "最低价格",
     *     "max_price": "最高价格"
     *   }
     * })
     */
    getDetail: (recruitmentid: number) => {
      return request('series/getRecruitmentDetail', { recruitmentid }, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.MINUTE_5
        }
      });
    },

    /**
     * 上传以往作品图片
     *
     * @ApiTitle    (上传以往作品图片)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/series/uploadPortfolio)
     * @ApiParams   (name="file", type="file", required=true, description="图片文件，支持jpg,jpeg,png,gif，大小限制10MB")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "上传成功",
     *   "time": "1234567890",
     *   "data": {
     *     "url": "图片访问URL",
     *     "path": "图片存储路径"
     *   }
     * })
     */
    uploadPortfolio: (filePath: string): Promise<{url: string; path: string}> => {
      return new Promise((resolve, reject) => {
        // 获取token
        const token = wx.getStorageSync('token');
        
        wx.uploadFile({
          url: API_ENDPOINTS.UPLOAD_PORTFOLIO,
          filePath,
          name: 'file',
          header: {
            // 添加认证头
            'Authorization': token ? `Bearer ${token}` : '',
            'token': token || ''
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              console.log('Upload response:', data);
              if (data && data.code === 1 && data.data) {
                resolve(data.data);
              } else {
                reject(new Error(data.msg || '上传失败'));
              }
            } catch (e) {
              console.error('Parse upload response failed:', e);
              reject(new Error('上传响应格式错误'));
            }
          },
          fail: (error) => {
            console.error('Upload request failed:', error);
            reject(new Error(`上传请求失败: ${error.errMsg}`));
          }
        });
      });
    },

    /**
     * 提交招募申请
     *
     * @ApiTitle    (提交招募申请)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/series/submitApplication)
     * @ApiParams   (name="recruitment_id", type="integer", required=true, description="招募ID")
     * @ApiParams   (name="cards_count", type="integer", required=true, description="申请卡牌数量")
     * @ApiParams   (name="pen_name", type="string", required=false, description="想要使用的笔名")
     * @ApiParams   (name="artwork_types", type="string", required=true, description="可画画种，多个用逗号分隔")
     * @ApiParams   (name="art_styles", type="string", required=true, description="擅长画风，多个用逗号分隔")
     * @ApiParams   (name="shipping_address", type="string", required=true, description="邮寄地址")
     * @ApiParams   (name="contact_phone", type="string", required=true, description="联系电话")
     * @ApiParams   (name="contact_wechat", type="string", required=false, description="联系微信")
     * @ApiParams   (name="remarks", type="string", required=false, description="备注说明")
     * @ApiParams   (name="portfolio_images", type="array", required=true, description="作品图片数组，包含1-3张图片")
     * @ApiParams   (name="portfolio_images.url", type="string", required=true, description="图片URL")
     * @ApiParams   (name="portfolio_images.path", type="string", required=true, description="图片路径")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "申请成功",
     *   "time": "1234567890",
     *   "data": {
     *     "id": "申请ID"
     *   }
     * })
     */
    submitApplication: (data: {
      recruitment_id: number;
      cards_count: number;
      contact_info: string;
      portfolio_images: Array<{
        url: string;
        path: string;
      }>;
    }) => {
      return request('series/submitApplication', data, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * 获取申请状态
     *
     * @ApiTitle    (获取申请状态)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/series/getApplicationStatus)
     * @ApiParams   (name="recruitment_id", type="integer", required=true, description="招募ID")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "time": "1234567890",
     *   "data": {
     *     "id": "申请ID",
     *     "recruitment_id": "招募ID",
     *     "artist_id": "画师ID",
     *     "artist_name": "画师名称",
     *     "cards_count": "申请卡牌数量",
     *     "contact_info": "联系方式",
     *     "status": "申请状态",
     *     "createtime": "创建时间",
     *     "portfolio_images": [{
     *       "id": "图片ID",
     *       "application_id": "申请ID",
     *       "image_url": "图片URL",
     *       "image_path": "图片路径",
     *       "createtime": "创建时间"
     *     }]
     *   }
     * })
     */
    getApplicationStatus: (recruitment_id: number) => {
      return request('series/getApplicationStatus', { recruitment_id:recruitment_id }, {
        method: 'GET',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

  },
  /**
   * 工作室studio/detail
   */
  studio: {
    /**
    * 工作室详情
    */
   detail: (id: number) => {
     return request('studio/detail', { id }, {
       method: 'GET',
       cache: {
         strategy: CacheStrategy.BOTH,
         expireTime: CACHE_TIME.HOUR_24
       }
     });
   },

 },
  /**
   * 图库studio/detail
   */
  gallery: {
    /**
    * 系列详情
    */
    seriesDetail: (id: number) => {
      return request('series/seriesDetail', { series_id:id }, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },
    /**
    * 图库详情
    */
    galleryDetail: (id: number) => {
      return request('studio/gallery', { series_id:id }, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },
    /**
    * 图库图像列表
    */
    galleryimageslist: (id: number) => {
      return request('studio/images', { gallery_id:id }, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },
    /**
    * 提交画师选择的图像id
    */
    saveSelection: (id: number,image_ids:string) => {
      return request('studio/saveSelection', { gallery_id:id,image_ids:image_ids }, {
        method: 'POST',
        cache: {
          strategy: CacheStrategy.BOTH,
          expireTime: CACHE_TIME.HOUR_24
        }
      });
    },

  },
  /**
   * 关于我的
   */
  Status: {
    /**
     * 获取系列申请状态
     */
    getApplicationStatus: () => {
      return request('series/getApplicationStatus', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.NONE
        }
      });
    },
    /**
     * 获取图库图像申请状态
     */
    getUserGalleryStatus: () => {
      return request('series/getUserGalleryStatus', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.NONE
        }
      });
    },

  },
  /**
   * AI接口
   */
  ai: {
    /**
     * 中文翻译成英文API
     * 
     * @description 将中文文本翻译成英文，特别适用于AI绘画的提示词翻译
     * @param content 要翻译的中文文本
     * @param user_id 用户ID
     * @returns 翻译后的英文文本
     */
    translateChineseToEnglish: (content: string, user_id: string) => {
      console.log('翻译前的提示词3:', content);
      return request('zhipu_ai/translateChineseToEnglish', { content, user_id }, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },

    /**
     * AI绘画提示词扩充API
     * 
     * @description 将简单的AI绘画提示词扩充为专业详细的提示词
     * @param content 要扩充的原始提示词
     * @param user_id 用户ID
     * @returns 扩充后的提示词
     */
    expandDrawingPrompt: (content: string, user_id: string) => {
      return request('zhipu_ai/expandDrawingPrompt', { content, user_id}, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
    },
    /*Pollinations服务接口*/
    /**
     * 获取系列申请状态
     */
    getAvailableModels: () => {
      return request('pollinations/getAvailableModels', null, {
        method: 'GET',
        cache: {
          strategy: CacheStrategy.NONE
        }
      });
    },
  }
};


    
    
    
    
    
    
    
    
    
    
    
    
        
        
        






export {
  Api as default,
  EventType
}; 

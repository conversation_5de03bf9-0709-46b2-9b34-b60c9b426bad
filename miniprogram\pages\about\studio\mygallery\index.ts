// index.ts
import { layoutUtil } from '../../../../utils/layout';
import Api, {} from '../../../../utils/api';
import eventBus from '../../../../utils/eventBus';
Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    filterType: 'all', // 默认筛选已通过状态
    galleryList: null, // 原始数据
    filteredGalleryList: [], // 筛选后的数据
    counts: {
      all: 0,
      confirmed: 0,
      pending: 0,
      rejected: 0
    }
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      this.getGalleryList()
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    // 切换筛选条件
    switchFilter: function(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        filterType: type
      });
      this.filterGallery(type);
    },
    
    // 计算各状态的图库数量
    calculateCounts: function(list) {
      if (!list) return;
      
      const counts = {
        all: list.length,
        confirmed: 0,
        pending: 0,
        rejected: 0
      };
      
      list.forEach(item => {
        if (item.selection_status === 'confirmed') {
          counts.confirmed++;
        } else if (item.selection_status === 'pending') {
          counts.pending++;
        } else if (item.selection_status === 'rejected') {
          counts.rejected++;
        }
      });
      
      this.setData({ counts });
    },
    
    // 筛选图库列表
    filterGallery: function(type) {
      if (!this.data.galleryList) return;
      
      let filtered = [];
      if (type === 'all') {
        filtered = this.data.galleryList;
      } else {
        filtered = this.data.galleryList.filter(item => item.selection_status === type);
      }
      
      this.setData({
        filteredGalleryList: filtered
      });
    },
    
    /*获取图库列表 */
    getGalleryList: async function() {
      try {
        const res = await Api.Status.getUserGalleryStatus()
        console.log('获取到的图库列表数据:', res);
        
        // 检查返回数据格式
        if (Array.isArray(res)) {
          // 验证每个图库对象是否有gallery_id
          const validItems = res.map(item => {
            if (!item.gallery_id) {
              console.warn('图库项缺少gallery_id:', item);
              // 如果没有gallery_id但有id，尝试使用id作为gallery_id
              if (item.id) {
                item.gallery_id = item.id;
              }
            }
            return item;
          });
          
          this.setData({
            galleryList: validItems
          });
          
          // 计算各状态的图库数量
          this.calculateCounts(validItems);
          
          // 应用默认筛选
          this.filterGallery(this.data.filterType);
        } else {
          console.error('API返回的不是数组:', res);
          this.setData({
            galleryList: [],
            filteredGalleryList: []
          });
        }
      } catch (error) {
        console.error('获取图库列表失败:', error);
        this.setData({
          galleryList: [],
          filteredGalleryList: []
        });
        wx.showToast({
          title: '获取图库列表失败',
          icon: 'none'
        });
      }
    },
    // 查看已选图片
    viewGallery: function(e) {
      const galleryId = e.currentTarget.dataset.id;
      if (galleryId) {
        console.log('跳转到图库详情，gallery_id:', galleryId);
        // 使用wx.navigateTo确保跳转到新页面
        wx.navigateTo({
          url: `/pages/about/studio/mygallery/detail/index?gallery_id=${galleryId}`,
          success: () => {
            console.log('跳转成功');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '无效的图库ID',
          icon: 'none'
        });
      }
    },
    
    // 继续选择图片
    continueSelect: function(e) {
      const galleryId = e.currentTarget.dataset.id;
      if (galleryId) {
        console.log('跳转到选图页面，gallery_id:', galleryId);
        // 跳转到图库选择页面
        wx.navigateTo({
          url: `/pages/series/gallery/index?gallery_id=${galleryId}`,
          success: () => {
            console.log('跳转成功');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '无效的图库ID',
          icon: 'none'
        });
      }
    },
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '工具箱',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '工具箱',
        path: '/pages/tool/test/test'
      };
    }
  }
});

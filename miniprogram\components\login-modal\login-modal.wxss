.login_modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: none;
}

.login_modal.show {
  display: block;
}

.login_mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  animation: login_fadeIn 0.3s ease;
}

.login_content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  animation: login_slideIn 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.login_close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
  transition: all 0.3s;
}

.login_close:active {
  opacity: 0.7;
}

.login_title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}

.login_form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像相关样式 */
.login_avatar-wrapper,
.login_avatar-btn {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 50rpx;
  padding: 0;
  background: none;
  border: none;
  position: relative;
  display: block;
}

.login_avatar-wrapper::after,
.login_avatar-btn::after {
  border: none;
  display: none;
}

.login_avatar,
.login_avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 2rpx solid #e0e0e0;
}

.login_avatar-wrapper:active .login_avatar,
.login_avatar-btn:active .login_avatar-preview {
  transform: scale(0.95);
}

.login_avatar-wrapper text,
.login_avatar-tip {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  width: 100%;
  text-align: center;
}

/* 昵称输入相关样式 */
.login_nickname-input,
.login_input-field {
  width: 80%;
  height: 88rpx;
  background: #f5f5f5;
  border-radius: 44rpx;
  padding: 0 30rpx;
  margin: 40rpx 0;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s;
  border: 2rpx solid transparent;
}

.login_nickname-input:focus,
.login_input-field:focus {
  border-color: #07c160;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}

.login_input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

/* 按钮相关样式 */
.login_btn,
.login_auth-btn {
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  background: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 20rpx;
  transition: all 0.3s;
  border: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login_btn::after,
.login_auth-btn::after {
  border: none;
}

.login_btn:active,
.login_auth-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.login_btn.disabled,
.login_auth-btn[disabled] {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.8;
}

.login_btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

/* 协议相关样式 */
.login_agreement,
.login_tips {
  margin-top: 40rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.login_agreement .login_link,
.login_link-text {
  color: #07c160;
  display: inline;
  padding: 0 4rpx;
}

.login_agreement .login_link:active,
.login_link-text:active {
  opacity: 0.7;
}

/* 状态卡片样式 */
.login_status-card {
  width: 100%;
  margin: 30rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.login_status-header {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.login_status-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.login_user-info {
  display: flex;
  align-items: center;
}

.login_info-content {
  flex: 1;
}

.login_nickname {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.login_openid {
  font-size: 24rpx;
  color: #666;
  display: block;
  word-break: break-all;
}

/* 动画效果 */
@keyframes login_fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes login_slideIn {
  from {
    transform: translate(-50%, -60%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

/* 在现有样式基础上添加 */
.login_privacy {
  margin-top: 30rpx;
  font-size: 26rpx;
  color: #333;
}

.privacy-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.privacy-checkbox checkbox {
  transform: scale(0.8);
  margin-right: 8rpx;
}

.privacy-links {
  margin-top: 10rpx;
  text-align: center;
}

.login_link {
  color: #07c160;
  display: inline;
} 
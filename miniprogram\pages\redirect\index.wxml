<wxs src="../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <view class="content-wrapper">
    <view class="loading-container" wx:if="{{!isDebug}}">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在跳转...</text>
      </view>
    </view>
    
    <view class="debug-container" wx:if="{{isDebug}}">
      <view class="debug-content">
        <view class="debug-title">调试模式</view>
        
        <!-- 原始参数 -->
        <view class="section-title">原始参数</view>
        <view class="debug-params">
          <view class="param-item" wx:for="{{debugParams}}" wx:key="key">
            <text class="param-key">{{item.key}}: </text>
            <text class="param-value">{{item.value}}</text>
          </view>
          
          <view class="no-params" wx:if="{{debugParams.length === 0}}">
            <text>无可用参数</text>
          </view>
        </view>
        
        <!-- 解密后的参数 -->
        <view class="section-title" style="margin-top: 20rpx;">解密后参数</view>
        <view wx:if="{{decryptError}}" class="error-message">{{decryptError}}</view>
        <view class="debug-params" wx:elif="{{hasDecrypted}}">
          <view class="param-item" wx:for="{{decryptedParams}}" wx:key="key">
            <text class="param-key">{{item.key}}: </text>
            <text class="param-value">{{item.value}}</text>
          </view>
          
          <view class="no-params" wx:if="{{decryptedParams.length === 0}}">
            <text>解密后无可用参数</text>
          </view>
        </view>
        <view wx:else class="loading-message">
          <view class="mini-spinner"></view>
          <text>正在解密...</text>
        </view>
      </view>
    </view>
  </view>
</view>
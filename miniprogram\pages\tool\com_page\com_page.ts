// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,  // 默认展开
    showFloatLayer: false,
    showColorPicker: false,
    showFontPicker: false,
    showPaperPicker: false,
    showPaperCompactPicker: false,
    showCompactFontPicker: false,
    // 裁剪工具相关
    showCropper: false,
    tempFilePath: '',
    croppedImage: '',
    originalImage: '',
    cropperOpts: {
      width: 750,
      height: 750,
      maxScale: 2.5,
      minScale: 1,
      quality: 0.8
    },
    // 预设颜色数组
    presetColors: ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'],
    // 预设字体数组
    presetFonts: ['Arial', 'Verdana', 'Times New Roman', 'Georgia', 'Courier New'],
    // 预设纸张尺寸
    presetPaperSizes: [
      { name: 'A4', width: 210, height: 297 },
      { name: 'A5', width: 148, height: 210 },
      { name: 'B5', width: 176, height: 250 }
    ],
    // 默认紧凑版字体
    defaultCompactFont: { name: '默认字体', value: 'default' }
  },
  lifetimes: {
    attached() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    // 防止滚动穿透
    preventTouchMove() {
      return false
    },

    // 打开浮动层
    openFloatLayer() {
      this.setData({
        showFloatLayer: true
      });
    },

    // 关闭浮动层
    closeFloatLayer() {
      this.setData({
        showFloatLayer: false
      });
    },

    // 颜色选择器相关方法
    onColorChange1(e: any) {
      console.log('默认样式颜色变化：', e.detail.color);
    },

    onColorChange2(e: any) {
      console.log('预设颜色样式颜色变化：', e.detail.color);
    },

    onColorChange3(e: any) {
      console.log('隐藏预设样式颜色变化：', e.detail.color);
    },

    onColorChange4(e: any) {
      console.log('浮层中颜色变化：', e.detail.color);
    },

    // 打开颜色选择器浮层
    openColorPicker() {
      this.setData({
        showColorPicker: true
      });
    },

    // 关闭颜色选择器浮层
    closeColorPicker() {
      this.setData({
        showColorPicker: false
      });
    },

    // 字体选择器相关方法
    onFontChange1(e: any) {
      console.log('默认样式字体变化：', e.detail.font);
    },

    onFontChange2(e: any) {
      console.log('预设字体样式变化：', e.detail.font);
    },

    onFontChange3(e: any) {
      console.log('自定义大小字体变化：', e.detail.font);
    },

    onFontChange4(e: any) {
      console.log('浮层中字体变化：', e.detail.font);
    },

    // 打开字体选择器浮层
    openFontPicker() {
      this.setData({
        showFontPicker: true
      });
    },

    // 关闭字体选择器浮层
    closeFontPicker() {
      this.setData({
        showFontPicker: false
      });
    },

    // 纸张选择器相关方法
    onPaperChange1(e: any) {
      console.log('默认样式纸张变化：', e.detail);
    },

    onPaperChange2(e: any) {
      console.log('预设纸张样式变化：', e.detail);
    },

    onPaperChange3(e: any) {
      console.log('自定义单位纸张变化：', e.detail);
    },

    onPaperChange4(e: any) {
      console.log('浮层中纸张变化：', e.detail);
    },

    // 打开纸张选择器浮层
    openPaperPicker() {
      this.setData({
        showPaperPicker: true
      });
    },

    // 关闭纸张选择器浮层
    closePaperPicker() {
      this.setData({
        showPaperPicker: false
      });
    },

    // 紧凑版纸张选择器相关方法
    onPaperCompactChange1(e: any) {
      console.log('默认样式紧凑版纸张变化：', e.detail.size);
    },

    onPaperCompactChange2(e: any) {
      console.log('自定义按钮文字紧凑版纸张变化：', e.detail.size);
    },

    onPaperCompactChange3(e: any) {
      console.log('自定义Z-Index紧凑版纸张变化：', e.detail.size);
    },

    onPaperCompactChange4(e: any) {
      console.log('浮层中紧凑版纸张变化：', e.detail.size);
    },

    // 打开紧凑版纸张选择器浮层
    openPaperCompactPicker() {
      this.setData({
        showPaperCompactPicker: true
      });
    },

    // 关闭紧凑版纸张选择器浮层
    closePaperCompactPicker() {
      this.setData({
        showPaperCompactPicker: false
      });
    },

    // 紧凑版字体选择器相关方法
    onCompactFontChange1(e: any) {
      console.log('默认样式紧凑版字体变化：', e.detail.font);
    },

    onCompactFontChange2(e: any) {
      console.log('自定义预览文字紧凑版字体变化：', e.detail.font);
    },

    onCompactFontChange3(e: any) {
      console.log('默认选中字体紧凑版字体变化：', e.detail.font);
    },

    onCompactFontChange4(e: any) {
      console.log('浮层中紧凑版字体变化：', e.detail.font);
    },

    // 打开紧凑版字体选择器浮层
    openCompactFontPicker() {
      this.setData({
        showCompactFontPicker: true
      });
    },

    // 关闭紧凑版字体选择器浮层
    closeCompactFontPicker() {
      this.setData({
        showCompactFontPicker: false
      });
    },

    // 裁剪工具相关方法
    handleUpload() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          this.setData({
            tempFilePath,
            showCropper: true,
            croppedImage: '', // 清空之前的裁剪结果
            originalImage: tempFilePath // 保存原始图片路径
          });
        }
      });
    },

    handleCropperCancel() {
      this.setData({
        showCropper: false,
        tempFilePath: ''
      });
    },

    handleCropperComplete(e: any) {
      console.log('裁剪完成：', e.detail);
      const { path, width, height } = e.detail;
      
      if (path) {
        this.setData({
          croppedImage: path,
          showCropper: false,
          tempFilePath: '' // 清空临时文件路径
        });
        
        // 显示成功提示
        wx.showToast({
          title: '裁剪成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: '裁剪失败',
          icon: 'error',
          duration: 2000
        });
      }
    },

    // 预览裁剪后的图片
    previewImage() {
      if (this.data.croppedImage) {
        wx.previewImage({
          urls: [this.data.croppedImage],
          current: this.data.croppedImage
        });
      }
    },

    // 预览原始图片
    previewOriginalImage() {
      if (this.data.originalImage) {
        wx.previewImage({
          urls: [this.data.originalImage],
          current: this.data.originalImage
        });
      }
    },
    // 底部自定义导航栏收缩状态
    handleTabBarChange: function(data: { 
    isCollapsed: boolean,
    expandedHeight: number,
    collapsedHeight: number,
    currentHeight: number 
  }) {
    this.setData({
      isTabBarCollapsed: data.isCollapsed,
      tabBarHeight: data.currentHeight
    });
  }
  }
});

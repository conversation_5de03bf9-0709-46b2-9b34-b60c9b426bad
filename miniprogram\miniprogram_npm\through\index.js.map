{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var Stream = require('stream')\n\n// through\n//\n// a stream that does nothing but re-emit the input.\n// useful for aggregating a series of changing but not ending streams into one stream)\n\nexports = module.exports = through\nthrough.through = through\n\n//create a readable writable stream.\n\nfunction through (write, end, opts) {\n  write = write || function (data) { this.queue(data) }\n  end = end || function () { this.queue(null) }\n\n  var ended = false, destroyed = false, buffer = [], _ended = false\n  var stream = new Stream()\n  stream.readable = stream.writable = true\n  stream.paused = false\n\n//  stream.autoPause   = !(opts && opts.autoPause   === false)\n  stream.autoDestroy = !(opts && opts.autoDestroy === false)\n\n  stream.write = function (data) {\n    write.call(this, data)\n    return !stream.paused\n  }\n\n  function drain() {\n    while(buffer.length && !stream.paused) {\n      var data = buffer.shift()\n      if(null === data)\n        return stream.emit('end')\n      else\n        stream.emit('data', data)\n    }\n  }\n\n  stream.queue = stream.push = function (data) {\n//    console.error(ended)\n    if(_ended) return stream\n    if(data === null) _ended = true\n    buffer.push(data)\n    drain()\n    return stream\n  }\n\n  //this will be registered as the first 'end' listener\n  //must call destroy next tick, to make sure we're after any\n  //stream piped from here.\n  //this is only a problem if end is not emitted synchronously.\n  //a nicer way to do this is to make sure this is the last listener for 'end'\n\n  stream.on('end', function () {\n    stream.readable = false\n    if(!stream.writable && stream.autoDestroy)\n      process.nextTick(function () {\n        stream.destroy()\n      })\n  })\n\n  function _end () {\n    stream.writable = false\n    end.call(stream)\n    if(!stream.readable && stream.autoDestroy)\n      stream.destroy()\n  }\n\n  stream.end = function (data) {\n    if(ended) return\n    ended = true\n    if(arguments.length) stream.write(data)\n    _end() // will emit or queue\n    return stream\n  }\n\n  stream.destroy = function () {\n    if(destroyed) return\n    destroyed = true\n    ended = true\n    buffer.length = 0\n    stream.writable = stream.readable = false\n    stream.emit('close')\n    return stream\n  }\n\n  stream.pause = function () {\n    if(stream.paused) return\n    stream.paused = true\n    return stream\n  }\n\n  stream.resume = function () {\n    if(stream.paused) {\n      stream.paused = false\n      stream.emit('resume')\n    }\n    drain()\n    //may have become paused again,\n    //as drain emits 'data'.\n    if(!stream.paused)\n      stream.emit('drain')\n    return stream\n  }\n  return stream\n}\n\n"]}
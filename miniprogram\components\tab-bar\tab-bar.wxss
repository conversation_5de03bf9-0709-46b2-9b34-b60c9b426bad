/* tab-bar.wxss */

.tab-bar-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.nav-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(46, 93, 248, 0.98) 0%,
    rgba(75, 116, 247, 0.98) 25%,
    rgba(46, 93, 248, 0.98) 50%,
    rgba(75, 116, 247, 0.98) 75%,
    rgba(46, 93, 248, 0.98) 100%);
  background-size: 300% 300%;
  animation: gradientFlow 8s ease infinite;
  box-shadow: 0 -4rpx 30rpx rgba(46, 93, 248, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 100;
  overflow: visible;
  transform: translateY(0);
  transition: transform 0.3s ease;
  overflow: hidden;
}

.nav-footer.collapsed {
  transform: translateY(calc(100% - 24px));
}

/* 添加底部导航的光效果 */
.nav-footer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: lightShine 6s ease-in-out infinite;
  pointer-events: none;
  overflow: hidden;
}
.tab-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-shrink: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  position: relative;
  transition: all 0.3s ease;
}

/* 底部指示条样式 */
.tab-item::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 28rpx;
  height: 3rpx;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 4rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active::after {
  opacity: 1;
}

/* 图标容器 */
.tab-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon {
  transform: scale(1.1);
}

.tab-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.75);
  margin-top: 6rpx;
  transition: all 0.3s ease;
}

.tab-text-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.arrow-indicator {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid rgba(255, 255, 255, 0.75);
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  transform: translateX(-50%) rotate(0deg);
  transition: all 0.3s ease;
  opacity: 0.75;
}

.arrow-indicator.up {
  transform: translateX(-50%) rotate(180deg);
}

.tab-item.active .arrow-indicator {
  border-top-color: #ffffff;
  opacity: 1;
}

.tab-item.active .tab-text {
  color: #ffffff;
  font-weight: 500;
}

.tab-item:active {
  transform: scale(0.92);
  opacity: 0.8;
}

/* 添加涟漪效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.tab-item::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.tab-item:active::before {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
    box-shadow: 0 -4rpx 30rpx rgba(46, 93, 248, 0.15);
  }
  50% {
    background-position: 100% 50%;
    box-shadow: 0 -4rpx 40rpx rgba(46, 93, 248, 0.25);
  }
  100% {
    background-position: 0% 50%;
    box-shadow: 0 -4rpx 30rpx rgba(46, 93, 248, 0.15);
  }
}

@keyframes lightShine {
  0% {
    transform: translate(-100%, -100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(100%, 100%) rotate(45deg);
    opacity: 0;
  }
}

/* 图标样式 */
.icon-image {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.4;
  transition: all 0.3s ease;
  filter: brightness(0) invert(1);
}

.icon-image.active {
  opacity: 1;
  transform: scale(1.1);
}

.tab-bar-container {
  position: relative;
  width: 100%;
}
/* 分类面板样式 */
.category-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.category-panel.show {
  visibility: visible;
  opacity: 1;
}

.category-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 60vh;
  overflow-y: auto;
}

.category-panel.show .category-content {
  transform: translateY(0);
}
/* 
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 0 8rpx;
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 1;
}

.category-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
} */

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 8rpx 16rpx;
}

.tool-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tool-item.active {
  background: rgba(46, 93, 248, 0.1);
  border: 1px solid rgba(46, 93, 248, 0.2);
}

.tool-item.active .tool-name {
  color: #2e5df8;
  font-weight: 600;
}



.tool-item.active .tool-icon .tool-img {
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(36%) sepia(97%) saturate(1645%) hue-rotate(214deg) brightness(99%) contrast(91%);
}

.tool-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tool-item:active {
  transform: scale(0.98);
}

.tool-item:active::after {
  opacity: 1;
}

.tool-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tool-icon .tool-img {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
}

.tool-info {
  flex: 1;
  min-width: 0;
}

.tool-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}

.tool-desc {
  font-size: 22rpx;
  color: rgba(46, 93, 248, 0.8);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}
.tool-item.active .tool-desc {
  color: rgba(46, 93, 248, 0.8);
}
/* 收缩按钮样式 */
.collapse-button {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 50rpx;
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 -4rpx 10rpx rgba(46, 93, 248, 0.15);
  padding: 0 20rpx;
  gap: 8rpx;
}

.collapse-button .button-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  line-height: 1;
}

.collapse-icon {
  width: 16rpx;
  height: 16rpx;
  border-left: 3rpx solid rgba(255, 255, 255, 0.9);
  border-bottom: 3rpx solid rgba(255, 255, 255, 0.9);
  transition: transform 0.3s ease;
  position: relative;
  top: 0;
}

.collapse-button:not(.collapsed) .collapse-icon {
  transform: rotate(-45deg);
}

.collapse-button.collapsed .collapse-icon {
  transform: rotate(135deg);
} 

<!-- tab-bar.wxml -->
<wxs src="../../utils/constants.wxs" module="constants" />

<view class="tab-bar-container">
  <!-- 底部导航栏 -->
   <!-- 收缩按钮 -->
    <view class="collapse-button {{isCollapsed ? 'collapsed' : ''}}" bindtap="toggleCollapse" style="bottom: {{currentHeight}}px">
      <view class="collapse-icon"></view>
    </view>
  <view class="nav-footer {{isCollapsed ? 'collapsed' : ''}}" style="{{contentStyle}}">
    
    
    <view class="tab-content" style="{{contentStyle}}">
      <!-- 页面计数器 -->
      <view class="page-counter" wx:if="{{pagesCount > 0}}">
        <text class="counter-text">{{pagesCount}}</text>
      </view>
      <view class="tab-item {{activeTabIndex === index ? 'active' : ''}}" 
        wx:for="{{navMenus}}" 
        wx:key="id"
        bindtap="switchTab"
        data-index="{{index}}"
        data-category-id="{{item.id}}"
        data-url="{{item.url}}"
        >
        <view class="tab-icon">
          <image src="{{constants.STATIC_URL.ICON}}{{item.icon}}.svg" mode="aspectFit" class="icon-image {{activeTabIndex === index ? 'active' : ''}}"></image>
        </view>
        <view class="tab-text-wrapper">
          <text class="tab-text">{{item.type === 'tab' ? item.text : item.text}}</text>
          <view wx:if="{{item.type === 'category'}}" class="arrow-indicator {{showCategoryPanel && selectedCategory === item.id ? 'up' : 'down'}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分类面板 -->
  <view class="category-panel {{showCategoryPanel ? 'show' : ''}}" 
    catchtap="closeCategoryPanel"
    style="bottom: {{tabBarStyle}}">
    <view class="category-content" catchtap="preventDefault" style="{{safeAreaStyle}}">
      <view class="tools-grid">
        <view class="tool-item {{tool.url === currentPath ? 'active' : ''}}" 
          wx:for="{{submenuslist}}" 
          wx:key="id"
          wx:for-item="tool"
          catchtap="onToolTap"
          data-url="{{tool.url}}">
          <view class="tool-icon">
            <image class="tool-img-class" src="{{constants.STATIC_URL.ICON}}{{tool.icon}}.svg" mode="aspectFit"></image>
          </view>
          <view class="tool-info">
            <text class="tool-name">{{tool.text}}</text>
            <text class="tool-desc">{{tool.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <login-modal id="loginModal" />
</view> 

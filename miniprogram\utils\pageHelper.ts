/**
 * 页面辅助工具
 */

/**
 * 更新页面标题（同时更新导航栏和页面数据中的标题）
 * @param page 页面实例 this
 * @param title 要设置的标题
 */
export const updatePageTitle = async (page: any, title: string) => {
  if (!title) return;
  
  try {
    // 同时更新导航栏标题和页面数据
    await Promise.all([
      page.setData({
        pagetitle: title
      }),
      wx.setNavigationBarTitle({
        title: title
      })
    ]);

    // 强制再次更新确保生效
    setTimeout(() => {
      if (page.data.pagetitle !== title) {
        page.setData({ pagetitle: title });
      }
    }, 50);
  } catch (error) {
    console.error('更新页面标题失败:', error);
  }
};

export default {
  updatePageTitle
}; 
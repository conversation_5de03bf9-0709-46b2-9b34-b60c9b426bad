var globalThis=this,self=this;module.exports=require("../_commons/0.js")([{ids:[9],modules:{210:function(e,r,t){"use strict";t.r(r);var u=t(24),n=t.n(u),a=t(25),o=t.n(a),i=function(){for(var e,r=arguments.length,t=new Array(r),u=0;u<r;u++)t[u]=arguments[u];var n,a,o=t[0]||"",i=t.length-1;if(i<1)return o;for(e=1;e<i+1;)o=o.replace(/%s/,"{#"+e+"#}"),e++;for(o.replace("%s",""),e=1;void 0!==(n=t[e]);)a=new RegExp("{#"+e+"#}","g"),o=o.replace(a,n),e++;return o},F="%s必填",s="长度最少为%s",l="长度最大为%s",d="长度在%s和%s之间",f="最多只能输入%s个字",c="值最小为%s",h="值最大为%s",v="值的范围为%s和%s之间",m="请输入正确的手机号",p="请输入正确的电子邮件",D="请输入正确的URL地址",g="值和字段%s不相等",x={required:function(e,r){return!1===e.required?"":function(e){return 0!==e&&!1!==e&&!e}(r)?i(e.message||F,e.name):""},minlength:function(e,r){var t=e.minlength;return(r=r||"").length<t?i(e.message||s,t):""},maxlength:function(e,r){var t=e.maxlength;return(r=r||"").length>t?i(e.message||l,t):""},rangelength:function(e,r){var t=e.rangelength;return(r=r||"").length>t[1]||r.length<t[0]?i(e.message||d,t[0],t[1]):""},min:function(e,r){var t=e.min;return r<t?i(e.message||c,t):""},max:function(e,r){var t=e.max;return r>t?i(e.message||h,t):""},range:function(e,r){var t=e.range;return r<t[0]||r>t[1]?i(e.message||v,t[0],t[1]):""},mobile:function(e,r){return r=r||"",!1===e.mobile?"":11!==r.length?i(e.message||m):""},email:function(e,r){return!1===e.email||/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i.test(r)?"":i(e.message||p)},url:function(e,r){return!1===e.url||/^(https?|s?ftp|weixin):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(r)?"":e.message||D},equalTo:function(e,r,t,u){var n=e.equalTo;return r!==u[n]?i(e.message||g,e.name):""},bytelength:function(e,r,t){return t=e.param,(r=r||"").replace(/[^\x00-\xff]/g,"**").length>t?i(e.message||f,t):""}},E=function(e,r){if(!e&&r||e&&!r)return!0;for(var t in r)if(e[t]!==r[t])return!0;for(var u in e)if(e[u]!==r[u])return!0;return!1},y=Object.prototype.toString,b=function(e,r){for(var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n="",a=Object.keys(e),o=0,i=a.length;o<i;++o){var F=a[o];if("name"!==F&&"message"!==F){var s=void 0!==e.validator?e.validator:x[F];if("function"==typeof s&&(n=s(e,r,t,u)))return n}}return n},_=function(){return o()((function e(r,t){n()(this,e),this.models=void 0,this.rules=void 0,this.errors=void 0,this.models=r,this.rules=t,this.errors={}}),[{key:"validate",value:function(e){var r=this;return new Promise((function(t){var u=0,n=r.errors,a=r.models;Object.keys(r.rules).forEach((function(e){var t=n[e];r._innerValidateField(e,a[e],(function(r,a){r||u++,E(t,a)&&(n[e]=a)}))})),Object.keys(n).forEach((function(e){n[e]||delete n[e]})),t({isValid:!u,errors:u?n:void 0}),e&&e(!u,u?n:void 0)}))}},{key:"validateField",value:function(e,r,t){var u=this;return new Promise((function(n){u._innerValidateField(e,r,(function(r,a){var o={};o[e]=a,n({valid:r,error:r?void 0:a}),t&&t(r,r?void 0:o);var i=u.errors[e];E(i,a)&&(a||delete u.errors[e],u.errors[e]=a)}))}))}},{key:"_innerValidateField",value:function(e,r,t){var u=this.rules[e];if(!u)return console.warn("[form-validator] rule name ".concat(e," not exists.")),void t(!0);"function"==typeof r&&(t=r,r=void 0);var n=!1,a=this.models;if("[object Array]"===y.call(u))u.forEach((function(u){u.name=e;var o=b(u,r||a[e],u.param,a);o&&!n&&(n=!0,t(!1,o?{message:o,rule:u}:void 0))})),n||t(!n);else{var o=u;o.name=e;var i=b(o,r||a[e],o.param,a);i&&(n=!0),t(!n,i?{message:i,rule:o}:void 0)}}},{key:"setModel",value:function(e){this.models=e}},{key:"setRules",value:function(e){this.rules=e}}],[{key:"addMethod",value:function(e,r){x[e]=r}}])}();function C(e){e.data.prop&&(this.data.formItems[e.data.prop]=e),e.setInForm&&e.setInForm(),this.data.firstItem||(this.data.firstItem=e)}function A(e){e.data.prop&&delete this.data.formItems[e.data.prop]}Component({properties:{models:{type:Object,value:{},observer:"_modelChange"},rules:{type:Array,value:[],observer:"_rulesChange"},extClass:{type:String,value:""}},data:{errors:{},formItems:{},firstItem:null},relations:{"../cell/cell":{type:"descendant",linked:C,unlinked:A},"../checkbox-group/checkbox-group":{type:"descendant",linked:C,unlinked:A}},attached:function(){this.initRules(),this.formValidator=new _(this.data.models,this.data.newRules)},methods:{initRules:function(e){var r={};return(e||this.data.rules).forEach((function(e){e.name&&e.rules&&(r[e.name]=e.rules||[])})),this.setData({newRules:r}),r},_modelChange:function(e,r){var t=this;if(!this.formValidator)return e;this.formValidator.setModel(e);var u=function(e,r){if(!e&&r)return r;if(!r&&e)return e;var t={},u=!1;for(var n in r)e[n]!==r[n]&&(u=!0,t[n]=r[n]);for(var a in e)e[a]!==r[a]&&(u=!0,t[a]=r[a]);return u?t:null}(r,e);if(u){var n=!0,a=[],o={};Object.keys(u).forEach((function(e){t.formValidator.validateField(e,u[e],(function(r,t){t&&t[e]&&(a.push(t[e]),o[e]=t[e]),n=r}))})),this._showErrors(u,o),this.triggerEvent(n?"success":"fail",n?{trigger:"change"}:{errors:a,trigger:"change"})}return e},_rulesChange:function(e){var r=this.initRules(e);return this.formValidator&&this.formValidator.setRules(r),e},_showAllErrors:function(e){var r=this;Object.keys(this.data.newRules).forEach((function(t){r._showError(t,e&&e[t])}))},_showErrors:function(e,r){var t=this;Object.keys(e).forEach((function(e){t._showError(e,r&&r[e])}))},_showError:function(e,r){var t=this.data.formItems[e];t&&t.data.showError&&t.setError(r)},validate:function(e){var r=this;return this.formValidator.validate((function(t,u){r._showAllErrors(u);var n=r.handleErrors(u);r.triggerEvent(t?"success":"fail",t?{trigger:"validate"}:{errors:n,trigger:"validate"}),e&&e(t,n)}))},validateField:function(e,r){var t=this,u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){};return this.formValidator.validateField(e,r,(function(r,n){t._showError(e,n);var a=t.handleErrors(n);t.triggerEvent(r?"success":"fail",r?{trigger:"validate"}:{errors:a,trigger:"validate"}),u&&u(r,a)}))},handleErrors:function(e){if(e){var r=[];return this.data.rules.forEach((function(t){e[t.name]&&(e[t.name].name=t.name,r.push(e[t.name]))})),r}return e},addMethod:function(e,r){return this.formValidator.addMethod(e,r)}}});r.default=_},24:function(e,r){e.exports=function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},25:function(e,r,t){var u=t(65);function n(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}e.exports=function(e,r,t){return r&&n(e.prototype,r),t&&n(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},4:function(e,r,t){e.exports=t(210)},5:function(e,r){function t(r){"@babel/helpers - typeof";return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},65:function(e,r,t){var u=t(5).default,n=t(66);e.exports=function(e){var r=n(e,"string");return"symbol"==u(r)?r:r+""},e.exports.__esModule=!0,e.exports.default=e.exports},66:function(e,r,t){var u=t(5).default;e.exports=function(e,r){if("object"!=u(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports}},entries:[[4,0]]}]);
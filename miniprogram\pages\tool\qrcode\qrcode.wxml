<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="小程序码生成" showBack="{{true}}" showMore=""></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll" enhanced="{{true}}" bounces="{{true}}">
      <!-- 新增：扫码参数显示区域 -->
      <!-- 扫码结果显示区域 -->
<view class="scan-result" >
  <text class="scan-result-title">扫码参数</text>
          <text class="scan-result-content">1. {{scanResult}}</text>
          <text class="scan-result-content">2. {{scanResult_all}}</text>
          <view class="scan-result-content expandable-item">
            <view class="expandable-header" bindtap="toggleCurrentPage">
              <text>3. </text>
              <text class="expand-icon">{{isCurrentPageExpanded ? '▼' : '▶'}}</text>
              <text>当前页面信息</text>
            </view>
            <view class="expandable-content" wx:if="{{isCurrentPageExpanded}}">
              <block wx:for="{{currentPageArray}}" wx:key="key">
                <view class="object-item">
                  <text class="item-key">{{item.key}}: </text>
                  <text class="item-value">{{item.value}}</text>
                </view>
              </block>
            </view>
          </view>
          <text class="scan-result-content">4. {{currentPageOptions}}</text>
</view>
      <!-- 参数设置区域 -->
      <view class="qrcode-form">
        <view class="form-item">
          <text class="form-label">跳转页面</text>
          <picker bindchange="onPageChange" value="{{pageIndex}}" range="{{pages}}" range-key="name">
            <view class="picker">
              {{pages[pageIndex].name || '请选择页面'}}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">场景参数</text>
          <input class="form-input" 
                 type="text" 
                 placeholder="请输入场景参数" 
                 value="{{scene}}"
                 bindinput="onSceneInput" />
        </view>

        <view class="form-item">
          <text class="form-label">二维码尺寸</text>
          <slider class="form-slider" 
                  min="280" 
                  max="1280" 
                  step="10" 
                  value="{{width}}" 
                  show-value 
                  bindchange="onWidthChange" />
        </view>

        <view class="form-item">
          <text class="form-label">检查页面路径</text>
          <switch checked="{{check_path}}" bindchange="onCheckPathChange" />
        </view>

        <view class="form-item">
          <text class="form-label">小程序版本</text>
          <picker bindchange="onEnvVersionChange" value="0" range="{{envVersions}}" range-key="name">
            <view class="picker">
              {{currentVersionName}}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">自动配色</text>
          <switch checked="{{auto_color}}" bindchange="onAutoColorChange" />
        </view>

        <block wx:if="{{!auto_color}}">
          <view class="form-item">
            <text class="form-label">线条颜色</text>
            <view class="color-picker">
              <view class="color-preview" style="background: rgb({{line_color.r}}, {{line_color.g}}, {{line_color.b}})"></view>
              <view class="color-sliders">
                <slider class="color-slider" min="0" max="255" value="{{line_color.r}}" bindchange="onLineColorChange" data-channel="r" show-value />
                <slider class="color-slider" min="0" max="255" value="{{line_color.g}}" bindchange="onLineColorChange" data-channel="g" show-value />
                <slider class="color-slider" min="0" max="255" value="{{line_color.b}}" bindchange="onLineColorChange" data-channel="b" show-value />
              </view>
            </view>
          </view>
        </block>

        <view class="form-item">
          <text class="form-label">透明背景</text>
          <switch checked="{{is_hyaline}}" bindchange="onHyalineChange" />
        </view>

        <button class="generate-btn" bindtap="generateQRCode" loading="{{generating}}">
          生成小程序码
        </button>
      </view>

      <!-- 预览区域 -->
      <view class="qrcode-preview" wx:if="{{qrcodePath}}">
        <text class="preview-title">预览</text>
        <image class="qrcode-image" src="{{qrcodePath}}" mode="aspectFit" bindtap="previewQRCode"></image>
        <button class="save-btn" bindtap="saveQRCode">保存到相册</button>
        
      </view>

      <!-- 说明区域 -->
      <view class="tool-intro">
        <view class="intro-header">
          <text class="intro-icon">🎯</text>
          <text class="intro-title">使用说明</text>
        </view>
        <view class="intro-content">
          <view class="intro-section">
            <text class="section-title">功能介绍</text>
            <text class="section-text">本工具可以帮助你生成带参数的小程序码，支持自定义页面跳转、场景参数、样式配置等。</text>
          </view>
          <view class="intro-section">
            <text class="section-title">参数说明</text>
            <view class="feature-list">
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">场景参数：支持任意字符，无长度限制（将自动加密）</text>
              </view>
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">二维码尺寸：280px ~ 1280px</text>
              </view>
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">检查页面：开启时需要页面已发布</text>
              </view>
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">小程序版本：可选开发版、体验版、正式版</text>
              </view>
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">自动配色：系统自动选择最佳配色方案</text>
              </view>
              <view class="feature-item">
                <text class="feature-dot">•</text>
                <text class="feature-text">透明背景：生成透明底色的小程序码</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"></tab-bar>
</view>




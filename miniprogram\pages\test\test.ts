// index.ts
import { layoutUtil } from '../../utils/layout';
import Api, {} from '../../utils/api';
import eventBus from '../../utils/eventBus';
Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    navMenus: [],
    tools: [],
    loading: true,
    isEmpty: false
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    // 添加显示登录浮窗的方法
    showLoginModal: function() {
      // 触发显示登录浮窗事件
      eventBus.emit('showLoginModal', {});
    },
    
    // 带参数的显示登录浮窗方法（例如绑定账号的场景）
    showLoginModalWithParams: function(userId: number, source: string) {
      eventBus.emit('showLoginModal', {
        fromRedirect: true,
        user_id: userId,
        source: source
      });
    },
    
    // 如果需要隐藏登录浮窗，直接触发 hideLoginModal 事件
    hideLoginModal: function() {
      eventBus.emit('hideLoginModal');
    },
    
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '工具箱',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '工具箱',
        path: '/pages/tool/test/test'
      };
    },

    // 测试带参数的登录浮窗
    showLoginWithParams: function() {
      this.showLoginModalWithParams(12345, 'test-page');
    }
  }
});

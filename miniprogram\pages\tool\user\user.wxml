<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="页面标题" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="user-info-card">
        <button wx:if="{{!hasUserInfo}}" class="auth-btn" bindtap="getUserProfile">获取用户信息</button>
        <block wx:else>
          <view class="user-info-header" wx:if="{{userInfo}}">
            <image class="avatar" src="{{userInfo.avatar || userInfo.avatarUrl}}" mode="aspectFill"></image>
            <view class="user-info">
              <text class="nickname">{{userInfo.nickname || userInfo.nickName}}</text>
            </view>
          </view>
          <view class="user-info-detail">
            <text class="detail-label">OpenID：</text>
            <text class="detail-value">{{openid || '未获取'}}</text>
          </view>
        </block>
      </view>
      <view class="layout-debug">
        <view class="test-c">
          <block wx:if="{{!hasUserInfo}}">
            <button class="auth-btn" bindtap="getUserProfile">授权登录</button>
            <button class="login-btn" bindtap="handleLogin">普通登录</button>
          </block>
          <block wx:else>
            <view class="user-info-header" wx:if="{{userInfo}}">
              <image class="avatar" src="{{userInfo.avatar || userInfo.avatarUrl}}" mode="aspectFill"></image>
              <view class="user-info">
                <text class="nickname">{{userInfo.nickname || userInfo.nickName}}</text>
              </view>
            </view>
          </block>
          <view wx:for="{{navMenus}}" wx:key="index" class="test-item">
            {{item.id}}{{item.text}}
          </view>
        </view>
        <view class="section">
          <view class="title">布局信息</view>
          {{layoutUtil.getLayoutInfo()}}
          <view class="info-item">
            <text class="label">状态栏高度：</text>
            <text class="value">{{layoutInfo.statusBarHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="label">导航栏总高度：</text>
            <text class="value">{{layoutInfo.navigationHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="label">胶囊按钮高度：</text>
            <text class="value">{{layoutInfo.menuButtonHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="label">胶囊按钮顶部位置：</text>
            <text class="value">{{layoutInfo.menuButtonTop}}px</text>
          </view>
          <view class="info-item">
            <text class="label">底部导航总高度：</text>
            <text class="value">{{layoutInfo.tabBarHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="label">底部安全区域高度：</text>
            <text class="value">{{layoutInfo.safeAreaBottom}}px</text>
          </view>
        </view>

        <view class="section">
          <view class="title">样式信息</view>
          <view class="style-item">
            <text class="label">内容区域样式：</text>
            <text class="value">{{layoutStyles.contentStyle}}</text>
          </view>
          <view class="style-item">
            <text class="label">顶部样式：</text>
            <text class="value">{{layoutStyles.contentStyleTop}}</text>
          </view>
          <view class="style-item">
            <text class="label">底部样式：</text>
            <text class="value">{{layoutStyles.contentStyleBottom}}</text>
          </view>
          <view class="style-item">
            <text class="label">底部导航样式：</text>
            <text class="value">{{layoutStyles.tabBarStyle}}</text>
          </view>
          <view class="style-item">
            <text class="label">导航内容样式：</text>
            <text class="value">{{layoutStyles.tabBarContentStyle}}</text>
          </view>
        </view>

        <view class="section">
          <view class="title">胶囊按钮信息</view>
          <view class="info-item" wx:for="{{['width', 'height', 'top', 'right', 'bottom', 'left']}}" wx:key="*this">
            <text class="label">{{item}}：</text>
            <text class="value">{{layoutInfo.menuButtonBounding[item]}}px</text>
          </view>
        </view>

        <view class="section">
          <view class="title">系统信息</view>
          <view class="info-item">
            <text class="label">机型：</text>
            <text class="value">{{layoutInfo.systemInfo.model}}</text>
          </view>
          <view class="info-item">
            <text class="label">品牌：</text>
            <text class="value">{{layoutInfo.systemInfo.brand}}</text>
          </view>
          <view class="info-item">
            <text class="label">系统：</text>
            <text class="value">{{layoutInfo.systemInfo.system}}</text>
          </view>
          <view class="info-item">
            <text class="label">屏幕宽度：</text>
            <text class="value">{{layoutInfo.systemInfo.screenWidth}}px</text>
          </view>
          <view class="info-item">
            <text class="label">屏幕高度：</text>
            <text class="value">{{layoutInfo.systemInfo.screenHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="label">窗口宽度：</text>
            <text class="value">{{layoutInfo.systemInfo.windowWidth}}px</text>
          </view>
          <view class="info-item">
            <text class="label">窗口高度：</text>
            <text class="value">{{layoutInfo.systemInfo.windowHeight}}px</text>
          </view>
        </view>
      </view>
      <view class="test-c">
        <view wx:for="{{20}}" wx:key="index" class="test-item">
        测试内容 {{index + 1}}</view>
      </view>
        <!-- 说明开始 -->
        <view class="tool-intro">
            <view class="intro-header">
              <text class="intro-icon">🎯</text>
              <text class="intro-title">工具说明</text>
            </view>
            <view class="intro-content">
              <view class="intro-section">
                <text class="section-title">主要功能</text>
                <text class="section-text">本工具可以帮助你将图片快速划分为等比例的网格，适用于：绘画临摹、像素画制作、设计稿分割等场景。</text>
              </view>
              <view class="intro-section">
                <text class="section-title">使用步骤</text>
                <view class="step-list">
                  <view class="step-item">
                    <text class="step-num">1</text>
                    <text class="step-text">上传并裁剪你的图片</text>
                  </view>
                  <view class="step-item">
                    <text class="step-num">2</text>
                    <text class="step-text">调整网格、坐标样式</text>
                  </view>
                  <view class="step-item">
                    <text class="step-num">3</text>
                    <text class="step-text">点击格子可放大查看</text>
                  </view>
                  <view class="step-item">
                    <text class="step-num">4</text>
                    <text class="step-text">导出网格图或完整图</text>
                  </view>
                </view>
              </view>
              <view class="intro-section">
                <text class="section-title">特色功能</text>
                <view class="feature-list">
                  <view class="feature-item">
                    <text class="feature-dot">•</text>
                    <text class="feature-text">支持多种纸张尺寸计算</text>
                  </view>
                  <view class="feature-item">
                    <text class="feature-dot">•</text>
                    <text class="feature-text">网格样式自由调整</text>
                  </view>
                  <view class="feature-item">
                    <text class="feature-dot">•</text>
                    <text class="feature-text">单格放大高清导出</text>
                  </view>
                  <view class="feature-item">
                    <text class="feature-dot">•</text>
                    <text class="feature-text">坐标系统辅助定位</text>
                  </view>
                </view>
              </view>
            </view>
        </view>
        <!-- 说明结束 -->
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>




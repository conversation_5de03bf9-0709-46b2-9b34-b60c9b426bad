import Api from '../../utils/api';

Component({
  data: {
    showLoading: true,
    layoutStyle: 'height:300px',
    currentChart: 'column', // 当前图表类型
    chartData: {} as any,
    userStats: null as any, // 添加用户统计数据
    opts: {
      color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE"],
      padding: [15, 15, 0, 5],
      legend: {
        show: true,
        position: "bottom",
        float: "center",
        padding: 10,
        margin: 5
      },
      xAxis: {
        disableGrid: true,
        fontColor: "#666666"
      },
      yAxis: {
        gridType: "dash",
        dashLength: 2,
        data: [
          {
            min: 0
          }
        ]
      },
      extra: {
        column: {
          type: "group",
          width: 30,
          activeBgColor: "#000000",
          activeBgOpacity: 0.08
        }
      }
    }
  },
  lifetimes: {
    attached() {
      this.initChart();
    }
  },
  methods: {
    // 切换图表类型
    switchChartType(e) {
      const type = e.currentTarget.dataset.type;
      
      // 先清空chartData，避免不同图表类型间的数据结构干扰
      this.setData({
        currentChart: type,
        chartData: {}
      });
      
      // 根据图表类型设置不同的配置项
      this.setChartOptions(type);
      
      // 为词云图设置更大的布局区域
      if (type === 'word') {
        this.setData({
          layoutStyle: 'height:400px'
        });
      } else {
        this.setData({
          layoutStyle: 'height:300px'
        });
      }
      
      // 更新图表数据
      this.generateChartData(type);
    },
    
    // 根据图表类型设置不同的配置
    setChartOptions(type) {
      let opts = {};
      
      // 基础公共配置
      const baseOpts = {
        color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE"],
        padding: [15, 15, 0, 5],
        legend: {
          show: true,
          position: "bottom",
          float: "center",
          padding: 10,
          margin: 5
        }
      };
      
      // 根据图表类型设置特定配置
      switch(type) {
        case 'column':
          opts = {
            ...baseOpts,
            xAxis: {
              disableGrid: true,
              fontColor: "#666666"
            },
            yAxis: {
              gridType: "dash",
              dashLength: 2,
              data: [{ min: 0 }]
            },
            extra: {
              column: {
                type: "group",
                width: 30,
                activeBgColor: "#000000",
                activeBgOpacity: 0.08
              }
            }
          };
          break;
        case 'line':
          opts = {
            ...baseOpts,
            xAxis: {
              disableGrid: true,
              fontColor: "#666666"
            },
            yAxis: {
              gridType: "dash",
              dashLength: 2,
              data: [{ min: 0 }]
            },
            extra: {
              line: {
                type: "curve",
                width: 3,
                activeType: "hollow"
              }
            }
          };
          break;
        case 'area':
          opts = {
            ...baseOpts,
            xAxis: {
              disableGrid: true,
              fontColor: "#666666"
            },
            yAxis: {
              gridType: "dash",
              dashLength: 2,
              data: [{ min: 0 }]
            },
            extra: {
              area: {
                type: "curve",
                opacity: 0.2,
                addLine: true,
                width: 2,
                gradient: true
              }
            }
          };
          break;
        case 'bar':
          opts = {
            ...baseOpts,
            padding: [15, 30, 0, 5],
            xAxis: {
              boundaryGap: "justify",
              disableGrid: false,
              min: 0,
              axisLine: false
            },
            yAxis: {},
            extra: {
              bar: {
                type: "group",
                width: 30,
                meterBorde: 1,
                meterFillColor: "#FFFFFF",
                activeBgColor: "#000000",
                activeBgOpacity: 0.08
              }
            }
          };
          break;
        case 'mount':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 5],
            xAxis: {
              disableGrid: true,
            },
            yAxis: {
              data: [{ min: 0 }]
            },
            extra: {
              mount: {
                type: "mount",
                widthRatio: 1.5,
              }
            }
          };
          break;
        case 'pie':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            legend: {
              show: true,
              position: "right",
              lineHeight: 25
            },
            extra: {
              pie: {
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: true,
                borderWidth: 3,
                borderColor: "#FFFFFF"
              }
            }
          };
          break;
        case 'ring':
          opts = {
            ...baseOpts,
            padding: [5, 5, 5, 5],
            rotate: false,
            dataLabel: true,
            legend: {
              show: true,
              position: "right",
              lineHeight: 25
            },
            title: {
              name: "收益率",
              fontSize: 15,
              color: "#666666"
            },
            subtitle: {
              name: "70%",
              fontSize: 25,
              color: "#1890FF"
            },
            extra: {
              ring: {
                ringWidth: 30,
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: true,
                borderWidth: 3,
                borderColor: "#FFFFFF"
              }
            }
          };
          break;
        case 'rose':
          opts = {
            ...baseOpts,
            padding: [5, 5, 5, 5],
            legend: {
              show: true,
              position: "left",
              lineHeight: 25
            },
            extra: {
              rose: {
                type: "area",
                minRadius: 50,
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: false,
                borderWidth: 2,
                borderColor: "#FFFFFF"
              }
            }
          };
          break;
        case 'radar':
          opts = {
            ...baseOpts,
            padding: [5, 5, 5, 5],
            dataLabel: false,
            legend: {
              show: true,
              position: "right",
              lineHeight: 25
            },
            extra: {
              radar: {
                gridType: "radar",
                gridColor: "#CCCCCC",
                gridCount: 5,
                opacity: 0.2,
                max: 100
              }
            }
          };
          break;
        case 'gauge':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            title: {
              name: "80%",
              fontSize: 25,
              color: "#2fc25b",
              offsetY: 50
            },
            subtitle: {
              name: "完成率",
              fontSize: 15,
              color: "#1890ff",
              offsetY: -50
            },
            extra: {
              gauge: {
                type: "default",
                width: 30,
                labelColor: "#666666",
                startAngle: 0.75,
                endAngle: 0.25,
                startNumber: 0,
                endNumber: 100,
                labelFormat: "",
                splitLine: {
                  fixRadius: 0,
                  splitNumber: 10,
                  width: 30,
                  color: "#FFFFFF",
                  childNumber: 5,
                  childWidth: 12
                },
                pointer: {
                  width: 24,
                  color: "auto"
                }
              }
            }
          };
          break;
        case 'candle':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            enableScroll: true,
            enableMarkLine: true,
            dataLabel: false,
            xAxis: {
              labelCount: 4,
              itemCount: 40,
              disableGrid: true,
              scrollShow: true,
              scrollAlign: "left"
            },
            yAxis: {},
            extra: {
              candle: {
                color: {
                  upLine: "#f04864",
                  upFill: "#f04864",
                  downLine: "#2fc25b",
                  downFill: "#2fc25b"
                },
                average: {
                  show: true,
                  name: ["MA5", "MA10", "MA30"],
                  day: [5, 10, 20],
                  color: ["#1890ff", "#2fc25b", "#facc14"]
                }
              },
              markLine: {
                type: "dash",
                dashLength: 5,
                data: [
                  {
                    value: 2150,
                    lineColor: "#f04864",
                    showLabel: true
                  },
                  {
                    value: 2350,
                    lineColor: "#f04864",
                    showLabel: true
                  }
                ]
              }
            }
          };
          break;
        case 'funnel':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            legend: {
              show: true,
              position: "right",
              lineHeight: 25
            },
            extra: {
              funnel: {
                activeOpacity: 0.3,
                activeWidth: 10,
                border: true,
                borderWidth: 2,
                borderColor: "#FFFFFF",
                fillOpacity: 1,
                labelAlign: "right"
              }
            }
          };
          break;
        case 'scatter':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            dataLabel: false,
            xAxis: {
              disableGrid: false,
              gridType: "dash",
              splitNumber: 5,
              boundaryGap: "justify",
              min: 0
            },
            yAxis: {
              disableGrid: false,
              gridType: "dash",
            },
            extra: {
              scatter: {}
            }
          };
          break;
        case 'bubble':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            xAxis: {
              disableGrid: false,
              gridType: "dash",
              splitNumber: 5,
              boundaryGap: "justify",
              min: 0,
              max: 250
            },
            yAxis: {
              disableGrid: false,
              gridType: "dash",
              data: [{
                min: 0,
                max: 150
              }]
            },
            extra: {
              bubble: {
                border: 2,
                opacity: 0.5,
              },
            }
          };
          break;
        case 'mix':
          opts = {
            ...baseOpts,
            padding: [15, 15, 0, 15],
            xAxis: {
              disableGrid: true,
            },
            yAxis: {
              disabled: false,
              disableGrid: false,
              splitNumber: 5,
              gridType: "dash",
              dashLength: 4,
              gridColor: "#CCCCCC",
              padding: 10,
              showTitle: true,
              data: []
            },
            extra: {
              mix: {
                column: {
                  width: 20
                }
              },
            }
          };
          break;
        case 'word':
          opts = {
            ...baseOpts,
            padding: [15, 15, 15, 15],
            background: '#FFFFFF',
            legend: {
              show: false
            },
            extra: {
              word: {
                type: 'vertical',  // 设置为垂直类型，支持文字旋转
                autoColors: true,  // 启用自动颜色
                maxFontSize: 60,   // 最大字体大小
                minFontSize: 12,   // 最小字体大小
                rotate: true      // 允许文字旋转
              }
            }
          };
          break;
        case 'map':
          opts = {
            ...baseOpts,
            padding: [0, 0, 0, 0],
            dataLabel: true,
            extra: {
              map: {
                border: true,
                borderWidth: 1,
                borderColor: "#666666",
                fillOpacity: 0.6,
                activeBorderColor: "#F04864",
                activeFillColor: "#FACC14",
                activeFillOpacity: 1,
                activeTextColor: "#000000"
              },
            }
          };
          break;
        case 'arcbar':
          opts = {
            ...baseOpts,
            title: {
              name: "百分比",
              fontSize: 25,
              color: "#00FF00"
            },
            subtitle: {
              name: "默认标题",
              fontSize: 15,
              color: "#666666"
            },
            extra: {
              arcbar: {
                type: "default",
                width: 12,
                backgroundColor: "#E9E9E9",
                startAngle: 0.75,
                endAngle: 0.25,
                gap: 2
              }
            }
          };
          break;
        case 'tline':
          opts = {
            ...baseOpts,
            padding: [15, 10, 0, 15],
            xAxis: {
              disableGrid: false,
              boundaryGap: "justify",
            },
            yAxis: {
              gridType: "dash",
              dashLength: 2,
              data: [{
                min: 0,
                max: 80
              }]
            },
            extra: {
              line: {
                type: "curve",
                width: 2
              },
            }
          };
          break;
        case 'tarea':
          opts = {
            ...baseOpts,
            padding: [15, 10, 0, 15],
            xAxis: {
              disableGrid: true,
              boundaryGap: "justify",
            },
            yAxis: {
              gridType: "dash",
              dashLength: 2,
              data: [{
                min: 0,
                max: 80
              }]
            },
            extra: {
              area: {
                type: "curve",
                opacity: 0.2,
                addLine: true,
                width: 2,
                gradient: true
              },
            }
          };
          break;
        default:
          opts = baseOpts;
      }
      
      this.setData({ opts });
    },
    
    // 初始化图表数据
    initChart() {
      this.setData({
        showLoading: true,
        chartData: {} // 确保开始时chartData为空对象
      });
      
      // 获取用户统计数据
      this.getUserStats();
      
      // 设置初始图表配置
      this.setChartOptions(this.data.currentChart);
      
      // 生成图表数据
      this.generateChartData(this.data.currentChart);
    },
    
    // 获取用户统计数据
    getUserStats() {
      // 获取当前用户ID
      const app = getApp();
      const userId = app.globalData.userId || 1; // 默认为1，实际应从全局获取
      
      // 显示加载中
      wx.showLoading({
        title: '加载数据中...',
      });
      
      // 调用API获取用户统计数据
      Api.user.userStats(userId).then(res => {
        console.log('用户统计数据:', res);
        
        // 更新数据
        this.setData({
          userStats: res
        });
        
        // 根据获取的数据更新图表
        this.generateChartData(this.data.currentChart);
        
        wx.hideLoading();
      }).catch(err => {
        console.error('获取用户统计数据失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      });
    },
    
    // 根据图表类型生成数据
    generateChartData(type) {
      this.setData({ showLoading: true });
      
      // 模拟API数据加载
      setTimeout(() => {
        let chartData: any = {};
        
        // 如果有用户统计数据，则使用真实数据
        if (this.data.userStats) {
        switch(type) {
          case 'column':
              chartData = this.generateColumnChartData();
              break;
          case 'line':
              chartData = this.generateLineChartData();
              break;
          case 'area':
              chartData = this.generateAreaChartData();
              break;
          case 'mount':
              chartData = this.generateMountChartData();
              break;
            case 'bar':
              chartData = this.generateBarChartData();
              break;
            case 'pie':
              chartData = this.generatePieChartData();
              break;
            case 'ring':
              chartData = this.generateRingChartData();
              break;
            case 'radar':
              chartData = this.generateRadarChartData();
              break;
            case 'gauge':
              chartData = this.generateGaugeChartData();
              break;
            case 'funnel':
              chartData = this.generateFunnelChartData();
              break;
            case 'mix':
              chartData = this.generateMixChartData();
              break;
            case 'word':
              chartData = this.generateWordChartData();
              break;
            case 'map':
              chartData = this.generateMapChartData();
              break;
            default:
              // 如果是其他图表类型，使用模拟数据
              chartData = this.generateMockChartData(type);
          }
        } else {
          // 如果没有用户统计数据，使用模拟数据
          chartData = this.generateMockChartData(type);
        }

        this.setData({
          chartData,
          showLoading: false
        });

        wx.showToast({
          title: '数据已更新',
          icon: 'success',
          duration: 1000
        });
      }, 800);
    },
    
    // 生成柱状图数据
    generateColumnChartData() {
      const stats = this.data.userStats;
      return {
        categories: ["总作品", "已完成", "未完成", "进行中"],
        series: [
          {
            name: "作品数量",
            data: [
              stats.basic.total_works,
              stats.basic.completed_works,
              stats.basic.total_works - stats.basic.completed_works,
              stats.overview.active_projects
            ]
          }
        ]
      };
    },
    
    // 生成折线图数据
    generateLineChartData() {
      const stats = this.data.userStats;
      return {
        categories: ["总项目", "活跃项目", "已完成项目", "参与系列"],
        series: [
          {
            name: "项目趋势",
            data: [
              stats.overview.total_projects,
              stats.overview.active_projects,
              stats.overview.completed_projects,
              stats.series.participated
            ]
          }
        ]
      };
    },
    
    // 生成区域图数据
    generateAreaChartData() {
      const stats = this.data.userStats;
      return {
        categories: ["总提交", "草稿", "已提交", "已审核", "已完成"],
        series: [
          {
            name: "提交状态",
            data: [
              stats.submission.total,
              stats.submission.draft,
              stats.submission.submitted,
              stats.submission.reviewed,
              stats.submission.completed
            ]
          }
        ]
      };
    },
    
    // 生成山峰图数据
    generateMountChartData() {
      const stats = this.data.userStats;
      return {
        categories: ["总项目", "活跃项目", "完成项目", "参与系列", "参与画廊"],
        series: [
          {
            name: "项目统计",
            data: [
              { name: "总项目", value: stats.overview.total_projects },
              { name: "活跃项目", value: stats.overview.active_projects },
              { name: "完成项目", value: stats.overview.completed_projects },
              { name: "参与系列", value: stats.series.participated },
              { name: "参与画廊", value: stats.galleries.participated }
            ],
            formatter: (val) => val
          }
        ]
      };
    },
    
    // 生成条状图数据
    generateBarChartData() {
      const stats = this.data.userStats;
      return {
        categories: ["招募中", "已批准", "已拒绝", "已取消"],
        series: [
          {
            name: "招募状态",
            data: [
              stats.recruitment.pending,
              stats.recruitment.approved,
              stats.recruitment.rejected,
              stats.recruitment.cancelled
            ]
          }
        ]
      };
    },
    
    // 生成饼图数据
    generatePieChartData() {
      const stats = this.data.userStats;
      const selection = stats.selection;
      
      return {
        series: [
          {
            name: "遴选状态",
            data: [
              { name: "待定", value: selection.pending },
              { name: "已确认", value: selection.confirmed },
              { name: "已取消", value: selection.cancelled }
            ],
            formatter: (val) => val
          }
        ]
      };
    },
    
    // 生成圆环图数据
    generateRingChartData() {
      const stats = this.data.userStats;
      const submission = stats.submission;
      const submitted = parseInt(submission.submitted_cards);
      const remaining = parseInt(submission.total_cards) - submitted;
      
      return {
        series: [
          {
            name: "卡片完成情况",
            data: [
              { name: "已提交", value: submitted },
              { name: "未提交", value: remaining }
            ],
            formatter: (val) => val
          }
        ]
      };
    },
    
    // 生成雷达图数据
    generateRadarChartData() {
      const stats = this.data.userStats;
      return {
        categories: [
          "作品完成率", "招募成功率", "遴选确认率", "卡片完成率", "项目活跃度", "系列参与度"
        ],
        series: [
          {
            name: "用户能力指标",
            data: [
              stats.basic.completion_rate,
              stats.recruitment.approved / (stats.recruitment.total || 1) * 100,
              stats.selection.confirmed / (stats.selection.total || 1) * 100,
              stats.submission.completion_rate,
              stats.overview.active_projects / (stats.overview.total_projects || 1) * 100,
              stats.series.participated > 0 ? 100 : 0
            ]
          }
        ]
      };
    },
    
    // 生成仪表盘数据
    generateGaugeChartData() {
      const stats = this.data.userStats;
      const completionRate = stats.basic.completion_rate / 100; // 转换为0-1的小数

      return {
        series: [
          {
            name: "作品完成率",
            data: completionRate,
            formatter: (val) => (val * 100).toFixed(0) + '%'
          }
        ],
        categories: [
          {
            value: 0.4,
            color: "#f04864"
          },
          {
            value: 0.8,
            color: "#facc14"
          },
          {
            value: 1,
            color: "#2fc25b" 
          }
        ]
      };
    },
    
    // 生成漏斗图数据
    generateFunnelChartData() {
      const stats = this.data.userStats;
      const submission = stats.submission;
      
      return {
        series: [
          {
            name: "提交流程",
            data: [
              { name: "总数", value: submission.total },
              { name: "草稿", value: submission.draft },
              { name: "已提交", value: submission.submitted },
              { name: "已审核", value: submission.reviewed },
              { name: "已完成", value: submission.completed }
            ],
            formatter: (val) => val
          }
        ]
      };
    },
    
    // 生成混合图数据
    generateMixChartData() {
      const stats = this.data.userStats;
      
      return {
        categories: ["总作品", "已完成", "活跃项目", "参与系列"],
        series: [
          {
            name: "数量",
            type: "column",
            data: [
              stats.basic.total_works,
              stats.basic.completed_works,
              stats.overview.active_projects,
              stats.series.participated
            ]
          },
          {
            name: "完成率",
            type: "line",
            data: [
              100,
              stats.basic.completion_rate,
              stats.overview.completed_projects / (stats.overview.total_projects || 1) * 100,
              stats.submission.completion_rate
            ]
          }
        ]
      };
    },
    
    // 生成词云图数据
    generateWordChartData() {
      const stats = this.data.userStats;
      const wordData = [];
      
      // 添加系列名称数据
      if (stats.series && stats.series.series_names && stats.series.series_names.length > 0) {
        stats.series.series_names.forEach((name, index) => {
          wordData.push({
            name: name,
            value: 85 - (index * 5), // 给不同的系列名称分配不同的权重
            textSize: 20 - index,  // 添加textSize属性
            color: null            // 允许自动颜色
          });
        });
      }
      
      // 添加基本统计数据
      wordData.push({ name: "作品完成率", value: stats.basic.completion_rate, textSize: 18, color: null });
      wordData.push({ name: "总作品数", value: stats.basic.total_works, textSize: 16, color: null });
      wordData.push({ name: "已完成作品", value: stats.basic.completed_works, textSize: 16, color: null });
      
      // 添加项目数据
      wordData.push({ name: "总项目", value: stats.overview.total_projects * 10, textSize: 16, color: null });
      wordData.push({ name: "活跃项目", value: stats.overview.active_projects * 10, textSize: 17, color: null });
      wordData.push({ name: "已完成项目", value: stats.overview.completed_projects * 10, textSize: 15, color: null });
      
      // 添加提交相关数据
      wordData.push({ name: "卡片完成率", value: stats.submission.completion_rate, textSize: 16, color: null });
      wordData.push({ name: "总卡片", value: parseInt(stats.submission.total_cards), textSize: 14, color: null });
      wordData.push({ name: "已提交卡片", value: parseInt(stats.submission.submitted_cards), textSize: 14, color: null });
      
      // 添加一些固定的关键词
      wordData.push({ name: "艺术创作", value: 75, textSize: 18, color: null });
      wordData.push({ name: "作品集", value: 65, textSize: 17, color: null });
      wordData.push({ name: "插画", value: 60, textSize: 16, color: null });
      wordData.push({ name: "创意", value: 55, textSize: 15, color: null });
      wordData.push({ name: "设计", value: 50, textSize: 15, color: null });
      
      // 添加用户昵称
      if(stats.user_info && stats.user_info.nickname) {
        wordData.push({ name: stats.user_info.nickname, value: 90, textSize: 22, color: null });
      }
      
      return {
        series: wordData
      };
    },
    
    // 生成模拟数据（用于不基于用户统计的图表类型）
    generateMockChartData(type) {
      switch(type) {
        case 'column':
        case 'line':
        case 'area':
          return {
              categories: ["2016", "2017", "2018", "2019", "2020", "2021"],
              series: [
                {
                  name: "销售额",
                  data: [35, 20, 25, 37, 45, 56]
                },
                {
                  name: "增长率",
                  data: [15, 25, 18, 34, 29, 42]
                }
              ]
            };
        case 'mount':
          return {
            categories: ["2016", "2017", "2018", "2019", "2020", "2021"],
            series: [
              {
                name: "销售额",
                data: [
                  { name: "2016", value: 35 },
                  { name: "2017", value: 20 },
                  { name: "2018", value: 25 },
                  { name: "2019", value: 37 },
                  { name: "2020", value: 45 },
                  { name: "2021", value: 56 }
                ],
                formatter: (val) => val
              },
              {
                name: "增长率",
                data: [
                  { name: "2016", value: 15 },
                  { name: "2017", value: 25 },
                  { name: "2018", value: 18 },
                  { name: "2019", value: 34 },
                  { name: "2020", value: 29 },
                  { name: "2021", value: 42 }
                ],
                formatter: (val) => val
              }
            ]
          };
          case 'bar':
          return {
              categories: ["北京", "上海", "广州", "深圳", "杭州", "南京"],
              series: [
                {
                  name: "去年",
                  data: [35, 20, 25, 37, 45, 56]
                },
                {
                  name: "今年",
                  data: [28, 40, 32, 45, 49, 60]
                }
              ]
            };
          case 'pie':
          case 'ring':
          case 'rose':
          return {
              series: [
                {
                  name: "区域分布",
                  data: [
                    { name: "北京", value: Math.floor(Math.random() * 30) + 10 },
                    { name: "上海", value: Math.floor(Math.random() * 30) + 10 },
                    { name: "广州", value: Math.floor(Math.random() * 30) + 10 },
                    { name: "深圳", value: Math.floor(Math.random() * 30) + 10 },
                    { name: "杭州", value: Math.floor(Math.random() * 30) + 10 },
                    { name: "南京", value: Math.floor(Math.random() * 30) + 10 }
                  ],
                  formatter: (val) => val + '%'
                }
              ]
            };
          case 'radar':
          return {
              categories: [
                "销售", "管理", "技术", "客服", "研发", "市场"
              ],
              series: [
                {
                  name: "预算分配",
                  data: [85, 65, 80, 70, 90, 75]
                },
                {
                  name: "实际开销",
                  data: [72, 80, 72, 65, 78, 90]
                }
              ]
            };
          case 'gauge':
            const randomValue = Math.floor(Math.random() * 60) + 40;
            const formattedValue = randomValue / 100; // 转换为0-1的小数

            // 对于仪表盘，首先设置series
          return {
              series: [
                {
                  name: "完成率",
                  data: formattedValue,
                  formatter: (val) => val * 100 + '%'
                }
              ]
            };
          case 'funnel':
          return {
              series: [
                {
                  name: "转化率",
                  data: [
                    { name: "访问", value: Math.floor(Math.random() * 30) + 90 },
                    { name: "注册", value: Math.floor(Math.random() * 30) + 60 },
                    { name: "加入购物车", value: Math.floor(Math.random() * 20) + 40 },
                    { name: "支付", value: Math.floor(Math.random() * 20) + 20 },
                    { name: "成交", value: Math.floor(Math.random() * 15) + 15 }
                  ],
                  formatter: (val) => val
                }
              ]
            };
          case 'scatter':
            const generateScatterData = (count) => {
              return Array(count).fill(0).map(() => {
                return {
                  // 随机生成 x, y 坐标
                  x: Math.floor(Math.random() * 100),
                  y: Math.floor(Math.random() * 100)
                };
              });
            };
            
          return {
              series: [
                {
                  name: "数据集A",
                  data: generateScatterData(20)
                },
                {
                  name: "数据集B",
                  data: generateScatterData(20)
                }
              ]
            };
          case 'bubble':
            const generateBubbleData = (count) => {
              return Array(count).fill(0).map(() => {
                return {
                  // 随机生成 x, y, r(气泡大小)
                  x: Math.floor(Math.random() * 100),
                  y: Math.floor(Math.random() * 100),
                  r: Math.floor(Math.random() * 20) + 5
                };
              });
            };
            
          return {
              series: [
                {
                  name: "数据集A",
                  data: generateBubbleData(15)
                },
                {
                  name: "数据集B",
                  data: generateBubbleData(15)
                }
              ]
            };
          case 'candle':
            // K线图数据格式: [开盘价, 收盘价, 最低价, 最高价]
          return {
              categories: ['2020/1', '2020/2', '2020/3', '2020/4', '2020/5', '2020/6', '2020/7', '2020/8', '2020/9', '2020/10'],
              series: [
                {
                  name: "股票价格",
                  data: [
                    [2320, 2400, 2290, 2450],
                    [2300, 2290, 2270, 2350],
                    [2295, 2350, 2260, 2380],
                    [2370, 2380, 2350, 2450],
                    [2380, 2170, 2150, 2400],
                    [2160, 2190, 2150, 2230],
                    [2190, 2320, 2180, 2330],
                    [2320, 2400, 2290, 2450],
                    [2400, 2440, 2390, 2500],
                    [2450, 2420, 2380, 2490]
                  ]
                }
              ]
            };
          case 'mix':
          return {
              categories: ["2016", "2017", "2018", "2019", "2020", "2021"],
              series: [
                {
                  name: "销售额",
                  type: "column",
                  data: [35, 20, 25, 37, 45, 56]
                },
                {
                  name: "增长率",
                  type: "line",
                  data: [15, 25, 18, 34, 29, 42]
                }
              ]
            };
          case 'word':
          return {
              series: [
                {
                  name: "词云",
                  data: [
                    { name: "微信小程序", value: 80 },
                    { name: "uCharts", value: 65 },
                    { name: "前端开发", value: 60 },
                    { name: "可视化", value: 55 },
                    { name: "JavaScript", value: 50 },
                    { name: "CSS", value: 45 },
                    { name: "HTML", value: 40 },
                    { name: "数据分析", value: 35 },
                    { name: "图表", value: 30 },
                    { name: "云开发", value: 25 },
                    { name: "小程序开发", value: 20 },
                    { name: "移动端", value: 15 }
                  ],
                  formatter: (val) => val
                }
              ]
            };
          case 'map':
          // 临时解决方案：使用简化的地图数据
          // 注意：实际应用中应加载完整的GeoJSON数据
          const mapData = [
            {
              "name": "北京",
              "value": 30,
              "color": "#1890FF",
              "fillOpacity": 0.6,
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[116.2, 40.1], [116.8, 40.1], [116.8, 39.6], [116.2, 39.6], [116.2, 40.1]]]
              },
              "properties": {
                "name": "北京",
                "centroid": [116.4, 39.9]
              }
            },
            {
              "name": "上海",
              "value": 25,
              "color": "#91CB74",
              "fillOpacity": 0.6,
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[121.2, 31.4], [121.8, 31.4], [121.8, 30.9], [121.2, 30.9], [121.2, 31.4]]]
              },
              "properties": {
                "name": "上海",
                "centroid": [121.5, 31.2]
              }
            },
            {
              "name": "广州",
              "value": 20,
              "color": "#FAC858",
              "fillOpacity": 0.6,
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[113.1, 23.4], [113.6, 23.4], [113.6, 22.9], [113.1, 22.9], [113.1, 23.4]]]
              },
              "properties": {
                "name": "广州",
                "centroid": [113.3, 23.1]
              }
            },
            {
              "name": "深圳",
              "value": 15,
              "color": "#EE6666",
              "fillOpacity": 0.6,
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[113.9, 22.7], [114.4, 22.7], [114.4, 22.2], [113.9, 22.2], [113.9, 22.7]]]
              },
              "properties": {
                "name": "深圳",
                "centroid": [114.1, 22.5]
              }
            },
            {
              "name": "杭州",
              "value": 10,
              "color": "#73C0DE",
              "fillOpacity": 0.6,
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[119.9, 30.4], [120.4, 30.4], [120.4, 29.9], [119.9, 29.9], [119.9, 30.4]]]
              },
              "properties": {
                "name": "杭州",
                "centroid": [120.2, 30.2]
              }
            }
          ];
          return mapData;
          case 'arcbar':
          return {
              series: [
                {
                  name: "进度",
                  data: 0.75,
                  formatter: (val) => val * 100 + '%'
                }
              ]
            };
          case 'tline':
          case 'tarea':
            const currentTime = new Date();
            const timePoints = [];
            const randomData = [];
            for (let i = 0; i < 12; i++) {
              const time = new Date(currentTime.getTime() - (11 - i) * 3600 * 1000);
              timePoints.push(time.getTime() / 1000);
              randomData.push(Math.floor(Math.random() * 50) + 10);
            }
            
          return {
              categories: timePoints,
              series: [
                {
                  name: "时间数据",
                  data: randomData
                }
              ]
            };
      }
    },

    // 图表点击事件
    _getIndex(e) {
      console.log('图表点击事件:', e.detail);
      const { currentIndex, legendIndex, seriesIndex, categoryIndex } = e.detail;
      
      if (currentIndex > -1) {
        const chartType = this.data.currentChart;
        
        // 根据不同图表类型处理点击事件
        if (['pie', 'ring', 'rose', 'funnel'].includes(chartType)) {
          const item = this.data.chartData.series[0].data[currentIndex];
          wx.showToast({
            title: `${item.name}: ${item.value}`,
            icon: 'none',
            duration: 2000
          });
        } else if (['gauge', 'arcbar'].includes(chartType)) {
          const value = this.data.chartData.series[0].data * 100;
          wx.showToast({
            title: `${chartType === 'gauge' ? '完成率' : '进度'}: ${value.toFixed(0)}%`,
            icon: 'none',
            duration: 2000
          });
        } else if (['column', 'line', 'area', 'radar', 'bar', 'mix', 'tline', 'tarea'].includes(chartType)) {
          if (seriesIndex !== undefined && categoryIndex !== undefined) {
            const series = this.data.chartData.series[seriesIndex];
            let category = this.data.chartData.categories[categoryIndex];
            const value = series.data[categoryIndex];
            let seriesName = series.name || '';
            let seriesType = series.type ? `(${series.type})` : '';
            
            // 对时间轴类型的图表进行特殊处理
            if (['tline', 'tarea'].includes(chartType) && typeof category === 'number') {
              category = new Date(category * 1000).toLocaleTimeString();
            }
            
            wx.showToast({
              title: `${category}: ${seriesName}${seriesType} - ${value}`,
              icon: 'none',
              duration: 2000
            });
          }
        } else if (chartType === 'scatter') {
          if (seriesIndex !== undefined && currentIndex !== undefined) {
            const series = this.data.chartData.series[seriesIndex];
            const point = series.data[currentIndex];
            
            wx.showToast({
              title: `${series.name}: (${point.x}, ${point.y})`,
              icon: 'none',
              duration: 2000
            });
          }
        } else if (chartType === 'bubble') {
          if (seriesIndex !== undefined && currentIndex !== undefined) {
            const series = this.data.chartData.series[seriesIndex];
            const point = series.data[currentIndex];
            
            wx.showToast({
              title: `${series.name}: (${point.x}, ${point.y}), 大小: ${point.r}`,
              icon: 'none',
              duration: 2000
            });
          }
        } else if (chartType === 'candle') {
          if (seriesIndex !== undefined && categoryIndex !== undefined) {
            const series = this.data.chartData.series[seriesIndex];
            const category = this.data.chartData.categories[categoryIndex];
            const candleData = series.data[categoryIndex];
            
            wx.showToast({
              title: `${category}: 开盘${candleData[0]}, 收盘${candleData[1]}, 最低${candleData[2]}, 最高${candleData[3]}`,
              icon: 'none',
              duration: 2000
            });
          }
        } else if (chartType === 'word') {
          if (currentIndex !== undefined) {
            const item = this.data.chartData.series[0].data[currentIndex];
            wx.showToast({
              title: `${item.name}: ${item.value}`,
              icon: 'none',
              duration: 2000
            });
          }
        } else if (chartType === 'map') {
          if (currentIndex !== undefined) {
            // 对于地图图表，数据格式不同，直接访问地图数据项
            const mapData = this.data.chartData[currentIndex];
            if (mapData && mapData.properties) {
            wx.showToast({
                title: `${mapData.properties.name}`,
                icon: 'none',
                duration: 2000
              });
            }
          }
        } else if (chartType === 'mount') {
          if (seriesIndex !== undefined && currentIndex !== undefined) {
            const series = this.data.chartData.series[seriesIndex];
            const point = series.data[currentIndex];
            
            wx.showToast({
              title: `${point.name}: ${series.name} - ${point.value}`,
              icon: 'none',
              duration: 2000
            });
          }
        }
      }
    },

    // 错误处理
    _error(e) {
      console.error('图表加载错误:', e.detail);
      this.setData({
        showLoading: false
      });
    },

    // 图表加载完成
    _complete(e) {
      console.log('图表加载完成', e.detail);
    },

    // 更新图表数据
    updateChart() {
      // 根据当前图表类型更新数据
      this.generateChartData(this.data.currentChart);
    },

    // 生成地图图表数据
    generateMapChartData() {
      const stats = this.data.userStats;
      
      // 创建基于用户统计数据的简化地图
      const cities = [
        // 这里使用固定的城市位置，但是值根据用户统计信息变化
        {
          name: "北京",
          value: stats.basic.total_works,
          color: "#1890FF",
          fillOpacity: stats.basic.completion_rate / 100,
          geometry: {
            type: "Polygon",
            coordinates: [[[116.2, 40.1], [116.8, 40.1], [116.8, 39.6], [116.2, 39.6], [116.2, 40.1]]]
          },
          properties: {
            name: "总作品: " + stats.basic.total_works,
            centroid: [116.4, 39.9]
          }
        },
        {
          name: "上海",
          value: stats.basic.completed_works,
          color: "#91CB74",
          fillOpacity: stats.basic.completion_rate / 100,
          geometry: {
            type: "Polygon",
            coordinates: [[[121.2, 31.4], [121.8, 31.4], [121.8, 30.9], [121.2, 30.9], [121.2, 31.4]]]
          },
          properties: {
            name: "已完成: " + stats.basic.completed_works,
            centroid: [121.5, 31.2]
          }
        },
        {
          name: "广州",
          value: stats.overview.total_projects,
          color: "#FAC858",
          fillOpacity: 0.6,
          geometry: {
            type: "Polygon",
            coordinates: [[[113.1, 23.4], [113.6, 23.4], [113.6, 22.9], [113.1, 22.9], [113.1, 23.4]]]
          },
          properties: {
            name: "总项目: " + stats.overview.total_projects,
            centroid: [113.3, 23.1]
          }
        },
        {
          name: "深圳",
          value: stats.overview.active_projects,
          color: "#EE6666",
          fillOpacity: 0.6,
          geometry: {
            type: "Polygon",
            coordinates: [[[113.9, 22.7], [114.4, 22.7], [114.4, 22.2], [113.9, 22.2], [113.9, 22.7]]]
          },
          properties: {
            name: "活跃项目: " + stats.overview.active_projects,
            centroid: [114.1, 22.5]
          }
        },
        {
          name: "杭州",
          value: stats.overview.completed_projects,
          color: "#73C0DE",
          fillOpacity: 0.6,
          geometry: {
            type: "Polygon",
            coordinates: [[[119.9, 30.4], [120.4, 30.4], [120.4, 29.9], [119.9, 29.9], [119.9, 30.4]]]
          },
          properties: {
            name: "完成项目: " + stats.overview.completed_projects,
            centroid: [120.2, 30.2]
          }
        }
      ];
      
      return cities;
    }
  }
}); 
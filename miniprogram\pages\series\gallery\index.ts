// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api, {} from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
import pageHelper from '../../../utils/pageHelper';
import { DOMAIN } from '../../../utils/constants';

Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    pagetitle: '参考图库',
    baseUrl: DOMAIN, // 使用常量作为图片前缀
    selectedCount: 0,
    loading: true,
    showArtistsPopup: false, // 画师列表弹窗状态
    showImageArtistsPopup: false, // 单张图片的画师列表弹窗状态
    currentViewingImage: null, // 当前查看的图片
    savedSelectionState: null, // 存储选择状态
    isHeaderExpanded: false, // 控制头部区域是否展开
    tabBarHeight:25,
    // 添加登录相关状态
    isLogin: false,
    loginChecked: false,
    loginModalVisible: false,
    // 添加是否有画师选择的状态
    hasNoArtistSelections: true
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this)/* 底部导航栏折叠 */);
      eventBus.on('pageInfoUpdate', this.handlePageInfo.bind(this));
      
      // 添加登录相关事件监听
      eventBus.on('loginModalEvent', this.handleLoginModalEvent.bind(this));
      eventBus.on('loginSuccess', this.handleLoginSuccess.bind(this));
      eventBus.on('loginStatusUpdate', this.handleLoginStatusUpdate.bind(this));
      
      // 注册页面登录功能
      eventBus.emit('registerLoginPage', {
        source: 'gallery'
      });
      
      // 确保已初始化选择计数
      if (this.data.selectedCount === undefined) {
        this.setData({
          selectedCount: 0
        });
      }

      // 监听页面显示事件，用于检测预览结束
      const pageInstance = this.getPageInstance();
      if (pageInstance) {
        pageInstance.onShow = () => {
          this.checkRestoreSelectionState();
        };
      }
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this)/* 底部导航栏折叠解除 */);
      eventBus.off('pageInfoUpdate', this.handlePageInfo);
      
      // 移除登录相关事件监听
      eventBus.off('loginModalEvent', this.handleLoginModalEvent.bind(this));
      eventBus.off('loginSuccess', this.handleLoginSuccess.bind(this));
      eventBus.off('loginStatusUpdate', this.handleLoginStatusUpdate.bind(this));
    }  
  },

  methods: {
    handleTabBarChange(data: { isCollapsed: any; currentHeight: any; }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    async handlePageInfo(data: {path: string, params: Record<string, any>}) {
      // 直接使用传递的参数
      const params = data.params;
      console.log('传递的参数', params);
      
      if (!params || !params.gallery_id) {
        console.warn('缺少必要的参数gallery_id');
        return;
      }

      try {
        
        // 获取响应数据
        const response = await Api.gallery.galleryimageslist(params.gallery_id);
        console.log('获取响应数据', response);
        console.log('获取选择列表', response.images.selections);
        
        // 检查选择列表中是否包含当前用户的选择
        const userSelectionInfo = this.checkUserInSelections(response.images && response.images.selections ? response.images.selections : []);
        console.log('当前用户选择信息', userSelectionInfo);
        
        // 处理API返回格式 - 直接使用返回的数据
        let galleryData = response;
        
        // 确保galleryData是一个有效对象
        if (!galleryData || typeof galleryData !== 'object') {
          console.error('无效的图库数据:', galleryData);
          wx.showToast({
            title: '获取图库数据失败',
            icon: 'none'
          });
          return;
        }
        
        // 确保gallery对象存在
        if (!galleryData.gallery) {
          galleryData.gallery = {
            selection_mode: "0",
            show_selected_artist: "1",
            max_selections: 3,
            min_selections: 1
          };
          console.warn('画廊配置信息缺失，使用默认配置');
        }
        
        // 确保images和list属性存在
        if (!galleryData.images) {
          galleryData.images = { list: [], selections: [] };
          console.warn('图片列表缺失，初始化为空数组');
        }
        
        if (!galleryData.images.list) {
          galleryData.images.list = [];
          console.warn('图片列表缺失，初始化为空数组');
        }

        if (!galleryData.images.selections) {
          galleryData.images.selections = [];
          console.warn('选择列表缺失，初始化为空数组');
        }
        
        // 判断用户是否已提交选择（不可修改）
        let userHasSubmitted = false;
        if (userSelectionInfo.isCurrentUser && userSelectionInfo.hasSubmitted) {
          // 用户已经提交过选择，设置提交状态标记
          userHasSubmitted = true;
          console.log('用户已提交过选择，不可修改');
        }
        
        // 保存用户选择信息到数据中，方便后续使用
        galleryData.userSelectionInfo = userSelectionInfo;
        galleryData.userHasSubmitted = userHasSubmitted;
        
        // 处理图片数据，添加选择状态属性
        if (Array.isArray(galleryData.images.list)) {
          // 处理图片列表，为每个图片添加选择状态属性
          galleryData.images.list = galleryData.images.list.map(item => {
            if (!item) return null; // 跳过无效项
            
            // 确保选择信息初始化为空数组
            if (!item.selections) {
              item.selections = [];
            }
            
            // 初始状态设为未选择
            let selected = false;
            let selectionIndex = 0;
            let isUserSelected = false;
            
            // 标记其他用户选择的图片
            const otherUsersSelections = [];
            
            // 处理选择列表中的图片
            if (galleryData.images.selections && Array.isArray(galleryData.images.selections)) {
              const userInfo = wx.getStorageSync('userInfo');
              const userId = userInfo && userInfo.id;
              
              // 根据是否允许查看画师信息来收集其他用户的选择信息
              const showArtistInfo = galleryData.gallery.show_selected_artist == "1";
              
              // 收集选择了当前图片的用户
              galleryData.images.selections.forEach(sel => {
                // 检查当前图片是否在该用户的选择列表中
                if (sel.image_ids && sel.image_ids.includes(item.id.toString())) {
                  // 检查是不是当前用户
                  if (!userId || sel.artist_id != userId) {
                    // 其他用户选择了这张图片
                    if (showArtistInfo) {
                      otherUsersSelections.push(sel);
                    } else {
                      // 如果不允许查看画师信息，使用匿名信息
                      otherUsersSelections.push({
                        artist_id: sel.artist_id,
                        artist_name: '匿名画师', // 隐藏真实姓名
                        selection_time: '已选择',  // 隐藏选择时间
                        image_ids: sel.image_ids
                      });
                    }
                  } else {
                    // 当前用户选择了这张图片
                    selected = true;
                    isUserSelected = true;
                    // 找出在用户选择列表中的位置作为索引
                    const imageIdStr = item.id.toString();
                    selectionIndex = sel.image_ids.indexOf(imageIdStr) + 1;
                  }
                }
              });
            }
            
            return {
              ...item,
              selected: selected,
              selectionIndex: selectionIndex,
              isUserSelected: isUserSelected, // 标记为用户选择
              otherUsersSelections: otherUsersSelections, // 添加其他用户选择信息
              hasOtherSelections: otherUsersSelections.length > 0 // 快速判断是否被其他用户选择
            };
          }).filter(item => item !== null); // 过滤掉无效项
        } else {
          console.error('图片列表格式错误:', galleryData.images.list);
          galleryData.images.list = [];
        }
        
        // 更新视图数据
        this.setData({ 
          galleryData: galleryData,
          loading: false,
          // 根据用户选择信息设置已选择数量
          selectedCount: userSelectionInfo.isCurrentUser && userSelectionInfo.selectionInfo && 
                         userSelectionInfo.selectionInfo.image_ids ? 
                         userSelectionInfo.selectionInfo.image_ids.length : 0,
          // 设置用户是否已提交，不可修改
          userHasSubmitted: userHasSubmitted,
          // 更新是否有画师选择的状态
          hasNoArtistSelections: !galleryData.images.list.some(item => {
            return item.hasOtherSelections || 
                   (galleryData.gallery.selection_mode != 0 && 
                    item.selections && 
                    item.selections.length > 0);
          })
        });
        
        // 更新标题
        if (galleryData && galleryData.gallery && galleryData.gallery.name) {
          await pageHelper.updatePageTitle(this, galleryData.gallery.name);
        }
        
        // 禁用系统截屏功能
        this.disableScreenCapture();
        
        // 数据加载完成后，先检查登录状态
        this.checkLoginStatus().then(isLoggedIn => {
          if (isLoggedIn) {
            // 如果用户已登录，但在选择列表中检测不到选择信息，再尝试获取用户选择状态
            if (!this.data.galleryData.userSelectionInfo || 
                !this.data.galleryData.userSelectionInfo.isCurrentUser) {
              
              // 先尝试从本地存储恢复选择状态
              const restoredFromStorage = this.restoreSelectionFromStorage();
              
              // 如果本地存储没有有效的选择状态，则尝试从服务器获取用户选择状态
              if (!restoredFromStorage) {
                this.getUserGallerySelection(params.gallery_id);
              }
            } else {
              console.log('已从选择列表获取到用户选择信息，无需额外查询');
            }
          }
        });
        
      } catch (error) {
        console.error('页面信息更新失败:', error);
        wx.showToast({
          title: '页面信息更新失败',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 添加检查当前用户是否在选择列表中的方法
    checkUserInSelections(selections) {
      try {
        if (!selections || !Array.isArray(selections) || selections.length === 0) {
          console.log('没有选择列表数据');
          return { isCurrentUser: false };
        }
        
        // 获取当前用户信息
        const userInfo = wx.getStorageSync('userInfo');
        if (!userInfo || !userInfo.id) {
          console.log('未获取到当前用户信息');
          return { isCurrentUser: false };
        }
        
        // 在选择列表中查找当前用户
        const currentUserSelection = selections.find(item => item.artist_id == userInfo.id);
        
        if (currentUserSelection) {
          console.log('找到当前用户的选择', currentUserSelection);
          return {
            isCurrentUser: true,
            selectionInfo: currentUserSelection,
            // 明确标记用户已提交过选择
            hasSubmitted: true
          };
        } else {
          console.log('当前用户未在选择列表中');
          return { isCurrentUser: false };
        }
      } catch (error) {
        console.error('检查用户选择时出错:', error);
        return { isCurrentUser: false };
      }
    },

    // 禁用截屏方法
    disableScreenCapture() {
      // 使用wx.any类型规避TypeScript类型检查
      const wxAny: any = wx;
      if (wxAny.setVisualEffectOnCapture) {
        wxAny.setVisualEffectOnCapture({
          visualEffect: 'hidden',
          success: () => {
            console.log('截屏保护已启用');
          },
          fail: (err) => {
            console.error('截屏保护启用失败:', err);
          }
        });
      } else {
        console.warn('当前环境不支持截屏保护功能');
      }
    },

    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 图片选择处理
    onImageTap(e) {
      try {
        
        // 确保从事件对象中获取index - 不使用可选链
        const index = e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.index;
        
        // 严格检查index值
        if (index === undefined || index === null) {
          console.error('无效的图片索引: undefined');
          return;
        }
        
        // 转换为数字类型
        const indexNum = Number(index);
        
        if (isNaN(indexNum)) {
          console.error('图片索引不是有效数字:', index);
          return;
        }
        
        // 确保galleryData存在且images.list是有效数组
        if (!this.data.galleryData || !this.data.galleryData.images || !Array.isArray(this.data.galleryData.images.list)) {
          console.error('画廊数据不完整或格式错误');
          return;
        }
        
        const imagesList = this.data.galleryData.images.list;
        
        // 检查索引是否在有效范围内
        if (indexNum < 0 || indexNum >= imagesList.length) {
          console.error('图片索引超出范围:', indexNum, '列表长度:', imagesList.length);
          return;
        }
        
        const image = imagesList[indexNum];
        
        // 检查image对象是否有效
        if (!image) {
          console.error('图片数据无效, 索引:', indexNum);
          return;
        }
        
        // 检查是否已被他人选择
        if (this.data.galleryData.gallery && this.data.galleryData.gallery.selection_mode == "0" && image.hasOtherSelections) {
          // 检查是否允许查看画师信息
          if (this.data.galleryData.gallery.show_selected_artist == "1") {
            // 如果允许查看画师信息，显示已选择该图片的画师列表
            this.showImageArtistsList(image);
            return;
          } else {
            // 如果不允许查看画师信息，只显示普通提示
            wx.showToast({
              title: '该图片已被他人选择',
              icon: 'none'
            });
            return;
          }
        }
        
        // 保存当前的选择状态到本地变量
        const currentSelections = imagesList.map(img => ({
          id: img.id,
          selected: img.selected,
          selectionIndex: img.selectionIndex
        }));
        
        
        // 根据selection_mode过滤预览的图片列表
        let previewUrls = [];
        let currentUrl = this.data.baseUrl + image.url;
        
        if (this.data.galleryData.gallery.selection_mode == 0) {
          // 禁止重复选择模式：只显示未被他人选择的图片
          previewUrls = imagesList
            .filter(img => !img.hasOtherSelections)
            .map(img => this.data.baseUrl + img.url);
          
          // 检查当前图片是否在预览列表中
          if (!previewUrls.includes(currentUrl)) {
            // 如果不在预览列表中（理论上不应该发生，因为前面已经检查过），使用第一张可预览的图片
            if (previewUrls.length > 0) {
              currentUrl = previewUrls[0];
            } else {
              // 如果没有可预览的图片，则不执行预览
              wx.showToast({
                title: '没有可预览的图片',
                icon: 'none'
              });
              return;
            }
          }
        } else {
          // 允许重复选择模式：显示所有图片
          previewUrls = imagesList.map(img => this.data.baseUrl + img.url);
        }
        
        
        // 执行预览
        if (previewUrls.length > 0) {
          wx.previewImage({
            current: currentUrl,
            urls: previewUrls,
            showmenu: false, // 尝试禁用菜单，但此参数可能不被所有版本支持
            complete: () => {
              // 预览完成后恢复选择状态
              this.restoreSelectionState(currentSelections);
            }
          });
        } else {
          wx.showToast({
            title: '没有可预览的图片',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('图片点击处理发生错误:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 显示单张图片的画师列表
    showImageArtistsList(e) {
      try {
        // 处理两种调用方式：直接传入图片对象或从事件中获取索引
        let image;
        
        if (e && e.currentTarget && e.currentTarget.dataset) {
          // 从事件中获取索引
          const index = e.currentTarget.dataset.index;
          if (index !== undefined && this.data.galleryData && this.data.galleryData.images && this.data.galleryData.images.list) {
            image = this.data.galleryData.images.list[index];
          }
        } else {
          // 直接传入图片对象
          image = e;
        }
        
        console.log('准备显示画师列表，图片数据:', image);
        
        // 检查图片是否有效
        if (!image) {
          console.error('图片对象无效');
          wx.showToast({
            title: '无法显示画师信息',
            icon: 'none'
          });
          return;
        }
        
        // 检查是否有其他用户选择信息
        if (!image.otherUsersSelections || image.otherUsersSelections.length === 0) {
          console.log('该图片没有其他用户选择信息');
          // 不直接返回，仍然显示空的弹窗
        }
        
        // 设置当前查看的图片和其画师信息
        this.setData({
          currentViewingImage: {
            ...image,
            // 确保画师选择列表存在，即使为空
            artistSelections: image.otherUsersSelections ? image.otherUsersSelections.map(selection => {
              // 确保每个选择信息都有status属性
              return {
                ...selection,
                status: selection.status || 'pending' // 默认为pending状态
              };
            }) : []
          },
          showImageArtistsPopup: true
        });
        
        console.log('显示画师列表完成');
      } catch (error) {
        console.error('显示画师列表失败:', error);
        wx.showToast({
          title: '无法显示画师信息',
          icon: 'none'
        });
      }
    },
    
    // 关闭单张图片的画师列表弹窗
    hideImageArtistsPopup() {
      this.setData({
        showImageArtistsPopup: false
      });
    },
    
    // 恢复选择状态的方法，使其优先使用来自选择列表的信息
    restoreSelectionState(savedSelections) {
      try {
        // 如果在选择列表中已经有用户的选择信息，则优先使用
        if (this.data.galleryData && 
            this.data.galleryData.userSelectionInfo && 
            this.data.galleryData.userSelectionInfo.isCurrentUser) {
          console.log('使用来自选择列表的用户选择信息，忽略传入的选择状态');
          return;
        }
        
        if (!savedSelections || !Array.isArray(savedSelections) || savedSelections.length === 0) {
          console.log('没有保存的选择状态需要恢复');
          return;
        }
        
        const { galleryData } = this.data;
        
        if (!galleryData || !galleryData.images || !Array.isArray(galleryData.images.list)) {
          console.error('无法恢复选择状态：画廊数据不完整');
          return;
        }
        
        // 根据ID匹配恢复选择状态
        const updatedList = galleryData.images.list.map(image => {
          const savedState = savedSelections.find(s => s.id === image.id);
          
          if (savedState && savedState.selected) {
            return {
              ...image,
              selected: true,
              selectionIndex: savedState.selectionIndex
            };
          }
          
          return image;
        });
        
        // 计算已选择数量
        const selectedCount = updatedList.filter(item => item.selected).length;
        this.setData({
          'galleryData.images.list': updatedList,
          selectedCount
        });
      } catch (error) {
        console.error('恢复选择状态失败:', error);
      }
    },
    
    // 提交选择
    async submitSelection() {
      try {
        // 检查用户是否已提交过选择
        if (this.data.userHasSubmitted || this.data.galleryData.userHasSubmitted) {
          wx.showToast({
            title: '您已提交过选择，不能重复提交',
            icon: 'none'
          });
          
          // 已经提交过的，可以直接跳转到个人画廊页面
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/redirect/index?url=/pages/about/studio/mygallery/index'
            });
          }, 1500);
          
          return;
        }
        
        // 检查用户是否在选择列表中
        if (this.data.galleryData.userSelectionInfo && 
            this.data.galleryData.userSelectionInfo.isCurrentUser && 
            this.data.galleryData.userSelectionInfo.hasSubmitted) {
          wx.showToast({
            title: '您已在选择列表中，不能修改',
            icon: 'none'
          });
          
          // 已经提交过的，可以直接跳转到个人画廊页面
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/redirect/index?url=/pages/about/studio/mygallery/index'
            });
          }, 1500);
          
          return;
        }
        
        // 登录检查
        const isLoggedIn = await this.checkLoginStatus();
        if (!isLoggedIn) {
          return;
        }
        
        // 继续原有的提交选择逻辑
        // 确认选择的图片数量是否符合要求
        if (!this.data.galleryData || !this.data.galleryData.gallery) {
          wx.showToast({
            title: '画廊数据无效',
            icon: 'none'
          });
          return;
        }
        
        const minSelections = this.data.galleryData.gallery.min_selections || 1;
        const maxSelections = this.data.galleryData.gallery.max_selections || 1;
        
        if (this.data.selectedCount < minSelections) {
          wx.showToast({
            title: `至少需要选择${minSelections}张图片`,
            icon: 'none'
          });
          return;
        }
        
        if (this.data.selectedCount > maxSelections) {
          wx.showToast({
            title: `最多只能选择${maxSelections}张图片`,
            icon: 'none'
          });
          return;
        }
        
        this.confirmAndSubmitSelection();
      } catch (error) {
        console.error('提交选择错误:', error);
        wx.showToast({
          title: '提交选择失败',
          icon: 'none'
        });
      }
    },
    
    // 确认并提交选择（确认登录状态后调用）
    confirmAndSubmitSelection() {
      // 获取选中的图片ID数组
      const selectedImageIds = this.data.galleryData.images.list
        .filter(item => item.selected)
        .map(item => item.id)
        .sort((a, b) => {
          // 查找原始对象获取selectionIndex用于排序
          const itemA = this.data.galleryData.images.list.find(img => img.id === a);
          const itemB = this.data.galleryData.images.list.find(img => img.id === b);
          // 替换可选链为普通条件判断
          return ((itemA && itemA.selectionIndex) || 0) - ((itemB && itemB.selectionIndex) || 0);
        });
      
      // 显示确认对话框
      wx.showModal({
        title: '确认提交',
        content: `您已选择${this.data.selectedCount}张图片，确认提交吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用提交API
            wx.showLoading({
              title: '提交中...',
            });
            
            // 执行API调用
            this.doSubmitSelection(selectedImageIds);
          }
        }
      });
    },
    
    // 执行提交API调用
    async doSubmitSelection(selectedImageIds) {
      try {
        const result = await Api.gallery.saveSelection(this.data.galleryData.gallery.id,selectedImageIds);
        // 提交成功
        wx.hideLoading();
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        console.log('成功提交的图片ID:', selectedImageIds);
        
        // 清除本地存储中的选择状态
        if (this.data.galleryData && this.data.galleryData.gallery && this.data.galleryData.gallery.id) {
          const galleryId = this.data.galleryData.gallery.id;
          const storageKey = 'gallery_selection';
          try {
            wx.removeStorageSync(storageKey);
            console.log('已清除本地存储的选择状态');
          } catch (error) {
            console.error('清除本地存储失败:', error);
          }
        }
        
        // 提交成功后导航到个人画廊页面
        wx.navigateTo({
          url: '/pages/redirect/index?url=/pages/about/studio/mygallery/index'
        });
        console.log('转向:', '/pages/redirect/index?url=/pages/about/studio/mygallery/index');
        
      } catch (error) {
        // 提交失败处理
        wx.hideLoading();
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
        console.error('提交选择失败:', error);
      }
    },

    // 添加登录相关方法
    
    // 处理登录弹窗事件
    handleLoginModalEvent(data: {show: boolean, source?: string}) {
      this.setData({
        loginModalVisible: data.show
      });
    },
    
    // 处理登录成功事件
    handleLoginSuccess(data: {userInfo: any}) {
      this.setData({
        isLogin: true,
        userInfo: data.userInfo
      });
      
      // 登录成功后，尝试获取用户选择状态
      if (this.data.galleryData && this.data.galleryData.gallery) {
        const galleryId = this.data.galleryData.gallery.id;
        if (galleryId) {
          // 先尝试从本地存储恢复选择状态
          const restoredFromStorage = this.restoreSelectionFromStorage();
          
          // 如果本地存储没有有效的选择状态，则尝试从服务器获取用户选择状态
          if (!restoredFromStorage) {
            this.getUserGallerySelection(galleryId);
          }
        }
      }
    },
    
    // 处理登录状态更新事件
    handleLoginStatusUpdate(data: {isLogin: boolean, userInfo?: any}) {
      this.setData({
        isLogin: data.isLogin,
        userInfo: data.userInfo
      });
    },
    
    // 检查登录状态
    async checkLoginStatus() {
      // 避免重复检查
      if (this.data.loginChecked) {
        return this.data.isLogin;
      }
      
      try {
        this.setData({ loginChecked: true });
        
        // 检查本地存储
        const token = wx.getStorageSync('token');
        const userInfo = wx.getStorageSync('userInfo');
        
        if (token && userInfo) {
          // 验证后端登录状态
          const res = await Api.common.logintest();
          if (res && res.code !== 401) {
            this.setData({ isLogin: true });
            return true;
          }
        }
        
        // 未登录，自动显示登录弹窗
        this.showLoginModal();
        return false;
      } catch (error) {
        console.error('登录状态检查失败:', error);
        // 出错时也显示登录弹窗
        this.showLoginModal();
        return false;
      }
    },
    
    // 显示登录弹窗
    showLoginModal() {
      // 如果已有登录弹窗显示，不重复显示
      if (this.data.loginModalVisible) return;
      
      eventBus.emit('loginModalEvent', {
        show: true,
        source: 'gallery',
        callback: (userInfo) => {
          this.setData({
            isLogin: true,
            userInfo: userInfo
          });
          
          // 登录成功后，尝试获取用户选择状态
          if (this.data.galleryData && this.data.galleryData.gallery) {
            const galleryId = this.data.galleryData.gallery.id;
            if (galleryId) {
              // 先尝试从本地存储恢复选择状态
              const restoredFromStorage = this.restoreSelectionFromStorage();
              
              // 如果本地存储没有有效的选择状态，则尝试从服务器获取用户选择状态
              if (!restoredFromStorage) {
                this.getUserGallerySelection(galleryId);
              }
            }
          }
        }
      });
    },

    // 添加移动缩放相关方法
    onMovableChange(e) {
      // 处理拖动事件
      // console.log('位置变化:', e.detail);
    },

    // 图片选择切换
    toggleSelection(e) {
      
      try {
        // 直接从事件对象获取索引
        const index = e.currentTarget.dataset.index;
        
        // 如果索引无效，提前返回
        if (index === undefined || index === null) {
          wx.showToast({
            title: '无效的操作',
            icon: 'none'
          });
          return;
        }
        
        // 确保数据存在
        if (!this.data.galleryData || !this.data.galleryData.images || !this.data.galleryData.images.list) {
          wx.showToast({
            title: '数据加载中，请稍后再试',
            icon: 'none'
          });
          return;
        }
        
        // 检查用户是否已提交选择，不能修改
        if (this.data.userHasSubmitted || this.data.galleryData.userHasSubmitted) {
          wx.showToast({
            title: '您已提交过选择，不能修改',
            icon: 'none'
          });
          return;
        }
        
        // 检查用户是否在选择列表中
        if (this.data.galleryData.userSelectionInfo && 
            this.data.galleryData.userSelectionInfo.isCurrentUser && 
            this.data.galleryData.userSelectionInfo.hasSubmitted) {
          wx.showToast({
            title: '您已在选择列表中，不能修改',
            icon: 'none'
          });
          return;
        }
        
        const imagesList = this.data.galleryData.images.list;
        const image = imagesList[index];
        
        // 确保找到了有效的图片对象
        if (!image) {
          console.error('未找到对应索引的图片:', index);
          return;
        }
        
        // 确保gallery配置存在
        const gallery = this.data.galleryData.gallery || {
          selection_mode: "0",
          max_selections: 3,
          show_selected_artist: "0"
        };
        
        // 检查是否是当前用户已选择的图片（从其他设备或时间选择的）
        if (this.data.galleryData.userSelectionInfo && 
            this.data.galleryData.userSelectionInfo.isCurrentUser && 
            image.isUserSelected) {
          // 这是用户已经选择过的图片，可以显示特殊提示
          wx.showToast({
            title: '您已经选择过该图片',
            icon: 'none'
          });
          return;
        }
        
        // 检查是否已被他人选择，根据selection_mode判断是否允许重复选择
        if (gallery.selection_mode == "0" && image.hasOtherSelections) {
          // 禁止重复选择模式下，检查是否被其他用户选择过
          
          // 已被其他用户选择，且不允许重复选择
          if (gallery.show_selected_artist == "1") {
            // 如果允许查看画师信息，显示已选择该图片的画师列表
            this.showImageArtistsList(image);
            return;
          } else {
            // 如果不允许查看画师信息，只显示普通提示
            wx.showToast({
              title: '该图片已被他人选择',
              icon: 'none'
            });
            return;
          }
        }
        
        // 处理选择/取消选择逻辑
        if (image.selected) {
          
          // 更新所有图片的选择状态
          const newList = imagesList.map((item, idx) => {
            if (idx === index) {
              // 当前项取消选择
              return {
                ...item,
                selected: false,
                selectionIndex: 0
              };
            } else if (item.selected && item.selectionIndex > image.selectionIndex) {
              // 序号大于当前项的，序号减1
              return {
                ...item,
                selectionIndex: item.selectionIndex - 1
              };
            }
            // 其他项保持不变
            return item;
          });
          
          // 更新状态
          this.setData({
            'galleryData.images.list': newList,
            selectedCount: this.data.selectedCount - 1
          }, () => {
            // 回调中检查数据是否确实更新
            console.log('数据更新后的选择状态:', 
              this.data.selectedCount,
              this.data.galleryData.images.list.filter(item => item.selected).length
            );
          });
          
          
        } else {
          // 未选中，需要选择
          // 检查是否已达到最大选择数量
          if (this.data.selectedCount >= gallery.max_selections) {
            wx.showToast({
              title: `最多只能选择${gallery.max_selections}张图片`,
              icon: 'none'
            });
            return;
          }
          
          
          // 计算新的选择序号
          const newSelectionIndex = this.data.selectedCount + 1;
          
          // 更新图片列表
          const newList = imagesList.map((item, idx) => {
            if (idx === index) {
              // 当前项设为选中
              return {
                ...item,
                selected: true,
                selectionIndex: newSelectionIndex
              };
            }
            // 其他项保持不变
            return item;
          });
          
          // 更新状态
          this.setData({
            'galleryData.images.list': newList,
            selectedCount: newSelectionIndex
          }, () => {
            // 回调中检查数据是否确实更新
            console.log('数据更新后的选择状态:', 
              this.data.selectedCount,
              this.data.galleryData.images.list.filter(item => item.selected).length
            );
          });
          
        }
        
        // 添加触感反馈
        wx.vibrateShort({
          type: 'medium'
        });
        
        // 操作结束后保存选择状态
        this.saveSelectionToStorage();
        
      } catch (error) {
        console.error('选择操作失败:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    // 显示画师列表弹窗
    showArtistsList() {
      // 检查是否有画师选择了图片
      const hasNoSelections = !this.data.galleryData.images.list.some(item => item.hasOtherSelections);
      
      // 合并同一画师选择的图像
      const artistImages = {};
      
      // 遍历所有图片，收集每个画师选择的图片
      if (!hasNoSelections && this.data.galleryData.images.list) {
        this.data.galleryData.images.list.forEach(item => {
          if (item.hasOtherSelections && item.otherUsersSelections) {
            item.otherUsersSelections.forEach(selection => {
              const artistId = selection.artist_id;
              
              // 如果画师ID还不存在，创建一个新条目
              if (!artistImages[artistId]) {
                artistImages[artistId] = {
                  artist_id: artistId,
                  artist_name: selection.artist_name,
                  selection_time: selection.selection_time,
                  status: selection.status || 'pending', // 添加状态信息，默认为pending
                  images: []
                };
              }
              
              // 添加图片到画师的图片列表中
              if (!artistImages[artistId].images.some(img => img.id === item.id)) {
                artistImages[artistId].images.push({
                  id: item.id,
                  url: item.url,
                  thumbnail_url: item.thumbnail_url
                });
              }
            });
          }
        });
      }
      
      // 转换为数组
      const mergedArtistsList = Object.values(artistImages);
      
      this.setData({
        showArtistsPopup: true,
        hasNoArtistSelections: hasNoSelections,
        mergedArtistsList: mergedArtistsList
      });
    },
    
    // 隐藏画师列表弹窗
    hideArtistsList() {
      this.setData({
        showArtistsPopup: false,
        mergedArtistsList: [] // 清除合并后的列表数据
      });
    },
    
    // 阻止冒泡
    preventBubble() {
      return;
    },


    // 添加获取页面实例的辅助方法
    getPageInstance() {
      let pages = getCurrentPages();
      if (pages.length > 0) {
        return pages[pages.length - 1];
      }
      return null;
    },

    // 添加检查并恢复选择状态的方法
    checkRestoreSelectionState() {
      if (this.data.savedSelectionState) {
        const savedState = this.data.savedSelectionState;
        this.restoreSelectionState(savedState);
        this.setData({ savedSelectionState: null }); // 清除保存的状态
      }
    },

    // 添加保存选择状态到本地存储的方法
    saveSelectionToStorage() {
      try {
        if (!this.data.galleryData || !this.data.galleryData.images) return;
        
        const selectionState = this.data.galleryData.images.list
          .filter(item => item.selected)
          .map(item => ({
            id: item.id,
            selectionIndex: item.selectionIndex
          }));
        
        // 修改画廊ID获取方式 - 不使用可选链
        const galleryId = this.data.galleryData.gallery && this.data.galleryData.gallery.id || 'default';
        const storageKey = 'gallery_selection';
        
        wx.setStorageSync(storageKey, {
          state: selectionState,
          timestamp: new Date().getTime(),
          count: this.data.selectedCount
        });
        
        console.log('选择状态已保存到本地存储', selectionState);
      } catch (error) {
        console.error('保存选择状态失败:', error);
      }
    },

    // 添加从本地存储恢复选择状态的方法
    restoreSelectionFromStorage() {
      try {
        if (!this.data.galleryData || !this.data.galleryData.gallery) return;
        
        // 修改画廊ID获取方式 - 不使用可选链
        const galleryId = this.data.galleryData.gallery && this.data.galleryData.gallery.id || 'default';
        const storageKey = 'gallery_selection';
        const savedData = wx.getStorageSync(storageKey);
        
        if (!savedData || !savedData.state || !Array.isArray(savedData.state)) {
          console.log('本地存储中没有有效的选择状态');
          return false;
        }
        
        // 检查时间戳，如果保存时间超过30分钟，则不恢复
        const now = new Date().getTime();
        if (now - savedData.timestamp > 30 * 60 * 1000) {
          console.log('保存的选择状态已过期');
          wx.removeStorageSync(storageKey);
          return false;
        }
        
        // 恢复选择状态
        const updatedList = this.data.galleryData.images.list.map(image => {
          const savedItem = savedData.state.find(item => item.id === image.id);
          if (savedItem) {
            return {
              ...image,
              selected: true,
              selectionIndex: savedItem.selectionIndex
            };
          }
          return {
            ...image,
            selected: false,
            selectionIndex: 0
          };
        });
        
        this.setData({
          'galleryData.images.list': updatedList,
          selectedCount: savedData.count
        });
        
        console.log('已从本地存储恢复选择状态', savedData.state);
        return true;
      } catch (error) {
        console.error('恢复选择状态失败:', error);
        return false;
      }
    },

    // 切换头部区域展开/折叠状态
    toggleHeader() {
      this.setData({
        isHeaderExpanded: !this.data.isHeaderExpanded
      });
    },

    // 阻止图片操作菜单
    preventImageAction(e) {
      console.log('阻止图片操作菜单');
      return false;
    },

    // 获取用户已选择的图片并自动勾选
    async getUserGallerySelection(galleryId) {
      try {
        // 先检查是否已经从选择列表中获取到了用户选择信息
        if (this.data.galleryData && 
            this.data.galleryData.userSelectionInfo && 
            this.data.galleryData.userSelectionInfo.isCurrentUser) {
          console.log('已从选择列表获取到用户选择信息，无需再从服务器查询');
          return;
        }
        
        // 获取用户图库状态
        const galleryStatusResponse = await Api.Status.getUserGalleryStatus();
        console.log('galleryStatusResponse',galleryStatusResponse);
        
        if (!galleryStatusResponse || !Array.isArray(galleryStatusResponse)) {
          console.log('没有获取到有效的图库状态数据');
          return;
        }
        
        // 在返回的列表中找到当前画廊
        const galleryInfo = galleryStatusResponse.find(item => {
          return (item.gallery_id == galleryId || item.id == galleryId);
        });
        
        if (!galleryInfo || !galleryInfo.selected_images || !Array.isArray(galleryInfo.selected_images) || galleryInfo.selected_images.length === 0) {
          console.log('该画廊没有用户已选图片');
          return;
        }
        
        console.log('找到用户已选图片:', galleryInfo.selected_images);
        
        // 如果没有图片列表，提前返回
        if (!this.data.galleryData || !this.data.galleryData.images || !this.data.galleryData.images.list) {
          return;
        }
        
        // 检查选择模式和限制
        const gallery = this.data.galleryData.gallery || { selection_mode: "0", max_selections: 3 };
        
        // 定义选择映射的接口
        interface SelectedImageInfo {
          id: number;
          selectionIndex: number;
        }
        
        // 整理已选图片数据，保持选择顺序
        // 通过创建一个映射表保存用户选择的图片ID及其顺序
        const selectedImagesMap: Record<number, SelectedImageInfo> = {};
        galleryInfo.selected_images.forEach((img, index) => {
          selectedImagesMap[img.id] = {
            id: img.id,
            selectionIndex: index + 1
          };
        });
        
        // 准备更新图片列表，自动勾选已选图片
        const updatedList = this.data.galleryData.images.list.map(image => {
          // 检查图片是否在已选列表中
          if (selectedImagesMap[image.id]) {
            // 图片已被用户选中，设置选中状态和索引
            return {
              ...image,
              selected: true,
              selectionIndex: selectedImagesMap[image.id].selectionIndex,
              isUserSelected: true // 标记为用户选择
            };
          }
          
          // 未选中的图片
          return {
            ...image,
            selected: false,
            selectionIndex: 0,
            isUserSelected: false
          };
        });
        
        // 计算已选择数量
        const selectedCount = Object.keys(selectedImagesMap).length;
        
        // 检查是否超出最大选择数量
        if (selectedCount > gallery.max_selections) {
          console.warn(`已选图片数量(${selectedCount})超过最大限制(${gallery.max_selections})，将保留最先选择的${gallery.max_selections}张`);
          
          // 按顺序保留最先选择的图片
          const sortedSelected = Object.values(selectedImagesMap)
            .sort((a, b) => a.selectionIndex - b.selectionIndex)
            .slice(0, gallery.max_selections);
          
          // 创建新的映射表
          const limitedMap: Record<number, SelectedImageInfo> = {};
          sortedSelected.forEach(item => {
            limitedMap[item.id] = item;
          });
          
          // 重新处理图片列表
          const limitedList = updatedList.map(image => {
            if (limitedMap[image.id]) {
              return {
                ...image,
                selected: true,
                selectionIndex: limitedMap[image.id].selectionIndex,
                isUserSelected: true
              };
            }
            return {
              ...image,
              selected: false,
              selectionIndex: 0,
              isUserSelected: false
            };
          });
          
          // 更新状态
          this.setData({
            'galleryData.images.list': limitedList,
            selectedCount: gallery.max_selections
          });
          
          console.log(`已自动勾选用户已选图片，限制为最大数量${gallery.max_selections}张`);
        } else {
          // 更新状态
          this.setData({
            'galleryData.images.list': updatedList,
            selectedCount
          });
          
          console.log('已自动勾选用户已选图片，数量:', selectedCount);
        }
        
        // 操作完成后保存选择状态到本地存储
        setTimeout(() => {
          this.saveSelectionToStorage();
        }, 100);
        
      } catch (error) {
        console.error('获取用户选择状态失败:', error);
      }
    },
  }
});

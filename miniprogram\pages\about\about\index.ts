// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
import { DOMAIN, COMMON_ASSETS } from '../../../utils/constants';

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    // 用户信息
    isLogin: false,
    userInfo: {},
    baseUrl: DOMAIN,
    defaultAvatar: COMMON_ASSETS.DEFAULT_AVATAR,
    // 用户统计数据
    userStats: null,
    hasUserStats: false,
    // 用户最近活动
    recentActivities: [],
    hasRecentActivities: false,
    // 功能菜单项
    menuItems: [
      { id: 'profile', name: '个人资料', icon: 'me', color: '#2196F3' },
      { id: 'series', name: '系列申请', icon: 'star', color: '#FF9800' },
      { id: 'mygallery', name: '已选图库', icon: 'album', color: '#2196F3' },
      // { id: 'feedback', name: '意见反馈', icon: 'comment', color: '#4CAF50' },
      // { id: 'about', name: '关于我们', icon: 'info', color: '#9C27B0' },
      // { id: 'share', name: '分享应用', icon: 'share', color: '#FF5722' },
      // { id: 'help', name: '帮助中心', icon: 'help', color: '#3F51B5' },
      // { id: 'message', name: '消息通知', icon: 'bellring-on', color: '#009688' },
      // { id: 'vip', name: '会员特权', icon: 'like', color: '#FFC107' }
    ],
    // 设置项
    settingItems: [
      { id: 'profile', name: '个人资料', icon: 'me', color: '#2196F3' },
      { id: 'security', name: '账户安全', icon: 'lock', color: '#FF5722', value: '已绑定手机' },
      { id: 'privacy', name: '隐私设置', icon: 'eyes-on', color: '#4CAF50' },
      { id: 'notification', name: '通知设置', icon: 'bellring-on', color: '#9C27B0' },
      { id: 'theme', name: '外观设置', icon: 'like', color: '#FF9800' }
    ],
    loading: false,
    // 公告列表数据
    announcementList: [],
    hasAnnouncements: false,
    // 工作室全体公告
    studioAllAnnouncements: [],
    hasStudioAllAnnouncements: false,
    // 工作室系列公告
    studioSeriesAnnouncements: [],
    hasStudioSeriesAnnouncements: false,
    // 当前选中的公告详情
    currentAnnouncement: null,
    // 是否显示公告详情
    showAnnouncementDetail: false,
    // 是否显示更多公告
    showMoreAnnouncements: false,
    // 当前查看的公告类型 'all' 或 'series'
    currentAnnouncementType: 'all',
    // 图表数据
    chartData: {},
    // 当前图表类型
    currentChartType: 'ring',
    // 图表配置
    chartOpts: {
      color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE"],
      padding: [15, 15, 15, 15],
      legend: {
        show: true,
        position: "bottom",
        float: "center",
        fontSize: 10,
        padding: 5,
        margin: 5
      },
      extra: {
        ring: {
          ringWidth: 30,
          activeOpacity: 0.5,
          activeRadius: 10,
          offsetAngle: 0,
          labelWidth: 15,
          border: true,
          borderWidth: 3,
          borderColor: "#FFFFFF"
        }
      }
    }
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // 监听登录成功事件
      eventBus.on('loginSuccess', this.onLoginSuccess.bind(this));
      // 页面加载时检查登录状态
      this.checkLoginStatus();
      // 加载公告列表
      // this.getAnnouncementList();
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      eventBus.off('loginSuccess', this.onLoginSuccess.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    // 检查登录状态
    checkLoginStatus: async function() {
      try {
        // 使用 Api.common.logintest() 检查登录状态
        const res = await Api.common.logintest();
        console.log('logintest 返回结果:', res);
        
        if (res && res.code !== 401) {
          console.log('登录有效，获取用户信息');
          // 已登录，获取用户信息
          this.getUserInfoFromStorage();
          // 获取用户统计数据
          // this.getUserStats();
          // 获取用户最近活动
          // this.getRecentActivities();
        } else {
          console.log('需要登录，重置状态');
          // 未登录，显示登录浮窗
          this.setData({ isLogin: false, userInfo: {} });
          // 自动显示登录窗口
          // this.showLoginModal();
        }
      } catch (error) {
        console.error('检查登录状态发生错误:', error);
        // 捕获到401错误表示未登录
        if (error && error.code === 401) {
          console.log('401错误，需要登录');
          this.setData({ isLogin: false, userInfo: {} });
          // 自动显示登录窗口
          // this.showLoginModal();
        } else {
          console.error('检查登录状态失败', error);
          this.setData({ isLogin: false, userInfo: {} });
        }
      } finally {
        console.log('登录检查完成');
      }
    },
    
    // 从本地存储获取用户信息
    getUserInfoFromStorage: function() {
      try {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
          this.setData({
            isLogin: true,
            userInfo: userInfo
          });
        } else {
          this.setData({ isLogin: false });
        }
      } catch (error) {
        console.error('获取存储的用户信息失败', error);
        this.setData({ isLogin: false });
      }
    },
    
    // 获取用户统计数据
    getUserStats: async function() {
      if (!this.data.isLogin || !this.data.userInfo.id) {
        return;
      }
      
      try {
        this.setData({ loading: true });
        
        // 获取用户统计数据
        const res = await Api.user.userStats(this.data.userInfo.id);
        console.log('获取用户统计数据:', res);
        
        if (res && res.code === 200) {
          this.setData({
            userStats: res.data,
            hasUserStats: true,
            loading: false
          });
          
          // 生成图表数据
          this.generateChartData();
        } else {
          console.error('获取用户统计数据失败:', res);
          this.setData({
            userStats: null,
            hasUserStats: false,
            loading: false
          });
        }
      } catch (error) {
        console.error('获取用户统计数据出错:', error);
        this.setData({
          userStats: null,
          hasUserStats: false,
          loading: false
        });
      }
    },
    
    // 获取用户最近活动
    getRecentActivities: async function() {
      if (!this.data.isLogin || !this.data.userInfo.id) {
        return;
      }
      
      try {
        // 模拟获取用户最近活动
        // 实际项目中应该通过API获取
        const activities = [
          { 
            id: 1, 
            type: 'image', 
            title: '生成了新图片', 
            description: '风景山水画', 
            time: '2023-06-15 10:30:45', 
            icon: 'edit'
          },
          { 
            id: 2, 
            type: 'series', 
            title: '参与了系列', 
            description: '春日系列', 
            time: '2023-06-14 16:22:30', 
            icon: 'star'
          },
          { 
            id: 3, 
            type: 'gallery', 
            title: '添加了图库', 
            description: '风景照片集', 
            time: '2023-06-12 09:15:22', 
            icon: 'album'
          }
        ];
        
        this.setData({
          recentActivities: activities,
          hasRecentActivities: activities.length > 0
        });
        
        console.log('获取用户最近活动:', activities);
      } catch (error) {
        console.error('获取用户最近活动失败:', error);
        this.setData({
          recentActivities: [],
          hasRecentActivities: false
        });
      }
    },
    
    // 生成图表数据
    generateChartData: function() {
      if (!this.data.hasUserStats) {
        return;
      }
      
      // 根据当前选择的图表类型生成数据
      const chartType = this.data.currentChartType;
      let chartData = {};
      
      if (this.data.userStats) {
        const stats = this.data.userStats;
        
        // 使用环形图统计用户数据
        if (chartType === 'ring') {
          // 假设stats中有相关数据，实际应该根据API返回结构调整
          // 这里使用模拟数据
          const usedCount = stats.used_count || 10;
          const totalCount = stats.total_count || 100;
          const remainingCount = totalCount - usedCount;
          
          chartData = {
            series: [
              {
                name: "使用情况",
                data: [
                  { name: "已使用", value: usedCount },
                  { name: "剩余", value: remainingCount }
                ]
              }
            ]
          };
        }
      }
      
      this.setData({ chartData: chartData });
    },
    
    // 切换图表类型
    switchChartType: function(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({ 
        currentChartType: type 
      });
      
      // 重新生成图表数据
      this.generateChartData();
    },
    
    // 登录成功回调
    onLoginSuccess: function(userInfo) {
      // 现在直接接收userInfo参数，而不是event.detail
      if (userInfo) {
        this.setData({
          isLogin: true,
          userInfo: userInfo
        });
        console.log('登录成功，用户信息:', userInfo);
        
        // 获取用户统计数据
        this.getUserStats();
        // 获取用户最近活动
        this.getRecentActivities();
      }
    },
    
    // 显示登录浮窗
    showLoginModal: function() {
      // 修复：按照事件总线规范发送loginModalEvent事件
      eventBus.emit('loginModalEvent', {
        show: true,
        source: 'about',
        callback: (userInfo) => {
          // 登录成功后的回调处理
          this.setData({
            isLogin: true,
            userInfo: userInfo
          });
          console.log('登录成功，用户信息:', userInfo);
          
          // 获取用户统计数据
          this.getUserStats();
          // 获取用户最近活动
          this.getRecentActivities();
        }
      });
    },
    
    // 隐藏登录浮窗
    hideLoginModal: function() {
      eventBus.emit('loginModalEvent', {
        show: false
      });
    },
    
    // 菜单项点击处理
    handleMenuClick: function(e) {
      const id = e.currentTarget.dataset.id;
      console.log('点击了菜单项:', id);
      
      // 如果未登录，显示登录窗口
      if (!this.data.isLogin) {
        this.showLoginModal();
        return;
      }
      
      // 根据id处理不同菜单项的点击
      switch(id) {
        case 'profile':
          wx.navigateTo({
            url: '/pages/about/artistProfile/index'
          });
          break;
        case 'series':
          wx.navigateTo({
            url: '/pages/about/studio/series/index'
          });
          break;
        case 'mygallery':
          wx.navigateTo({
            url: '/pages/about/studio/mygallery/index'
          });
          break;
        case 'share':
          this.onShareAppMessage();
          break;
        case 'feedback':
          wx.navigateTo({
            url: '/pages/feedback/index'
          });
          break;
        case 'uchart':
          wx.navigateTo({
            url: '/pages/uchart-demo/index'
          });
          break;
        // 其他菜单项处理...
        default:
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    
    // 设置项点击处理
    handleSettingClick: function(e) {
      const id = e.currentTarget.dataset.id;
      console.log('点击了设置项:', id);
      
      // 如果未登录，显示登录窗口
      if (!this.data.isLogin) {
        this.showLoginModal();
        return;
      }
      
      // 根据id处理不同设置项的点击
      switch(id) {
        case 'profile':
          wx.navigateTo({
            url: '/pages/about/artistProfile/index'
          });
          break;
        // 其他设置项处理...
        default:
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    
    // 退出登录
    logout: function() {
      wx.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录状态
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('token');
            this.setData({
              isLogin: false,
              userInfo: {},
              userStats: null,
              hasUserStats: false,
              recentActivities: [],
              hasRecentActivities: false
            });
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          }
        }
      });
    },
    
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '个人中心',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '个人中心',
        path: '/pages/about/about/index'
      };
    },

    // 添加下拉刷新功能
    onPullDownRefresh: function() {
      this.setData({ loading: true });
      
      // 模拟刷新数据
      setTimeout(() => {
        this.checkLoginStatus();
        this.setData({ loading: false });
        wx.stopPullDownRefresh();
      }, 1500);
    },
    
    // 刷新用户信息
    refreshUserInfo: function() {
      if (!this.data.isLogin) {
        this.showLoginModal();
        return;
      }
      
      this.setData({ loading: true });
      
      // 显示加载中动画
      wx.showLoading({
        title: '刷新中...',
      });
      
      // 刷新用户数据
      Promise.all([
        this.getUserInfoFromStorage(),
        this.getUserStats(),
        this.getRecentActivities()
      ]).then(() => {
        this.setData({ loading: false });
        wx.hideLoading();
        
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }).catch(error => {
        console.error('刷新数据失败:', error);
        this.setData({ loading: false });
        wx.hideLoading();
        
        wx.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      });
    },

    // 获取公告列表
    getAnnouncementList: async function() {
      try {
        // 获取全体公告
        const allRes = await Api.common.getAnnouncementList('studio', 'studio_all', '');
        console.log('获取全体公告结果:', allRes);
        
        // 获取系列公告
        const seriesRes = await Api.common.getAnnouncementList('studio', 'series', '');
        console.log('获取系列公告结果:', seriesRes);
        
        // 处理全体公告
        if (allRes && allRes.length > 0) {
          // 格式化时间
          const formattedAllList = allRes.map(item => {
            return {
              ...item,
              formattedTime: item.createtime ? item.createtime.split(' ')[0] : ''
            };
          });
          
          this.setData({
            studioAllAnnouncements: formattedAllList,
            hasStudioAllAnnouncements: true
          });
        } else {
          this.setData({
            studioAllAnnouncements: [],
            hasStudioAllAnnouncements: false
          });
        }
        
        // 处理系列公告
        if (seriesRes && seriesRes.length > 0) {
          // 格式化时间
          const formattedSeriesList = seriesRes.map(item => {
            return {
              ...item,
              formattedTime: item.createtime ? item.createtime.split(' ')[0] : ''
            };
          });
          
          this.setData({
            studioSeriesAnnouncements: formattedSeriesList,
            hasStudioSeriesAnnouncements: true
          });
        } else {
          this.setData({
            studioSeriesAnnouncements: [],
            hasStudioSeriesAnnouncements: false
          });
        }
        
        // 设置总体公告状态
        this.setData({
          hasAnnouncements: this.data.hasStudioAllAnnouncements || this.data.hasStudioSeriesAnnouncements
        });
      } catch (error) {
        console.error('获取公告列表失败:', error);
        this.setData({
          studioAllAnnouncements: [],
          hasStudioAllAnnouncements: false,
          studioSeriesAnnouncements: [],
          hasStudioSeriesAnnouncements: false,
          hasAnnouncements: false
        });
      }
    },
    
    // 格式化时间戳
    formatTime: function(timestamp) {
      if (!timestamp) return '';
      
      // 如果是秒级时间戳，转换为毫秒级
      if (timestamp.toString().length === 10) {
        timestamp = timestamp * 1000;
      }
      
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },

    // 查看公告详情
    viewAnnouncementDetail: function(e) {
      const { type, index } = e.currentTarget.dataset;
      let announcement;
      
      if (type === 'all') {
        announcement = this.data.studioAllAnnouncements[index];
      } else {
        announcement = this.data.studioSeriesAnnouncements[index];
      }
      
      this.setData({
        currentAnnouncement: announcement,
        showAnnouncementDetail: true
      });
    },
    
    // 查看更多公告
    viewMoreAnnouncements: function(e) {
      const { type } = e.currentTarget.dataset;
      
      this.setData({
        showMoreAnnouncements: true,
        currentAnnouncementType: type
      });
    },
    
    // 关闭公告详情
    closeAnnouncementDetail: function() {
      this.setData({
        showAnnouncementDetail: false,
        currentAnnouncement: null
      });
    },
    
    // 关闭更多公告列表
    closeMoreAnnouncements: function() {
      this.setData({
        showMoreAnnouncements: false
      });
    },


    // 跳转到uCharts图表示例页面
    navigateToUChart: function() {
      wx.navigateTo({
        url: '/pages/uchart-demo/index'
      });
    }
  }
});

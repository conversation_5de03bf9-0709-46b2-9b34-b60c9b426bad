{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nfunction isPromise(obj) {\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function';\n}\n\n/**\n * Return a function that will run a function asynchronously or synchronously\n *\n * example:\n * runAsync(wrappedFunction, callback)(...args);\n *\n * @param   {Function} func  Function to run\n * @param   {Function} cb    Callback function passed the `func` returned value\n * @return  {Function(arguments)} Arguments to pass to `func`. This function will in turn\n *                                return a Promise (Node >= 0.12) or call the callbacks.\n */\n\nvar runAsync = module.exports = function (func, cb) {\n  cb = cb || function () {};\n\n  return function () {\n\n    var args = arguments;\n\n    var promise = new Promise(function (resolve, reject) {\n      var resolved = false;\n      const wrappedResolve = function (value) {\n        if (resolved) {\n          console.warn('Run-async promise already resolved.')\n        }\n        resolved = true;\n        resolve(value);\n      }\n\n      var rejected = false;\n      const wrappedReject = function (value) {\n        if (rejected) {\n          console.warn('Run-async promise already rejected.')\n        }\n        rejected = true;\n        reject(value);\n      }\n\n      var usingCallback = false;\n      var callbackConflict = false;\n      var contextEnded = false;\n\n      var answer = func.apply({\n        async: function () {\n          if (contextEnded) {\n            console.warn('Run-async async() called outside a valid run-async context, callback will be ignored.');\n            return function() {};\n          }\n          if (callbackConflict) {\n            console.warn('Run-async wrapped function (async) returned a promise.\\nCalls to async() callback can have unexpected results.');\n          }\n          usingCallback = true;\n          return function (err, value) {\n            if (err) {\n              wrappedReject(err);\n            } else {\n              wrappedResolve(value);\n            }\n          };\n        }\n      }, Array.prototype.slice.call(args));\n\n      if (usingCallback) {\n        if (isPromise(answer)) {\n          console.warn('Run-async wrapped function (sync) returned a promise but async() callback must be executed to resolve.');\n        }\n      } else {\n        if (isPromise(answer)) {\n          callbackConflict = true;\n          answer.then(wrappedResolve, wrappedReject);\n        } else {\n          wrappedResolve(answer);\n        }\n      }\n      contextEnded = true;\n    });\n\n    promise.then(cb.bind(null, null), cb);\n\n    return promise;\n  }\n};\n\nrunAsync.cb = function (func, cb) {\n  return runAsync(function () {\n    var args = Array.prototype.slice.call(arguments);\n    if (args.length === func.length - 1) {\n      args.push(this.async());\n    }\n    return func.apply(this, args);\n  }, cb);\n};\n"]}
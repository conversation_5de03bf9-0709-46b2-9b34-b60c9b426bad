{"version": 3, "sources": ["encoding.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar iconvLite = require('iconv-lite');\n\n// Expose to the world\nmodule.exports.convert = convert;\n\n/**\n * Convert encoding of an UTF-8 string or a buffer\n *\n * @param {String|Buffer} str String to be converted\n * @param {String} to Encoding to be converted to\n * @param {String} [from='UTF-8'] Encoding to be converted from\n * @return {Buffer} Encoded string\n */\nfunction convert(str, to, from) {\n    from = checkEncoding(from || 'UTF-8');\n    to = checkEncoding(to || 'UTF-8');\n    str = str || '';\n\n    var result;\n\n    if (from !== 'UTF-8' && typeof str === 'string') {\n        str = Buffer.from(str, 'binary');\n    }\n\n    if (from === to) {\n        if (typeof str === 'string') {\n            result = Buffer.from(str);\n        } else {\n            result = str;\n        }\n    } else {\n        try {\n            result = convertIconvLite(str, to, from);\n        } catch (E) {\n            console.error(E);\n            result = str;\n        }\n    }\n\n    if (typeof result === 'string') {\n        result = Buffer.from(result, 'utf-8');\n    }\n\n    return result;\n}\n\n/**\n * Convert encoding of astring with iconv-lite\n *\n * @param {String|Buffer} str String to be converted\n * @param {String} to Encoding to be converted to\n * @param {String} [from='UTF-8'] Encoding to be converted from\n * @return {Buffer} Encoded string\n */\nfunction convertIconvLite(str, to, from) {\n    if (to === 'UTF-8') {\n        return iconvLite.decode(str, from);\n    } else if (from === 'UTF-8') {\n        return iconvLite.encode(str, to);\n    } else {\n        return iconvLite.encode(iconvLite.decode(str, from), to);\n    }\n}\n\n/**\n * Converts charset name if needed\n *\n * @param {String} name Character set\n * @return {String} Character set name\n */\nfunction checkEncoding(name) {\n    return (name || '')\n        .toString()\n        .trim()\n        .replace(/^latin[\\-_]?(\\d+)$/i, 'ISO-8859-$1')\n        .replace(/^win(?:dows)?[\\-_]?(\\d+)$/i, 'WINDOWS-$1')\n        .replace(/^utf[\\-_]?(\\d+)$/i, 'UTF-$1')\n        .replace(/^ks_c_5601\\-1987$/i, 'CP949')\n        .replace(/^us[\\-_]?ascii$/i, 'ASCII')\n        .toUpperCase();\n}\n"]}
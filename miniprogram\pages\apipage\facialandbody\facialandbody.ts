// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';

interface Canvas {
  createImage(): {
    onload: (value: unknown) => void;
    onerror: (err: any) => void;
    src: string;
    width: number;
    height: number;
  };
  getContext(type: string): any;
  width: number;
  height: number;
}

interface NekosBestResponse {
  results: Array<{
    artist_href: string;
    artist_name: string;
    source_url: string;
    url: string;
    anime_name: string;
  }>;
}

interface WxRequestSuccessResult<T> {
  data: T;
  statusCode: number;
  header: { [key: string]: string };
  cookies: string[];
}

interface EndpointInfo {
  format: string;
}

interface Endpoints {
  [key: string]: EndpointInfo;
}

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    currentDetail: { images: [] },
    loading: true,
    isEmpty: false,
    categories: [] as Array<{name: string; value: string; format: string}>,
    amounts: [1, 3, 5],
    selectedCategoryIndex: 7,
    selectedAmountIndex: 0,
    selectedCategory: '',
    showCategorySelector: false,
    loadingTimers: {} as Record<number, number>,
    globalCountdown: 0,
    globalCountdownTimer: 0,
    shareImagePath: '',
    loadingProgress: 0,
    loadingText: '准备加载...',
    loadingStep: 1,
    loadingSteps: [
      { progress: 0, text: '初始化资源...' },
      { progress: 20, text: '连接服务器...' },
      { progress: 40, text: '请求图片数据...' },
      { progress: 60, text: '下载图片内容...' },
      { progress: 80, text: '处理图片数据...' },
      { progress: 90, text: '即将完成...' },
      { progress: 100, text: '加载完成' }
    ]
  },

  lifetimes: {
    attached: async function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      await this.fetchCategories();
      if (this.data.categories.length > 0) {
        this.setData({
          selectedCategory: this.data.categories[0].value
        });
        this.fetchImages();
      }
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // 清理所有计时器
      Object.values(this.data.loadingTimers).forEach(timerId => {
        clearTimeout(timerId as number);
      });
      if (this.data.globalCountdownTimer) {
        clearInterval(this.data.globalCountdownTimer);
      }
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    onCategoryChange(e: any) {
      const index = e.detail.value;
      this.setData({
        selectedCategoryIndex: index,
        selectedCategory: this.data.categories[index].value
      });
      this.fetchImages();
    },

    onAmountChange(e: any) {
      this.setData({
        selectedAmountIndex: e.detail.value
      });
      this.fetchImages();
    },

    async fetchSingleImage(index: number) {
      try {
        const category = this.data.categories[this.data.selectedCategoryIndex].value;
        
        const response = await new Promise<WxRequestSuccessResult<NekosBestResponse>>((resolve, reject) => {
          wx.request({
            url: `https://nekos.best/api/v2/${category}`,
            method: 'GET',
            data: { amount: 1 },
            success: resolve,
            fail: reject
          });
        });

        if (response.statusCode === 200 && response.data.results && response.data.results.length > 0) {
          const newImage = {
            ...response.data.results[0],
            isLoaded: false,
            retryCount: 0,
            countdown: 0
          };

          // 更新指定索引的图片
          const newImages = [...this.data.currentDetail.images];
          newImages[index] = newImage;

          this.setData({
            ['currentDetail.images']: newImages
          });

          // 开始加载计时器
          this.startImageLoadingTimer(index, newImage.url);
        } else {
          throw new Error('Failed to fetch image');
        }
      } catch (error) {
        console.error('获取图片失败:', error);
        wx.showToast({
          title: '获取图片失败',
          icon: 'none'
        });
      }
    },

    async fetchImages() {
      this.setData({ loading: true });
      // 清理现有的计时器
      Object.values(this.data.loadingTimers).forEach(timerId => {
        clearTimeout(timerId as number);
      });
      if (this.data.globalCountdownTimer) {
        clearInterval(this.data.globalCountdownTimer);
      }
      this.setData({ 
        loadingTimers: {},
        globalCountdown: 0,
        globalCountdownTimer: 0
      });

      try {
        const category = this.data.categories[this.data.selectedCategoryIndex].value;
        const amount = this.data.amounts[this.data.selectedAmountIndex];
        
        const response = await new Promise<WxRequestSuccessResult<NekosBestResponse>>((resolve, reject) => {
          wx.request({
            url: `https://nekos.best/api/v2/${category}`,
            method: 'GET',
            data: { amount },
            success: resolve,
            fail: reject
          });
        });

        if (response.statusCode === 200 && response.data.results) {
          const images = response.data.results.map((result, index) => ({
            ...result,
            isLoaded: false,
            retryCount: 0,
            countdown: 0
          }));

          this.setData({
            currentDetail: { images },
            loading: false,
            isEmpty: images.length === 0
          });

          images.forEach((image, index) => {
            this.startImageLoadingTimer(index, image.url);
          });
        } else {
          throw new Error('Failed to fetch images');
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        this.setData({
          loading: false,
          isEmpty: true
        });
      }
    },

    startImageLoadingTimer(index: number, imageUrl: string) {
      const existingTimerId = this.data.loadingTimers[index];
      if (existingTimerId) {
        clearTimeout(existingTimerId);
      }

      // 重置加载进度
      this.setData({ 
        loadingProgress: 0, 
        loadingText: this.data.loadingSteps[0].text,
        loadingStep: 1
      });

      // 模拟加载进度
      let currentStep = 0;
      const progressInterval = setInterval(() => {
        const nextStep = currentStep + 1;
        if (nextStep < this.data.loadingSteps.length - 1) { // 最后一步（100%）留给onImageLoad
          currentStep = nextStep;
          this.setData({
            loadingProgress: this.data.loadingSteps[currentStep].progress,
            loadingText: this.data.loadingSteps[currentStep].text,
            loadingStep: currentStep + 1
          });
        }
      }, 300);

      const timerId = setTimeout(() => {
        const currentImage = this.data.currentDetail.images[index];
        if (currentImage && !currentImage.isLoaded) {
          if (currentImage.retryCount < 3) {
            console.log(`图片 ${index + 1} 加载超时，正在重试...`);
            
            const newImages = [...this.data.currentDetail.images];
            newImages[index] = {
              ...currentImage,
              retryCount: currentImage.retryCount + 1,
              url: `${imageUrl}?retry=${currentImage.retryCount + 1}`
            };
            
            this.setData({
              'currentDetail.images': newImages
            });

            this.startImageLoadingTimer(index, imageUrl);
          } else {
            wx.showToast({
              title: `第${index + 1}张图片加载失败`,
              icon: 'none'
            });
          }
        }
        clearInterval(progressInterval);
      }, 15000);

      this.setData({
        [`loadingTimers.${index}`]: timerId
      });
    },

    onImageLoad(e: any) {
      const index = e.currentTarget.dataset.index;
      
      const existingTimerId = this.data.loadingTimers[index];
      if (existingTimerId) {
        clearTimeout(existingTimerId);
        const newTimers = { ...this.data.loadingTimers };
        delete newTimers[index];
        this.setData({ loadingTimers: newTimers });
      }

      // 完成加载进度
      this.setData({
        loadingProgress: 100,
        loadingText: this.data.loadingSteps[this.data.loadingSteps.length - 1].text,
        loadingStep: this.data.loadingSteps.length,
        [`currentDetail.images[${index}].isLoaded`]: true
      });
    },

    // 下载图片
    async downloadImage(e: any) {
      const { url } = e.currentTarget.dataset;
      const index = e.currentTarget.dataset.index;
      if (!url) return;

      wx.showLoading({
        title: '正在保存...',
        mask: true
      });

      try {
        const isGif = this.data.categories[this.data.selectedCategoryIndex].format === 'gif';
        
        if (isGif) {
          // GIF图片直接下载
          const downloadRes = await new Promise<WechatMiniprogram.DownloadFileSuccessCallbackResult>((resolve, reject) => {
            wx.downloadFile({
              url: url,
              success: resolve,
              fail: reject
            });
          });

          if (downloadRes.statusCode === 200) {
            await new Promise((resolve, reject) => {
              wx.saveImageToPhotosAlbum({
                filePath: downloadRes.tempFilePath,
                success: () => {
                  wx.hideLoading();
                  wx.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                  resolve(null);
                },
                fail: (err) => {
                  wx.hideLoading();
                  if (err.errMsg.includes('auth deny')) {
                    wx.showModal({
                      title: '提示',
                      content: '需要您授权保存图片到相册',
                      success: (res) => {
                        if (res.confirm) {
                          wx.openSetting();
                        }
                      }
                    });
                  } else {
                    wx.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                  reject(err);
                }
              });
            });
          } else {
            throw new Error('下载文件失败');
          }
        } else {
          // 静态图片使用canvas处理
          const query = this.createSelectorQuery();
          const canvas = await new Promise<Canvas>((resolve, reject) => {
            query.select('#downloadCanvas')
              .fields({ node: true, size: true })
              .exec((res) => {
                if (res[0] && res[0].node) {
                  resolve(res[0].node as Canvas);
                } else {
                  reject(new Error('获取画布失败'));
                }
              });
          });

          const image = canvas.createImage();
          await new Promise((resolve, reject) => {
            image.onload = resolve;
            image.onerror = reject;
            image.src = url;
          });

          canvas.width = image.width;
          canvas.height = image.height;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(image, 0, 0, image.width, image.height);

          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas,
              success: (res) => resolve(res.tempFilePath),
              fail: reject
            });
          });

          await new Promise((resolve, reject) => {
            wx.saveImageToPhotosAlbum({
              filePath: tempFilePath,
              success: () => {
                wx.hideLoading();
                wx.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
                resolve(null);
              },
              fail: (err) => {
                wx.hideLoading();
                if (err.errMsg.includes('auth deny')) {
                  wx.showModal({
                    title: '提示',
                    content: '需要您授权保存图片到相册',
                    success: (res) => {
                      if (res.confirm) {
                        wx.openSetting();
                      }
                    }
                  });
                } else {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
                reject(err);
              }
            });
          });
        }
      } catch (error) {
        wx.hideLoading();
        console.error('下载图片失败:', error);
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    },

    startCountdown() {
      // 清除已存在的倒计时
      if (this.data.globalCountdownTimer) {
        clearInterval(this.data.globalCountdownTimer);
      }

      // 设置初始倒计时
      this.setData({
        globalCountdown: 15
      });

      // 创建新的倒计时
      const timerId = setInterval(() => {
        const currentCount = this.data.globalCountdown;
        if (currentCount <= 1) {
          clearInterval(this.data.globalCountdownTimer);
          this.setData({
            globalCountdown: 0,
            globalCountdownTimer: 0
          });
        } else {
          this.setData({
            globalCountdown: currentCount - 1
          });
        }
      }, 1000);

      // 保存计时器ID
      this.setData({
        globalCountdownTimer: timerId
      });
    },

    refreshImage(e: any) {
      const index = e.currentTarget.dataset.index;
      const image = this.data.currentDetail.images[index];
      
      if (this.data.loading || this.data.globalCountdown > 0) return;
      
      wx.vibrateShort({ type: 'light' });
      this.fetchSingleImage(index);
      this.startCountdown();
    },

    // 创建分享图
    async createShareImage(): Promise<string> {
      if (!this.data.currentDetail.images || 
          !this.data.currentDetail.images[0] || 
          !this.data.currentDetail.images[0].url) {
        throw new Error('No image to share');
      }

      const query = this.createSelectorQuery();
      const canvas = await new Promise<Canvas>((resolve, reject) => {
        query.select('#downloadCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0] && res[0].node) {
              resolve(res[0].node as Canvas);
            } else {
              reject(new Error('获取画布失败'));
            }
          });
      });

      const image = canvas.createImage();
      await new Promise((resolve, reject) => {
        image.onload = resolve;
        image.onerror = reject;
        image.src = this.data.currentDetail.images[0].url;
      });

      // 设置画布宽度为450px，高度按比例计算
      const targetWidth = 450;
      const targetHeight = Math.floor((targetWidth / image.width) * image.height);

      canvas.width = targetWidth;
      canvas.height = targetHeight;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(image, 0, 0, targetWidth, targetHeight);

      // 将画布内容保存为临时文件
      const tempFilePath = await new Promise<string>((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvas,
          fileType: 'jpg',
          quality: 0.8,
          success: (res) => resolve(res.tempFilePath),
          fail: reject
        });
      });

      return tempFilePath;
    },

    async onShareTimeline() {
      try {
        const shareImagePath = await this.createShareImage();
        this.setData({ shareImagePath });
        
        return {
          title: `${this.data.categories[this.data.selectedCategoryIndex].name}表情动作参考`,
          imageUrl: shareImagePath
        };
      } catch (error) {
        console.error('创建分享图失败:', error);
        return {
          title: '表情动作参考',
          query: ''
        };
      }
    },

    async onShareAppMessage() {
      try {
        const shareImagePath = await this.createShareImage();
        this.setData({ shareImagePath });

        return {
          title: `${this.data.categories[this.data.selectedCategoryIndex].name}表情动作参考`,
          path: '/pages/apipage/facialandbody/facialandbody',
          imageUrl: shareImagePath
        };
      } catch (error) {
        console.error('创建分享图失败:', error);
        return {
          title: '表情动作参考',
          path: '/pages/apipage/facialandbody/facialandbody'
        };
      }
    },

    // 显示分类选择器
    showCategorySelector() {
      this.setData({ showCategorySelector: true });
    },

    // 隐藏分类选择器
    hideCategorySelector() {
      this.setData({ showCategorySelector: false });
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 什么都不做，只是阻止事件冒泡
    },

    // 选择分类
    selectCategory(e: any) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        selectedCategoryIndex: index,
        selectedCategory: this.data.categories[index].value,
        showCategorySelector: false
      });
      this.fetchImages();
    },

    // 获取分类列表
    async fetchCategories() {
      try {
        const response = await new Promise<WxRequestSuccessResult<Endpoints>>((resolve, reject) => {
          wx.request({
            url: 'https://nekos.best/api/v2/endpoints',
            method: 'GET',
            success: resolve,
            fail: reject
          });
        });

        if (response.statusCode === 200) {
          const endpoints = response.data;
          const categories = Object.entries(endpoints).map(([key, info]) => ({
            name: this.getChineseName(key),
            value: key,
            format: info.format
          }));

          // 按照格式和名称排序
          categories.sort((a, b) => {
            if (a.format === b.format) {
              return a.name.localeCompare(b.name);
            }
            return a.format === 'png' ? -1 : 1;
          });

          this.setData({ categories });
        }
      } catch (error) {
        console.error('获取分类列表失败:', error);
        wx.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      }
    },

    // 获取中文名称
    getChineseName(key: string): string {
      const nameMap: { [key: string]: string } = {
        neko: '猫娘',
        waifu: '老婆',
        husbando: '老公',
        kitsune: '狐娘',
        lurk: '潜伏',
        shoot: '射击',
        sleep: '睡觉',
        shrug: '耸肩',
        stare: '凝视',
        wave: '招手',
        poke: '戳戳',
        smile: '微笑',
        peck: '啄吻',
        wink: '眨眼',
        blush: '脸红',
        smug: '得意',
        tickle: '挠痒',
        yeet: '扔掉',
        think: '思考',
        highfive: '击掌',
        feed: '喂食',
        bite: '咬咬',
        bored: '无聊',
        nom: '吃吃',
        yawn: '哈欠',
        facepalm: '捂脸',
        cuddle: '抱抱',
        kick: '踢踢',
        happy: '开心',
        hug: '拥抱',
        baka: '笨蛋',
        pat: '拍拍',
        angry: '生气',
        run: '跑步',
        nod: '点头',
        nope: '摇头',
        kiss: '亲吻',
        dance: '跳舞',
        punch: '打击',
        handshake: '握手',
        slap: '打脸',
        cry: '哭泣',
        pout: '嘟嘴',
        handhold: '牵手',
        thumbsup: '点赞',
        laugh: '大笑'
      };
      return nameMap[key] || key;
    }
  }
});

// 使用命名空间避免类型冲突
namespace CompactFontPicker {
  export type FontItem = {
    id: number;
    name: string;
    value: string;
    format: string;
    path: string;
    tags?: string[];
    weigh?: number;
  }

  export type FontCategory = {
    name: string;
    type: string;
    fonts: FontItem[];
  }

  export type AppConfig = {
    api: {
      font: {
        baseUrl: string;
        preview: string;
        fontsListUrl: string;
        timeout: number;
        retry: number;
      }
    }
  }

  export type FontResponse = {
    categories: FontCategory[];
    settings?: {
      defaultCategory?: string;
    }
  }

  // 系统信息类型
  export type SystemInfo = WechatMiniprogram.SystemInfo & {
    safeArea?: WechatMiniprogram.SafeArea;
    screenHeight?: number;
    bottomSafeHeight?: number;
  }
}

import Api from '../../utils/api';

Component({
  properties: {
    previewText: {
      type: String,
      value: '字体'
    },
    defaultFont: {
      type: Object,
      value: null
    },
    tabBarHeight: {
      type: Number,
      value: 0
    },
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    visible: false,
    selectedFont: null as CompactFontPicker.FontItem | null,
    currentCategory: '',
    isLoading: false,
    styleVars: '',
    safeAreaBottom: 0,
    systemInfo: null as WechatMiniprogram.SystemInfo | null,
    fontCategories: [] as CompactFontPicker.FontCategory[]
  },

  lifetimes: {
    attached() {
      console.log('紧凑版字体选择器组件初始化');
      this.init();
    }
  },

  methods: {
    async init() {
      try {
        console.log('初始化字体选择器...');
        await this.loadFontsList();
        
        // 设置默认字体
        if (this.properties.defaultFont) {
          this.setData({
            selectedFont: this.properties.defaultFont
          });
          console.log('设置默认字体:', this.properties.defaultFont);
        }

        // 获取系统信息和安全区域
        this.getSystemInfo();
      } catch (error) {
        console.error('初始化失败:', error);
        wx.showToast({
          title: '加载字体失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    async loadFontsList() {
      try {
        console.log('正在加载字体列表...');
        this.setData({ isLoading: true });
        
        const response = await Api.font.getCategories();
        console.log('API响应:', response);

        if (Array.isArray(response)) {
          this.setData({
            fontCategories: response,
            isLoading: false
          });
          console.log('字体加载成功:', this.data.fontCategories);

          // 设置默认分类
          if (response.length > 0) {
            this.setData({
              currentCategory: response[0].type
            });
            console.log('设置默认分类:', response[0].type);
          }
        } else {
          console.error('无效的API响应格式，期望数组但收到:', typeof response);
          throw new Error('无效的数据格式');
        }
      } catch (error) {
        console.error('加载字体列表失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载字体失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 获取系统信息和安全区域
    getSystemInfo() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        let safeAreaBottom = 0;
        
        // 计算底部安全区域高度
        if (systemInfo.safeArea) {
          safeAreaBottom = systemInfo.screenHeight - systemInfo.safeArea.bottom;
        }
        
        // 如果是iPhone X及以上机型，底部安全区域至少为34px
        const isIPhoneX = /iPhone X|iPhone 1[1-9]|iPhone 2[0-9]/.test(systemInfo.model) || 
                         (systemInfo.safeArea && safeAreaBottom >= 34);
        
        if (isIPhoneX && safeAreaBottom < 34) {
          safeAreaBottom = 34;
        }
        
        this.setData({
          systemInfo,
          safeAreaBottom
        });
      } catch (error) {
        console.error('获取系统信息失败:', error);
      }
    },
    
    // 显示选择器
    showPicker() {
      console.log('显示字体选择器');
      this.setData({ visible: true });
    },

    // 隐藏选择器
    hidePicker() {
      console.log('隐藏字体选择器');
      this.setData({ visible: false });
    },

    // 阻止冒泡
    preventBubble() {
      return;
    },

    // 切换分类
    onCategoryChange(e: WechatMiniprogram.TouchEvent) {
      const category = e.currentTarget.dataset.category;
      console.log('切换分类:', category);
      this.setData({
        currentCategory: category
      });
    },

    // 选择字体
    async onSelectFont(e: WechatMiniprogram.TouchEvent) {
      const font = e.currentTarget.dataset.font;
      console.log('选择字体:', font);
      
      try {
        // 更新选中状态
        this.setData({
          selectedFont: font
        });

        // 触发选择事件
        this.triggerEvent('confirm', {
          font: font
        });

        // 选择完成后自动关闭弹窗
        this.hidePicker();

        wx.showToast({
          title: '已选择字体',
          icon: 'success',
          duration: 1500
        });
      } catch (error) {
        console.error('选择字体失败:', error);
        wx.showToast({
          title: '选择失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
});
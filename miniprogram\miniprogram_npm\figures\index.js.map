{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nconst escapeStringRegexp = require('escape-string-regexp');\n\nconst platform = process.platform;\n\nconst main = {\n\ttick: '✔',\n\tcross: '✖',\n\tstar: '★',\n\tsquare: '▇',\n\tsquareSmall: '◻',\n\tsquareSmallFilled: '◼',\n\tplay: '▶',\n\tcircle: '◯',\n\tcircleFilled: '◉',\n\tcircleDotted: '◌',\n\tcircleDouble: '◎',\n\tcircleCircle: 'ⓞ',\n\tcircleCross: 'ⓧ',\n\tcirclePipe: 'Ⓘ',\n\tcircleQuestionMark: '?⃝',\n\tbullet: '●',\n\tdot: '․',\n\tline: '─',\n\tellipsis: '…',\n\tpointer: '❯',\n\tpointerSmall: '›',\n\tinfo: 'ℹ',\n\twarning: '⚠',\n\thamburger: '☰',\n\tsmiley: '㋡',\n\tmustache: '෴',\n\theart: '♥',\n\tarrowUp: '↑',\n\tarrowDown: '↓',\n\tarrowLeft: '←',\n\tarrowRight: '→',\n\tradioOn: '◉',\n\tradioOff: '◯',\n\tcheckboxOn: '☒',\n\tcheckboxOff: '☐',\n\tcheckboxCircleOn: 'ⓧ',\n\tcheckboxCircleOff: 'Ⓘ',\n\tquestionMarkPrefix: '?⃝',\n\toneHalf: '½',\n\toneThird: '⅓',\n\toneQuarter: '¼',\n\toneFifth: '⅕',\n\toneSixth: '⅙',\n\toneSeventh: '⅐',\n\toneEighth: '⅛',\n\toneNinth: '⅑',\n\toneTenth: '⅒',\n\ttwoThirds: '⅔',\n\ttwoFifths: '⅖',\n\tthreeQuarters: '¾',\n\tthreeFifths: '⅗',\n\tthreeEighths: '⅜',\n\tfourFifths: '⅘',\n\tfiveSixths: '⅚',\n\tfiveEighths: '⅝',\n\tsevenEighths: '⅞'\n};\n\nconst win = {\n\ttick: '√',\n\tcross: '×',\n\tstar: '*',\n\tsquare: '█',\n\tsquareSmall: '[ ]',\n\tsquareSmallFilled: '[█]',\n\tplay: '►',\n\tcircle: '( )',\n\tcircleFilled: '(*)',\n\tcircleDotted: '( )',\n\tcircleDouble: '( )',\n\tcircleCircle: '(○)',\n\tcircleCross: '(×)',\n\tcirclePipe: '(│)',\n\tcircleQuestionMark: '(?)',\n\tbullet: '*',\n\tdot: '.',\n\tline: '─',\n\tellipsis: '...',\n\tpointer: '>',\n\tpointerSmall: '»',\n\tinfo: 'i',\n\twarning: '‼',\n\thamburger: '≡',\n\tsmiley: '☺',\n\tmustache: '┌─┐',\n\theart: main.heart,\n\tarrowUp: main.arrowUp,\n\tarrowDown: main.arrowDown,\n\tarrowLeft: main.arrowLeft,\n\tarrowRight: main.arrowRight,\n\tradioOn: '(*)',\n\tradioOff: '( )',\n\tcheckboxOn: '[×]',\n\tcheckboxOff: '[ ]',\n\tcheckboxCircleOn: '(×)',\n\tcheckboxCircleOff: '( )',\n\tquestionMarkPrefix: '？',\n\toneHalf: '1/2',\n\toneThird: '1/3',\n\toneQuarter: '1/4',\n\toneFifth: '1/5',\n\toneSixth: '1/6',\n\toneSeventh: '1/7',\n\toneEighth: '1/8',\n\toneNinth: '1/9',\n\toneTenth: '1/10',\n\ttwoThirds: '2/3',\n\ttwoFifths: '2/5',\n\tthreeQuarters: '3/4',\n\tthreeFifths: '3/5',\n\tthreeEighths: '3/8',\n\tfourFifths: '4/5',\n\tfiveSixths: '5/6',\n\tfiveEighths: '5/8',\n\tsevenEighths: '7/8'\n};\n\nif (platform === 'linux') {\n\t// the main one doesn't look that good on Ubuntu\n\tmain.questionMarkPrefix = '?';\n}\n\nconst figures = platform === 'win32' ? win : main;\n\nconst fn = str => {\n\tif (figures === main) {\n\t\treturn str;\n\t}\n\n\tObject.keys(main).forEach(key => {\n\t\tif (main[key] === figures[key]) {\n\t\t\treturn;\n\t\t}\n\n\t\tstr = str.replace(new RegExp(escapeStringRegexp(main[key]), 'g'), figures[key]);\n\t});\n\n\treturn str;\n};\n\nmodule.exports = Object.assign(fn, figures);\n"]}
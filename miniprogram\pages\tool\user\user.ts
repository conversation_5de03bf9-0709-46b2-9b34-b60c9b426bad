// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api, { NavMenuItem, ToolItem } from '../../../utils/api';
import { API_ENDPOINTS } from '../../../utils/constants';

// 添加请求拦截器
const originalRequest = wx.request;
Object.defineProperty(wx, 'request', {
  configurable: true,
  enumerable: true,
  writable: true,
  value: function(options) {
    const token = wx.getStorageSync('token');
    if (token) {
      options.header = {
        ...options.header,
        'Authorization': `Bearer ${token}`
      };
    }
    return originalRequest.call(this, options);
  }
});

// 常量定义
const CONSTANTS = {
  TOAST_DURATION: 2000
};

interface IComponentData {
  layoutInfo: any;
  layoutStyles: {
    contentStyle: string;
    contentStyleTop: string;
    contentStyleBottom: string;
    tabBarStyle: string;
    tabBarContentStyle: string;
  };
  navMenus: NavMenuItem[];
  tools: ToolItem[];
  loading: boolean;
  isEmpty: boolean;
  hasUserInfo: boolean;
  userInfo: WechatMiniprogram.UserInfo | null;
  openid: string | null;
}

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    layoutInfo: layoutUtil.getLayoutInfo(),
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    layoutStyles: {
      contentStyle: layoutUtil.getContentStyle(),
      contentStyleTop: layoutUtil.getContentStyleTop(),
      contentStyleBottom: layoutUtil.getContentStyleBottom(),
      tabBarStyle: layoutUtil.getTabBarStyle(),
      tabBarContentStyle: layoutUtil.getTabBarContentStyle()
    },
    navMenus: [],
    tools: [],
    loading: true,
    isEmpty: false,
    hasUserInfo: false,
    userInfo: null,
    openid: null
  } as IComponentData,

  lifetimes: {
    attached() {

      // 获取导航菜单和工具列表
      this.loadNavData();
      
      // 检查本地存储的登录状态
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          hasUserInfo: true,
          userInfo
        });
      }
    }
  },

  methods: {
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 加载导航数据
    async loadNavData() {
      try {
        this.setData({ loading: true });
        const navMenus = await Api.nav.getMenuList();
        console.log('navMenus',navMenus);
        this.setData({
          navMenus: navMenus,
          loading: false,
        });
      } catch (error) {
        console.error('获取导航数据失败:', error);
        this.setData({ loading: false });
      }
    },

    // 统一的页面跳转处理
    handlePageNavigation(e: WechatMiniprogram.CustomEvent) {
      const { url } = e.currentTarget.dataset;
      if (url) {
        wx.navigateTo({
          url,
          fail: () => {
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    },

    // 导航点击处理
    onNavClick(e: WechatMiniprogram.CustomEvent) {
      this.handlePageNavigation(e);
    },

    // 工具点击处理
    onToolClick(e: WechatMiniprogram.CustomEvent) {
      this.handlePageNavigation(e);
    },

    // 刷新数据
    async refreshData() {
      await this.loadNavData();
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '工具箱',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '工具箱',
        path: '/pages/tool/test/test'
      };
    },

    // 普通登录
    handleLogin() {
      console.log('开始普通登录流程');
      wx.login({
        success: (res) => {
          console.log('wx.login 成功，获取到 code:', res.code);
          if (res.code) {
            console.log('准备发送登录请求到服务器');
            wx.request({
              url: API_ENDPOINTS.WX_LOGIN,
              method: 'POST',
              data: {
                code: res.code
              },
              success: (result: any) => {
                console.log('服务器返回数据:', result.data);
                if (result.data.code === 1) {
                  console.log('登录成功，token:', result.data.data.token);
                  console.log('用户信息:', result.data.data.userinfo);
                  console.log('openid:', result.data.data.openid);
                  
                  // 登录成功，保存token和用户信息
                  wx.setStorageSync('token', result.data.data.token);
                  wx.setStorageSync('userInfo', result.data.data.userinfo);
                  
                  this.setData({
                    hasUserInfo: true,
                    userInfo: result.data.data.userinfo,
                    openid: result.data.data.openid
                  });

                  wx.showToast({
                    title: '登录成功',
                    icon: 'success',
                    duration: 1500
                  });
                } else {
                  console.error('登录失败:', result.data.msg);
                  wx.showToast({
                    title: result.data.msg || '登录失败',
                    icon: 'none'
                  });
                }
              },
              fail: (error) => {
                console.error('请求失败:', error);
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: (error) => {
          console.error('wx.login 失败:', error);
        }
      });
    },

    // 获取用户信息并登录
    getUserProfile() {
      console.log('开始授权登录流程');
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (userInfo) => {
          console.log('获取用户信息成功:', userInfo);
          wx.login({
            success: (res) => {
              console.log('wx.login 成功，获取到 code:', res.code);
              if (res.code) {
                console.log('准备发送登录请求到服务器，携带用户信息');
                wx.request({
                  url: API_ENDPOINTS.WX_LOGIN,
                  method: 'POST',
                  data: {
                    code: res.code,
                    nickname: userInfo.userInfo.nickName,
                    avatar: userInfo.userInfo.avatarUrl
                  },
                  success: (result: any) => {
                    console.log('服务器返回数据:', result.data);
                    if (result.data.code === 1) {
                      console.log('登录成功，token:', result.data.data.token);
                      console.log('用户信息:', result.data.data.userinfo);
                      console.log('openid:', result.data.data.openid);
                      
                      wx.setStorageSync('token', result.data.data.token);
                      wx.setStorageSync('userInfo', result.data.data.userinfo);
                      
                      this.setData({
                        hasUserInfo: true,
                        userInfo: result.data.data.userinfo,
                        openid: result.data.data.openid
                      });

                      wx.showToast({
                        title: '登录成功',
                        icon: 'success',
                        duration: 1500
                      });
                    } else {
                      console.error('登录失败:', result.data.msg);
                      wx.showToast({
                        title: result.data.msg || '登录失败',
                        icon: 'none'
                      });
                    }
                  },
                  fail: (error) => {
                    console.error('请求失败:', error);
                    wx.showToast({
                      title: '网络错误',
                      icon: 'none'
                    });
                  }
                });
              }
            },
            fail: (error) => {
              console.error('wx.login 失败:', error);
            }
          });
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error);
          wx.showToast({
            title: '您取消了授权',
            icon: 'none'
          });
        }
      });
    }
  }
});

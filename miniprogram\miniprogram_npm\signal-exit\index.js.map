{"version": 3, "sources": ["index.js", "signals.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Note: since nyc uses this module to output coverage, any lines\n// that are in the direct sync flow of nyc's outputCoverage are\n// ignored, since we can never get coverage for them.\n// grab a reference to node's real process object right away\nvar process = global.process\n\nconst processOk = function (process) {\n  return process &&\n    typeof process === 'object' &&\n    typeof process.removeListener === 'function' &&\n    typeof process.emit === 'function' &&\n    typeof process.reallyExit === 'function' &&\n    typeof process.listeners === 'function' &&\n    typeof process.kill === 'function' &&\n    typeof process.pid === 'number' &&\n    typeof process.on === 'function'\n}\n\n// some kind of non-node environment, just no-op\n/* istanbul ignore if */\nif (!processOk(process)) {\n  module.exports = function () {\n    return function () {}\n  }\n} else {\n  var assert = require('assert')\n  var signals = require('./signals.js')\n  var isWin = /^win/i.test(process.platform)\n\n  var EE = require('events')\n  /* istanbul ignore if */\n  if (typeof EE !== 'function') {\n    EE = EE.EventEmitter\n  }\n\n  var emitter\n  if (process.__signal_exit_emitter__) {\n    emitter = process.__signal_exit_emitter__\n  } else {\n    emitter = process.__signal_exit_emitter__ = new EE()\n    emitter.count = 0\n    emitter.emitted = {}\n  }\n\n  // Because this emitter is a global, we have to check to see if a\n  // previous version of this library failed to enable infinite listeners.\n  // I know what you're about to say.  But literally everything about\n  // signal-exit is a compromise with evil.  Get used to it.\n  if (!emitter.infinite) {\n    emitter.setMaxListeners(Infinity)\n    emitter.infinite = true\n  }\n\n  module.exports = function (cb, opts) {\n    /* istanbul ignore if */\n    if (!processOk(global.process)) {\n      return function () {}\n    }\n    assert.equal(typeof cb, 'function', 'a callback must be provided for exit handler')\n\n    if (loaded === false) {\n      load()\n    }\n\n    var ev = 'exit'\n    if (opts && opts.alwaysLast) {\n      ev = 'afterexit'\n    }\n\n    var remove = function () {\n      emitter.removeListener(ev, cb)\n      if (emitter.listeners('exit').length === 0 &&\n          emitter.listeners('afterexit').length === 0) {\n        unload()\n      }\n    }\n    emitter.on(ev, cb)\n\n    return remove\n  }\n\n  var unload = function unload () {\n    if (!loaded || !processOk(global.process)) {\n      return\n    }\n    loaded = false\n\n    signals.forEach(function (sig) {\n      try {\n        process.removeListener(sig, sigListeners[sig])\n      } catch (er) {}\n    })\n    process.emit = originalProcessEmit\n    process.reallyExit = originalProcessReallyExit\n    emitter.count -= 1\n  }\n  module.exports.unload = unload\n\n  var emit = function emit (event, code, signal) {\n    /* istanbul ignore if */\n    if (emitter.emitted[event]) {\n      return\n    }\n    emitter.emitted[event] = true\n    emitter.emit(event, code, signal)\n  }\n\n  // { <signal>: <listener fn>, ... }\n  var sigListeners = {}\n  signals.forEach(function (sig) {\n    sigListeners[sig] = function listener () {\n      /* istanbul ignore if */\n      if (!processOk(global.process)) {\n        return\n      }\n      // If there are no other listeners, an exit is coming!\n      // Simplest way: remove us and then re-send the signal.\n      // We know that this will kill the process, so we can\n      // safely emit now.\n      var listeners = process.listeners(sig)\n      if (listeners.length === emitter.count) {\n        unload()\n        emit('exit', null, sig)\n        /* istanbul ignore next */\n        emit('afterexit', null, sig)\n        /* istanbul ignore next */\n        if (isWin && sig === 'SIGHUP') {\n          // \"SIGHUP\" throws an `ENOSYS` error on Windows,\n          // so use a supported signal instead\n          sig = 'SIGINT'\n        }\n        /* istanbul ignore next */\n        process.kill(process.pid, sig)\n      }\n    }\n  })\n\n  module.exports.signals = function () {\n    return signals\n  }\n\n  var loaded = false\n\n  var load = function load () {\n    if (loaded || !processOk(global.process)) {\n      return\n    }\n    loaded = true\n\n    // This is the number of onSignalExit's that are in play.\n    // It's important so that we can count the correct number of\n    // listeners on signals, and don't wait for the other one to\n    // handle it instead of us.\n    emitter.count += 1\n\n    signals = signals.filter(function (sig) {\n      try {\n        process.on(sig, sigListeners[sig])\n        return true\n      } catch (er) {\n        return false\n      }\n    })\n\n    process.emit = processEmit\n    process.reallyExit = processReallyExit\n  }\n  module.exports.load = load\n\n  var originalProcessReallyExit = process.reallyExit\n  var processReallyExit = function processReallyExit (code) {\n    /* istanbul ignore if */\n    if (!processOk(global.process)) {\n      return\n    }\n    process.exitCode = code || /* istanbul ignore next */ 0\n    emit('exit', process.exitCode, null)\n    /* istanbul ignore next */\n    emit('afterexit', process.exitCode, null)\n    /* istanbul ignore next */\n    originalProcessReallyExit.call(process, process.exitCode)\n  }\n\n  var originalProcessEmit = process.emit\n  var processEmit = function processEmit (ev, arg) {\n    if (ev === 'exit' && processOk(global.process)) {\n      /* istanbul ignore else */\n      if (arg !== undefined) {\n        process.exitCode = arg\n      }\n      var ret = originalProcessEmit.apply(this, arguments)\n      /* istanbul ignore next */\n      emit('exit', process.exitCode, null)\n      /* istanbul ignore next */\n      emit('afterexit', process.exitCode, null)\n      /* istanbul ignore next */\n      return ret\n    } else {\n      return originalProcessEmit.apply(this, arguments)\n    }\n  }\n}\n", "// This is not the set of all possible signals.\n//\n// It IS, however, the set of all signals that trigger\n// an exit on either Linux or BSD systems.  Linux is a\n// superset of the signal names supported on BSD, and\n// the unknown signals just fail to register, so we can\n// catch that easily enough.\n//\n// Don't bother with SIGKILL.  It's uncatchable, which\n// means that we can't fire any callbacks anyway.\n//\n// If a user does happen to register a handler on a non-\n// fatal signal like SIGWINCH or something, and then\n// exit, it'll end up firing `process.emit('exit')`, so\n// the handler will be fired anyway.\n//\n// SIGBUS, SIGFPE, SIGSEGV and SIGILL, when not raised\n// artificially, inherently leave the process in a\n// state from which it is not safe to try and enter JS\n// listeners.\nmodule.exports = [\n  'SIGABRT',\n  'SIGALRM',\n  'SIGHUP',\n  'SIGINT',\n  'SIGTERM'\n]\n\nif (process.platform !== 'win32') {\n  module.exports.push(\n    'SIGVTALRM',\n    'SIGXCPU',\n    'SIGXFS<PERSON>',\n    'SIGUSR2',\n    'SIGTR<PERSON>',\n    'SIGSYS',\n    'SIGQUIT',\n    'SIGIOT'\n    // should detect profiler and enable/disable accordingly.\n    // see #21\n    // 'SIGPROF'\n  )\n}\n\nif (process.platform === 'linux') {\n  module.exports.push(\n    'SIGIO',\n    'SIGPOLL',\n    'SIGPWR',\n    'SIGSTKFLT',\n    'SIGUNUSED'\n  )\n}\n"]}
/**
 * 通用 uCharts 图表组件
 * 
 * 特性：
 * 1. 支持一个页面多个图表实例
 * 2. 支持多种图表类型
 * 3. 简化配置，提供常用图表预设
 * 4. 支持数据更新和图表动态切换
 */

// 引入uCharts库
const uCharts = require('../qiun-wx-ucharts/u-charts.js');

Component({
  options: {
    // 允许组件样式影响外部样式，避免样式隔离
    styleIsolation: 'shared'
  },
  properties: {
    // 图表类型：column, line, area, mount, bar, pie, ring, radar, gauge, candle, ...
    chartType: {
      type: String,
      value: 'column'
    },
    // 图表ID，一个页面多个图表时必须不同
    chartId: {
      type: String,
      value: 'uchart'
    },
    // 图表数据
    chartData: {
      type: Object,
      value: {},
      observer: function(newVal, oldVal) {
        // 当数据变化时更新图表
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.updateChart();
        }
      }
    },
    // 图表配置项
    opts: {
      type: Object,
      value: {}
    },
    // 使用Canvas 2D绘制，性能更好
    canvas2d: {
      type: Boolean,
      value: true
    },
    // 图表高度
    height: {
      type: Number,
      value: 300,
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.setData({
            chartHeight: newVal + 'px'
          });
        }
      }
    },
    // 图表宽度(为0则自适应父容器宽度)
    width: {
      type: Number,
      value: 0,
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== 0) {
          this.setData({
            chartWidth: newVal + 'px'
          });
        }
      }
    },
    // 样式预设类型 (简化配置)
    preset: {
      type: String,
      value: 'default'
    },
    // 是否显示加载中状态
    loading: {
      type: Boolean,
      value: false
    },
    // 是否禁用图表交互
    disableTouch: {
      type: Boolean,
      value: false
    }
  },

  data: {
    chartWidth: '100%', // 默认宽度100%
    chartHeight: '300px', // 默认高度300px
    uChart: null, // 图表实例
    isPC: false, // 是否PC端
    lastTapTime: 0 // 最后一次点击时间，用于判断双击
  },

  lifetimes: {
    attached() {
      // 设置图表高度
      this.setData({
        chartHeight: this.properties.height + 'px'
      });
      
      // 如果有指定宽度，则设置宽度
      if (this.properties.width > 0) {
        this.setData({
          chartWidth: this.properties.width + 'px'
        });
      }
      
      // 初始化图表
      this.initChart();
    },
    detached() {
      // 组件销毁时清除图表实例
      if (this.data.uChart) {
        this.data.uChart = null;
      }
    }
  },

  pageLifetimes: {
    // 页面显示时初始化图表
    show() {
      if (!this.data.uChart && !this.properties.loading) {
        setTimeout(() => {
          this.initChart();
        }, 100);
      }
    },
    // 页面尺寸变化时重新绘制图表
    resize() {
      if (this.data.uChart) {
        this.updateChart();
      }
    }
  },

  methods: {
    /**
     * 初始化图表
     */
    initChart() {
      const chartData = this.properties.chartData;
      if (!chartData || (chartData.series && chartData.series.length === 0)) {
        return;
      }
      
      // 创建图表前先获取容器宽高
      const query = wx.createSelectorQuery().in(this);
      query.select('#' + this.properties.chartId)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res[0] || !res[0].node) {
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 设置画布尺寸
          const width = res[0].width;
          const height = res[0].height;
          const pixelRatio = wx.getSystemInfoSync().pixelRatio;
          canvas.width = width * pixelRatio;
          canvas.height = height * pixelRatio;
          ctx.scale(pixelRatio, pixelRatio);
          
          // 合并图表配置
          let opts = this.getMergedConfig();
          
          // 创建图表实例
          this.data.uChart = new uCharts({
            type: this.properties.chartType,
            context: ctx,
            width: width,
            height: height,
            series: chartData.series,
            categories: chartData.categories,
            animation: true,
            background: '#FFFFFF',
            pixelRatio: pixelRatio,
            ...opts
          });
          
          // 图表创建成功事件
          this.triggerEvent('inited', this.data.uChart);
        });
    },
    
    /**
     * 更新图表
     */
    updateChart() {
      if (!this.data.uChart) {
        this.initChart();
        return;
      }
      
      const chartData = this.properties.chartData;
      if (!chartData || !chartData.series) {
        return;
      }
      
      // 合并图表配置
      let opts = this.getMergedConfig();
      
      // 更新图表
      this.data.uChart.updateData({
        series: chartData.series,
        categories: chartData.categories,
        ...opts
      });
    },
    
    /**
     * 获取合并后的配置
     */
    getMergedConfig() {
      // 基础配置
      let baseConfig = {
        padding: [15, 15, 0, 5],
        legend: {
          show: true,
          position: "bottom",
          float: "center",
          padding: 10,
          margin: 5
        },
        xAxis: {
          disableGrid: true,
          fontColor: "#666666"
        },
        yAxis: {
          gridType: "dash",
          dashLength: 2,
          data: [{ min: 0 }]
        },
        extra: {}
      };
      
      // 根据预设类型获取不同的配置
      let presetConfig = this.getPresetConfig(this.properties.preset);
      
      // 用户自定义配置
      let userConfig = this.properties.opts || {};
      
      // 合并配置 (用户配置优先级最高)
      return {
        ...baseConfig,
        ...presetConfig,
        ...userConfig
      };
    },
    
    /**
     * 根据预设类型获取配置
     */
    getPresetConfig(preset) {
      // 预设配置
      const presetConfigs = {
        default: {},
        dark: {
          background: '#2e2e2e',
          fontColor: '#ffffff',
          xAxis: {
            fontColor: '#eeeeee'
          },
          yAxis: {
            fontColor: '#eeeeee',
            gridColor: '#666666'
          }
        },
        businessBlue: {
          color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE'],
          padding: [15, 15, 0, 5],
          xAxis: {
            disableGrid: true
          },
          yAxis: {
            gridType: "dash",
            dashLength: 2,
            data: [{ min: 0 }]
          }
        },
        colorful: {
          color: ['#0066cc', '#5cd9e8', '#ff9a9e', '#67c23a', '#e6a23c', '#f56c6c', '#909399'],
          extra: {
            column: {
              type: "group",
              width: 30,
              activeBgColor: "#000000",
              activeBgOpacity: 0.1
            },
            pie: {
              activeOpacity: 0.7,
              activeRadius: 10,
              offsetAngle: 0,
              labelWidth: 15,
              border: true,
              borderWidth: 3,
              borderColor: "#FFFFFF"
            }
          }
        }
      };
      
      return presetConfigs[preset] || presetConfigs.default;
    },
    
    /**
     * 图表点击事件
     */
    touchHandler(e) {
      if (this.properties.disableTouch) return;
      
      if (!this.data.uChart) return;
      
      // 获取点击位置
      let touches = e.touches[0];
      let canvasOffsetLeft = e.currentTarget.offsetLeft;
      let canvasOffsetTop = e.currentTarget.offsetTop;
      
      let x = touches.x - canvasOffsetLeft;
      let y = touches.y - canvasOffsetTop;
      
      // 触发图表事件
      let index = this.data.uChart.getCurrentDataIndex({x, y});
      
      // 如果点击了数据项
      if (index !== -1) {
        let item = this.data.uChart.getDataByIndex(index);
        // 触发点击事件
        this.triggerEvent('click', {
          index: index,
          item: item
        });
      }
    },
    
    /**
     * 双指缩放事件处理
     */
    touchStartHandler(e) {
      if (this.properties.disableTouch) return;
      if (e.touches.length > 1) {
        // 双指缩放
        let touches = e.touches;
        this.data.lastTouch = { x1: touches[0].x, y1: touches[0].y, x2: touches[1].x, y2: touches[1].y };
      } else {
        // 单指操作
        const now = Date.now();
        const touchTime = now - this.data.lastTapTime;
        
        // 双击事件(300ms内)
        if (touchTime < 300) {
          // 触发双击事件
          this.triggerEvent('dblclick', {});
        }
        this.data.lastTapTime = now;
      }
    },
    
    /**
     * 导出图表为图片
     */
    exportImage() {
      if (!this.data.uChart) return;
      
      const query = wx.createSelectorQuery().in(this);
      query.select('#' + this.properties.chartId)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res[0] || !res[0].node) {
            return;
          }
          
          const canvas = res[0].node;
          
          // 导出图片
          wx.canvasToTempFilePath({
            canvas: canvas,
            success: (res) => {
              this.triggerEvent('getImage', {
                tempFilePath: res.tempFilePath
              });
            },
            fail: (error) => {
              console.error('导出图片失败', error);
            }
          });
        });
    },
    
    /**
     * 重载图表
     */
    reload() {
      if (this.data.uChart) {
        this.data.uChart = null;
      }
      setTimeout(() => {
        this.initChart();
      }, 50);
    }
  }
}); 
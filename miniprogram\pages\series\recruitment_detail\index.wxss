/* 背景和容器样式优化 */
.content-scroll {
  padding: 20rpx;
  box-sizing: border-box;
}

.recruitment-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  /* margin: 40rpx 20rpx; */
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.recruitment-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.06);
}

.card-number {
  position: absolute;
  top: -25rpx;
  left: -25rpx;
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.3);
  border: 4rpx solid #fff;
}

.card-content {
  padding-left: 50rpx;
}

.card-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.card-info {
  margin-bottom: 30rpx;
  background: #f8fafd;
  padding: 20rpx;
  border-radius: 16rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  margin: 12rpx 0;
  display: flex;
  align-items: center;
}

.label {
  color: #888;
  min-width: 140rpx;
}

.card-action {
  text-align: right;
  margin-top: 20rpx;
}

.detail-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 44rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
}

.detail-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(74, 144, 226, 0.15);
}

/* 详情浮层优化 */
.detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.detail-popup.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 30rpx rgba(0, 0, 0, 0.1);
  max-height: 85vh;
}

.detail-popup.show .popup-content {
  transform: translateY(0);
}

.popup-close {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
  background: #f5f7fa;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.popup-close:active {
  background: #eef0f5;
}

.popup-title {
  font-size: 42rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 36rpx;
  padding-right: 80rpx;
  line-height: 1.4;
}

.popup-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 10rpx 0;
  margin: 0 -30rpx;
  padding: 0 30rpx;
}

.popup-scroll::-webkit-scrollbar {
  display: none;
}

.detail-section {
  margin-bottom: 40rpx;
  background: #f8fafd;
  padding: 30rpx;
  border-radius: 20rpx;
  position: relative;
  border: 2rpx solid rgba(74, 144, 226, 0.1);
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 6rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  letter-spacing: 0.5rpx;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 28rpx;
  background: #fff;
  color: #4a90e2;
  border-radius: 30rpx;
  margin: 12rpx 20rpx 12rpx 0;
  font-size: 26rpx;
  transition: all 0.2s ease;
  border: 2rpx solid rgba(74, 144, 226, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.05);
}

.tag:active {
  background: #f5f9ff;
  transform: scale(0.98);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
}

.info-grid .info-item {
  position: relative;
  padding: 16rpx 20rpx;
  background: #f8fafd;
  border-radius: 12rpx;
  margin: 0;
  flex-direction: column;
  align-items: flex-start;
}

.info-grid .label {
  color: #999;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  min-width: auto;
}

.info-grid .info-item text:not(.label) {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.popup-footer {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #eef0f5;
  position: relative;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.popup-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  height: 2rpx;
  background: linear-gradient(to right, 
    transparent, 
    rgba(74, 144, 226, 0.2), 
    rgba(74, 144, 226, 0.3), 
    rgba(74, 144, 226, 0.2), 
    transparent
  );
}

.apply-btn {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #357abd, #2c5c8f);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3),
              inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 4rpx;
}

.apply-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  border-radius: 48rpx 48rpx 0 0;
}

.apply-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, 
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.apply-btn:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2),
              inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #357abd, #2c5c8f, #234870);
}

.apply-btn:active::after {
  transform: translateX(100%);
}

.form-container {
  padding: 20rpx;
}

.form-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 6rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.input {
  width: 100%;
  height: 88rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  border: 2rpx solid #eef0f5;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2rpx rgba(74, 144, 226, 0.1);
}

.upload-desc {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 24rpx;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.upload-item {
  position: relative;
  width: 30%;
  height: 210rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.upload-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-text {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  color: #ff4d4f;
  font-size: 24rpx;
  text-align: center;
}

.file-name {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0) 100%);
  color: #fff;
  font-size: 22rpx;
  padding: 24rpx 10rpx 10rpx;
  box-sizing: border-box;
  text-align: center;
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  backdrop-filter: blur(2px);
}

.debug-info {
  display: none; /* 生产环境隐藏调试信息 */
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx;
  box-sizing: border-box;
  text-align: center;
  word-break: break-all;
  max-height: 60rpx;
  overflow: hidden;
  line-height: 1;
}

.delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  backdrop-filter: blur(4px);
  transform: scale(1);
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.delete-btn:active {
  background: rgba(255, 77, 79, 0.8);
  transform: scale(1.1);
}

.upload-btn {
  width: 30%;
  height: 210rpx;
  background: #f8fafd;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-bottom: 20rpx;
}

.upload-btn:active {
  background: #eef0f5;
  border-color: #4a90e2;
  transform: scale(0.98);
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.upload-btn:active .upload-icon {
  color: #4a90e2;
  transform: rotate(90deg);
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.submit-section {
  margin: 40rpx 0 60rpx;
  padding: 0 20rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 48rpx 48rpx 0 0;
}

.submit-btn:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
}

.submit-btn.disabled {
  background: #ccc;
  box-shadow: none;
  opacity: 0.8;
}

.btn-text {
  position: relative;
  z-index: 1;
  letter-spacing: 4rpx;
}

/* 招募详情样式 */
.recruitment-info {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: #fff;
  box-shadow: 0 8rpx 30rpx rgba(74, 144, 226, 0.2);
}

.info-header {
  margin-bottom: 24rpx;
}

.info-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.info-desc {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.6;
}

.info-stats {
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 30rpx;
  font-weight: 600;
}

/* 上传区域样式 */
.upload-section {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}

/* 表单项样式 */
.form-group {
  background: #f8fafd;
  border-radius: 16rpx;
  padding: 20rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 下拉选择器样式 */
.picker-box {
  position: relative;
}

.picker-content {
  width: 100%;
  height: 88rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  border: 2rpx solid #eef0f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.picker-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

.picker-text:empty {
  color: #999;
}

.picker-arrow {
  margin-left: 20rpx;
  color: #999;
  font-size: 24rpx;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

/* 弹出层样式 */
.picker-popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.picker-popup.show {
  visibility: visible;
  opacity: 1;
}

.picker-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.picker-panel {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.picker-popup.show .picker-panel {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #eef0f5;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.picker-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.picker-footer {
  padding: 20rpx 30rpx;
  display: flex;
  gap: 20rpx;
  border-top: 2rpx solid #eef0f5;
}

.picker-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.picker-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.picker-btn.confirm {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
}

/* 复选框样式优化 */
.checkbox-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.checkbox-item:last-child {
  border-bottom: none;
}

.checkbox-item checkbox {
  margin-right: 16rpx;
}

.checkbox-text {
  font-size: 30rpx;
  color: #333;
}

.checkbox-item.checked {
  background: rgba(74, 144, 226, 0.05);
}

.checkbox-item.checked .checkbox-text {
  color: #4a90e2;
}

/* 必填标记 */
.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 输入框占位符样式 */
.input::placeholder,
.textarea::placeholder {
  color: #999;
}

/* 禁用状态样式 */
.input:disabled,
.textarea:disabled,
.picker-content.disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* 错误状态样式 */
.error .input,
.error .textarea,
.error .picker-content {
  border-color: #ff4d4f;
}

.error-message {
  color: #ff4d4f;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 表单布局优化 */
.form-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 6rpx;
}

/* 提交按钮样式优化 */
.submit-section {
  margin: 40rpx 0 60rpx;
  padding: 0 20rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn.disabled {
  background: #ccc;
  box-shadow: none;
  opacity: 0.8;
}

.btn-text {
  position: relative;
  z-index: 1;
  letter-spacing: 4rpx;
}

/* 复选框组样式 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 16rpx;
  background: #fff;
  border-radius: 12rpx;
  border: 2rpx solid #eef0f5;
}

.checkbox-item {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8fafd;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.checkbox-item checkbox {
  margin-right: 8rpx;
  transform: scale(0.8);
}

.checkbox-text {
  font-size: 28rpx;
  color: #333;
}

/* 选中状态 */
.checkbox-item.checked {
  background: rgba(74, 144, 226, 0.1);
}

.checkbox-item.checked .checkbox-text {
  color: #4a90e2;
}

.upload-placeholder {
  width: 30%;
  height: 0;
}

/* 新的表单组样式 */
.form-group-container {
  background: #fff;
  border-radius: 16rpx;
  padding: 0;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.form-group-title {
  font-size: 28rpx;
  color: #888;
  padding: 24rpx 30rpx 10rpx;
  background: #f8fafd;
  position: relative;
}

.form-group-title.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}

.form-group-content {
  padding: 20rpx 30rpx;
}

.form-row {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-row:last-child {
  border-bottom: none;
}

.form-row-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.form-row-label.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}

.form-row-content {
  flex: 1;
  margin-left: 30rpx;
}

.form-input {
  width: 100%;
  height: 76rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  padding: 0;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  padding: 0;
  line-height: 1.6;
}

.form-picker {
  width: 100%;
  height: 76rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
}

.picker-placeholder {
  color: #999;
}

.picker-value {
  color: #333;
  width: 85%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-arrow {
  color: #bbb;
  font-size: 24rpx;
  margin-left: 8rpx;
}

/* 上传作品部分样式优化 */
.upload-container {
  margin-top: 20rpx;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.upload-card {
  position: relative;
  aspect-ratio: 1;
  background: #f8fafd;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.upload-card:active {
  transform: scale(0.98);
}

.upload-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-card-add {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 2rpx dashed #ddd;
  box-sizing: border-box;
}

.upload-add-icon {
  font-size: 60rpx;
  color: #bbb;
  font-weight: 300;
  margin-bottom: 10rpx;
}

.upload-add-text {
  font-size: 24rpx;
  color: #999;
}

.upload-delete {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  z-index: 10;
}

/* 表单提交按钮样式优化 */
.submit-button {
  margin-top: 60rpx;
  margin-bottom: 80rpx;
  padding: 0 30rpx;
}

.submit-button button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  letter-spacing: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.25);
  border: none;
  position: relative;
  overflow: hidden;
}

.submit-button button::after {
  display: none;
}

.submit-button button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.2);
}

.submit-button button.disabled {
  background: #cccccc;
  color: #ffffff;
  box-shadow: none;
}

/* 招募信息卡片优化 */
.recruitment-card {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(74, 144, 226, 0.2);
}


.recruitment-card-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.recruitment-card-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.recruitment-card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.recruitment-card-item {
  padding: 20rpx;
  text-align: center;
  border-right: 1rpx solid rgba(255, 255, 255, 0.1);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.recruitment-card-item:nth-child(2n) {
  border-right: none;
}

.recruitment-card-item:nth-last-child(1),
.recruitment-card-item:nth-last-child(2) {
  border-bottom: none;
}

.recruitment-card-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.recruitment-card-value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 添加输入提示样式 */
.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 错误提示样式 */
.input-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 上传按钮文字调整 */
.upload-add-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 0 10rpx;
}

/* 必填项标记调整 */
.form-row-label.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 输入字段校验失败样式 */
.form-input.error, 
.form-textarea.error,
.form-picker.error {
  border-bottom: 1rpx solid #ff4d4f;
}

/* 多选上传文件样式 */
.upload-card {
  position: relative;
  aspect-ratio: 1;
  background: #f8fafd;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.upload-card-add {
  border: 2rpx dashed #ddd;
  background: #f9f9f9;
}

.upload-card-add:active {
  background: #f0f0f0;
  border-color: #4a90e2;
}

/* 数量控制器样式 */
.counter-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 76rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}

.counter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76rpx;
  height: 76rpx;
  font-size: 38rpx;
  color: #4a90e2;
  background-color: #f0f0f0;
  transition: all 0.2s;
  user-select: none;
}

.counter-btn:active {
  background-color: #e5e5e5;
  color: #357abd;
}

.counter-btn.disabled {
  color: #ccc;
  background-color: #f5f5f5;
  pointer-events: none;
}

.counter-input {
  flex: 1;
  height: 76rpx;
  line-height: 76rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  background-color: #f9f9f9;
  border: none;
  margin: 0;
  padding: 0;
}

.counter-input.error {
  color: #ff4d4f;
}

/* 错误提示样式增强 */
.input-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 错误状态输入框样式 */
.form-input.error {
  color: #ff4d4f;
  border-bottom: 1rpx solid #ff4d4f;
}

/* 错误提示样式 */
.input-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 提示文字样式 */
.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 表单提示信息统一样式 */
.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 错误提示统一样式 */
.input-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
}

/* 选择器错误状态 */
.form-picker.error {
  border-bottom: 1rpx solid #ff4d4f;
}

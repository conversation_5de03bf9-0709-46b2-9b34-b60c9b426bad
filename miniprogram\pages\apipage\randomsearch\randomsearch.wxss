.refresh-button-container {
  padding: 30rpx 20rpx;
  display: flex;
  justify-content: center;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a90e2;
  color: white;
  border-radius: 40rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.refresh-button.counting {
  background: #a0a0a0;
  min-width: 120rpx;
}

.countdown {
  font-size: 32rpx;
  font-weight: bold;
}

.refresh-button[disabled] {
  background: #a0a0a0;
  box-shadow: none;
}

.refresh-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.refresh-button:not([disabled]):active {
  transform: scale(0.95);
}

.image-container {
  padding: 20rpx;
}

.image-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.image-wrapper {
  position: relative;
  width: 100%;
  min-height: 400rpx;
  background: #f5f5f5;
  overflow: hidden;
}

.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  background: rgba(245, 245, 245, 0.9);
  backdrop-filter: blur(10px);
}

.main-image {
  width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease-in-out;
}

.main-image.blur {
  filter: blur(10px);
  transform: scale(1.1);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(245, 245, 245, 0.9);
  backdrop-filter: blur(10px);
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  width: 80%;
  max-width: 500rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.loading-step {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  background: rgba(74, 144, 226, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background: #4a90e2;
  border-radius: 3rpx;
  transition: width 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.progress-inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.loading-message {
  width: 100%;
  text-align: center;
  background: rgba(74, 144, 226, 0.05);
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
}

.image-info {
  padding: 20rpx;
}

.artist-info {
  margin-bottom: 20rpx;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.image-stats {
  display: flex;
  margin-bottom: 20rpx;
}

.stat-item {
  margin-right: 30rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  gap: 10rpx;
}

.tag {
  background: #f0f0f0;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
}

.links {
  display: flex;
  gap: 20rpx;
}

.link-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  padding: 12rpx 0;
  background: #4a90e2;
  color: white;
  border-radius: 10rpx;
  text-align: center;
  line-height: 1.5;
}

.link-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .image-card {
    background: rgba(30, 30, 30, 0.9);
  }
  
  .image-wrapper {
    background: #333;
  }
  
  .image-loading {
    background: rgba(30, 30, 30, 0.9);
  }
  
  .loading-progress {
    background: rgba(30, 30, 30, 0.9);
  }
  
  .loading-step {
    color: #999;
    background: rgba(74, 144, 226, 0.2);
  }
  
  .loading-message {
    background: rgba(74, 144, 226, 0.1);
  }
  
  .progress-bar {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .progress-inner {
    background: #2d5a8e;
  }
  
  .progress-text {
    color: #999;
  }
  
  .loading-text {
    color: #999;
  }
  
  .label {
    color: #999;
  }
  
  .value {
    color: #fff;
  }
  
  .tag {
    background: #333;
    color: #fff;
  }

  .refresh-button {
    background: #2d5a8e;
  }

  .refresh-button[disabled],
  .refresh-button.counting {
    background: #4a4a4a;
  }

  .refresh-button-container {
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .loading-state {
    background: rgba(30, 30, 30, 0.9);
  }
}

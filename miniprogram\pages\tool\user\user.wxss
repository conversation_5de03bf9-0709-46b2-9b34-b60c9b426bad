.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.layout-debug {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.section {
  margin-bottom: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2e5df8;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #eee;
}

.info-item, .style-item {
  display: flex;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.label {
  color: #666;
  min-width: 200rpx;
  margin-right: 20rpx;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.style-item {
  flex-direction: column;
}

.style-item .label {
  margin-bottom: 8rpx;
}

.style-item .value {
  background: #f5f7fa;
  padding: 12rpx;
  border-radius: 8rpx;
  font-family: monospace;
  font-size: 24rpx;
}

/* test.wxss */
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading {
  color: #999;
  font-size: 28rpx;
}

/* 导航菜单样式 */
.nav-section {
  padding: 20rpx;
}

.nav-menu {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.nav-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 工具列表样式 */
.tool-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

.tool-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.tool-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.feature-item {
  font-size: 22rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.user-info-card {
  margin: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.auth-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
  margin: 0 0 20rpx 0;
  text-align: center;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #ffffff;
  color: #07c160;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
  margin: 0;
  text-align: center;
  border: 2rpx solid #07c160;
}

.auth-btn::after,
.login-btn::after {
  border: none;
}

.auth-btn:active,
.login-btn:active {
  opacity: 0.8;
}

.user-info-section {
  width: 100%;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  width: 160rpx;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  word-break: break-all;
}

.user-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.gender {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.user-info-detail {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
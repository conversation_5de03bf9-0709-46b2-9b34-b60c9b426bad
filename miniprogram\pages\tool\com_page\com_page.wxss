.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 浮动层按钮样式 */
.float-btn-wrapper {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.float-btn {
  background: #4CAF50;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 浮动层样式 */
.float-layer-container {
  position: fixed;
  top: var(--top-nav-height);
  left: 0;
  width: 100%;
  height: var(--content-area-height);
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.float-layer-container.show {
  visibility: visible;
  opacity: 1;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.float-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.12);
}

.float-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 32rpx;
  font-weight: 500;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.float-body {
  padding: 30rpx;
  min-height: 200rpx;
  font-size: 28rpx;
  color: #333;
}

/* 颜色选择器展示区域样式 */
.color_demo {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.color_demo_header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.color_demo_icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.color_demo_title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.color_demo_scroll {
  width: 100%;
  white-space: nowrap;
}

.color_demo_list {
  display: inline-flex;
  padding: 10rpx 0;
}

.color_demo_item {
  display: inline-block;
  margin-right: 30rpx;
  min-width: 200rpx;
  text-align: center;
}

.color_demo_item:last-child {
  margin-right: 0;
}

.color_demo_item_title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.color_demo_float {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.color_demo_btn {
  background: #4CAF50;
  color: white;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  width: auto !important;
  display: inline-block;
}

/* 字体选择器展示区域样式 */
.font_demo {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.font_demo_header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.font_demo_icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.font_demo_title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.font_demo_scroll {
  width: 100%;
  white-space: nowrap;
}

.font_demo_list {
  display: inline-flex;
  padding: 10rpx 0;
}

.font_demo_item {
  display: inline-block;
  margin-right: 30rpx;
  min-width: 200rpx;
  text-align: center;
}

.font_demo_item:last-child {
  margin-right: 0;
}

.font_demo_item_title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.font_demo_float {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.font_demo_btn {
  background: #4CAF50;
  color: white;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  width: auto !important;
  display: inline-block;
}

/* 纸张选择器展示区域样式 */
.paper_demo {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.paper_demo_header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.paper_demo_icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.paper_demo_title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.paper_demo_scroll {
  width: 100%;
  white-space: nowrap;
}

.paper_demo_list {
  display: inline-flex;
  padding: 10rpx 0;
}

.paper_demo_item {
  display: inline-block;
  margin-right: 30rpx;
  min-width: 200rpx;
  text-align: center;
}

.paper_demo_item:last-child {
  margin-right: 0;
}

.paper_demo_item_title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.paper_demo_float {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.paper_demo_btn {
  background: #4CAF50;
  color: white;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  width: auto !important;
  display: inline-block;
}

/* 裁剪工具展示区域样式 */
.cropper_demo {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.cropper_demo_header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.cropper_demo_icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.cropper_demo_title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.cropper_demo_content {
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-btn {
  background: #4CAF50;
  color: white;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  width: auto !important;
  display: inline-block;
  margin: 20rpx 0;
}

.result-area {
  width: 100%;
  text-align: center;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12rpx;
}

.result-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.result-image {
  width: 100%;
  height: 400rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background: #fff;
  object-fit: contain;
}

.result-image:active {
  opacity: 0.8;
}

.result-area .upload-btn {
  margin-top: 0;
}

/* 裁剪组件容器样式 */
.cropper-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.cropper-container.show {
  opacity: 1;
  visibility: visible;
}

/* 确保裁剪组件占满容器 */
.cropper-container wxmy-cropper {
  width: 100%;
  height: 100%;
  display: block;
}

/* 裁剪结果对比展示样式 */
.compare-area {
  width: 100%;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12rpx;
}

.image-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  text-align: center;
}

.preview-image {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background: #fff;
  object-fit: contain;
}

.preview-image:active {
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  max-width: 200rpx;
  font-size: 28rpx;
  padding: 12rpx 0;
  border-radius: 8rpx;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  border: none;
}

.action-btn.primary {
  background: #4CAF50;
  color: white;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

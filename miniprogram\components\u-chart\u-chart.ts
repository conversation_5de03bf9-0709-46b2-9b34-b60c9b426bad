// 引入图表库和处理函数
const uCharts = require('./u-charts.min.js');

interface ChartDataItem {
  name: string;
  data?: number[];
  value?: number;
  color?: string;
  type?: string;
}

interface ChartData {
  categories?: string[];
  series?: ChartDataItem[];
  indicators?: string[];
  radarIndicators?: any[];
  tooltip?: any;
  extra?: any;
  radar?: any;
  [key: string]: any;
}

interface ChartOptions {
  padding?: number[];
  enableScroll?: boolean;
  animation?: boolean;
  duration?: number;
  timing?: string;
  rotate?: boolean;
  rotateLock?: boolean;
  dataLabel?: boolean;
  dataPointShape?: boolean;
  dataPointShapeType?: string;
  touchMoveLimit?: number;
  background?: string;
  fontColor?: string;
  xAxis?: any;
  yAxis?: any;
  extra?: any;
  color?: string[];
  radar?: any;
  [key: string]: any;
}

// 自定义触摸事件接口，避免与内置TouchEvent冲突
interface ChartTouchEvent {
  touches: Array<{
    x: number;
    y: number;
  }>;
  currentTarget: {
    offsetLeft: number;
    offsetTop: number;
  };
}

Component({
  options: {
    addGlobalClass: true
  },
  
  properties: {
    // 图表类型: column, line, area, pie, ring, radar, gauge, candle等
    type: {
      type: String,
      value: 'column'
    },
    // 图表数据
    chartData: {
      type: Object,
      value: {},
      observer: function(newVal: ChartData, oldVal: ChartData) {
        if (this._isObjectValueEqual(newVal, oldVal)) return;
        this._drawChartByNewData(newVal);
      }
    },
    // 图表配置
    opts: {
      type: Object,
      value: {}
    },
    // 图表宽度
    width: {
      type: Number,
      value: 750
    },
    // 图表高度
    height: {
      type: Number,
      value: 500
    },
    // 图表ID
    canvasId: {
      type: String,
      value: 'uchart'
    },
    // 是否禁用缩放
    disableScroll: {
      type: Boolean,
      value: false
    },
    // 是否启用图表点击
    enableClick: {
      type: Boolean,
      value: true
    },
    // 样式预设
    preset: {
      type: String,
      value: 'default',
      observer: function(newVal: string, oldVal: string) {
        if (newVal === oldVal) return;
        this._applyPreset(newVal);
      }
    },
    // 是否显示加载
    loading: {
      type: Boolean,
      value: false
    }
  },
  
  data: {
    pixelRatio: 1,
    cWidth: 375,
    cHeight: 250,
    inited: false,
    // 标记是否已经处理过初始数据，避免重复绘制
    initialDataProcessed: false
  },
  
  lifetimes: {
    attached() {
      // 设置默认样式预设，但不立即绘制
      this._applyPresetWithoutRedraw(this.data.preset);
    },
    
    ready() {
      // 初始化图表并直接使用传入的数据，避免先显示默认数据
      this._initChart();
    }
  },
  
  methods: {
    /**
     * 初始化图表
     */
    _initChart() {
      const query = wx.createSelectorQuery().in(this);
      query.select('#' + this.data.canvasId)
        .fields({ node: true, size: true })
        .exec(res => {
          if (!res[0] || !res[0].node) {
            console.error('获取图表Canvas节点失败');
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          const pixelRatio = wx.getSystemInfoSync().pixelRatio;
          canvas.width = res[0].width * pixelRatio;
          canvas.height = res[0].height * pixelRatio;
          ctx.scale(pixelRatio, pixelRatio);
          
          this.setData({
            pixelRatio,
            cWidth: res[0].width,
            cHeight: res[0].height,
            inited: true
          });
          
          // 初始化图表
          this.ctx = ctx;
          this.canvas = canvas;
          
          // 绘制图表（如果有数据）
          if (this.data.chartData && Object.keys(this.data.chartData).length > 0) {
            this._drawChart(this.data.chartData);
            this.setData({ initialDataProcessed: true });
          }
          
          // 触发初始化完成事件
          this.triggerEvent('inited', {
            canvas,
            width: res[0].width,
            height: res[0].height
          });
        });
    },
    
    /**
     * 绘制图表
     */
    _drawChart(chartData: ChartData) {
      if (!this.ctx || !this.data.inited) {
        console.warn('图表尚未初始化完成，无法绘制');
        return;
      }
      
      const { type, opts, cWidth, cHeight, loading } = this.data;
      
      if (loading) {
        console.log('图表加载中，跳过绘制');
        return;
      }
      
      // 清空画布
      this.ctx.clearRect(0, 0, cWidth, cHeight);
      
      // 合并配置
      const defaultOpts = this._getDefaultOpts();
      const mergedOpts = Object.assign({}, defaultOpts, opts);
      
      // 为特定图表类型应用额外配置
      let config: any = {
        type,
        context: this.ctx,
        width: cWidth,
        height: cHeight,
        series: chartData.series || [],
        categories: chartData.categories || [],
        animation: true,
        background: '#FFFFFF',
        padding: [15, 15, 0, 15],
        enableScroll: true,
        ...mergedOpts,
      };
      
      // 特殊处理雷达图的配置
      if (type === 'radar') {
        // 将chartData中的radarIndicators复制到配置中
        if (chartData.radarIndicators) {
          config.categories = chartData.categories || []; // 确保类别标签正确
          
          // 优化雷达图指标配置，确保每个指标都包含必要信息
          const indicators = chartData.radarIndicators.map(item => ({
            text: item.text,
            max: item.max || 100,
            color: item.color || '#666666'  // 确保每个指标都有颜色
          }));
          
          config.radar = { 
            indicator: indicators,
            ...((opts && opts.radar) || {})
          };
          
          // 将indicators的text抽取出来放入categories，兼容旧版绘制逻辑
          config.categories = indicators.map(item => item.text);
        }
        
        // 处理雷达图的额外配置，强制启用标签显示
        config.extra = {
          ...(config.extra || {}),
          radar: {
            ...(config.extra?.radar || {}),
            ...(chartData.extra?.radar || {}),
            labelShow: true,  // 强制显示标签
            fontSize: (chartData.extra?.radar?.fontSize || opts?.radar?.fontSize || 18),  // 设置字体大小
            labelPadding: (chartData.extra?.radar?.labelPadding || opts?.radar?.labelPadding || 15),  // 设置标签距离
          }
        };
      }
      
      // 创建图表实例
      try {
        if (this.chart) {
          // 更新数据时合并原有配置
          const updateConfig = {
            series: chartData.series || [],
            categories: chartData.categories || [],
            ...chartData
          };
          
          // 特殊处理雷达图的更新
          if (type === 'radar') {
            // 确保雷达图的indicator配置正确
            const radarIndicators = chartData.radarIndicators || this.chart.opts?.radar?.indicator || [];
            
            updateConfig.radar = { 
              indicator: radarIndicators.map((item: any) => ({
                text: item.text,
                max: item.max || 100,
                color: item.color || '#666666'  // 确保每个指标都有颜色
              })),
              ...((opts && opts.radar) || {})
            };
            
            // 确保categories包含指标文本，用于标签显示
            if (radarIndicators.length > 0) {
              updateConfig.categories = radarIndicators.map((item: any) => item.text);
            }
            
            // 更新雷达图额外配置
            updateConfig.extra = {
              ...(updateConfig.extra || {}),
              radar: {
                ...(chartData.extra?.radar || {}),
                ...(updateConfig.extra?.radar || {}),
                labelShow: true
              }
            };
          }
          
          this.chart.updateData(updateConfig);
        } else {
          this.chart = new uCharts(config);
        }
      } catch (error) {
        console.error('绘制图表失败', error);
      }
    },
    
    /**
     * 获取默认图表配置
     */
    _getDefaultOpts(): ChartOptions {
      const baseOpts: ChartOptions = {
        padding: [15, 15, 0, 15],
        enableScroll: !this.data.disableScroll,
        animation: true,
        duration: 1000,
        timing: 'easeOut',
        rotate: false,
        rotateLock: false,
        dataLabel: true,
        dataPointShape: true,
        dataPointShapeType: 'solid',
        touchMoveLimit: 60
      };
      
      // 根据图表类型返回不同的默认配置
      switch (this.data.type) {
        case 'radar':
          return {
            ...baseOpts,
            // 雷达图默认配置
            extra: {
              radar: {
                gridType: 'polygon',
                gridColor: '#CCCCCC',
                gridCount: 5,
                labelColor: '#666666',
                opacity: 0.2,
                border: true
              }
            }
          };
        default:
          return baseOpts;
      }
    },
    
    /**
     * 应用样式预设（不重绘）
     * 仅在初始化时应用预设配置，但不触发重绘
     */
    _applyPresetWithoutRedraw(preset: string) {
      let opts: ChartOptions = {};
      
      switch (preset) {
        case 'dark':
          opts = {
            background: '#222222',
            padding: [15, 15, 0, 15],
            fontColor: '#CCCCCC',
            xAxis: {
              fontColor: '#CCCCCC',
              axisLineColor: '#444444'
            },
            yAxis: {
              fontColor: '#CCCCCC',
              axisLineColor: '#444444'
            },
            extra: {
              column: {
                linearType: 'custom'
              },
              radar: {
                gridColor: '#444444',
                labelColor: '#CCCCCC'
              }
            }
          };
          break;
        
        case 'businessBlue':
          opts = {
            background: '#F2F6FC',
            fontColor: '#333333',
            padding: [15, 15, 0, 15],
            xAxis: {
              fontColor: '#666666'
            },
            yAxis: {
              fontColor: '#666666'
            },
            extra: {
              column: {
                linearType: 'none'
              },
              radar: {
                gridColor: '#CCE6FF',
                labelColor: '#333333'
              }
            },
            color: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#6DC8EC']
          };
          break;
        
        case 'colorful':
          opts = {
            background: '#FFFFFF',
            padding: [15, 15, 0, 15],
            color: ['#FF6B3B', '#626681', '#FFC100', '#9FB40F', '#76523B', '#DAD5B5', '#0E8E89', '#E19348', '#F383A2', '#247FEA'],
            extra: {
              column: {
                linearType: 'custom'
              },
              pie: {
                activeOpacity: 0.9,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15
              },
              radar: {
                gridColor: '#EEEEEE',
                labelColor: '#333333'
              }
            }
          };
          break;
          
        default: // default预设
          opts = {
            background: '#FFFFFF',
            padding: [15, 15, 0, 15],
            color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
            extra: {
              column: {
                linearType: 'none'
              },
              radar: {
                gridColor: '#CCCCCC',
                labelColor: '#666666'
              }
            }
          };
      }
      
      this.setData({
        opts: { ...this.data.opts, ...opts }
      });
    },
    
    /**
     * 应用样式预设并重绘
     */
    _applyPreset(preset: string) {
      this._applyPresetWithoutRedraw(preset);
      
      // 如果图表已经初始化，重新绘制
      if (this.chart && this.data.chartData && Object.keys(this.data.chartData).length > 0) {
        this._drawChart(this.data.chartData);
      }
    },
    
    /**
     * 根据新数据重新绘制图表
     */
    _drawChartByNewData(newData: ChartData) {
      if (!this.data.inited) {
        return;
      }
      
      // 避免在页面加载时重复绘制
      if (!this.data.initialDataProcessed && this.chart) {
        this.setData({ initialDataProcessed: true });
      }
      
      // 特殊处理雷达图数据 - 确保radarIndicators在更新时被保留
      if (this.data.type === 'radar' && this.chart && this.chart.opts && this.chart.opts.radar) {
        // 如果新数据中没有radarIndicators，但图表实例有，则使用图表实例中的配置
        if (!newData.radarIndicators && this.chart.opts.radar.indicator) {
          newData.radarIndicators = this.chart.opts.radar.indicator;
        }
      }
      
      this._drawChart(newData);
    },
    
    /**
     * 触摸事件处理
     */
    touchStart(e: any) {
      if (this.chart && e.touches && e.touches.length > 0) {
        this.chart.touchLegend(e);
        this.chart.scrollStart(e);
      }
    },
    
    touchMove(e: any) {
      if (this.chart && e.touches && e.touches.length > 0) {
        this.chart.scroll(e);
      }
    },
    
    touchEnd(e: any) {
      if (this.chart) {
        this.chart.scrollEnd(e);
        
        // 触发图表点击事件
        if (this.data.enableClick) {
          const currentIndex = this.chart.getCurrentDataIndex(e);
          const item = this.chart.getToolTipData(e);
          
          if (typeof currentIndex !== 'undefined' && currentIndex > -1) {
            this.triggerEvent('click', {
              index: currentIndex,
              item
            });
          }
        }
      }
    },
    
    /**
     * 比较两个对象值是否相等
     */
    _isObjectValueEqual(a: any, b: any): boolean {
      if (!a || !b) return false;
      if (a === b) return true;
      
      const aProps = Object.keys(a);
      const bProps = Object.keys(b);
      
      if (aProps.length !== bProps.length) return false;
      
      return JSON.stringify(a) === JSON.stringify(b);
    }
  }
}); 
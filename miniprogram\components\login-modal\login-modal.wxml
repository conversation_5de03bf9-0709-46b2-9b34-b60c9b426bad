<wxs src="../../utils/constants.wxs" module="constants" />

<view class="login_modal {{show ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
  <view class="login_mask" catchtouchmove="preventDefault"></view>
  <view class="login_content"> 
    <!-- <view class="login_close" bindtap="onClose">×</view> -->
    <view class="login_title">{{modalTitle}}</view>
    <view class="login_form">
      <block wx:if="{{isSettingInfo}}">
        <block wx:if="{{needSetAvatar}}">
          <button class="login_avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
            <image class="login_avatar" src="{{tempUserInfo.avatarUrl || userInfo.avatar || constants.COMMON_ASSETS.DEFAULT_AVATAR}}" mode="aspectFill"></image>
            <text>点击选择头像</text>
          </button>
        </block>
        <block wx:if="{{needSetNickname}}">
          <input type="nickname" class="login_nickname-input" placeholder="请输入昵称" bindinput="onInputNickname" value="{{tempUserInfo.nickName || userInfo.nickname}}" />
        </block>
        <button class="login_btn {{canLogin && hasAgreedPrivacy ? '' : 'disabled'}}" bindtap="handleLogin">确认</button>
      </block>
      <block wx:else>
        <button class="login_btn {{hasAgreedPrivacy ? '' : 'disabled'}}" bindtap="handleLogin">登录</button>
      </block>
    </view>
    <view class="login_privacy">
      <checkbox-group bindchange="onPrivacyCheckboxChange">
        <label class="privacy-checkbox">
          <checkbox value="agreed" checked="{{hasAgreedPrivacy}}" />
          <text>我已阅读并同意</text>
          <text class="login_link" bindtap="handlePrivacyShow">《用户隐私保护指引》</text>
        </label>
      </checkbox-group>
    </view>
  </view>
</view> 
<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="图像打格" showBack="{{true}}" showMore="">顶部导航</nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
        <!-- 内容区域开始 -->
        <view class="upload-section"  wx:if="{{!croppedImage}}">
          <button bindtap="handleUpload" class="upload-btn">点击上传图片</button>
        </view>
        <view class="preview-section" wx:if="{{croppedImage}}">
          <view class="preview-container">
            <image class="preview-image" src="{{croppedImage}}" mode="widthFix"></image>
            <view class="preview-grid">
              <view class="grid-content">
                <!-- 横向坐标 -->
                <view class="grid-coordinates" 
                      wx:if="{{coordinateSettings.horizontalPosition !== 2}}"
                      style="top:{{coordinateSettings.horizontalPosition === 0 ? '0' : 'auto'}};bottom:{{coordinateSettings.horizontalPosition === 1 ? '0' : 'auto'}};height:calc(100% / {{gridSettings.rows}})">
                  <view class="coordinate-cell" 
                        wx:for="{{coordinates}}" 
                        wx:key="*this"
                        style="width:calc(100% / {{gridSettings.columns}});font-size:{{coordinateSettings.fontSize}}rpx;color:{{coordinateSettings.color}};opacity:calc({{coordinateSettings.opacity}} / 100);padding-bottom:{{coordinateSettings.horizontalPosition === 1 ? '4rpx' : '0'}};padding-top:{{coordinateSettings.horizontalPosition === 0 ? '4rpx' : '0'}};display:flex;justify-content:center;align-items:{{coordinateSettings.horizontalPosition === 1 ? 'flex-end' : 'flex-start'}}">
                    {{item}}
                  </view>
                </view>
                <!-- 纵向坐标 -->
                <view class="vertical-coordinates"
                      wx:if="{{coordinateSettings.verticalPosition !== 2}}"
                      style="left:{{coordinateSettings.verticalPosition === 0 ? '0' : 'auto'}};right:{{coordinateSettings.verticalPosition === 1 ? '0' : 'auto'}};width:calc(100% / {{gridSettings.columns}})">
                  <view class="vertical-cell"
                        wx:for="{{verticalCoordinates}}"
                        wx:key="*this"
                        style="height:calc(100% / {{gridSettings.rows}});top:calc({{index}} * (100% / {{gridSettings.rows}}));font-size:{{coordinateSettings.fontSize}}rpx;color:{{coordinateSettings.color}};opacity:calc({{coordinateSettings.opacity}} / 100);padding:0 {{coordinateSettings.verticalPosition === 1 ? '8rpx' : '0'}} 0 {{coordinateSettings.verticalPosition === 0 ? '8rpx' : '0'}};display:flex;justify-content:{{coordinateSettings.verticalPosition === 1 ? 'flex-end' : 'flex-start'}};align-items:center">
                    {{item}}
                  </view>
                </view>
                <!-- 网格线 -->
                <view class="grid-lines">
                  <view class="grid-line-horizontal" 
                        wx:for="{{gridSettings.rows - 1}}" 
                        wx:key="*this"
                        style="top:calc(({{index}} + 1) * (100% / {{gridSettings.rows}})); background-color:{{gridSettings.color}}; opacity:calc({{gridSettings.opacity}} / 100);">
                  </view>
                  <view class="grid-line-vertical" 
                        wx:for="{{gridSettings.columns - 1}}" 
                        wx:key="*this"
                        style="left:calc(({{index}} + 1) * (100% / {{gridSettings.columns}})); background-color:{{gridSettings.color}}; opacity:calc({{gridSettings.opacity}} / 100);">
                  </view>
                </view>
                <!-- 点击区域 -->
                <view class="grid-click-areas">
                  <view wx:for="{{gridSettings.rows}}" 
                        wx:for-item="row"
                        wx:for-index="rowIndex"
                        wx:key="row">
                    <view class="grid-cell {{isAnnotated && (annotatedCell.row === rowIndex || annotatedCell.col === index) && !(annotatedCell.row === rowIndex && annotatedCell.col === index) ? 'highlighted' : ''}}" 
                          wx:for="{{gridSettings.columns}}" 
                          wx:for-item="col"
                          wx:key="col"
                          data-row="{{rowIndex}}"
                          data-col="{{index}}"
                          bindtap="onGridCellTap"
                          style="width:calc(100% / {{gridSettings.columns}}); height:calc(100% / {{gridSettings.rows}}); top:calc({{rowIndex}} * (100% / {{gridSettings.rows}})); left:calc({{index}} * (100% / {{gridSettings.columns}})); {{isAnnotated && (annotatedCell.row === rowIndex || annotatedCell.col === index) && !(annotatedCell.row === rowIndex && annotatedCell.col === index) ? 'background-color:' + annotationSettings.color + ';opacity:calc(' + annotationSettings.opacity + ' / 100)' : ''}}">
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="config-section" wx:if="{{croppedImage}}">
          <view class="config-button-group">
            <view class="button-row">
              <view class="feature-btn" bindtap="showSizeCalculator">
                <text class="feature-icon">📏</text>
                <text class="feature-text">计算尺寸</text>
              </view>
              <view class="feature-btn" bindtap="toggleMainSettings">
                <text class="feature-icon">⚙️</text>
                <text class="feature-text">网格设置</text>
              </view>
            </view>
            <view class="button-row">
              <view class="feature-btn" bindtap="downloadGridOnly">
                <text class="feature-icon">⊞</text>
                <text class="feature-text">导出网格</text>
              </view>
              <view class="feature-btn" bindtap="downloadCombinedImage">
                <text class="feature-icon">⬇️</text>
                <text class="feature-text">导出完整图</text>
              </view>
            </view>
            <view class="button-row">
              <view class="feature-btn primary" bindtap="handleUpload">
                <text class="feature-icon">📸</text>
                <text class="feature-text">重新上传图片</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明开始 -->
        <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}grid.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">图像网格工具</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">自定义网格</text>
                    <text class="feature-desc">支持自由调整行列数，灵活设置网格大小</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">坐标标注</text>
                    <text class="feature-desc">显示网格坐标，便于定位和参考</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">区域放大</text>
                    <text class="feature-desc">支持单格放大查看，细节更清晰</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">尺寸计算</text>
                    <text class="feature-desc">自动计算实际打印尺寸，轻松掌握比例</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">上传图片</text>
                    <text class="step-desc">选择并裁剪需要添加网格的图片</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">调整网格</text>
                    <text class="step-desc">设置网格的行数和列数，调整网格样式</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">查看效果</text>
                    <text class="step-desc">预览网格效果，可以放大单个格子查看细节</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">导出图片</text>
                    <text class="step-desc">保存处理后的图片或单个放大的格子</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">合理分割</text>
                  <text class="tip-text">根据图片内容选择合适的网格数量，避免过密或过疏</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">对齐参考</text>
                  <text class="tip-text">利用坐标标注进行位置定位，保证绘画准确性</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">细节处理</text>
                  <text class="tip-text">使用放大功能仔细观察重要细节部分</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">尺寸把控</text>
                  <text class="tip-text">使用尺寸计算功能，确保作品比例准确</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
        <!-- 标注设置面板 -->
        <view class="annotation-settings {{showAnnotationSettings ? 'show' : ''}}" catch:tap="preventBubble">
          <view class="annotation-settings-header">
            <text class="text">标注设置</text>
            <view class="close-settings" catch:tap="toggleAnnotationSettings">×</view>
          </view>
          <view class="annotation-settings-content">
            <view class="settings-item">
              <text class="text">颜色：</text>
              <color-picker 
                value="{{annotationSettings.color}}" 
                bindconfirm="onAnnotationColorConfirm"
                showPresets="{{true}}"
              />
            </view>
          </view>
        </view>
        <!-- 操作选择浮层 -->
        <view class="action-popup {{showActionPopup ? 'show' : ''}}" bindtap="hideActionPopup">
          <view class="action-content" catch:tap="preventBubble">
            <view class="action-info">
              <text class="coordinate-text">{{currentCell.coordinate}}</text>
            </view>
            <view class="action-buttons">
              <button class="action-btn {{isAnnotated && currentCell.row === annotatedCell.row && currentCell.col === annotatedCell.col ? 'active' : ''}}" 
                      bindtap="handleAnnotation">
                <text class="action-icon">✏️</text>
                <text class="text">{{isAnnotated && currentCell.row === annotatedCell.row && currentCell.col === annotatedCell.col ? '取消标注' : '标注'}}</text>
              </button>
              <view class="action-divider"></view>
              <button class="action-btn" bindtap="handleZoom">
                <text class="action-icon">🔍</text>
                <text class="text">放大</text>
              </button>
            </view>
          </view>
        </view>
        <!-- 放大显示遮罩层 -->
        <view class="zoom-mask" wx:if="{{showZoomImage}}" bindtap="hideZoomImage" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}};top:0">
          <view class="zoom-content" catch:tap="preventBubble">
            <view class="image-container" 
                  bindtouchstart="dragStart"
                  bindtouchmove="dragMove"
                  bindtouchend="dragEnd"
                  style="transform: translate3d({{dragX}}px, {{dragY}}px, 0)">
              <view class="image-wrapper" style="transform: scale({{zoomScale}})">
                <image class="zoomed-image" 
                      src="{{zoomedImage}}" 
                      mode="widthFix"></image>
                <!-- 放大图片的网格层 -->
                <view class="zoomed-grid" wx:if="{{zoomGridSettings.show}}">
                  <view class="grid-lines">
                    <view class="grid-line-horizontal" 
                          wx:for="{{zoomGridSettings.rows - 1}}" 
                          wx:key="*this"
                          style="top:calc(({{index}} + 1) * (100% / {{zoomGridSettings.rows}})); background-color:{{zoomGridSettings.color}}; opacity:calc({{zoomGridSettings.opacity}} / 100);">
                    </view>
                    <view class="grid-line-vertical" 
                          wx:for="{{zoomGridSettings.columns - 1}}" 
                          wx:key="*this"
                          style="left:calc(({{index}} + 1) * (100% / {{zoomGridSettings.columns}})); background-color:{{zoomGridSettings.color}}; opacity:calc({{zoomGridSettings.opacity}} / 100);">
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="download-btn" catch:tap="downloadZoomedImage">
            <text class="download-icon">下载 {{zoomScale}}倍 分块图</text>
          </view>
          <view class="zoom-closed-btn" bindtap="hideZoomImage">
            <text class="zoom-closed-icon">×</text>
          </view>
          <!-- <view class="grid-settings-btn" catch:tap="toggleGridSettings">
            <text class="settings-icon">⚙️</text>
            <text class="text">网格设置</text>
          </view> -->
          <view class="zoom-controls">
            <view class="zoom-slider" catch:tap="preventBubble">
              <slider min="100" max="400" value="{{zoomScale * 100}}" 
                    bindchanging="onZoomScaleChange" 
                    activeColor="#07c160" 
                    block-size="28" 
                    block-color="#fff"/>
              <text class="zoom-value">{{zoomScale}}x</text>
            </view>

            <view class="grid-settings-btn" catch:tap="toggleGridSettings">
              <text class="settings-icon">⚙️</text>
              <text class="text">网格设置</text>
            </view>
          </view>
        </view>
        <!-- 放大图片的网格设置面板 -->
        <view class="zoom-grid-settings-mask {{showGridSettings ? 'show' : ''}}" bindtap="toggleGridSettings">
          <view class="zoom-grid-settings {{showGridSettings ? 'show' : ''}}" catch:tap="preventBubble">
            <view class="settings-header">
              <text class="text">网格设置</text>
              <view class="close-settings" bindtap="toggleGridSettings">×</view>
            </view>
            <view class="settings-content">
              <view class="settings-item">
                <text class="text">显示网格：</text>
                <switch checked="{{zoomGridSettings.show}}" bindchange="onZoomGridShowChange" color="#07c160" />
              </view>
              <view class="settings-item">
                <text class="text">行数：</text>
                <view class="number-control">
                  <view class="control-btn" bindtap="onZoomGridRowsDecrease">-</view>
                  <input type="number" value="{{zoomGridSettings.rows}}" disabled="true" />
                  <view class="control-btn" bindtap="onZoomGridRowsIncrease">+</view>
                </view>
              </view>
              <view class="settings-item">
                <text class="text">列数：</text>
                <view class="number-control">
                  <view class="control-btn" bindtap="onZoomGridColumnsDecrease">-</view>
                  <input type="number" value="{{zoomGridSettings.columns}}" disabled="true" />
                  <view class="control-btn" bindtap="onZoomGridColumnsIncrease">+</view>
                </view>
              </view>
              <view class="settings-item">
                <text class="text">颜色：</text>
                <color-picker 
                  value="{{zoomGridSettings.color}}" 
                  bindconfirm="onZoomGridColorConfirm"
                  showPresets="{{true}}"
                />
              </view>
            </view>
          </view>
        </view>
      
      <!-- 用于裁剪的隐藏canvas -->
      <canvas canvas-id="cropCanvas" style="position: fixed; left: -9999px; width: {{canvasWidth}}px; height: {{canvasHeight}}px;"></canvas>
      <wxmy-cropper
          wx:if="{{showCropper}}"
          imageSrc="{{tempFilePath}}"
          bindclose="handleCropperClose"
          bindcropped="handleCropperClose"
          cropperRatio="{{1}}"
        ></wxmy-cropper>
      <!-- <wxcropper 
        wx:if="{{showCropper}}"
        imageSrc="{{tempFilePath}}"
        bindclose="handleCropperClose"
        cropperRatio="{{1}}"
      /> -->
      <!-- 导出加载遮罩 -->
      <view class="export-loading-mask" wx:if="{{showExportLoading}}">
        <view class="export-loading-content">
          <view class="export-loading-icon"></view>
          <text class="text">正在导出...</text>
        </view>
      </view>
      <!-- 设置面板遮罩层 -->
      <view class="settings-mask {{showMainSettings ? 'show' : ''}}" bindtap="toggleMainSettings"></view>
      <!-- 主设置面板 -->
      <view class="main-settings {{showMainSettings ? 'show' : ''}}" style=" {{showMainSettings ? 'padding-top:'+contentStyle_top+'px' : ''}}">
        <view class="settings-content">
          <!-- 网格设置 -->
          <view class="settings-group" hidden="{{tab_current !== 'grid'}}">
            <view class="settings-item">
              <text class="text">行数：</text>
              <view class="number-control">
                <view class="control-btn" bindtap="onGridRowsDecrease">-</view>
                <input type="number" value="{{gridSettings.rows}}" disabled="true" />
                <view class="control-btn" bindtap="onGridRowsIncrease">+</view>
              </view>
            </view>
            <view class="settings-item">
              <text class="text">列数：</text>
              <view class="number-control">
                <view class="control-btn" bindtap="onGridColumnsDecrease">-</view>
                <input type="number" value="{{gridSettings.columns}}" disabled="true" />
                <view class="control-btn" bindtap="onGridColumnsIncrease">+</view>
              </view>
            </view>
            <view class="settings-item">
              <text class="text">颜色：</text>
              <color-picker 
                value="{{gridSettings.color}}" 
                bindconfirm="onColorConfirm"
                showPresets="{{true}}"
              />
            </view>
          </view>

          <!-- 坐标设置 -->
          <view class="settings-group" hidden="{{tab_current !== 'coordinate'}}">
            <view class="settings-item">
              <text class="text">横向位置：</text>
              <picker class="position-picker" bindchange="onHorizontalPositionChange" value="{{coordinateSettings.horizontalPosition}}" range="{{positionOptions.horizontal}}">
                <view class="picker-content">
                  <text class="text">{{positionOptions.horizontal[coordinateSettings.horizontalPosition]}}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
            <view class="settings-item">
              <text class="text">纵向位置：</text>
              <picker class="position-picker" bindchange="onVerticalPositionChange" value="{{coordinateSettings.verticalPosition}}" range="{{positionOptions.vertical}}">
                <view class="picker-content">
                  <text class="text">{{positionOptions.vertical[coordinateSettings.verticalPosition]}}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
            <view class="settings-item">
              <text class="text">字号：</text>
              <view class="number-control">
                <view class="control-btn" bindtap="onCoordinateFontSizeDecrease">-</view>
                <input type="number" value="{{coordinateSettings.fontSize}}" disabled="true" />
                <view class="control-btn" bindtap="onCoordinateFontSizeIncrease">+</view>
              </view>
            </view>
            <view class="settings-item">
              <text class="text">颜色：</text>
              <color-picker 
                value="{{coordinateSettings.color}}" 
                bindconfirm="onCoordinateColorConfirm"
                showPresets="{{true}}"
              />
            </view>
          </view>

          <!-- 标注设置 -->
          <view class="settings-group" hidden="{{tab_current !== 'annotation'}}">
            <view class="settings-item">
              <text class="text">颜色：</text>
              <color-picker 
                value="{{annotationSettings.color}}" 
                bindconfirm="onAnnotationColorConfirm"
                showPresets="{{true}}"
              />
            </view>
          </view>
        </view>

        <!-- Tab切换导航 - 移到底部 -->
        <view class="settings-tabs">
          <view class="tab-item {{tab_current === 'grid' ? 'active' : ''}}" 
                data-tab="grid" 
                bindtap="switchTab">
            <text class="tab-icon">⊞</text>
            <text class="text">网格</text>
          </view>
          <view class="tab-item {{tab_current === 'coordinate' ? 'active' : ''}}" 
                data-tab="coordinate" 
                bindtap="switchTab">
            <text class="tab-icon">📍</text>
            <text class="text">坐标</text>
          </view>
          <view class="tab-item {{tab_current === 'annotation' ? 'active' : ''}}" 
                data-tab="annotation" 
                bindtap="switchTab">
            <text class="tab-icon">✏️</text>
            <text class="text">标注</text>
          </view>
        </view>
      </view>
      <!-- 尺寸计算结果弹窗 -->
      <view class="size-calculator-mask {{showSizeCalculator ? 'show' : ''}}" bindtap="hideSizeCalculator">
        <view class="size-calculator {{showSizeCalculator ? 'show' : ''}}" catch:tap="preventBubble">
          <view class="calculator-header">
            <view class="header-title">
              <text class="title-icon">📏</text>
              <text class="text">尺寸计算结果</text>
            </view>
            <view class="close-calculator" bindtap="hideSizeCalculator">×</view>
          </view>
          <view class="calculator-content">
            <view class="paper-size-selector">
              <view class="selector-label">
                <text class="label-icon">📄</text>
                <text class="text">纸张选择</text>
              </view>
              <paper-size-picker
                button-text="选择纸张尺寸"
                current-size="{{currentPaperSize}}"
                bindselect="onPaperSizeSelect"
              />
            </view>
            <view class="calculation-results">
              <view class="result-card">
                <view class="result-item">
                  <text class="result-label">
                    <text class="label-icon">⊞</text>
                    <text class="text">网格</text>
                  </text>
                  <text class="result-value highlight">{{gridSettings.rows}} 行 × {{gridSettings.columns}} 列</text>
                </view>
                <view class="result-item">
                  <text class="result-label">
                    <text class="label-icon">📊</text>
                    <text class="text">单格</text>
                  </text>
                  <text class="result-value">{{calculatedResults.cellWidth}} × {{calculatedResults.cellHeight}} cm</text>
                </view>
                <view class="result-item">
                  <text class="result-label">
                    <text class="label-icon">📐</text>
                    <text class="text">实际</text>
                  </text>
                  <text class="result-value">{{calculatedResults.totalWidth}} × {{calculatedResults.totalHeight}} cm</text>
                </view>
                <view class="result-item">
                  <text class="result-label">
                    <text class="label-icon">↔️</text>
                    <text class="text">留白</text>
                  </text>
                  <text class="result-value">
                    <block wx:if="{{calculatedResults.verticalMargin !== '0.00'}}">上 {{calculatedResults.verticalMargin}}cm，下 {{calculatedResults.verticalMargin}}cm</block>
                    <block wx:if="{{calculatedResults.horizontalMargin !== '0.00'}}">{{calculatedResults.verticalMargin !== '0.00' ? '，' : ''}}左 {{calculatedResults.horizontalMargin}}cm，右 {{calculatedResults.horizontalMargin}}cm</block>
                    <block wx:if="{{calculatedResults.verticalMargin === '0.00' && calculatedResults.horizontalMargin === '0.00'}}">无</block>
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
        <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="1"><!-- 底部导航 --></tab-bar>
</view>

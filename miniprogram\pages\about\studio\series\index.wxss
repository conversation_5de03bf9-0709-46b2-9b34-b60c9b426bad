.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  filter: brightness(0.9);
}

.application-container {
  padding: 30rpx 24rpx 120rpx;
}

/* 头部卡片样式 */
.header-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.header-content {
  margin-bottom: 24rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: #666;
}

.header-stat {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-divider {
  width: 1rpx;
  background-color: rgba(0, 0, 0, 0.05);
  margin: 10rpx 0;
}

.stat-value {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空数据样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin: 20rpx 0 10rpx;
  font-weight: bold;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.empty-button {
  background: #07c160;
  color: white;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 100rpx;
}

.empty-button:active {
  opacity: 0.8;
}

/* 申请列表样式 */
.application-list {
  margin-bottom: 40rpx;
}

.application-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 0;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.application-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background: #fafafa;
}

.application-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.application-status {
  font-size: 24rpx;
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
}

.status-pending {
  background-color: #ff9800;
}

.status-approved {
  background-color: #4caf50;
}

.status-rejected {
  background-color: #f44336;
}

.application-content {
  padding: 24rpx 30rpx;
}

.application-row {
  display: flex;
  margin-bottom: 20rpx;
}

.application-col {
  flex: 1;
}

.info-group {
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  display: block;
}

.series-name {
  font-weight: 500;
}

.time-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.time-info mp-icon {
  margin-right: 8rpx;
}

/* 时间容器内添加更多样式 */
.time-info-container {
  padding-top: 16rpx;
  border-top: 1rpx dashed rgba(0, 0, 0, 0.08);
  margin-top: 12rpx;
}

.application-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  background: #fafafa;
}

.action-tag {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #4caf50;
}

.action-tag.waiting {
  color: #ff9800;
}

.action-tag.rejected {
  color: #f44336;
}

.action-tag mp-icon {
  margin-right: 6rpx;
}

.view-detail-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 16rpx;
  background: #f0f0f0;
  border-radius: 100rpx;
}

.view-detail-btn mp-icon {
  margin-left: 4rpx;
}

/* 动作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30rpx;
}

.action-button {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #07c160, #0bab53);
  color: white;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 100rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.2);
}

.action-button text {
  margin-left: 8rpx;
}

/* 详情弹窗样式升级 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  perspective: 1000px;
}

.detail-modal.show {
  visibility: visible;
  opacity: 1;
}

.detail-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
  backdrop-filter: blur(4px);
}

.detail-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.15);
  z-index: 2;
  transform: translateY(100%) scale(0.95);
  transform-origin: center bottom;
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  flex-direction: column;
  max-height: 92vh;
  overflow: hidden;
}

.detail-modal.show .detail-container {
  transform: translateY(0) scale(1);
}

.detail-header {
  padding: 40rpx 40rpx 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  position: relative;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}

.detail-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 15%;
  height: 70%;
  width: 8rpx;
  background: linear-gradient(to bottom, #07c160, #09ad55);
  border-radius: 4rpx;
}

.detail-close {
  height: 70rpx;
  width: 70rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  cursor: pointer;
}

.detail-close:active {
  background: rgba(0, 0, 0, 0.08);
  transform: scale(0.9) rotate(90deg);
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: 70vh;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.detail-status-bar {
  width: calc(100% + 48rpx);
  margin-left: -24rpx;
  line-height: 70rpx;
  text-align: center;
  color: white;
  font-size: 30rpx;
  font-weight: bold;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.detail-status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  right: -50%;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(255,255,255,0) 0%, 
    rgba(255,255,255,0.2) 50%, 
    rgba(255,255,255,0) 100%);
  animation: shine 2s infinite linear;
  z-index: 1;
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.status-pending {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.status-approved {
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.status-rejected {
  background: linear-gradient(135deg, #f44336, #c62828);
}

.detail-section {
  margin: 30rpx 0 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
  animation: sectionFadeIn 0.5s forwards;
  opacity: 0;
  transform: translateY(20rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  position: relative;
}

@keyframes sectionFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-section:nth-child(1) { animation-delay: 0.1s; }
.detail-section:nth-child(2) { animation-delay: 0.2s; }
.detail-section:nth-child(3) { animation-delay: 0.3s; }
.detail-section:nth-child(4) { animation-delay: 0.4s; }
.detail-section:nth-child(5) { animation-delay: 0.5s; }

.detail-section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(0, 0, 0, 0.06);
}

.detail-section-title::before {
  content: '';
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  background: #07c160;
  border-radius: 50%;
  margin-right: 12rpx;
  box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
}

.detail-item {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.detail-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #777;
  display: inline-block;
  margin-bottom: 0;
  width: 160rpx;
  flex-shrink: 0;
  padding-top: 4rpx;
  font-weight: 500;
}

.detail-value {
  font-size: 30rpx;
  color: #333;
  display: inline-block;
  word-break: break-all;
  line-height: 1.5;
  flex: 1;
  letter-spacing: 0.5rpx;
}

.portfolio-links {
  color: #1976D2;
  text-decoration: none;
  border-bottom: 1rpx solid #1976D2;
  padding-bottom: 2rpx;
  transition: all 0.3s ease;
}

.detail-remarks {
  background: #f9f9f9;
  padding: 30rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 8rpx rgba(0, 0, 0, 0.03);
  border-left: 8rpx solid rgba(7, 193, 96, 0.3);
}

.detail-remarks::before {
  content: '"';
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  font-size: 100rpx;
  color: rgba(0, 0, 0, 0.03);
  font-family: serif;
}

.detail-footer {
  padding: 30rpx 40rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  background: linear-gradient(to bottom, #ffffff, #f9f9f9);
  position: relative;
}

.detail-footer::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.05) 50%, 
    transparent 100%);
}

.detail-btn {
  height: 88rpx;
  border-radius: 44rpx;
  text-align: center;
  font-size: 30rpx;
  min-width: 260rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.detail-btn.cancel {
  color: #666;
  background: #f0f0f0;
}

.detail-btn.cancel:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.detail-btn.primary {
  background: linear-gradient(135deg, #07c160, #09ad55);
  color: white;
  box-shadow: 0 8rpx 16rpx rgba(7, 193, 96, 0.3);
  position: relative;
  overflow: hidden;
}

.detail-btn.primary:active {
  transform: translateY(3rpx) scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
}

.detail-btn.primary::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.5s, opacity 0.5s;
}

.detail-btn.primary:active::after {
  transform: scale(1);
  opacity: 1;
  transition: 0s;
}

.detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.6;
  }
  50% { 
    transform: scale(1.05);
    opacity: 1;
  }
}

.detail-loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4rpx solid #07c160;
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
  margin-bottom: 30rpx;
}

.detail-loading text {
  font-size: 28rpx;
  color: #999;
  animation: pulse 2s ease-in-out infinite;
}

/* 添加Tab选项卡样式 */
.tab-container {
  display: flex;
  background: #ffffff;
  padding: 0 10rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-underline {
  position: absolute;
  bottom: 0;
  left: 15%;
  width: 70%;
  height: 6rpx;
  background: #07c160;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.tab-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.debug-info {
  font-size: 20rpx;
  color: #999;
  display: none; /* 调试完成后改为 none 即可隐藏 */
}

/**
 * AIAPI 接口封装模块

 */

import { request, CACHE_NONE, CACHE_MEMORY, CACHE_STORAGE, CACHE_BOTH, RequestOptions, StandardResponse } from './requestManager';

// 定义 CacheStrategy 常量
const CacheStrategy = {
  NONE: CACHE_NONE,
  MEMORY: CACHE_MEMORY,
  STORAGE: CACHE_STORAGE,
  BOTH: CACHE_BOTH
};

/**
 * 缓存时间常量（毫秒）
 */
const CACHE_TIME = {
  NONE: 0,
  MINUTE_5: 5 * 60 * 1000,
  HOUR_1: 60 * 60 * 1000,
  HOUR_24: 24 * 60 * 60 * 1000,
  WEEK_1: 7 * 24 * 60 * 60 * 1000
};
/**
 * Pollinations 相关接口参数类型定义
 */
interface PollinationsGenerateImageParams {
  prompt: string;
  width?: number;
  height?: number;
  seed?: number;
  model?: string;
  nologo?: boolean;
  referrer?: string;
  async?: boolean;
  [key: string]: any;
}
interface PollinationsGenerateImageUrlParams {
  prompt: string;
  width?: number;
  height?: number;
  seed?: number;
  model?: string;
  nologo?: boolean;
  referrer?: string;
  async?: boolean;
  [key: string]: any;
}
interface PollinationsTTSParams {
  text: string;
  voice?: string;
  model?: string;
  [key: string]: any;
}

interface PollinationsChatParams {
  model: string;
  messages: any[];
  stream?: boolean;
  seed?: number;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stop?: any;
  presence_penalty?: number;
  frequency_penalty?: number;
  [key: string]: any;
}

interface PollinationsGetTaskStatusParams {
  task_id: string;
}

interface PollinationsGetTasksParams {
  user_id?: string;
  page?: number;
  limit?: number;
}

interface PollinationsCancelTaskParams {
  task_id: string;
}

/**
 * zhipu 相关接口参数类型定义
 */
interface ZhipuAiChatParams {
  message: string;
  history?: any[];
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  stream?: boolean;
  system_prompt?: string;
  async?: boolean;
  [key: string]: any;
}

interface ZhipuAiGenerateImageParams {
  prompt: string;
  size?: string;
  quality?: string;
  async?: boolean;
  [key: string]: any;
}

interface ZhipuAiMultimodalChatParams {
  message: string;
  image_url?: string;
  image_base64?: string;
  history?: any[];
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  async?: boolean;
  [key: string]: any;
}

interface ZhipuAiGenerateVideoParams {
  prompt: string;
  negative_prompt?: string;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
  quality?: string;
  audio?: boolean;
  mode?: string;
  image_url?: string;
  image_base64?: string;
  strength?: number;
  motion_bucket_id?: number;
  async?: boolean;
  [key: string]: any;
}

interface ZhipuAiReasoningParams {
  message: string;
  history?: any[];
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  reasoning_steps?: number;
  system_prompt?: string;
  async?: boolean;
  [key: string]: any;
}

interface ZhipuAiTranslateChineseToEnglishParams {
  content: any;
}
interface ZhipuAitranslateParams {
  content: any;
  target_lang: string;
}
interface ZhipuAiExpandDrawingPromptParams {
  content: string;
}
interface ZhipuAiCheckViolationKeywordsParams {
  content: string;
}
/**
 * siliconflow 相关接口参数类型定义
 */
interface SiliconFlowChatParams {
  message: string;
  history?: any[];
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  stream?: boolean;
  system_prompt?: string;
  async?: boolean;
  [key: string]: any;
}

interface SiliconFlowGenerateImageParams {
  prompt: string;
  negative_prompt?: string;
  image_size?: string;
  batch_size?: number;
  seed?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  image?: string;
  async?: boolean;
  [key: string]: any;
}

/**
 * Pollinations服务接口
 */
const pollinations = {
  /**
   * 获取生图模型
   */
  getModels: () => {
    return request('pollinations/getModels', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.BOTH, expireTime: CACHE_TIME.HOUR_24 }
    });
  },

  /**
   * 文生图接口
   */
  generateImage: async (params: PollinationsGenerateImageParams) => {
    try {
      const initialResponse: StandardResponse = await request('pollinations/generateImage', params, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });

      // Check if a task ID is returned and the status is queued
      if (initialResponse.code === 1 && initialResponse.data && initialResponse.data.task_id && initialResponse.data.status === 'queued') {
        console.log(`Task ${initialResponse.data.task_id} queued. Polling status...`);
        // Poll for task completion and return the final response
        return await pollTaskStatus(initialResponse.data.task_id);
      } else {
        // Return the initial response if no task ID or not queued
        return initialResponse;
      }
    } catch (error) {
      console.error('Error in generateImage or polling:', error);
      throw error; // Re-throw the error after logging
    }
  },
  /**
   * 文生图接口——图像路径
   */
  generateImage_url: (params: PollinationsGenerateImageUrlParams) => {
    return request('pollinations/generateImage_url', params, {
        method: 'POST',
        cache: { strategy: CacheStrategy.NONE }
      });
  },
  /**
   * 获取可用语音列表
   */
  getAvailableVoices: () => {
    return request('pollinations/getAvailableVoices', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.BOTH, expireTime: CACHE_TIME.HOUR_24 }
    });
  },

  /**
   * 文本生成/对话接口
   */
  chat: (params: PollinationsChatParams) => {
    return request('pollinations/chat', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 获取可用文本模型列表
   */
  getAvailableTextModels: () => {
    return request('pollinations/getAvailableTextModels', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.BOTH, expireTime: CACHE_TIME.HOUR_24 }
    });
  },

  /**
   * 通用任务状态查询
   */
  getTaskStatus: (params: PollinationsGetTaskStatusParams) => {
    return request('pollinations/getTaskStatus', params, {
      method: 'GET',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 通用用户任务列表
   */
  getTasks: (params: PollinationsGetTasksParams = {}) => {
    return request('pollinations/getTasks', params, {
      method: 'GET',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 通用任务取消
   */
  cancelTask: (params: PollinationsCancelTaskParams) => {
    return request('pollinations/cancelTask', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },
  /**
   * 获取图像feed
   */
  feed: () => {
    return request('pollinations/feed', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.NONE }
    });
  },
};

/**
 * ZhipuAi服务接口
 */
const zhipu = {
  /**
   * AI聊天接口
   */
  chat: (params: ZhipuAiChatParams) => {
    return request('zhipu_ai/chat', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * AI图像生成接口
   */
  generateImage: (params: ZhipuAiGenerateImageParams) => {
    return request('zhipu_ai/generateImage', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 多模态图像理解接口
   */
  multimodalChat: (params: ZhipuAiMultimodalChatParams) => {
    return request('zhipu_ai/multimodalChat', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * AI视频生成接口
   */
  generateVideo: (params: ZhipuAiGenerateVideoParams) => {
    return request('zhipu_ai/generateVideo', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * AI推理功能接口
   */
  reasoning: (params: ZhipuAiReasoningParams) => {
    return request('zhipu_ai/reasoning', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 中文翻译英文接口
   */
  translateChineseToEnglish: (params: ZhipuAiTranslateChineseToEnglishParams) => {
    return request('zhipu_ai/translateChineseToEnglish', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },
  /**
   * 中文翻译英文接口
   */
  translate: (params: ZhipuAitranslateParams) => {
    return request('zhipu_ai/translate', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * AI绘画提示词扩写接口
   */
  expandDrawingPrompt: (params: ZhipuAiExpandDrawingPromptParams) => {
    return request('zhipu_ai/expandDrawingPrompt', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 获取可用模型
   */
  getModels: () => {
    return request('zhipu_ai/getModels', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.BOTH, expireTime: CACHE_TIME.HOUR_24 }
    });
  },
  /**
   * 检查违规关键词接口
   */
  checkViolationKeywords: (params: ZhipuAiCheckViolationKeywordsParams) => {
    return request('zhipu_ai/checkViolationKeywords', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  
};

/**
 * SiliconFlow服务接口
 */
const siliconflow = {
  /**
   * AI聊天接口
   */
  chat: (params: SiliconFlowChatParams) => {
    return request('siliconflow/chat', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * AI图像生成接口
   */
  generateImage: (params: SiliconFlowGenerateImageParams) => {
    return request('siliconflow/generateImage', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  },

  /**
   * 获取可用模型
   */
  getModels: () => {
    return request('siliconflow/getModels', null, {
      method: 'GET',
      cache: { strategy: CacheStrategy.BOTH, expireTime: CACHE_TIME.HOUR_24 }
    });
  },
};

/**
 * 轮询 Pollinations 任务状态直到完成
 * @param task_id 任务ID
 * @param interval 轮询间隔（毫秒）
 * @param maxAttempts 最大轮询次数
 * @returns 完成时的任务状态响应
 * @throws Error 任务失败或超时
 */
const pollTaskStatus = async (task_id: string, interval = 3000, maxAttempts = 60) => {
  for (let i = 0; i < maxAttempts; i++) {
    const statusResponse: StandardResponse = await request(`pollinations/getTaskStatus`, { task_id }, {
      method: 'GET',
      cache: { strategy: CacheStrategy.NONE }
    });

    if (statusResponse.code === 1 && statusResponse.data) {
      const status = statusResponse.data.status;
      if (status === 'completed') {
        // 结构适配：只有completed且有image_base64时才补充result字段
        if (!statusResponse.data.result && statusResponse.data.image_base64) {
          statusResponse.data.result = {
            image_base64: statusResponse.data.image_base64
          };
        }
        return statusResponse;
      } else if (status === 'failed') {
        throw new Error(`Task ${task_id} failed.`);
      } else if (status === 'cancelled') {
        throw new Error(`Task ${task_id} was cancelled.`);
      }
      // If status is queued or processing, continue polling
    } else {
      // Handle potential errors from the status check request itself
      console.error('Error fetching task status:', statusResponse);
      // Depending on desired behavior, you might want to throw an error or retry the status request
    }

    if (i < maxAttempts - 1) {
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }
  throw new Error(`Task ${task_id} timed out after ${maxAttempts} attempts.`);
};

interface PollinationsWebSocketGenerateImageParams {
  prompt: string;
  [key: string]: any;
}

const pollinationsWebSocket = {
  /**
   * WebSocket文生图接口
   */
  generateImage: (params: PollinationsWebSocketGenerateImageParams) => {
    return request('pollinations_web_socket/generateImage', params, {
      method: 'POST',
      cache: { strategy: CacheStrategy.NONE }
    });
  }
};

export default {
  pollinations,
  zhipu,
  siliconflow,
  pollinationsWebSocket
}; 



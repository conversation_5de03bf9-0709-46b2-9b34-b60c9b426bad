// index.ts
const app = getApp<IAppOption>();
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
interface PaperSize {
  name: string;
  width: number;
  height: number;
  unit?: string;
}

interface ComponentInstance {
  throttledScaleControlTouchMove: (e: any) => void;
  [key: string]: any;
}

// 添加节流函数的实现
function throttle(fn: Function, delay: number) {
  let lastCall = 0;
  let timeout: number | null = null;
  
  return function(this: any, ...args: any[]) {
    const now = Date.now();
    const context = this;
    
    if (now - lastCall >= delay) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      fn.apply(context, args);
      lastCall = now;
    } else if (!timeout) {
      timeout = setTimeout(() => {
        fn.apply(context, args);
        lastCall = Date.now();
        timeout = null;
      }, delay - (now - lastCall));
    }
  };
}

Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    currentTab: 0,
    // 图片上传和裁剪相关
    showCropper: false,
    tempFilePath: '',
    croppedImage: '',
    cropX: 0,
    cropY: 0,
    lastX: 0,
    lastY: 0,
    isDragging: false,
    isScaling: false, // 添加缩放状态
    scaleStartWidth: 0, // 开始缩放时的宽度
    scaleStartHeight: 0, // 开始缩放时的高度
    scaleStartX: 0, // 开始缩放时的X坐标
    scaleStartY: 0, // 开始缩放时的Y坐标
    canvasWidth: 300,
    canvasHeight: 300,
    imageInfo: {
      width: 0,
      height: 0,
      path: '',
      scale: 1,
      imageLeft: 0,
      imageTop: 0,
      imageRight: 0,
      imageBottom: 0,
      cropperWidth: 0,
      cropperHeight: 0,
      cropperLeft: 0,
      cropperTop: 0
    },
    // 顶部提示
    showTopTips: false,
    topTipsMsg: '',
    topTipsType: 'info',
    tabList: [
      { id: 1, text: '首页', icon: 'home' },
      { id: 2, text: '发现', icon: 'search' },
      { id: 3, text: '我的', icon: 'me' }
    ],
    selectedSizeIndex: 0,
    paperSizes: [
      { name: '手绘卡 (6.4cm x 8.8cm)', width: 6.4, height: 8.8 },
      { name: 'A6 (10.5cm x 14.8cm)', width: 10.5, height: 14.8 },
      { name: 'A5 (14.8cm x 21cm)', width: 14.8, height: 21.0 },
      { name: 'A4 (21cm x 29.7cm)', width: 21, height: 29.7 }
    ] as PaperSize[],
    baseWidth: 300, // 基础裁剪框宽度
    originalImageWidth: 0,
    originalImageHeight: 0,
    // 添加放大倍数相关数据
    showScaleDialog: false,
    selectedScale: 1,
    scaleOptions: [
      { value: 1, text: '1x (原始尺寸)' },
      { value: 2, text: '2x (放大2倍)' },
      { value: 3, text: '3x (放大3倍)' },
      { value: 4, text: '4x (放大4倍)' }
    ],
    currentPaperSize: null as PaperSize | null,
  },

  lifetimes: {
    attached(this: ComponentInstance) {
      // 设置初始裁剪框尺寸
      const initialSize = this.data.paperSizes[0]
      const ratio = initialSize.height / initialSize.width
      this.setData({
        canvasWidth: this.data.baseWidth,
        canvasHeight: Math.round(this.data.baseWidth * ratio)
      })
      // 设置布局信息
      this.setData({
        ...layoutUtil.getLayoutInfo(),
      });
      
      // 初始化节流函数
      this.throttledCropperTouchMove = throttle(this.cropperTouchMove.bind(this), 16);
      this.throttledScaleControlTouchMove = throttle(this.scaleControlTouchMove.bind(this), 16);
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 分享给朋友
    onShareAppMessage: function () {
      return {
        title: '图片裁剪工具 - 支持多种纸张尺寸的裁剪工具',
        path: '/pages/tool/img_cropper/img_cropper',
        imageUrl: '/assets/share-image.png',
        success: function(res) {
          wx.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: function(res) {
          wx.showToast({
            title: '分享失败',
            icon: 'none',
            duration: 2000
          });
        }
      };
    },

    // 分享到朋友圈
    onShareTimeline: function () {
      return {
        title: '图片裁剪工具 - 提供常用纸张尺寸的固定比例裁剪选项',
        query: '',
        imageUrl: '/assets/share-image.png'
      };
    },

    // 选择图片并上传
    handleUpload() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          this.setData({
            tempFilePath,
            showCropper: true,
            croppedImage: '' // 清空之前的裁剪结果
          });
        }
      });
    },

    // 调整裁剪框大小和位置
    adjustCropperSize(width: number, height: number) {
      const imageInfo = this.data.imageInfo
      if (!imageInfo.width || !imageInfo.height) return

      // 使用向下取整确保不会超出边界
      const maxWidth = Math.floor(imageInfo.imageRight - imageInfo.imageLeft)
      const maxHeight = Math.floor(imageInfo.imageBottom - imageInfo.imageTop)
      
      let newWidth = Math.min(width, maxWidth)
      let newHeight = Math.min(height, maxHeight)
      const ratio = height / width

      // 确保宽高比保持不变
      if (newWidth * ratio > maxHeight) {
        newWidth = Math.floor(maxHeight / ratio)
        newHeight = maxHeight
      }
      
      if (newHeight / ratio > maxWidth) {
        newHeight = Math.floor(maxWidth * ratio)
        newWidth = maxWidth
      }

      // 确保居中位置使用整数坐标
      const newX = Math.round(imageInfo.imageLeft + (maxWidth - newWidth) / 2)
      const newY = Math.round(imageInfo.imageTop + (maxHeight - newHeight) / 2)

      this.setData({
        canvasWidth: newWidth,
        canvasHeight: newHeight,
        cropX: newX,
        cropY: newY
      })
    },

    // 尺寸选择改变
    onSizeChange(e: any) {
      const index = parseInt(e.detail.value)
      const selectedSize = this.data.paperSizes[index]
      
      // 计算新的裁剪框尺寸
      const ratio = selectedSize.height / selectedSize.width
      const newWidth = this.data.baseWidth
      const newHeight = Math.round(newWidth * ratio)

      this.setData({ selectedSizeIndex: index }, () => {
        // 调整裁剪框大小和位置
        this.adjustCropperSize(newWidth, newHeight)
      })
    },

    // 图片加载完成
    imageLoad(e: any) {
      const { width, height } = e.detail;
      const query = wx.createSelectorQuery().in(this);
      query.select('.cropper-area').boundingClientRect((rect: any) => {
        if (!rect) return;
        
        const scale = Math.min(rect.width / width, rect.height / height);
        const scaledWidth = width * scale;
        const scaledHeight = height * scale;
        
        const imageLeft = (rect.width - scaledWidth) / 2;
        const imageTop = (rect.height - scaledHeight) / 2;
        const imageRight = imageLeft + scaledWidth;
        const imageBottom = imageTop + scaledHeight;

        // 获取纸张选择器组件实例
        const paperSizePickerComponent = this.selectComponent('#paper-size-picker');
        
        this.setData({
          imageInfo: {
            width: scaledWidth,
            height: scaledHeight,
            path: this.data.tempFilePath,
            scale,
            imageLeft,
            imageTop,
            imageRight,
            imageBottom,
            cropperWidth: this.data.canvasWidth,
            cropperHeight: this.data.canvasHeight,
            cropperLeft: 0,
            cropperTop: 0
          }
        }, () => {
          // 等待组件初始化完成后获取默认尺寸
          if (paperSizePickerComponent && paperSizePickerComponent.data.selectedSize) {
            const defaultSize = paperSizePickerComponent.data.selectedSize;
            
            // 手动触发尺寸选择事件
            this.onPaperSizeSelect({
              detail: {
                size: defaultSize
              }
            });
          }
        });
      }).exec();
    },

    // 开始拖动裁剪框
    cropperTouchStart(e: any) {
      const touch = e.touches[0]
      this.setData({
        lastX: touch.clientX,
        lastY: touch.clientY,
        isDragging: true
      })
    },

    // 拖动裁剪框
    cropperTouchMove(e: any) {
      if (!this.data.isDragging) return
      const touch = e.touches[0]
      const moveX = touch.clientX - this.data.lastX
      const moveY = touch.clientY - this.data.lastY
      
      // 获取图片信息
      const imageInfo = this.data.imageInfo
      
      // 计算新的裁剪框位置
      let newX = this.data.cropX + moveX
      let newY = this.data.cropY + moveY
      
      // 严格限制在图片范围内
      // 限制左边界
      newX = Math.max(imageInfo.imageLeft, newX)
      // 限制右边界
      newX = Math.min(imageInfo.imageLeft + imageInfo.width - this.data.canvasWidth, newX)
      // 限制上边界
      newY = Math.max(imageInfo.imageTop, newY)
      // 限制下边界
      newY = Math.min(imageInfo.imageTop + imageInfo.height - this.data.canvasHeight, newY)
      
      this.setData({
        cropX: newX,
        cropY: newY,
        lastX: touch.clientX,
        lastY: touch.clientY
      })
    },

    // 结束拖动
    cropperTouchEnd() {
      this.setData({
        isDragging: false
      })
    },

    // 取消裁剪
    handleCropperCancel() {
      this.setData({
        showCropper: false,
        tempFilePath: ''
      })
    },

    // 处理裁剪完成
    handleCropperComplete(e: any) {
      console.log('裁剪完成：', e.detail);
      const { path } = e.detail;
      
      if (path) {
        this.setData({
          croppedImage: path,
          showCropper: false,
          tempFilePath: '' // 清空临时文件路径
        });
        
        // 显示成功提示
        this.showTopTips('裁剪成功', 'success');
      } else {
        this.showTopTips('裁剪失败', 'error');
      }
    },

    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 切换底部标签
    switchTab(e: any) {
      const { index } = e.detail;
      this.setData({
        currentTab: index
      });
      // 这里可以添加实际的页面切换逻辑
    },

    // 返回上一页
    navigateBack() {
      wx.navigateBack();
    },

    // 显示更多菜单
    showMore() {
      this.setData({
        showActionsheet: true
      });
    },

    // 处理图片下载
    handleDownload() {
      this.setData({
        showScaleDialog: true,
        selectedScale: 1
      });
    },

    // 处理放大倍数选择
    handleScaleSelect(e: any) {
      const index = e.detail.value;
      this.setData({
        selectedScale: this.data.scaleOptions[index].value
      });
    },

    // 取消放大倍数选择
    cancelScaleDialog() {
      this.setData({
        showScaleDialog: false
      });
    },

    // 确认放大倍数并下载
    confirmScaleAndDownload() {
      const scale = this.data.selectedScale;
      this.setData({
        showScaleDialog: false
      });

      wx.showLoading({
        title: '正在导出...',
        mask: true
      });

      // 获取图片信息
      wx.getImageInfo({
        src: this.data.croppedImage,
        success: (res) => {
          const width = res.width * scale;
          const height = res.height * scale;

          // 创建临时文件用于存储
          const tempFilePath = this.data.croppedImage;
          
          // 创建canvas上下文
          const query = wx.createSelectorQuery();
          query.select('#exportCanvas')
            .fields({ node: true, size: true })
            .exec((result) => {
              const canvas = result[0].node;
              const ctx = canvas.getContext('2d');

              canvas.width = width;
              canvas.height = height;

              // 加载图片
              const img = canvas.createImage();
              img.src = tempFilePath;
              img.onload = () => {
                // 绘制放大后的图片
                ctx.drawImage(img, 0, 0, width, height);

                // 导出图片
                wx.canvasToTempFilePath({
                  canvas,
                  width,
                  height,
                  destWidth: width,
                  destHeight: height,
                  fileType: 'jpg',
                  quality: 1,
                  success: (res) => {
                    // 保存图片到相册
                    wx.saveImageToPhotosAlbum({
                      filePath: res.tempFilePath,
                      success: () => {
                        wx.hideLoading();
                        this.showTopTips('保存成功', 'success');
                      },
                      fail: () => {
                        wx.hideLoading();
                        this.showTopTips('保存失败', 'error');
                      }
                    });
                  },
                  fail: () => {
                    wx.hideLoading();
                    this.showTopTips('导出失败', 'error');
                  }
                });
              };
              img.onerror = () => {
                wx.hideLoading();
                this.showTopTips('图片加载失败', 'error');
              };
            });
        },
        fail: () => {
          wx.hideLoading();
          this.showTopTips('获取图片信息失败', 'error');
        }
      });
    },

    // 显示顶部提示
    showTopTips(msg: string, type: 'success' | 'error' | 'info' = 'info') {
      this.setData({
        showTopTips: true,
        topTipsMsg: msg,
        topTipsType: type
      });

      setTimeout(() => {
        this.setData({
          showTopTips: false
        });
      }, 3000);
    },

    // 处理纸张尺寸选择
    onPaperSizeSelect(e: any) {
      // 从事件中获取选择的尺寸，注意数据结构变化
      const selectedSize = e.detail.size;
      if (!selectedSize || !selectedSize.width || !selectedSize.height) {
        console.error('Invalid paper size:', e.detail);
        return;
      }
      
      this.setData({ currentPaperSize: selectedSize });
      
      // 确保使用数值进行计算
      const width = parseFloat(selectedSize.width);
      const height = parseFloat(selectedSize.height);
      
      if (isNaN(width) || isNaN(height)) {
        console.error('Invalid dimensions:', width, height);
        return;
      }
      
      const ratio = height / width;
      
      // 获取图片区域的可用空间
      const imageInfo = this.data.imageInfo;
      if (!imageInfo || !imageInfo.width || !imageInfo.height) {
        // 如果图片信息不可用，显示提示并返回
        // wx.showToast({
        //   title: '请先上传图片',
        //   icon: 'none',
        //   duration: 2000
        // });
        return;
      }

      const availableWidth = imageInfo.width * 0.8;
      const availableHeight = imageInfo.height * 0.8;
      
      let newWidth, newHeight;
      
      if (ratio > 1) {
        // 纵向纸张
        newHeight = availableHeight;
        newWidth = newHeight / ratio;
        if (newWidth > availableWidth) {
          newWidth = availableWidth;
          newHeight = newWidth * ratio;
        }
      } else {
        // 横向纸张
        newWidth = availableWidth;
        newHeight = newWidth * ratio;
        if (newHeight > availableHeight) {
          newHeight = availableHeight;
          newWidth = newHeight / ratio;
        }
      }
      
      // 确保尺寸为整数
      newWidth = Math.round(newWidth);
      newHeight = Math.round(newHeight);
      
      // 计算居中位置
      const newX = imageInfo.imageLeft + (imageInfo.width - newWidth) / 2;
      const newY = imageInfo.imageTop + (imageInfo.height - newHeight) / 2;
      
      this.setData({
        canvasWidth: newWidth,
        canvasHeight: newHeight,
        cropX: newX,
        cropY: newY
      });
      
      console.log('Paper size selected:', {
        original: selectedSize,
        ratio: ratio,
        newSize: {
          width: newWidth,
          height: newHeight
        }
      });
    },

    // 添加缩放控制触摸移动方法
    scaleControlTouchMove(e: any) {
      if (!this.data.isScaling) return;
      const touch = e.touches[0];
      
      // 计算移动距离
      const deltaX = touch.clientX - this.data.scaleStartX;
      const deltaY = touch.clientY - this.data.scaleStartY;
      
      // 使用对角线移动距离来计算缩放比例
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const direction = deltaX > 0 || deltaY > 0 ? 1 : -1;
      const scale = 1 + (direction * distance * 0.01);
      
      // 计算新的尺寸
      let newWidth = Math.round(this.data.scaleStartWidth * scale);
      let newHeight = Math.round(this.data.scaleStartHeight * scale);
      
      // 限制最小尺寸
      const minSize = 100;
      newWidth = Math.max(minSize, newWidth);
      newHeight = Math.max(minSize, newHeight);
      
      // 限制最大尺寸
      const imageInfo = this.data.imageInfo;
      const maxWidth = imageInfo.width;
      const maxHeight = imageInfo.height;
      
      newWidth = Math.min(maxWidth, newWidth);
      newHeight = Math.min(maxHeight, newHeight);
      
      // 更新裁剪框尺寸
      this.setData({
        canvasWidth: newWidth,
        canvasHeight: newHeight
      });
    },

    // 开始缩放
    scaleControlTouchStart(e: any) {
      const touch = e.touches[0];
      this.setData({
        isScaling: true,
        scaleStartX: touch.clientX,
        scaleStartY: touch.clientY,
        scaleStartWidth: this.data.canvasWidth,
        scaleStartHeight: this.data.canvasHeight
      });
    },

    // 结束缩放
    scaleControlTouchEnd() {
      this.setData({
        isScaling: false
      });
    }
  }
});

<template name="body">
  <view class="weui-gallery {{innerShow ? 'weui-animate-fade-in' : 'weui-animate-fade-out'}} {{extClass}}" aria-modal="true" tabindex="0">
    <view class="weui-gallery__info">{{current+1}}/{{currentImgs.length}}</view>
    <swiper class="weui-gallery__img__wrp" bindtap="hideGallery" indicator-dots="{{false}}" bindchange="change" current="{{current}}" autoplay="{{false}}" duration="{{500}}">
      <block wx:for="{{currentImgs}}" wx:key="index">
        <swiper-item>
          <image mode="aspectFit" class="weui-gallery__img" src="{{item}}"/>
        </swiper-item>
      </block>
    </swiper>
    <view class="weui-gallery__opr" wx:if="{{showDelete}}">
      <view aria-role="button" aria-label="删除" class="weui-gallery__del" bindtap="deleteImg" hover-class="weui-active">
          <view class="weui-icon-delete weui-icon_gallery-delete"/>
      </view>
    </view>
  </view>
</template>

<root-portal enable="{{true}}" wx:if="{{rootPortal && wrapperShow}}">
  <view class="root-portal-box">
    <template is="body" data="{{showDelete, extClass, current, innerShow, currentImgs}}"/>
  </view>
</root-portal>
<view wx:elif="{{!rootPortal && wrapperShow}}">
  <template is="body" data="{{showDelete, extClass, current, innerShow, currentImgs}}"/>
</view>


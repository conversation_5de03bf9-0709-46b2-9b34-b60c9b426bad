.paper-picker {
  display: inline-block;
  position: relative;
  z-index: 1;
}

/* 触发按钮样式 */
.paper-trigger {
  display:block;
  align-items: center;
  /* padding: 12rpx 24rpx; */
  /* background: var(--primary-color, #07c160); */
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
  /* border-radius: 40rpx; */
  border: none;
  box-shadow: 0 2rpx 12rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;

  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.paper-trigger:active {
  opacity: 0.85;
  transform: scale(0.96);
  box-shadow: 0 1rpx 4rpx rgba(7, 193, 96, 0.2);
}

.paper-preview-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.paper-icon {
  font-size: 32rpx;
  color: #ffffff;
  line-height: 1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.paper-value {
  font-size: 28rpx;
  color: #ffffff;
  max-width: 300rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 添加一个小箭头指示器 */
.paper-preview-wrapper::after {
  content: "▼";
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 8rpx;
  transition: transform 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 弹出层样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.25s ease;
}

.popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  height: 50vh;
  max-height: 50vh;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: all 0.25s ease;
  overflow: hidden;
  padding: 10rpx;
}

.popup-content.show {
  opacity: 1;
  visibility: visible;
}

/* 弹窗头部 */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
  gap: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-btn {
  padding: 16rpx;
  margin: 0;
  border: 2rpx solid #e0e0e0;
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.refresh-btn {
  padding: 16rpx;
  margin: 0;
  border: 2rpx solid #e0e0e0;
  font-size: 32rpx;
  color: #999;
  line-height: 1;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索框样式 */
.search-wrapper {
  flex-shrink: 0;
  flex: 1;
}

.search-input {
  height: 72rpx;
  line-height: 72rpx;
  border: 2rpx solid #e0e0e0;
  background: #f8f8f8;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #bbb;
}

/* 即时搜索结果列表样式 */
.instant-search-list {
  flex: 1;
  min-height: 0;
}

.instant-search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #eee;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.instant-search-item:last-child {
  margin-bottom: 0;
}

.instant-search-item:active {
  opacity: 0.85;
  transform: scale(0.98);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.item-category {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.instant-search-item .paper-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 0;
  word-break: break-all;
  white-space: normal;
}

.instant-search-item .paper-size {
  font-size: 24rpx;
  color: #666;
}

/* 分类标签 */
.category-tabs {
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
}

.category-tabs scroll-view {
  width: 100%;
}

.tabs-content {
  display: inline-flex;
  flex-wrap: nowrap;
  width: max-content;
}

.tab-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  margin-right: 16rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.tab-item:last-child {
  margin-right: 0;
}

.tab-item.active {
  color: #fff;
  /* background: #07c160; */
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
}

/* 纸张列表 */
.paper-list {
  flex: 1;
  display: flex;
  min-height: 0;
  overflow: hidden;
}

/* 纸张列表容器 */
.paper-container {
  flex: 1;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  overflow: hidden;
}

/* 纸张列 */
.paper-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 列标题 */
.column-title {
  border-bottom: 2rpx solid #f0f0f0;
  color: #adadad;
  font-size: 28rpx;
  font-weight: 500;
}

.title-text {
  
}

/* 纸张列滚动区域 */
.paper-column-scroll {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* 调整网格布局 */
.paper-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

/* 调整纸张项样式 */
.paper-item {
  /* background: #f1fbfc; */
  padding: 16rpx;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
  border: 2rpx solid #c4c2c2;
  margin-bottom: 10rpx;
}

.paper-item.active {
  background: rgba(46, 93, 248, 0.2);
  border-color: rgba(46, 93, 248, 0.98);
}

.paper-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.paper-size {
  font-size: 24rpx;
  color: #666;
}
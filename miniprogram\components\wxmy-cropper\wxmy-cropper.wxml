<!--cropper/cropper.wxml-->
<view class="wx-content-info" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}};">
  
  <!-- 纸张选择器 -->
  <view class="paper-size-selector">
    <paper-size-picker
      bindselect="onPaperSizeSelect" 
      current-size="{{currentPaperSize}}" 
      zIndex="2000"
      buttonText="选择纸张尺寸"
    />
  </view>

  <!-- 裁剪区域 -->
  <view class='cropper-content'>
    <view wx:if="{{showImg}}" class="wx-corpper">
      <view class="wx-corpper-content">
        <view class="wx-corpper-content-bg">
          <!-- 图片层 -->
          <image 
            src="{{filePath}}" 
            id="targetImg" 
            mode="aspectFit" 
            style="width:{{imageWidth}}px; height:{{imageHeight}}px; left:{{imageLeft}}px; top:{{imageTop}}px;"
          ></image>
          
          <!-- 裁剪框 -->
          <view class="wx-corpper-crop-box" 
            bind:touchstart="contentDragStart" 
            bind:touchmove="throttledContentDragMove" 
            bind:touchend="contentTouchEnd" 
            style="left:{{cutL}}px;top:{{cutT}}px;width:{{cropperBoxWidth}}px;height:{{cropperBoxHeight}}px;">
            <view class="wxmy-cropper-view-box">
              <!-- 网格线 -->
              <view class="wxmy-cropper-dashed-h"></view>
              <view class="wxmy-cropper-dashed-v"></view>
              
              <!-- 四角装饰 -->
              <view class="corner-decoration corner-lt"></view>
              <view class="corner-decoration corner-rt"></view>
              <view class="corner-decoration corner-lb"></view>
              <view class="corner-decoration corner-rb"></view>
              
              <!-- 缩放手柄 -->
              <view class="wxmy-cropper-scale-handle" 
                catch:touchstart="scaleStart" 
                catch:touchmove="throttledScaleMove" 
                catch:touchend="scaleEnd">
                <view class="scale-handle-icon"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮区域 -->
  <view class='cropper-config'>
    <view class="cropper-btn cropper-btn-cancel" bindtap="close">取消</view>
    <view class="cropper-btn cropper-btn-select" bindtap="getImage">选择图片</view>
    <view class="cropper-btn cropper-btn-confirm" bindtap="getImageInfo">确定</view>
  </view>

  <!-- 离屏Canvas -->
  <canvas type="2d" id="wxCropperCanvas" style="position:fixed; left:-9999px; top:-9999px;"></canvas>
</view>
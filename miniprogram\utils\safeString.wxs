/**
 * 安全字符串处理工具
 * 用于WXML中安全处理字符串，避免常见错误
 */

// 安全分割字符串
function safeSplit(str, separator) {
  // 检查是否是有效字符串
  if (str === undefined || str === null || typeof str !== 'string') {
    return [];
  }
  
  // 确保分隔符有效
  var sep = separator || ',';
  
  // 处理空字符串情况
  if (str.trim() === '') {
    return [];
  }
  
  // 安全分割并过滤空项
  try {
    var parts = str.split(sep);
    var result = [];
    
    // 过滤无效项
    for (var i = 0; i < parts.length; i++) {
      var part = parts[i];
      if (part && part.trim() !== '') {
        result.push(part.trim());
      }
    }
    
    return result;
  } catch (e) {
    return [];
  }
}

// 确保字符串值有效
function ensureString(value) {
  if (value === undefined || value === null) {
    return '';
  }
  return value.toString();
}

// 检查数组是否有有效长度
function hasItems(arr) {
  if (!arr || typeof arr !== 'object' || arr.length === undefined) {
    return false;
  }
  return arr.length > 0;
}

module.exports = {
  safeSplit: safeSplit,
  ensureString: ensureString,
  hasItems: hasItems
}; 
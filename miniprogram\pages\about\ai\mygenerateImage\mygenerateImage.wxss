/* 主题变量 */
:root {
  --main-color: #4F8CFF;
  --success-color: #34C759;
  --danger-color: #FF3B30;
  --card-radius: 18rpx;
  --card-shadow: 0 6rpx 24rpx rgba(79, 140, 255, 0.08);
  --card-bg: #fff;
  --bg-gradient: linear-gradient(135deg, #e3f0ff 0%, #f5f7fa 100%);
}

.bg-image {
  position: fixed;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  left: 0;
  top: 0;
  background: var(--bg-gradient);
  opacity: 0.7;
}

.container {
  min-height: 100vh;
  background: transparent;
}

.test-c {
    margin: 20rpx 20rpx 0;
    padding: 30rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
.task-list {
  margin: 24rpx 24rpx 0;
  padding: 0;
  background: transparent;
}
.task-item {
  margin-bottom: 24rpx;
  padding: 32rpx 28rpx 24rpx 28rpx;
  background: var(--card-bg);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  position: relative;
  border: none;
}
.task-item .task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.task-item .task-id {
  font-weight: bold;
  color: #222;
  font-size: 30rpx;
}
.task-item .task-status {
  margin-left: 12rpx;
}
.task-item .task-time {
  color: #888;
  font-size: 24rpx;
}
.task-item .task-prompt {
  color: #4F8CFF;
  font-size: 26rpx;
  margin-top: 4rpx;
  word-break: break-all;
}
.loading {
  text-align: center;
  color: var(--main-color);
  padding: 60rpx 0 40rpx 0;
  font-size: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 18rpx;
  animation: spin 1.2s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.empty {
  text-align: center;
  color: #bbb;
  padding: 60rpx 0 40rpx 0;
  font-size: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 18rpx;
  opacity: 0.5;
}
.pagination-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 28rpx 0 12rpx 0;
  padding: 0 24rpx;
}
.picker {
  padding: 12rpx 28rpx;
  background: #f0f4fa;
  border-radius: 12rpx;
  margin-right: 24rpx;
  color: #333;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}
.picker-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 8rpx;
}
.pagination {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.pagination button {
  margin: 0 4rpx;
  border-radius: 999rpx;
  background: #fff;
  color: var(--main-color);
  border: 2rpx solid var(--main-color);
  font-size: 26rpx;
  min-width: 48rpx;
  min-height: 48rpx;
  transition: background 0.2s, color 0.2s;
}
.pagination button[disabled], .pagination button.active {
  background: var(--main-color);
  color: #fff;
  border: 2rpx solid var(--main-color);
}

.status-tag {
  display: inline-block;
  padding: 4rpx 18rpx;
  border-radius: 999rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  margin-left: 8rpx;
}
.status-tag.processing {
  background: var(--main-color);
}
.status-tag.success {
  background: var(--success-color);
}
.status-tag.failed {
  background: var(--danger-color);
}

.task-image {
  width: 100%;
  height: auto; /* 保持图片比例 */
  margin-top: 20rpx;
  border-radius: var(--card-radius);
}
  
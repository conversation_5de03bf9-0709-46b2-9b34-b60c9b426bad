{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = function (args, opts) {\n    if (!opts) opts = {};\n    \n    var flags = { bools : {}, strings : {}, unknownFn: null };\n\n    if (typeof opts['unknown'] === 'function') {\n        flags.unknownFn = opts['unknown'];\n    }\n\n    if (typeof opts['boolean'] === 'boolean' && opts['boolean']) {\n      flags.allBools = true;\n    } else {\n      [].concat(opts['boolean']).filter(Boolean).forEach(function (key) {\n          flags.bools[key] = true;\n      });\n    }\n    \n    var aliases = {};\n    Object.keys(opts.alias || {}).forEach(function (key) {\n        aliases[key] = [].concat(opts.alias[key]);\n        aliases[key].forEach(function (x) {\n            aliases[x] = [key].concat(aliases[key].filter(function (y) {\n                return x !== y;\n            }));\n        });\n    });\n\n    [].concat(opts.string).filter(Boolean).forEach(function (key) {\n        flags.strings[key] = true;\n        if (aliases[key]) {\n            flags.strings[aliases[key]] = true;\n        }\n     });\n\n    var defaults = opts['default'] || {};\n    \n    var argv = { _ : [] };\n    Object.keys(flags.bools).forEach(function (key) {\n        setArg(key, defaults[key] === undefined ? false : defaults[key]);\n    });\n    \n    var notFlags = [];\n\n    if (args.indexOf('--') !== -1) {\n        notFlags = args.slice(args.indexOf('--')+1);\n        args = args.slice(0, args.indexOf('--'));\n    }\n\n    function argDefined(key, arg) {\n        return (flags.allBools && /^--[^=]+$/.test(arg)) ||\n            flags.strings[key] || flags.bools[key] || aliases[key];\n    }\n\n    function setArg (key, val, arg) {\n        if (arg && flags.unknownFn && !argDefined(key, arg)) {\n            if (flags.unknownFn(arg) === false) return;\n        }\n\n        var value = !flags.strings[key] && isNumber(val)\n            ? Number(val) : val\n        ;\n        setKey(argv, key.split('.'), value);\n        \n        (aliases[key] || []).forEach(function (x) {\n            setKey(argv, x.split('.'), value);\n        });\n    }\n\n    function setKey (obj, keys, value) {\n        var o = obj;\n        keys.slice(0,-1).forEach(function (key) {\n            if (o[key] === undefined) o[key] = {};\n            o = o[key];\n        });\n\n        var key = keys[keys.length - 1];\n        if (o[key] === undefined || flags.bools[key] || typeof o[key] === 'boolean') {\n            o[key] = value;\n        }\n        else if (Array.isArray(o[key])) {\n            o[key].push(value);\n        }\n        else {\n            o[key] = [ o[key], value ];\n        }\n    }\n    \n    function aliasIsBoolean(key) {\n      return aliases[key].some(function (x) {\n          return flags.bools[x];\n      });\n    }\n\n    for (var i = 0; i < args.length; i++) {\n        var arg = args[i];\n        \n        if (/^--.+=/.test(arg)) {\n            // Using [\\s\\S] instead of . because js doesn't support the\n            // 'dotall' regex modifier. See:\n            // http://stackoverflow.com/a/1068308/13216\n            var m = arg.match(/^--([^=]+)=([\\s\\S]*)$/);\n            var key = m[1];\n            var value = m[2];\n            if (flags.bools[key]) {\n                value = value !== 'false';\n            }\n            setArg(key, value, arg);\n        }\n        else if (/^--no-.+/.test(arg)) {\n            var key = arg.match(/^--no-(.+)/)[1];\n            setArg(key, false, arg);\n        }\n        else if (/^--.+/.test(arg)) {\n            var key = arg.match(/^--(.+)/)[1];\n            var next = args[i + 1];\n            if (next !== undefined && !/^-/.test(next)\n            && !flags.bools[key]\n            && !flags.allBools\n            && (aliases[key] ? !aliasIsBoolean(key) : true)) {\n                setArg(key, next, arg);\n                i++;\n            }\n            else if (/^(true|false)$/.test(next)) {\n                setArg(key, next === 'true', arg);\n                i++;\n            }\n            else {\n                setArg(key, flags.strings[key] ? '' : true, arg);\n            }\n        }\n        else if (/^-[^-]+/.test(arg)) {\n            var letters = arg.slice(1,-1).split('');\n            \n            var broken = false;\n            for (var j = 0; j < letters.length; j++) {\n                var next = arg.slice(j+2);\n                \n                if (next === '-') {\n                    setArg(letters[j], next, arg)\n                    continue;\n                }\n                \n                if (/[A-Za-z]/.test(letters[j]) && /=/.test(next)) {\n                    setArg(letters[j], next.split('=')[1], arg);\n                    broken = true;\n                    break;\n                }\n                \n                if (/[A-Za-z]/.test(letters[j])\n                && /-?\\d+(\\.\\d*)?(e-?\\d+)?$/.test(next)) {\n                    setArg(letters[j], next, arg);\n                    broken = true;\n                    break;\n                }\n                \n                if (letters[j+1] && letters[j+1].match(/\\W/)) {\n                    setArg(letters[j], arg.slice(j+2), arg);\n                    broken = true;\n                    break;\n                }\n                else {\n                    setArg(letters[j], flags.strings[letters[j]] ? '' : true, arg);\n                }\n            }\n            \n            var key = arg.slice(-1)[0];\n            if (!broken && key !== '-') {\n                if (args[i+1] && !/^(-|--)[^-]/.test(args[i+1])\n                && !flags.bools[key]\n                && (aliases[key] ? !aliasIsBoolean(key) : true)) {\n                    setArg(key, args[i+1], arg);\n                    i++;\n                }\n                else if (args[i+1] && /true|false/.test(args[i+1])) {\n                    setArg(key, args[i+1] === 'true', arg);\n                    i++;\n                }\n                else {\n                    setArg(key, flags.strings[key] ? '' : true, arg);\n                }\n            }\n        }\n        else {\n            if (!flags.unknownFn || flags.unknownFn(arg) !== false) {\n                argv._.push(\n                    flags.strings['_'] || !isNumber(arg) ? arg : Number(arg)\n                );\n            }\n            if (opts.stopEarly) {\n                argv._.push.apply(argv._, args.slice(i + 1));\n                break;\n            }\n        }\n    }\n    \n    Object.keys(defaults).forEach(function (key) {\n        if (!hasKey(argv, key.split('.'))) {\n            setKey(argv, key.split('.'), defaults[key]);\n            \n            (aliases[key] || []).forEach(function (x) {\n                setKey(argv, x.split('.'), defaults[key]);\n            });\n        }\n    });\n    \n    if (opts['--']) {\n        argv['--'] = new Array();\n        notFlags.forEach(function(key) {\n            argv['--'].push(key);\n        });\n    }\n    else {\n        notFlags.forEach(function(key) {\n            argv._.push(key);\n        });\n    }\n\n    return argv;\n};\n\nfunction hasKey (obj, keys) {\n    var o = obj;\n    keys.slice(0,-1).forEach(function (key) {\n        o = (o[key] || {});\n    });\n\n    var key = keys[keys.length - 1];\n    return key in o;\n}\n\nfunction isNumber (x) {\n    if (typeof x === 'number') return true;\n    if (/^0x[0-9a-f]+$/i.test(x)) return true;\n    return /^[-+]?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(e[-+]?\\d+)?$/.test(x);\n}\n\n"]}
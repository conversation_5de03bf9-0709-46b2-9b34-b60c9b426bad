<wxs src="../../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="我的图库" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <!-- 添加筛选选项 -->
    <view class="filter-container">
      <view class="filter-option {{filterType === 'all' ? 'active' : ''}}" bindtap="switchFilter" data-type="all">
        全部
        <text class="count-badge">{{counts.all || 0}}</text>
      </view>
      <view class="filter-option {{filterType === 'confirmed' ? 'active' : ''}}" bindtap="switchFilter" data-type="confirmed">
        已通过
        <text class="count-badge">{{counts.confirmed || 0}}</text>
      </view>
      <view class="filter-option {{filterType === 'pending' ? 'active' : ''}}" bindtap="switchFilter" data-type="pending">
        审核中
        <text class="count-badge">{{counts.pending || 0}}</text>
      </view>
      <view class="filter-option {{filterType === 'rejected' ? 'active' : ''}}" bindtap="switchFilter" data-type="rejected">
        已驳回
        <text class="count-badge">{{counts.rejected || 0}}</text>
      </view>
    </view>
    
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      
      <!-- 加载指示器 -->
      <view class="loading-container" wx:if="{{!galleryList}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 图库列表 -->
      <view class="gallery-list" wx:else>
        <view wx:for="{{filteredGalleryList}}" wx:key="id" class="gallery-card">
          <!-- 序号 -->
          <view class="card-number">{{index + 1}}</view>
          
          <view class="card-content">
            <!-- 标题 -->
            <view class="card-title">{{item.series_name || '未命名系列'}}</view>
            
            <!-- 基本信息 -->
            <view class="card-info">
              <view class="info-item">
                <text class="label">工作室：</text>
                <text>{{item.studio_name || '未知工作室'}}</text>
              </view>
              <view class="info-item">
                <text class="label">系列名称：</text>
                <text>{{item.series_name || '未知系列'}}</text>
              </view>
              <view class="info-item">
                <text class="label">选择情况：</text>
                <text>{{item.selected_images.length || 0}} / {{item.max_selections || 0}}张</text>
              </view>
              <view class="info-item">
                <text class="label">状态：</text>
                <text class="status {{item.selection_status === 'confirmed' ? 'status-completed' : item.selection_status === 'pending' ? 'status-pending' : item.selection_status === 'rejected' ? 'status-rejected' : ''}}">{{item.selection_status === 'confirmed' ? '已通过' : 
                    item.selection_status === 'pending' ? '审核中' : 
                    item.selection_status === 'rejected' ? '已驳回' : '待选择'}}
                </text>
              </view>
            </view>
            
            <!-- 操作按钮 -->
            <view class="card-actions">
              <!-- 如果有已选图片则显示查看按钮 -->
              <block wx:if="{{item.selected_images && item.selected_images.length > 0}}">
                <button class="action-btn view-btn" bindtap="viewGallery" data-id="{{item.gallery_id}}">
                  查看已选图片
                </button>
              </block>
              
              <!-- 如果未达到最大选择数量且非审核中状态，显示继续选择按钮 -->
              <block wx:if="{{item.selected_images.length < item.max_selections && item.selection_status !== 'pending'}}">
                <button class="action-btn select-btn" bindtap="continueSelect" data-id="{{item.gallery_id}}">
                  {{item.selected_images.length > 0 ? '继续选择' : '开始选择'}}
                </button>
              </block>
            </view>
          </view>
        </view>
        
        <!-- 空状态展示 -->
        <view class="empty-state" wx:if="{{filteredGalleryList.length === 0}}">
          <image class="empty-image" src="/assets/empty.png" mode="aspectFit"></image>
          <text class="empty-text">暂无图库信息</text>
        </view>
      </view>
      
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>




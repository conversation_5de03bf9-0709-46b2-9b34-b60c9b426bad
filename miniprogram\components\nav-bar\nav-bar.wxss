/* 顶部导航样式 */
.nav-bar {
  width: 100%;
  background: linear-gradient(45deg, 
    rgba(46, 93, 248, 0.98) 0%,
    rgba(75, 116, 247, 0.98) 25%,
    rgba(46, 93, 248, 0.98) 50%,
    rgba(75, 116, 247, 0.98) 75%,
    rgba(46, 93, 248, 0.98) 100%);
  background-size: 300% 300%;
  animation: gradientFlow 8s ease infinite;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 32rpx 32rpx;
}

/* 渐变流动动画 */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
    box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    background-position: 100% 50%;
    box-shadow: 0 4rpx 40rpx rgba(0, 0, 0, 0.25);
  }
  100% {
    background-position: 0% 50%;
    box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
  }
}

/* 添加光效果 */
.nav-bar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: lightShine 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes lightShine {
  0% {
    transform: translate(-100%, -100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(100%, 100%) rotate(45deg);
    opacity: 0;
  }
}

.nav-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 24rpx;
  position: relative;
  box-sizing: border-box;
}

.nav-left {
  flex: 1;
  height: 32px;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding-left: 12rpx;
  
}

.nav-icon-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.nav-icon-btn:active {
  transform: scale(0.92);
  opacity: 0.8;
}

.nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.nav-title {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6rpx 0;
}

.title-text {
  font-size: 34rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}

.title-border {
  width: 28rpx;
  height: 3rpx;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 4rpx;
  margin-top: 8rpx;
  transition: all 0.3s ease;
}

.nav-right {
  min-width: 120rpx;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
} 
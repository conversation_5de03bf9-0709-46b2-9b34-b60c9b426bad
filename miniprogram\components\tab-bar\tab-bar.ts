// tab-bar.ts - 自定义底部导航栏组件
import { layoutUtil } from '../../utils/layout';
import Api from '../../utils/api';
import eventBus from '../../utils/eventBus';
import { STATIC_URL } from '../../utils/constants';

// 声明全局 __wxConfig 变量
declare const __wxConfig: {
  pages: string[];
  subPackages?: Array<{
    root: string;
    pages: string[];
  }>;
  subpackages?: Array<{
    root: string;
    pages: string[];
  }>;
};

// 定义导航菜单项的接口
interface NavMenuItem {
  id: number;
  text: string;
  name: string | null;
  icon: string;
  url: string | null;
  visible: number;
  show_in_home: number;
  sort_order: number;
  submenus?: NavMenuItem[];
}

(function() {

  Component({
    // 组件的对外属性定义
    properties: {
      height: {
        type: Number,
        value: 88  // 默认高度88
      },
      currentTab: {
        type: Number,
        value: 0   // 默认选中第一个tab
      },
      activeTabIndex: {
        type: Number,
        value: 0
      },
      currentPath: {
        type: String,
        value: '',
        observer: function(newPath) {
          this.updateActiveTab(newPath);
        }
      }
    },

    // 组件的内部数据
    data: {
      selected: 0,              // 当前选中的tab索引
      tabList: [] as any[],     // tab列表数据
      categoryList: [] as any[], // 分类列表数据
      tabBarHeight: layoutUtil.getTabBarStyle(), // 使用 layoutUtil 获取高度
      showCategoryPanel: false, // 是否显示分类面板
      selectedCategory: null,   // 当前选中的分类
      activeTabIndex: 0,        // 当前激活的tab索引
      currentPath: '',         // 当前页面路径
      layoutUtil: layoutUtil,   // 初始化 layoutUtil
      navMenus: [] as NavMenuItem[], // 添加navMenus到data中
      submenuslist: [] as NavMenuItem[], // 添加submenuslist到data中
      loading: false,
      tabBarStyle: layoutUtil.getTabBarStyle(),//包含内容区域和安全区域的精确高度
      contentStyle: layoutUtil.getTabBarContentStyle(),//不包含安全区域的纯内容区域高度
      safeAreaStyle: `padding-bottom: ${layoutUtil.safeAreaBottom}px`,
      notouch: false,         // 弹出注册框后禁止触摸
      userInfo: wx.getStorageSync('userInfo'),
      isCollapsed: true,
      currentHeight: 24,
      hasPageLoginFunc: false, // 添加标记，检查页面是否有登录功能
      loginModalVisible: false, // 登录弹窗是否显示
    },

    // 组件生命周期函数
    lifetimes: {
      // 组件被附加到页面时执行
      attached: function() {
        // 获取当前页面参数
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        // 保存原始的 onShow 方法
        const originalOnShow = currentPage.onShow;
        // 重写 onShow 方法
        currentPage.onShow = function() {
          // 调用原始的 onShow
          if (originalOnShow) {
            originalOnShow.call(this);
          }
          
          // 获取当前页面路径
          const fullPath = '/' + this.route;
          
          // 整合所有可能的参数来源
          const pageParams = {
            ...(this.options || {}),
            ...((this.__displayReporter && this.__displayReporter.query) || {}),
            ...(wx.getLaunchOptionsSync().query || {})
          };

          // 通过 EventBus 发送页面信息
          eventBus.emit('pageInfoUpdate', {
            path: fullPath,
            params: pageParams
          });
        };
        
        // 初始化布局
        this.initLayout();
        
        // 确保 layoutUtil 可用
        if (!this.data.layoutUtil) {
          this.setData({
            layoutUtil: layoutUtil,
            tabBarStyle: layoutUtil.getTabBarStyle(),
            contentStyle: layoutUtil.getTabBarContentStyle()
          });
        }
        
        // 设置页面切换动画
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: '#2e5df8',
          animation: {
            duration: 0,
            timingFunc: 'easeIn'
          }
        });
        
        // 配置页面切换
        wx.enableAlertBeforeUnload({
          message: ''
        });
        
        // 获取当前页面路径
        const currentPath = '/' + currentPage.route;
        
        // 设置当前路径
        this.setData({ currentPath });
        
        // 获取导航菜单和工具列表
        this.loadNavData().then(() => {
          // 更新激活状态
          this.updateActiveTab(currentPath);
        });
        
        // 监听页面登录弹窗事件
        eventBus.on('loginModalEvent', this.handleLoginModalEvent.bind(this));
        eventBus.on('loginSuccess', this.handleLoginSuccess.bind(this));
        
        // 监听页面注册登录功能
        eventBus.on('registerLoginPage', this.handleRegisterLoginPage.bind(this));
      },
      detached: function() {
        // 移除事件监听
        eventBus.off('loginModalEvent', this.handleLoginModalEvent.bind(this));
        eventBus.off('loginSuccess', this.handleLoginSuccess.bind(this));
        eventBus.off('registerLoginPage', this.handleRegisterLoginPage.bind(this));
      }
    },

    // 页面生命周期
    pageLifetimes: {
      // 页面显示时
      show: function() {
        // 更新布局信息
        this.initLayout();
      }
    },

    // 组件的方法列表
    methods: {
      // 初始化布局
      initLayout: function() {
        // 获取最新的布局信息
        const layoutInfo = layoutUtil.getLayoutInfo();
        
        this.setData({
          tabBarHeight: layoutUtil.getTabBarStyle(),
          tabBarStyle: layoutUtil.getTabBarStyle(),
          contentStyle: layoutUtil.getTabBarContentStyle(),
          safeAreaStyle: `padding-bottom: ${layoutInfo.safeAreaBottom}px`
        });
      },

      // 更新激活状态的方法
      async updateActiveTab(path: string) {
        // console.log('更新激活状态的方法');
        // console.log('当前页面路径:', path);
        // console.log('导航菜单:', this.data.navMenus);
        if (!this.data.navMenus || !this.data.navMenus.length) return;
        
        // 先尝试完全匹配
        let index = this.data.navMenus.findIndex(item => item.url === path);
        let needLoginCheck = false;
        let matchedItem = null;
        
        // 如果没有完全匹配，则检查是否是二级页面
        if (index === -1) {
          // 遍历所有导航项
          index = this.data.navMenus.findIndex(item => {
            // 检查主导航URL是否存在且为当前路径的父级
            if (item.url) {
              const mainPath = item.url.split('/').slice(0, -1).join('/');
              const isMatch = path.startsWith(mainPath);
              
              if (isMatch) {
                // console.log('当前页面路径:', path);
                // console.log('匹配到的菜单项:', item);
                // console.log('needlogin状态:', item.needlogin);
                if (item.needlogin === 1) {
                  needLoginCheck = true;
                  matchedItem = item;
                }
              }
              return isMatch;
            }
            
            // 检查子菜单
            if (item.submenus && item.submenus.length) {
              return item.submenus.some(submenu => {
                if (submenu.url) {
                  const submenuPath = submenu.url.split('/').slice(0, -1).join('/');
                  const isMatch = path.startsWith(submenuPath);
                  
                  if (isMatch) {
                    // console.log('当前页面路径:', path);
                    // console.log('匹配到的子菜单项:', submenu);
                    // console.log('子菜单needlogin状态:', submenu.needlogin);
                    if (submenu.needlogin === 1) {
                      needLoginCheck = true;
                      matchedItem = submenu;
                    }
                  }
                  return isMatch;
                }
                return false;
              });
            }
            return false;
          });
        } else {
          // console.log('当前页面路径:', path);
          // console.log('完全匹配到的菜单项:', this.data.navMenus[index]);
          // console.log('needlogin状态:', this.data.navMenus[index].needlogin);
          if (this.data.navMenus[index].needlogin === 1) {
            needLoginCheck = true;
            matchedItem = this.data.navMenus[index];
          }
        }

        // 如果找到匹配的导航项，更新激活状态
        if (index !== -1) {
          this.setData({
            activeTabIndex: index
          });
          // 延迟检查登录状态，确保页面已完全加载
          if (needLoginCheck && matchedItem) {
            setTimeout(async () => {
              try {
                // 检查本地存储
                const token = wx.getStorageSync('token');
                const userInfo = wx.getStorageSync('userInfo');
                
                if (token && userInfo) {
                  // 验证后端登录状态
                  const res = await Api.common.logintest();
                  if (res && res.code === 401) {
                    // 未登录或登录失效，显示登录弹窗
                    this.showLoginModal();
                  }
                } else {
                  // 本地没有登录信息，显示登录弹窗
                  this.showLoginModal();
                }
              } catch (error) {
                console.error('登录状态检查失败:', error);
                this.showLoginModal();
              }
            }, 500); // 延迟500ms执行
          }
        }
      },

      // 加载导航数据
      async loadNavData() {
        wx.showLoading({ title: '加载中...' });
        try {
          this.setData({ loading: true });
          const navMenus = await Api.nav.getMenuList();
          wx.hideLoading();
          this.setData({
            navMenus: navMenus,
            loading: false
          });
        } catch (error) {
          this.setData({ loading: false });
        }
      },

      // 页面跳转方法
      navigatePage: function(options: any) {
        const { url, type = 'navigateTo', success, fail } = options;
        
        // 统一的跳转配置
        const navigationOptions = {
          url,
          success,
          fail,
          complete: () => {
            wx.hideLoading();
          }
        };
        
        // 根据类型选择跳转方式
        if (type === 'redirectTo') {
          wx.redirectTo(navigationOptions);
        } else {
          wx.navigateTo(navigationOptions);
        }
      },

      // 切换tab的处理函数
      switchTab: function(e: WechatMiniprogram.TouchEvent) {
        // 如果处于收缩状态，先展开导航栏
        if (this.data.isCollapsed) {
          this.toggleCollapse();
          return;
        }

        const {categoryId, url, index} = e.currentTarget.dataset;
        
        // 如果是分类类型的tab（没有url）
        if (!url) {
          // 只有在点击不同的分类时才更新状态
          if (this.data.selectedCategory !== categoryId) {
            this.setData({
              submenuslist: this.data.navMenus[index].submenus,
              selectedCategory: categoryId
            });
            this.toggleCategoryPanel(categoryId);
          } else {
            // 如果点击的是当前已选中的分类，则只切换面板显示状态
            this.toggleCategoryPanel(categoryId);
          }
          return;
        }

        // 如果是普通tab（有url），且目标页面不是当前页面
        if (url !== this.data.currentPath) {
          const pages = getCurrentPages();
          
          // 显示加载提示
          wx.showLoading({
            title: '加载中...',
            mask: true
          });

          // 当页面栈接近最大限制时，使用redirectTo
          if (pages.length >= 4) {
            this.navigatePage({
              url,
              type: 'redirectTo',
              success: () => {
                this.setData({
                  activeTabIndex: index,
                  currentPath: url,
                  showCategoryPanel: false
                });
                wx.hideLoading();
              },
              fail: () => {
                wx.hideLoading();
              }
            });
          } else {
            // 正常情况下使用navigateTo
            this.navigatePage({
              url,
              success: () => {
                this.setData({
                  activeTabIndex: index,
                  currentPath: url,
                  showCategoryPanel: false
                });
                wx.hideLoading();
              },
              fail: () => {
                wx.hideLoading();
              }
            });
          }
        }
      },

      // // 切换分类面板
      toggleCategoryPanel(categoryId: string) {
        if (this.data.selectedCategory === categoryId && this.data.showCategoryPanel) {
          this.setData({
            showCategoryPanel: false
          });
        } else {
          this.setData({
            showCategoryPanel: true
          });
        }
      },

      // 关闭分类面板
      closeCategoryPanel: function() {
        this.setData({
          showCategoryPanel: false
        });
      },
      // 工具项点击处理函数
      onToolTap: function(e: WechatMiniprogram.TouchEvent) {
        const { url } = e.currentTarget.dataset;
        
        if (url && url !== this.data.currentPath) {
          // 检查页面是否存在
          const checkPageExists = () => {
            try {
              // 移除开头的斜杠并获取页面路径，同时移除URL参数
              const pagePath = (url.startsWith('/') ? url.substr(1) : url).split('?')[0];
              
              // 尝试获取小程序页面配置
              let pageConfig: string[] = [];
              let subPackages: Array<{root: string; pages: string[]}> = [];
              
              try {
                pageConfig = __wxConfig.pages || [];
                subPackages = __wxConfig.subPackages || __wxConfig.subpackages || [];
              } catch (e) {
                console.warn('获取页面配置失败，可能在开发环境中:', e);
                return true;
              }
              
              // 检查主包中是否存在该页面
              if (pageConfig.includes(pagePath)) {
                return true;
              }
              
              // 检查分包中是否存在该页面
              for (const subPackage of subPackages) {
                const fullPath = `${subPackage.root}/${pagePath}`;
                if (subPackage.pages.includes(pagePath) || pageConfig.includes(fullPath)) {
                  return true;
                }
              }
              
              return false;
            } catch (err) {
              console.error('检查页面是否存在时出错:', err);
              return true;
            }
          };

          // 如果页面不存在，显示"功能正在制作中"的提示
          if (!checkPageExists()) {
            wx.showToast({
              title: '功能正在制作中',
              icon: 'none',
              duration: 2000
            });
            this.closeCategoryPanel();
            return;
          }

          const pages = getCurrentPages();
          // 当页面数达到5个时，清理前4个页面
          if (pages.length === 4) {  // 即将打开第5个页面时
            this.navigatePage({
              url,
              type: 'redirectTo',
              success: () => {
              }
            });
          } else {
            this.navigatePage({
              url,
              success: () => {
              }
            });
          }
        }
        this.closeCategoryPanel();
      },
      preventDefault() {
        // 阻止事件冒泡
      },

      // 处理登录弹窗事件
      handleLoginModalEvent(data: {show: boolean, source?: string}) {
        // 更新本地的登录弹窗状态
        this.setData({
          loginModalVisible: data.show
        });
        
        // 如果事件来源不是自己，则标记页面有登录功能
        if (data.source && data.source !== 'tabBar') {
          this.setData({
            hasPageLoginFunc: true
          });
        }
      },
      
      // 处理登录成功事件
      handleLoginSuccess(data: any) {
        // 确保data和data.userInfo不为空
        if (data && data.userInfo) {
          this.setData({
            userInfo: data.userInfo,
            notouch: false
          });
        } else {
          // 如果userInfo为空，只设置notouch
          this.setData({
            notouch: false
          });
          console.log('警告: 接收到的userInfo为空');
        }
      },
      
      // 显示登录弹窗的方法
      showLoginModal() {
        // 如果页面有自己的登录功能或已有登录弹窗显示，则不显示
        if (this.data.hasPageLoginFunc || this.data.loginModalVisible) {
          // console.log('页面有登录功能或已有登录弹窗显示，tab-bar 不显示登录弹窗');
          return;
        }
        
        // 通过事件总线发送登录弹窗事件
        eventBus.emit('loginModalEvent', {
          show: true,
          source: 'tabBar',
          callback: (userInfo) => {
            console.log('用户成功登录:', userInfo);
            // 确保userInfo不为空
            if (userInfo) {
              this.setData({
                notouch: false,
                userInfo: userInfo
              });
              
              // 通知其他组件登录成功
              eventBus.emit('loginSuccess', { userInfo });
            } else {
              this.setData({
                notouch: false
              });
              console.log('警告: 回调中接收到的userInfo为空');
            }
          }
        });
      },

      toggleCollapse() {
        const newCollapsedState = !this.data.isCollapsed;
        // 定义展开和收缩的高度
        const expandedHeight = 98; // 展开时的高度
        const collapsedHeight = 24; // 收缩时的高度 24px
        this.setData({
          isCollapsed: newCollapsedState,
          currentHeight: newCollapsedState ? collapsedHeight : expandedHeight
        });
        
        // 触发组件事件
        this.triggerEvent('collapseChange', { isCollapsed: newCollapsedState });
        
        // 使用事件总线广播状态变化
        eventBus.emit('tabBarCollapseChange', {
          isCollapsed: newCollapsedState,
          expandedHeight: expandedHeight, // 展开时的高度
          collapsedHeight: collapsedHeight, // 收缩时的高度
          currentHeight: newCollapsedState ? collapsedHeight : expandedHeight // 当前高度
        });
        
      },

      // 处理页面注册登录功能
      handleRegisterLoginPage(data: {source: string}) {
        // 标记页面有登录功能
        this.setData({
          hasPageLoginFunc: true
        });
        console.log('已注册页面登录功能:', data.source);
      },
    }
  });
})(); 
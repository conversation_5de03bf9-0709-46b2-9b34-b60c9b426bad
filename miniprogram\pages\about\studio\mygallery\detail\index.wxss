/* 背景图片 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.content-scroll {
  flex: 1;
  padding: 16rpx;
  box-sizing: border-box;
}

/* 加载指示器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(91, 157, 243, 0.2);
  border-top: 4rpx solid #3C7FD7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #718096;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 信息卡片样式 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px) saturate(1.8);
  -webkit-backdrop-filter: blur(25px) saturate(1.8);
  border-radius: 24rpx;
  margin: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.04),
    0 12rpx 32rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 16rpx;
}

.card-info {
  font-size: 28rpx;
  color: #4A5568;
}

.info-item {
  margin-bottom: 8rpx;
  display: flex;
}

.label {
  color: #718096;
  min-width: 160rpx;
}

/* 状态文本样式 */
.status {
  font-weight: 500;
}

.status-completed {
  color: #38A169;
}

.status-pending {
  color: #DD6B20;
}

.status-rejected {
  color: #E53E3E;
}

/* 下载全部按钮容器 */
.download-all-container {
  margin: 16rpx;
  padding: 12rpx;
}

.download-all-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #38A169;
  color: white;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(56, 161, 105, 0.3);
  border: none;
  transition: all 0.2s ease;
}

.download-all-btn::after {
  border: none;
}

.download-all-btn:active {
  transform: translateY(2rpx);
  background: #2F855A;
}

.download-all-btn[disabled] {
  background: #A0AEC0;
  color: #EDF2F7;
}

.download-all-icon {
  font-weight: bold;
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 下载进度条容器 */
.download-progress-container {
  margin: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #4A5568;
}

.progress-percent {
  font-size: 26rpx;
  color: #38A169;
  font-weight: 600;
}

.progress-bar-container {
  height: 14rpx;
  background: #E2E8F0;
  border-radius: 7rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #48BB78, #38A169);
  border-radius: 7rpx;
  transition: width 0.3s ease;
}

.progress-tip {
  margin-top: 12rpx;
  font-size: 22rpx;
  color: #718096;
  text-align: center;
  line-height: 1.4;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 8rpx;
}

/* 下载提示文本 */
.download-tip {
  width: 100%;
  padding: 16rpx 24rpx;
  background: rgba(56, 161, 105, 0.1);
  color: #2F855A;
  font-size: 26rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  text-align: center;
}

.download-icon-small {
  font-weight: bold;
  font-size: 28rpx;
}

.image-item {
  position: relative;
  width: calc(50% - 16rpx);
  margin: 8rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.gallery-image {
  width: 100%;
  height: 340rpx;
  display: block;
}

/* 保护状态下的图片样式 */
.protected-image {
  user-select: none;
  -webkit-user-select: none;
  pointer-events: auto;
  filter: contrast(0.95) brightness(0.95);
}

/* 水印样式 */
.watermark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 10;
}

.watermark-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.6);
  transform: rotate(-30deg);
  letter-spacing: 4rpx;
  margin: 6rpx 0;
}

/* 防盗保护提示 */
.protection-notice {
  display: flex;
  align-items: center;
  margin: 16rpx;
  padding: 20rpx;
  background: rgba(237, 137, 54, 0.1);
  border-left: 8rpx solid #ED8936;
  border-radius: 8rpx;
}

.notice-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: #ED8936;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 16rpx;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #C05621;
  margin-bottom: 6rpx;
}

.notice-desc {
  font-size: 24rpx;
  color: #DD6B20;
}

.image-number {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(60, 127, 215, 0.9);
  color: white;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

/* 下载按钮 */
.download-btn {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 50rpx;
  height: 50rpx;
  background: rgba(56, 161, 105, 0.9);
  color: white;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  transition: all 0.2s ease;
}

.download-btn:active {
  transform: translateY(2rpx);
  background: rgba(47, 133, 90, 0.9);
}

.download-icon {
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #718096;
  margin-bottom: 40rpx;
}

.action-btn {
  background: #3C7FD7;
  color: white;
  font-size: 28rpx;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(60, 127, 215, 0.2);
  border: none;
}

.action-btn::after {
  border: none;
}

/* 禁止滚动样式 */
.no-scroll {
  overflow: hidden !important;
} 
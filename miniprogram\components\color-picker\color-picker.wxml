<view class="color-picker" style="z-index: {{visible ? zIndex : 'auto'}};">
  <!-- 触发按钮 -->
  <view class="color-trigger" bindtap="showPicker">
    <view class="color-preview-wrapper">
      <view class="transparency-grid"></view>
      <view class="color-preview" style="background-color: {{currentColor}};"></view>
    </view>
    <block wx:if="{{showColorValue}}">
      <text class="color-value">{{currentColor}}</text>
    </block>
  </view>

  <!-- 弹出层 -->
  <block wx:if="{{visible}}">
    <!-- 遮罩层 -->
    <view 
      class="popup-mask {{visible ? 'show' : ''}}" 
      style="z-index: {{zIndex}}"
      catch:tap="hidePicker"
      catch:touchmove="preventDefault"
    ></view>
    
    <!-- 弹出内容 -->
    <view 
      class="popup-content {{visible ? 'show' : ''}}" 
      style="z-index: {{zIndex + 1}}"
      catch:tap="stopPropagation"
      catch:touchmove="preventDefault"
    >
      <!-- 颜色选择区域 -->
      <view class="picker-panel">
        <!-- 主选择区域 -->
        <view class="picker-main">
          <!-- 饱和度/明度选择器 -->
          <view 
            class="sv-picker" 
            catch:touchstart="onSVTouchStart"
            catch:touchmove="onSVTouchMove"
            catch:touchend="onSVTouchEnd"
            catch:touchcancel="onSVTouchEnd"
            style="background-color: hsl({{hue}}, 100%, 50%);"
          >
            <view class="sv-white"></view>
            <view class="sv-black"></view>
            <view class="sv-cursor" style="left: {{svCursorX}}px; top: {{svCursorY}}px;"></view>
          </view>

          <!-- 色相选择器 -->
          <view 
            class="hue-picker"
            catch:touchstart="onHueTouchStart"
            catch:touchmove="onHueTouchMove"
            catch:touchend="onHueTouchEnd"
            catch:touchcancel="onHueTouchEnd"
          >
            <view class="hue-cursor" style="top: {{hueCursorX}}px;"></view>
          </view>
        </view>

        <!-- 透明度选择器 -->
        <view class="alpha-container">
          <view class="transparency-grid"></view>
          <view 
            class="alpha-slider"
            style="background: linear-gradient(to right, transparent, {{pureColor}});"
            catch:touchstart="onAlphaTouchStart"
            catch:touchmove="onAlphaTouchMove"
            catch:touchend="onAlphaTouchEnd"
            catch:touchcancel="onAlphaTouchEnd"
          >
            <view class="alpha-cursor" style="left: {{alphaCursorX}}px;"></view>
          </view>
        </view>

        <!-- 预设颜色 -->
        <block wx:if="{{showPresets}}">
          <scroll-view class="preset-colors" scroll-x enable-flex enhanced show-scrollbar="{{false}}">
            <view 
              wx:for="{{presetColors}}" 
              wx:key="*this"
              class="preset-color {{currentColor === item ? 'active' : ''}}"
              style="background-color: {{item}};"
              data-color="{{item}}"
              catch:tap="onPresetColorTap"
            >
              <view class="transparency-grid" wx:if="{{item === '#FFFFFF'}}"></view>
            </view>
          </scroll-view>
        </block>

        <!-- 确认按钮 -->
        <view class="confirm-btn" catch:tap="onConfirm">确定</view>
      </view>
    </view>
  </block>
</view> 
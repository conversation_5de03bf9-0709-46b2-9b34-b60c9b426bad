// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api, {} from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
import pageHelper from '../../../utils/pageHelper';

Component({

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    pagetitle: '系列招募',
    recruitmentList: [],
    filterTabs: ['全部', '招募中', '未开始', '已结束', '已申请'],
    currentTab: 0, // 当前选中的标签页索引
    showInvitationTab: false, // 控制是否显示当前邀请标签
    isLogin: false, // 登录状态标记
    loginChecked: false, // 标记是否已经检查过登录状态
    loginModalVisible: false, // 登录弹窗是否显示
    lastClickedId: null, // 存储最后一次点击的ID
    showDetailPopup: false, // 控制详情浮窗显示
    currentRecruitment: null, // 当前查看的招募信息
    artStyleTags: [], // 存储艺术风格标签
    canShare: wx.canIUse('button.open-type.share'), // 判断是否支持分享按钮
    isLoading: false, // 加载状态指示
    isPagePreparing: true, // 新增页面准备标志
    _loadingStyles: false, // 新增加载画风数据标志
  },
  properties: {
    _boundHandlers: {
      type: null,
      value: null
    }
  },

  lifetimes: {
    created() {
      // 在创建时绑定所有处理函数
      this._boundHandlers = {
        handleTabBarChange: this.handleTabBarChange.bind(this),
        handleLoginSuccess: this.handleLoginSuccess.bind(this),
        handleLoginModalEvent: this.handleLoginModalEvent.bind(this)
      };
    },

    attached() {
      // 只监听导航栏折叠事件
      eventBus.on('tabBarCollapseChange', this._boundHandlers.handleTabBarChange);
      
      // 监听登录相关事件
      eventBus.on('loginSuccess', this._boundHandlers.handleLoginSuccess);
      eventBus.on('loginModalEvent', this._boundHandlers.handleLoginModalEvent);
      
      // 注册页面登录功能
      eventBus.emit('registerLoginPage', {
        source: 'recruitment'
      });
      this.getStylesAndTypes();
      // 直接加载列表数据，不检查登录
      this.loadRecruitmentList();
    },

    detached() {
      eventBus.off('tabBarCollapseChange', this._boundHandlers.handleTabBarChange);
      eventBus.off('loginSuccess', this._boundHandlers.handleLoginSuccess);
      eventBus.off('loginModalEvent', this._boundHandlers.handleLoginModalEvent);
      
      // 清理引用
      this._boundHandlers = null;
    },
  },

  pageLifetimes: {
    show: function() {
      // 设置一个标志，表示页面正在准备中
      this.setData({
        isPagePreparing: true
      });
      
      // 延长等待时间，确保页面完全加载
      setTimeout(() => {
        try {
          // 获取页面参数
          this.getCurrentPageParams();
          
          // 检查是否有recruitment_id参数
          const urlParams = this.data.urlParams || {};
          const urlRecruitmentId = urlParams.recruitment_id;
          
          // 根据是否有recruitment_id参数决定是否显示当前邀请标签
          if (urlRecruitmentId) {
            // 有recruitment_id参数，添加当前邀请标签并选中
            this.setData({
              filterTabs: ['当前邀请', '全部', '招募中', '未开始', '已结束', '已申请'],
              currentTab: 0,
              showInvitationTab: true
            });
          } else {
            // 没有recruitment_id参数，使用默认标签列表并选中全部
            this.setData({
              filterTabs: ['全部', '招募中', '未开始', '已结束', '已申请'],
              currentTab: 0,
              showInvitationTab: false
            });
          }
          
          // 添加额外检查，确保数据已经准备好
          if (this.data.recruitmentList && this.data.recruitmentList.length > 0) {
            // 数据已准备好，可以检查是否需要打开详情
            this.checkAndOpenRecruitmentDetail();
            // 应用过滤条件
            this.filterRecruitmentList(this.data.currentTab);
          } else {
            // 先加载数据再检查
            this.loadRecruitmentList().then(() => {
              // 加载成功后再检查是否需要打开详情
              this.checkAndOpenRecruitmentDetail();
            }).catch(error => {
              console.error('加载招募列表失败:', error);
            });
          }
          
          // 检查是否需要刷新页面数据
          this.checkAndRefreshData();
          
          // 清除准备标志
          this.setData({
            isPagePreparing: false
          });
        } catch (error) {
          console.error('页面显示逻辑出错:', error);
          this.setData({
            isPagePreparing: false
          });
        }
      }, 600); // 增加延迟时间，确保页面完全准备好
    }
  },

  methods: {
    // 处理导航栏折叠变化
    handleTabBarChange(data: { isCollapsed: any; currentHeight: any; }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    // 获取当前页面参数
    getCurrentPageParams() {
      try {
        const pages = getCurrentPages();
        if (!pages || pages.length === 0) {
          // 延迟尝试，因为有时页面栈在初始化完成后才能获取
          setTimeout(() => {
            this.getCurrentPageParams();
          }, 500);
          return;
        }
        
        const currentPage = pages[pages.length - 1];
        if (!currentPage) {
          return;
        }
        
        // 尝试获取URL参数
        const options = currentPage.options || {};
        
        this.setData({
          urlParams: options
        });
        
        // 检查是否有分享参数或URL查询参数
        const sourceParams = { ...options };
        
        // 如果有scene参数(扫码进入)，需要解析scene
        if (sourceParams.scene) {
          try {
            // 场景值通常是经过编码的，需要解码
            const sceneStr = String(sourceParams.scene || '');
            let scene = '';
            
            try {
              scene = decodeURIComponent(sceneStr);
            } catch (decodeErr) {
              console.error('解码scene参数失败:', decodeErr);
              scene = sceneStr; // 解码失败时使用原始字符串
            }
            
            // 解析scene参数，格式通常为 key1=value1&key2=value2
            const sceneParams = {};
            
            // 确保scene是有效的字符串再处理
            if (scene && typeof scene === 'string' && scene.length > 0) {
              // 安全地分割字符串
              const pairs = scene.split('&').filter(Boolean);
              
              for (let i = 0; i < pairs.length; i++) {
                const pair = pairs[i];
                
                if (pair && typeof pair === 'string') {
                  // 安全地分割key=value
                  const parts = pair.split('=').filter(Boolean);
                  
                  if (parts && parts.length >= 2) {
                    const key = parts[0];
                    const value = parts[1];
                    
                    if (key && value) {
                      sceneParams[key] = value;
                    }
                  }
                }
              }
            }
            
            Object.assign(sourceParams, sceneParams);
          } catch (e) {
            console.error('解析scene参数失败:', e);
          }
        }
        
        // 检查是否为空对象
        const hasParams = Object.keys(sourceParams).length > 0;
        
        if (hasParams) {
          // 将参数保存到data中
          this.setData({
            urlParams: sourceParams
          });
        }
      } catch (error) {
        console.error('获取页面参数时出错:', error);
      }
    },
    // 加载画风和画种列表
    getStylesAndTypes() {
      // 设置加载状态
      this.setData({
        _loadingStyles: true
      });
      
      return Api.recruitment.getStylesAndTypes()
        .then(res => {
          
          // 过滤掉"不限"选项（一般ID为1或名称为"不限"）
          const filteredTypes = (res.types.list || []).filter(item => item.code !== 'unlimited');
          const filteredStyles = (res.styles.list || []).filter(item => item.code !== 'unlimited');
          
          this.setData({ 
            artworkTypes: filteredTypes,
            artStyles: filteredStyles,
            _loadingStyles: false
          });
          
          return {artworkTypes: filteredTypes, artStyles: filteredStyles};
        })
        .catch(error => {
          console.error('获取画风和画种列表失败:', error);
          wx.showToast({
            title: '获取画风和画种列表失败',
            icon: 'error'
          });
          
          // 清除加载状态
          this.setData({
            _loadingStyles: false
          });
          
          // 继续传递错误以便上层处理
          return Promise.reject(error);
        });
    },
    // 加载招募列表方法
    loadRecruitmentList() {
      this.setData({ isLoading: true });
      
      return Api.recruitment.getRecruitmentList()
        .then(res => {
          
          // 确保res是有效对象
          if (!res || typeof res !== 'object') {
            throw new Error('API返回数据无效');
          }
          
          // 确保list是数组
          const rawList = res.list || [];
          if (!Array.isArray(rawList)) {
            throw new Error('API返回列表格式无效');
          }
          
          // 验证和清理列表数据
          const cleanList = rawList.map((item) => {
            // 防御性检查，确保item是有效对象
            if (!item || typeof item !== 'object') {
              return null; // 无效项返回null，后续过滤掉
            }
            
            // 深度复制对象，防止修改原始数据
            let cleanItem;
            try {
              cleanItem = JSON.parse(JSON.stringify(item));
            } catch (jsonError) {
              console.error('解析招募项数据时出错:', jsonError);
              cleanItem = { ...item }; // 回退到浅拷贝
            }
            
            // 确保art_styles是有效的字符串
            cleanItem.art_styles = this.ensureStringValue(cleanItem.art_styles || '');
            
            // 预先解析art_styles并存储，避免后续重复解析
            try {
              cleanItem._parsedArtStyles = this.parseArtStyleTags(cleanItem.art_styles);
            } catch (parseError) {
              console.error('预解析艺术风格标签时出错:', parseError);
              cleanItem._parsedArtStyles = [];
            }
            
            // 确保joined_count是有效的对象
            if (!cleanItem.joined_count || typeof cleanItem.joined_count !== 'object') {
              cleanItem.joined_count = { total: 0, approved: 0 };
            }
            
            // 确保applications是数组
            if (!Array.isArray(cleanItem.applications)) {
              cleanItem.applications = [];
            }
            
            return cleanItem;
          }).filter(item => item !== null); // 过滤掉无效的项
          
          this.setData({
            recruitmentList: cleanList,
            isLoading: false
          });
          
          // 加载数据后执行筛选
          this.filterRecruitmentList(this.data.currentTab);
          
          return cleanList; // 返回处理后的列表，方便调用方使用
        })
        .catch(error => {
          console.error('获取招募列表失败:', error);
          this.setData({ 
            isLoading: false,
            recruitmentList: [] // 确保失败时列表为空数组而不是undefined
          });
          
          wx.showToast({
            title: '获取招募列表失败',
            icon: 'none'
          });
          
          return []; // 失败时返回空数组
        });
    },

    // 切换标签页
    switchTab(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({ currentTab: index });
      
      // 根据不同标签筛选数据
      this.filterRecruitmentList(index);
    },

    // 筛选招募列表
    filterRecruitmentList(tabIndex) {
      try {
        // 确保tabIndex是有效数字
        const safeTabIndex = Number(tabIndex);
        if (isNaN(safeTabIndex) || safeTabIndex < 0 || safeTabIndex >= this.data.filterTabs.length) {
          console.error('无效的标签索引:', tabIndex);
          return;
        }
        
        // 确保获取到有效数据
        const allData = this.data.recruitmentList || [];
        
        // 如果没有数据，直接设置空数组并返回
        if (!Array.isArray(allData) || allData.length === 0) {
          this.setData({
            filteredRecruitmentList: []
          });
          return;
        }
        
        let filteredData = [];
        const now = new Date(); // 当前时间
        const userInfo = wx.getStorageSync('userInfo'); // 获取当前用户信息
        const currentUserId = userInfo ? userInfo.id : null; // 获取当前用户ID
        
        // 获取URL参数中的recruitment_id
        const urlParams = this.data.urlParams || {};
        const urlRecruitmentId = urlParams.recruitment_id ? Number(urlParams.recruitment_id) : null;
        
        // 首先为所有数据添加状态和按钮文字
        const processedData = allData.map(item => {
          // 防御性检查，确保item是有效对象
          if (!item || typeof item !== 'object') {
            return null; // 无效项返回null，后续过滤掉
          }
          
          // 克隆item以避免修改原始数据
          const processedItem = { ...item };
          
          try {
            // 安全地获取日期
            let startDate, endDate;
            try {
              startDate = new Date(processedItem.recruitment_start_date);
              endDate = new Date(processedItem.recruitment_end_date);
              
              // 检查日期是否有效
              if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                throw new Error('无效的日期格式');
              }
            } catch (dateError) {
              console.error('处理日期时出错:', dateError);
              // 设置默认日期，当前日期前后各一天
              startDate = new Date(now.getTime() - 86400000); // 昨天
              endDate = new Date(now.getTime() + 86400000);   // 明天
            }
            
            // 检查是否已申请
            let isApplied = false;
            try {
              if (currentUserId && 
                  processedItem.applications && 
                  Array.isArray(processedItem.applications) && 
                  processedItem.applications.some(app => app && app.artist_id === currentUserId)) {
                isApplied = true;
                processedItem.isApplied = true;
              } else {
                isApplied = false;
                processedItem.isApplied = false;
              }
            } catch (appError) {
              console.error('检查申请状态时出错:', appError);
              isApplied = false;
              processedItem.isApplied = false;
            }
            
            // 确定招募状态 - 按优先级：已结束 > 未开始 > 招募中
            let recruitStatus;
            if (endDate < now) {
              // 已结束状态优先级最高
              recruitStatus = 'ended';
              processedItem.recruitStatus = 'ended';
              processedItem.buttonText = '已结束';
            } else if (startDate > now) {
              // 未开始状态次之
              recruitStatus = 'notStarted';
              processedItem.recruitStatus = 'notStarted';
              processedItem.buttonText = '预览';
            } else {
              // 招募中
              recruitStatus = 'active';
              processedItem.recruitStatus = 'active';
              processedItem.buttonText = '申请';
            }
            
            // 确保状态已正确设置
            if (processedItem.recruitStatus !== recruitStatus) {
              processedItem.recruitStatus = recruitStatus;
            }
            
            // 如果已申请但未结束，显示"已申请"
            if (isApplied && recruitStatus !== 'ended') {
              processedItem.buttonText = '已申请';
            }
            
            // 确保art_styles是字符串
            processedItem.art_styles = this.ensureStringValue(processedItem.art_styles || '');
            
            // 预处理art_styles，避免在WXML中直接调用split
            try {
              // 检查是否已加载画风数据
              if (this.data.artStyles && this.data.artStyles.length > 0) {
                // 已加载数据，直接解析
                processedItem.artStyleTagArray = this.parseArtStyleTags(processedItem.art_styles);
              } else {
                // 尚未加载数据，使用简单分割方法
                processedItem.artStyleTagArray = processedItem.art_styles 
                  ? processedItem.art_styles.split(',').filter(s => s && s.trim() !== '')
                  : [];
                
                // 异步加载画风数据
                if (!this.data._loadingStyles) {
                  this.setData({
                    _loadingStyles: true
                  });
                  
                  this.getStylesAndTypes().then(() => {
                    // 加载完成后，重新调用过滤方法进行更新
                    this.setData({
                      _loadingStyles: false
                    });
                    this.filterRecruitmentList(this.data.currentTab);
                  }).catch(err => {
                    this.setData({
                      _loadingStyles: false
                    });
                    console.error('加载画风数据失败:', err);
                  });
                }
              }
            } catch (parseError) {
              console.error('预处理艺术风格标签出错:', parseError);
              processedItem.artStyleTagArray = [];
            }
            
            return processedItem;
          } catch (processError) {
            console.error('处理招募项时出错:', processError);
            // 出错时返回带基本属性的对象
            return {
              ...processedItem,
              recruitStatus: 'error',
              isApplied: false,
              buttonText: '数据错误',
              art_styles: '',
            };
          }
        }).filter(item => item !== null); // 过滤掉无效的项
        
        // 根据标签筛选
        try {
          // 确定当前是否显示当前邀请标签
          const hasInvitationTab = this.data.showInvitationTab;
          
          // 根据是否有当前邀请标签调整索引
          const tabIndexWithoutInvitation = hasInvitationTab ? safeTabIndex - 1 : safeTabIndex;
          
          if (hasInvitationTab && safeTabIndex === 0) {
            // 当前邀请标签
            if (urlRecruitmentId) {
              // 当前邀请标签不过滤openrecruitment
              filteredData = processedData.filter(item => item.id === urlRecruitmentId);
            } else {
              filteredData = []; // 如果没有指定ID，显示空列表
            }
          } else {
            // 处理其他标签
            switch(tabIndexWithoutInvitation) {
              case 0: // 全部
                // 其他标签需要筛选openrecruitment为"公开招募"的项
                filteredData = processedData.filter(item => item.openrecruitment === "公开招募");
                break;
              case 1: // 招募中
                filteredData = processedData.filter(item => 
                  item.recruitStatus === 'active' && item.openrecruitment === "公开招募"
                );
                break;
              case 2: // 未开始
                filteredData = processedData.filter(item => 
                  item.recruitStatus === 'notStarted' && item.openrecruitment === "公开招募"
                );
                break;
              case 3: // 已结束
                filteredData = processedData.filter(item => 
                  item.recruitStatus === 'ended' && item.openrecruitment === "公开招募"
                );
                break;
              case 4: // 已申请
                filteredData = processedData.filter(item => 
                  item.isApplied === true && item.openrecruitment === "公开招募"
                );
                break;
              default:
                filteredData = processedData.filter(item => item.openrecruitment === "公开招募"); // 默认显示公开招募
            }
          }
        } catch (filterError) {
          console.error('筛选数据时出错:', filterError);
          filteredData = processedData; // 出错时显示全部
        }
        
        // 确保结果是数组
        if (!Array.isArray(filteredData)) {
          filteredData = [];
        }
        
        // 更新界面
        this.setData({
          filteredRecruitmentList: filteredData
        });
      } catch (error) {
        console.error('筛选招募列表时出错:', error);
        // 出错时设置空数组
        this.setData({
          filteredRecruitmentList: []
        });
      }
    },

    // 检查并自动打开招募详情
    checkAndOpenRecruitmentDetail() {
      try {
        // 防御性检查，确保urlParams存在
        const urlParams = this.data.urlParams || {};
        
        // 检查urlParams中是否存在recruitment_id
        let recruitmentId = urlParams.recruitment_id;
        
        // 确保recruitmentId是有效值
        if (!recruitmentId) {
          return; // 如果没有ID，直接返回不处理
        }
        
        // 转换为数字
        recruitmentId = Number(recruitmentId);
        if (isNaN(recruitmentId) || recruitmentId <= 0) {
          console.error('无效的招募ID:', urlParams.recruitment_id);
          return;
        }
        
        // 使用Promise链式调用处理异步操作
        const loadDataAndShowDetail = () => {
          // 先检查招募列表是否已经加载
          if (!this.data.recruitmentList || this.data.recruitmentList.length === 0) {
            return this.loadRecruitmentList()
              .then(() => this.loadStylesIfNeeded())
              .then(() => this.checkLoginAndShowDetail(recruitmentId))
              .catch(error => {
                console.error('加载数据失败:', error);
              });
          } else {
            // 招募列表已加载，继续后续步骤
            return this.loadStylesIfNeeded()
              .then(() => this.checkLoginAndShowDetail(recruitmentId))
              .catch(error => {
                console.error('加载数据或检查登录失败:', error);
              });
          }
        };
        
        // 执行加载流程
        loadDataAndShowDetail();
      } catch (error) {
        console.error('自动打开招募详情失败:', error);
      }
    },
    
    // 根据需要加载画风数据的辅助方法
    loadStylesIfNeeded() {
      if (!this.data.artStyles || this.data.artStyles.length === 0) {
        return this.getStylesAndTypes().catch(error => {
          console.error('加载画风和画种数据失败:', error);
          // 继续处理，即使加载失败也不影响基本功能
          return Promise.resolve();
        });
      }
      // 已经有数据，直接返回已解决的Promise
      return Promise.resolve();
    },
    
    // 检查登录并显示详情的辅助方法
    checkLoginAndShowDetail(recruitmentId) {
      // 检查登录状态
      return this.checkLoginStatus()
        .then(isLoggedIn => {
          if (!isLoggedIn) {
            // 如果未登录，记录ID并返回，由登录成功回调处理
            this.setData({ lastClickedId: recruitmentId });
            return;
          }
          
          // 在执行showDetail前，确保列表中确实有这个招募ID
          // 对于通过URL参数指定的ID，无论openrecruitment是什么值都允许访问
          const targetRecruitment = this.data.recruitmentList.find(
            item => item && item.id === recruitmentId
          );
          
          if (!targetRecruitment) {
            console.error('没有找到对应ID的招募信息');
            return;
          }

          // 确保targetRecruitment中的art_styles是有效值
          if (targetRecruitment) {
            targetRecruitment.art_styles = this.ensureStringValue(targetRecruitment.art_styles || '');
          }
          
          // 构造安全的事件对象
          const safeEvent = { 
            currentTarget: { 
              dataset: { 
                id: recruitmentId 
              } 
            } 
          };
          
          // 直接调用showDetail
          this.showDetail(safeEvent);
        })
        .catch(error => {
          console.error('检查登录状态失败:', error);
        });
    },

    // 显示详情
    showDetail(e: WechatMiniprogram.CustomEvent) {
      try {
        // 获取ID，确保ID存在且有效
        const idParam = e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.id;
        const id = idParam ? Number(idParam) : null;
        
        if (!id) {
          console.error('无效的招募ID');
          return;
        }
        
        // 检查登录状态
        const isLogin = this.data.isLogin;
        
        if (!isLogin) {
          // 如果未登录，记录ID并显示登录弹窗
          this.setData({ lastClickedId: id });
          this.showLoginModal();
          return;
        }
        
        // 确保recruitmentList存在且不为空
        const recruitmentList = this.data.recruitmentList || [];
        if (recruitmentList.length === 0) {
          wx.showToast({
            title: '招募列表为空',
            icon: 'none'
          });
          return;
        }
        
        // 从URL参数获取recruitment_id
        const urlParams = this.data.urlParams || {};
        const urlRecruitmentId = urlParams.recruitment_id ? Number(urlParams.recruitment_id) : null;
        
        // 安全查找对应的招募信息
        // 判断是否是当前邀请的招募ID（通过URL参数传入的）
        // 如果是通过URL参数指定的ID，无论openrecruitment是什么值都允许访问
        // 如果不是通过URL参数指定的，只允许访问openrecruitment为"公开招募"的
        const isCurrentInvitation = id === urlRecruitmentId;
        const selectedRecruitment = recruitmentList.find(item => 
          item && typeof item === 'object' && item.id === id && 
          (isCurrentInvitation || item.openrecruitment === "公开招募")
        );
        
        if (!selectedRecruitment) {
          wx.showToast({
            title: '未找到招募信息',
            icon: 'none'
          });
          return;
        }
        
        // 复制一份数据以避免修改原始数据
        let recruitmentData;
        try {
          recruitmentData = JSON.parse(JSON.stringify(selectedRecruitment));
        } catch (jsonError) {
          console.error('解析招募数据时出错:', jsonError);
          recruitmentData = { ...selectedRecruitment }; // 浅拷贝作为备选方案
        }
        
        // 确保recruitmentData是有效的对象
        if (!recruitmentData || typeof recruitmentData !== 'object') {
          console.error('无效的招募数据');
          return;
        }
        
        // 确保art_styles字段存在且为字符串
        recruitmentData.art_styles = this.ensureStringValue(recruitmentData.art_styles || '');
        
        // 确保状态字段存在
        this.ensureRecruitmentStatus(recruitmentData);
        
        // 设置申请按钮文字 - 按优先级：已结束 > 未开始 > 已申请 > 正常
        if (recruitmentData.recruitStatus === 'ended') {
          recruitmentData.applyButtonText = '招募已结束';
        } else if (recruitmentData.recruitStatus === 'notStarted') {
          recruitmentData.applyButtonText = '招募未开始';
        } else if (recruitmentData.isApplied) {
          recruitmentData.applyButtonText = '已申请';
        } else {
          recruitmentData.applyButtonText = '申请系列';
        }
        
        
        // 检查是否需要加载画风数据
        if (!this.data.artStyles || this.data.artStyles.length === 0) {
          // 如果画风数据未加载，先加载数据
          this.getStylesAndTypes()
            .then(() => {
              // 加载完成后再解析标签
              const artStyleTags = this.parseArtStyleTags(recruitmentData.art_styles) || [];
              
              this.setData({
                currentRecruitment: recruitmentData,
                artStyleTags: artStyleTags,
                showDetailPopup: true
              });
            })
            .catch(error => {
              console.error('加载画风数据失败:', error);
              
              this.setData({
                currentRecruitment: recruitmentData,
                artStyleTags: [],
                showDetailPopup: true
              });
            });
        } else {
          // 已有画风数据，直接解析并显示
          const artStyleTags = this.parseArtStyleTags(recruitmentData.art_styles) || [];
          
          this.setData({
            currentRecruitment: recruitmentData,
            artStyleTags: Array.isArray(artStyleTags) ? artStyleTags : [], // 确保是数组
            showDetailPopup: true
          });
        }
      } catch (error) {
        console.error('获取招募详情失败:', error);
        wx.showToast({
          title: '获取招募详情失败',
          icon: 'none'
        });
      }
    },

    // 关闭详情浮窗
    closeDetailPopup() {
      try {
        // 先安全清理可能导致错误的字段
        this.setData({ 
          artStyleTags: [], // 先清空艺术风格标签数组
          showDetailPopup: false // 然后关闭浮窗
        });
        
        // 延迟清空当前招募信息，避免界面闪烁
        setTimeout(() => {
          this.setData({
            currentRecruitment: null
          });
        }, 300);
      } catch (error) {
        console.error('关闭详情弹窗时出错:', error);
        // 出错时强制重置所有相关状态
        this.setData({
          showDetailPopup: false,
          currentRecruitment: null,
          artStyleTags: []
        });
      }
    },

    // 申请系列
    applyForSeries() {
      const currentRecruitment = this.data.currentRecruitment;
      const id = currentRecruitment && currentRecruitment.id;
      if (id) {
        // 跳转到详情页面
        wx.navigateTo({
          url: `/pages/series/recruitment_detail/index?id=${id}`
        });
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      // 避免重复检查
      if (this.data.loginChecked) {
        return Promise.resolve(this.data.isLogin);
      }
      
      this.setData({ loginChecked: true });
      
      // 检查本地存储
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      
      if (token && userInfo) {
        // 验证后端登录状态
        return Api.common.logintest()
          .then(res => {
            if (res && res.code !== 401) {
              this.setData({ isLogin: true });
              return true;
            }
            // 登录失效，显示登录弹窗
            this.showLoginModal();
            return false;
          })
          .catch(error => {
            console.error('登录状态检查失败:', error);
            this.showLoginModal();
            return false;
          });
      }
      
      // 未登录，显示登录弹窗
      this.showLoginModal();
      return Promise.resolve(false);
    },

    // 处理登录弹窗事件
    handleLoginModalEvent(data: {show: boolean, source?: string}) {
      this.setData({
        loginModalVisible: data.show
      });
    },

    // 登录成功回调
    handleLoginSuccess(data: {userInfo: any}) {
      this.setData({
        isLogin: true,
        loginModalVisible: false
      });
      
      // 登录成功后重新筛选，确保"已申请"标签能正确显示
      this.filterRecruitmentList(this.data.currentTab);
      
      // 登录成功后获取之前点击的详情并显示浮窗
      const lastClickedId = this.data.lastClickedId;
      // 检查是否有 URL 参数中的 recruitment_id
      const urlParams = this.data.urlParams;
      const urlRecruitmentId = urlParams && urlParams.recruitment_id;
      
      // 优先使用 lastClickedId（用户点击的），如果没有则使用 URL 参数中的 recruitment_id
      const recruitmentId = lastClickedId || urlRecruitmentId;
      
      if (recruitmentId) {
        // 检查招募列表是否存在
        if (!this.data.recruitmentList || this.data.recruitmentList.length === 0) {
          return;
        }
        
        // 查找对应的招募信息
        // 判断是否是当前邀请的招募ID（通过URL参数传入的）
        // 如果是通过URL参数指定的ID，无论openrecruitment是什么值都允许访问
        const isCurrentInvitation = Number(recruitmentId) === Number(urlRecruitmentId);
        const selectedRecruitment = this.data.recruitmentList.find(item => 
          item.id === Number(recruitmentId) && 
          (isCurrentInvitation || item.openrecruitment === "公开招募")
        );
        
        if (selectedRecruitment) {
          // 确保画风数据已加载
          if (!this.data.artStyles || this.data.artStyles.length === 0) {
            // 如果画风数据未加载，先加载数据
            this.getStylesAndTypes()
              .then(() => {
                // 在显示前确保状态字段存在
                this.ensureRecruitmentStatus(selectedRecruitment);
                
                // 加载完成后再解析标签
                const artStyleTags = this.parseArtStyleTags(selectedRecruitment.art_styles);
                this.setData({
                  currentRecruitment: selectedRecruitment,
                  artStyleTags: artStyleTags,
                  showDetailPopup: true,
                  lastClickedId: null
                });
              })
              .catch(error => {
                console.error('加载画风数据失败:', error);
                // 即使加载失败，也显示招募信息，只是可能无法正确显示画风
                
                // 在显示前确保状态字段存在
                this.ensureRecruitmentStatus(selectedRecruitment);
                
                this.setData({
                  currentRecruitment: selectedRecruitment,
                  artStyleTags: [],
                  showDetailPopup: true,
                  lastClickedId: null
                });
              });
          } else {
            // 已有画风数据，直接解析并显示
            
            // 在显示前确保状态字段存在
            this.ensureRecruitmentStatus(selectedRecruitment);
            
            const artStyleTags = this.parseArtStyleTags(selectedRecruitment.art_styles);
            this.setData({
              currentRecruitment: selectedRecruitment,
              artStyleTags: artStyleTags,
              showDetailPopup: true,
              lastClickedId: null
            });
          }
        }
      }
    },

    // 显示登录弹窗
    showLoginModal() {
      if (this.data.loginModalVisible) return;
      
      // 保存URL参数中的recruitment_id
      const urlParams = this.data.urlParams;
      const urlRecruitmentId = urlParams && urlParams.recruitment_id;
      
      if (urlRecruitmentId && !this.data.lastClickedId) {
        this.setData({ lastClickedId: Number(urlRecruitmentId) });
      }
      
      eventBus.emit('loginModalEvent', {
        show: true,
        source: 'recruitment',
        callback: (userInfo) => {
          this.setData({
            isLogin: true,
            loginModalVisible: false
          });
          
          // 登录成功后重新筛选，确保"已申请"标签能正确显示
          this.filterRecruitmentList(this.data.currentTab);
          
          // 登录成功后获取之前点击的详情并显示浮窗
          const lastClickedId = this.data.lastClickedId;
          
          if (lastClickedId) {
            // 判断是否是当前邀请的招募ID（通过URL参数传入的）
            // 如果是通过URL参数指定的ID，无论openrecruitment是什么值都允许访问
            const isCurrentInvitation = lastClickedId === Number(urlRecruitmentId);
            // 查找对应的招募信息
            const selectedRecruitment = this.data.recruitmentList.find(item => 
              item.id === lastClickedId && 
              (isCurrentInvitation || item.openrecruitment === "公开招募")
            );
            
            if (selectedRecruitment) {
              // 确保画风数据已加载
              if (!this.data.artStyles || this.data.artStyles.length === 0) {
                // 如果画风数据未加载，先加载数据
                this.getStylesAndTypes()
                  .then(() => {
                    // 在显示前确保状态字段存在
                    this.ensureRecruitmentStatus(selectedRecruitment);
                    
                    // 加载完成后再解析标签
                    const artStyleTags = this.parseArtStyleTags(selectedRecruitment.art_styles);
                    this.setData({
                      currentRecruitment: selectedRecruitment,
                      artStyleTags: artStyleTags,
                      showDetailPopup: true,
                      lastClickedId: null
                    });
                  })
                  .catch(error => {
                    console.error('加载画风数据失败:', error);
                    // 即使加载失败，也显示招募信息，只是可能无法正确显示画风
                    this.setData({
                      currentRecruitment: selectedRecruitment,
                      artStyleTags: [],
                      showDetailPopup: true,
                      lastClickedId: null
                    });
                  });
              } else {
                // 已有画风数据，直接解析并显示
                const artStyleTags = this.parseArtStyleTags(selectedRecruitment.art_styles);
                this.setData({
                  currentRecruitment: selectedRecruitment,
                  artStyleTags: artStyleTags,
                  showDetailPopup: true,
                  lastClickedId: null
                });
              }
            }
          }
        }
      });
    },

    // 确保值为字符串的工具函数
    ensureStringValue(value) {
      if (value === undefined || value === null) {
        return '';
      }
      
      // 确保返回的是字符串类型
      return typeof value === 'string' ? value : String(value);
    },
    
    // 解析艺术风格标签的工具函数
    parseArtStyleTags(artStyles) {
      // 默认返回空数组
      let artStyleTags = [];
      
      try {
        // 防御性检查，确保是有值的
        if (artStyles === null || artStyles === undefined) {
          console.log('艺术风格为空，返回空数组');
          return [];
        }
        
        // 转为安全的字符串
        const safeArtStyles = this.ensureStringValue(artStyles);
        
        // 只有当字符串非空时才进行分割
        if (safeArtStyles && safeArtStyles.trim() !== '') {
          // 安全地分割字符串
          try {
            // 将逗号分隔的字符串转为数组
            const artStyleIds = safeArtStyles.split(',')
              .filter(item => item && typeof item === 'string')
              .map(item => item.trim())
              .filter(item => item !== '');
            
            // 检查是否有画风列表数据
            const artStyles = this.data.artStyles || [];
            
            // 如果有art_styles数据，将ID转为名称
            if (artStyles.length > 0) {
              // 尝试将每个值转为数字并在artStyles中查找对应项
              artStyleTags = artStyleIds.map(idStr => {
                const id = Number(idStr);
                if (!isNaN(id)) {
                  // 在画风列表中查找对应ID的项
                  const style = artStyles.find(s => s.id === id);
                  return style ? style.name : idStr; // 如果找到则返回名称，否则返回原始ID
                }
                return idStr; // 如果无法转为有效数字，直接返回原字符串
              });
            } else {
              // 没有画风列表数据，返回原始ID字符串
              artStyleTags = artStyleIds;
            }
          } catch (splitError) {
            console.error('分割艺术风格标签时出错:', splitError);
            return [];
          }
        } else {
          // 字符串为空，返回空数组
          return [];
        }
      } catch (error) {
        console.error('处理艺术风格标签时出错:', error);
        return [];
      }
      
      return artStyleTags;
    },

    // 添加分享方法
    onShareAppMessage(e) {
      // 获取当前招募信息ID
      const currentRecruitment = this.data.currentRecruitment;
      const recruitmentId = currentRecruitment && currentRecruitment.id;
      
      // 设置分享标题、路径和图片
      let shareTitle = currentRecruitment && currentRecruitment.title || '系列招募';
      let sharePath = `/pages/series/recruitment/index?recruitment_id=${recruitmentId}`;
      let imageUrl = currentRecruitment && currentRecruitment.cover_image || '';
      
      return {
        title: shareTitle,
        path: sharePath,
        imageUrl: imageUrl,
        success: function() {
          wx.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: function() {
          wx.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      };
    },

    // 检查是否需要刷新数据
    checkAndRefreshData() {
      const urlParams = this.data.urlParams || {};
      
      // 检查是否存在refresh参数
      if (urlParams.refresh === '1') {
        // 重新加载数据
        this.loadRecruitmentList();
        
        // 移除refresh参数，防止重复刷新
        this.removeRefreshParam();
      }
    },
    
    // 移除URL中的refresh参数
    removeRefreshParam() {
      try {
        const { urlParams } = this.data;
        if (!urlParams) return;
        
        // 创建新的参数对象，排除refresh和timestamp
        const newParams = {};
        for (const key in urlParams) {
          if (key !== 'refresh' && key !== 'timestamp') {
            newParams[key] = urlParams[key];
          }
        }
        
        // 仅更新数据层的urlParams，不进行页面跳转
        this.setData({
          urlParams: newParams
        });
      } catch (error) {
        console.error('移除refresh参数时出错:', error);
      }
    },

    // 确保状态字段存在
    ensureRecruitmentStatus(recruitment) {
      // 确保recruitStatus存在
      if (recruitment.recruitStatus === undefined) {
        const now = new Date();
        let startDate, endDate;
        
        try {
          startDate = new Date(recruitment.recruitment_start_date);
          endDate = new Date(recruitment.recruitment_end_date);
          
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('无效的日期格式');
          }
        } catch (e) {
          console.error('处理日期时出错:', e);
          startDate = new Date(now.getTime() - 86400000); // 昨天
          endDate = new Date(now.getTime() + 86400000);   // 明天
        }
        
        // 确定招募状态
        if (endDate < now) {
          recruitment.recruitStatus = 'ended';
        } else if (startDate > now) {
          recruitment.recruitStatus = 'notStarted';
        } else {
          recruitment.recruitStatus = 'active';
        }
      }
      
      // 确保isApplied字段存在
      if (recruitment.isApplied === undefined) {
        try {
          // 检查是否已申请
          const userInfo = wx.getStorageSync('userInfo');
          const currentUserId = userInfo ? userInfo.id : null;
          
          if (currentUserId && 
              recruitment.applications && 
              Array.isArray(recruitment.applications) && 
              recruitment.applications.some(app => app && app.artist_id === currentUserId)) {
            recruitment.isApplied = true;
          } else {
            recruitment.isApplied = false;
          }
        } catch (error) {
          console.error('检查申请状态时出错:', error);
          recruitment.isApplied = false;
        }
      }
      
      return recruitment; // 返回处理后的对象，方便链式调用
    },
  }
});

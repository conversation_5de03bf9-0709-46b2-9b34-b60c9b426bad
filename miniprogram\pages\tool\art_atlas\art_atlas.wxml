<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="图鉴生成" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}" bindtap="onTapBackground" bindscroll="onPageScroll">
      <!-- 作品浮窗 -->
      <view class="work-popup" wx:if="{{workPopup.show}}" catchtap="closeWorkPopup" catchtouchmove="preventTouchMove">
        <view class="work-popup-content" catchtap="preventBubble">
          <image class="work-popup-image" src="{{workPopup.url}}" mode="aspectFit"/>
          <input class="work-name-input popup-input" 
                 value="{{workPopup.name}}" 
                 data-artist-id="{{workPopup.artistId}}" 
                 data-work-id="{{workPopup.workId}}"
                 bindinput="onWorkNameInput"
                 placeholder="请输入作品名称"/>
          <view class="work-popup-actions">
            <button class="work-popup-delete" bindtap="deleteWorkFromPopup">删除作品</button>
            <button class="work-popup-close" bindtap="closeWorkPopup">关闭</button>
          </view>
        </view>
      </view>
      
      <!-- 内容区域开始 -->
      <view class="background-section">
        <view class="section-header">
          <view class="section-left">
            <text class="section-icon">🖼️</text>
            <text class="section-title">图鉴设置</text>
          </view>
          <button class="reupload-btn" bindtap="chooseImage" wx:if="{{backgroundImage}}">
            <text class="reupload-icon">🔄</text>
            <text class="reupload-text">重新上传</text>
          </button>
        </view>
        <!-- 添加TAB导航 -->
        <view class="bg-tabs" wx:if="{{backgroundImage}}">
          <view class="tab-item {{currentBgTab === 'preview' ? 'active' : ''}}" 
                bindtap="switchBgTab" 
                data-tab="preview">
            <text class="tab-text">图鉴背景设置</text>
          </view>
          <view class="tab-item {{currentBgTab === 'settings' ? 'active' : ''}}" 
                bindtap="switchBgTab" 
                data-tab="settings">
            <text class="tab-text">作品效果设置</text>
          </view>
        </view>
        <!-- TAB内容区域 -->
        <view class="tab-content {{currentBgTab === 'preview' ? '' : 'hidden'}}">
          <view class="upload-area" style="{{imageStyle}}">
            <block wx:if="{{!backgroundImage}}">
              <view class="upload-placeholder" bindtap="chooseImage">
                <text class="upload-icon">+</text>
                <text class="upload-text">点击上传图片</text>
              </view>
            </block>
            <view wx:else class="preview-container" style="{{imageStyle}}">
              <image class="preview-image" src="{{backgroundImage}}" mode="aspectFit" style="{{imageStyle}}"></image>
              <view class="preview-overlay" style="top: {{overlayInfo.top}}; left: {{overlayInfo.left}}; right: {{overlayInfo.right}}; bottom: {{overlayInfo.bottom}}; background-color: {{overlaySettings.backgroundColor}}; border-width: {{overlaySettings.borderWidth}}rpx; border-style: {{overlaySettings.borderStyle}}; border-radius: {{overlaySettings.borderRadius}}rpx; border-color: {{overlaySettings.borderColor}};" catchtap="toggleOverlaySettings">
                <text class="overlay-text">画师作品展示区</text>
                <view class="resize-handle top" 
                      bindtouchstart="onResizeStart" 
                      bindtouchmove="onResizeMove" 
                      bindtouchend="onResizeEnd"
                      data-direction="top">
                  <view class="resize-arrow">↑</view>
                </view>
                <view class="resize-handle right" 
                      bindtouchstart="onResizeStart" 
                      bindtouchmove="onResizeMove" 
                      bindtouchend="onResizeEnd"
                      data-direction="right">
                  <view class="resize-arrow">→</view>
                </view>
                <view class="resize-handle bottom" 
                      bindtouchstart="onResizeStart" 
                      bindtouchmove="onResizeMove" 
                      bindtouchend="onResizeEnd"
                      data-direction="bottom">
                  <view class="resize-arrow">↓</view>
                </view>
                <view class="resize-handle left" 
                      bindtouchstart="onResizeStart" 
                      bindtouchmove="onResizeMove" 
                      bindtouchend="onResizeEnd"
                      data-direction="left">
                  <view class="resize-arrow">←</view>
                </view>
              </view>
              <view class="artist-name-tag" 
                    bindtouchstart="onTagTouchStart" 
                    bindtouchmove="onTagTouchMove" 
                    bindtouchend="onTagTouchEnd"
                    style="width: {{artistTagInfo.width}}; height: {{artistTagInfo.height}}; left: {{artistTagInfo.left}}; top: {{artistTagInfo.top}};">
                <text class="artist-name" 
                      style="font-size:{{fontSettings.fontSize}}rpx;border-width:{{fontSettings.borderWidth}}rpx;border-style:{{fontSettings.borderStyle}};border-color:{{fontSettings.borderColor}};background-color:{{fontSettings.backgroundColor}};border-radius:{{fontSettings.borderRadius}}rpx;color:{{fontSettings.color}}"
                      catchtap="toggleFontSettings">画师名字</text>
              </view>
            </view>
          </view>
          <view class="image-info" wx:if="{{backgroundImage}}">
            <!-- 展示区设置面板 -->
            <view class="font-settings settings-panel {{overlaySettings.showSettings ? '' : 'hidden'}}" id="overlaySettings" catchtap="preventBubble">
              <view class="settings-header">展示区设置</view>
              <view class="settings-content">
                <view class="settings-row">
                  <text class="settings-label">背景色</text>
                  <color-picker bindchange="onOverlayColorChange" value="{{overlaySettings.backgroundColor}}" value="{{overlaySettings.backgroundColor}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">圆角大小</text>
                  <slider min="0" max="20" value="{{overlaySettings.borderRadius}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustOverlayBorderRadius"
                          show-value/>
                </view>
                <view class="settings-row">
                  <text class="settings-label">边框宽度</text>
                  <slider min="0" max="5" value="{{overlaySettings.borderWidth}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustOverlayBorderWidth"
                          show-value/>
                </view>
                <view class="settings-row" wx:if="{{overlaySettings.borderWidth >= 1}}">
                  <text class="settings-label">边框色</text>
                  <color-picker bindchange="onOverlayBorderColorChange" value="{{overlaySettings.borderColor}}" value="{{overlaySettings.borderColor}}" />
                </view>
                <view class="settings-row border-style" wx:if="{{overlaySettings.borderWidth >= 1}}">
                  <text class="settings-label">边框样式</text>
                  <radio-group class="border-style-group" bindchange="onOverlayBorderStyleChange">
                    <label class="border-style-item"><radio value="solid" checked="{{overlaySettings.borderStyle === 'solid'}}"/>实线</label>
                    <label class="border-style-item"><radio value="dashed" checked="{{overlaySettings.borderStyle === 'dashed'}}"/>虚线</label>
                    <label class="border-style-item"><radio value="dotted" checked="{{overlaySettings.borderStyle === 'dotted'}}"/>点线</label>
                  </radio-group>
                </view>
              </view>
            </view>
            <!-- 画师名字设置面板 -->
            <view class="font-settings settings-panel {{fontSettings.showSettings ? '' : 'hidden'}}" id="fontSettings" catchtap="preventBubble">
              <view class="settings-header">画师名字设置</view>
              <view class="settings-content">
                <view class="settings-row">
                  <text class="settings-label">背景颜色</text>
                  <color-picker bindchange="onColorChange" value="{{fontSettings.backgroundColor}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">字体颜色</text>
                  <color-picker bindchange="onFontColorChange" value="{{fontSettings.color}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">圆角大小</text>
                  <slider min="0" max="20" value="{{fontSettings.borderRadius}}" bindchange="adjustBorderRadius" activeColor="#6c5ce7" backgroundColor="#eee" block-size="20" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">边框宽度</text>
                  <slider min="0" max="5" value="{{fontSettings.borderWidth}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustBorderWidth"
                          show-value/>
                </view>
                <view class="settings-row" wx:if="{{fontSettings.borderWidth >= 1}}">
                  <text class="settings-label">边框色</text>
                  <color-picker bindchange="onBorderColorChange" value="{{fontSettings.borderColor}}" value="{{fontSettings.borderColor}}" />
                </view>
                <view class="settings-row border-style" wx:if="{{fontSettings.borderWidth >= 1}}">
                  <text class="settings-label">边框样式</text>
                  <radio-group class="border-style-group" bindchange="onBorderStyleChange">
                    <label class="border-style-item"><radio value="solid" checked="{{fontSettings.borderStyle === 'solid'}}"/>实线</label>
                    <label class="border-style-item"><radio value="dashed" checked="{{fontSettings.borderStyle === 'dashed'}}"/>虚线</label>
                    <label class="border-style-item"><radio value="dotted" checked="{{fontSettings.borderStyle === 'dotted'}}"/>点线</label>
                  </radio-group>
                </view>
              </view>
            </view>
            <view class="info-item">
              <text class="info-label">显示尺寸：</text>
              <text class="info-value">{{displaySize.width}}px × {{displaySize.height}}px</text>
              <view class="fold-btn {{foldStatus.sizeCalc ? 'folded' : ''}}" bindtap="toggleFold" data-section="sizeCalc">
                <text class="fold-icon">▼</text>
              </view>
            </view>
            <view class="info-formula {{foldStatus.sizeCalc ? 'hidden' : ''}}">
              <text class="formula-text">▼ 显示尺寸是如何转换为原始尺寸的？</text>
              <text class="formula-text">1. 原始宽度 = 显示宽度 ÷ 缩放比例</text>
              <text class="formula-text">   {{originalSize.width}}px = {{displaySize.width}}px ÷ {{scaleRatio}}</text>
              <text class="formula-text">2. 原始高度 = 显示高度 ÷ 缩放比例</text>
              <text class="formula-text">   {{originalSize.height}}px = {{displaySize.height}}px ÷ {{scaleRatio}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">原始尺寸：</text>
              <text class="info-value">{{originalSize.width}}px × {{originalSize.height}}px</text>
            </view>
            <view class="info-formula {{foldStatus.sizeCalc ? 'hidden' : ''}}">
              <text class="formula-text">▼ 原始尺寸是如何转换为显示尺寸的？</text>
              <text class="formula-text">1. 显示宽度 = 原始宽度 × 缩放比例</text>
              <text class="formula-text">   {{displaySize.width}}px = {{originalSize.width}}px × {{scaleRatio}}</text>
              <text class="formula-text">2. 显示高度 = 原始高度 × 缩放比例</text>
              <text class="formula-text">   {{displaySize.height}}px = {{originalSize.height}}px × {{scaleRatio}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">缩放比例：</text>
              <text class="info-value">{{scaleRatio * 100}}%</text>
            </view>
            <view class="info-formula {{foldStatus.sizeCalc ? 'hidden' : ''}}">
              <text class="formula-text">▼ 缩放比例是如何计算的？</text>
              <text class="formula-text">1. 缩放比例 = 显示宽度 ÷ 原始宽度</text>
              <text class="formula-text">   {{scaleRatio}} = {{displaySize.width}}px ÷ {{originalSize.width}}px</text>
              <text class="formula-text">2. 百分比显示 = 缩放比例 × 100</text>
              <text class="formula-text">   {{scaleRatio * 100}}% = {{scaleRatio}} × 100</text>
            </view>
            <view class="info-divider"></view>
            <view class="info-item">
              <text class="info-label">展示区尺寸：</text>
              <text class="info-value">{{overlayInfo.width}} × {{overlayInfo.height}}</text>
              <view class="fold-btn {{foldStatus.overlayCalc ? 'folded' : ''}}" bindtap="toggleFold" data-section="overlayCalc">
                <text class="fold-icon">▼</text>
              </view>
            </view>
            <view class="info-formula {{foldStatus.overlayCalc ? 'hidden' : ''}}">
              <text class="formula-text">展示区实际尺寸 = 显示尺寸 - (2 × 边距 × 缩放比例)</text>
              <text class="formula-text">边距 = {{BASE_SIZES.MARGIN}}rpx = {{displayMarginInPx}}px（显示）</text>
            </view>
            <view class="info-item">
              <text class="info-label">展示区边距：</text>
              <text class="info-value">上{{overlayInfo.top}} 下{{overlayInfo.bottom}} 左{{overlayInfo.left}} 右{{overlayInfo.right}}</text>
            </view>
            <view class="info-formula {{foldStatus.overlayCalc ? 'hidden' : ''}}">
              <text class="formula-text">边距实际尺寸 = 30rpx × (显示宽度 ÷ 750) × 缩放比例</text>
            </view>
            <view class="info-item">
              <text class="info-label">画师标签：</text>
              <text class="info-value">{{artistTagInfo.width}} × {{artistTagInfo.height}}，位置({{tagPosition.x}}px, {{tagPosition.y}}px)</text>
              <view class="fold-btn {{foldStatus.tagCalc ? 'folded' : ''}}" bindtap="toggleFold" data-section="tagCalc">
                <text class="fold-icon">▼</text>
              </view>
            </view>
            <view class="info-formula {{foldStatus.tagCalc ? 'hidden' : ''}}">
              <text class="formula-text">▼ 标签尺寸和位置计算</text>
              <text class="formula-text">1. 显示尺寸计算：</text>
              <text class="formula-text">   宽度 = (120rpx + 2 × 16rpx) × (显示宽度 ÷ 750)</text>
              <text class="formula-text">   高度 = 44rpx × (显示宽度 ÷ 750)</text>
              <text class="formula-text">2. 导出时尺寸和位置计算：</text>
              <text class="formula-text">   实际宽度 = 显示宽度 × 缩放比例</text>
              <text class="formula-text">   实际高度 = 显示高度 × 缩放比例</text>
              <text class="formula-text">   实际X坐标 = {{tagPosition.x}}px × {{scaleRatio}} = {{tagPosition.x * scaleRatio}}px</text>
              <text class="formula-text">   实际Y坐标 = {{tagPosition.y}}px × {{scaleRatio}} = {{tagPosition.y * scaleRatio}}px</text>
            </view>
          </view>
        </view>
        <view class="tab-content {{currentBgTab === 'settings' ? '' : 'hidden'}}" style="padding: 0;">
          <!-- 这里暂时留空，后续可以添加更多设置选项 -->
          <!-- 添加作品边框设置面板 -->
        <view class="settings-panel work-border-settings" style="width: 100%;margin: 0;">
          <view class="settings-header">作品样式设置</view>
          <view class="settings-content">
            <view class="settings-group">
              <view class="settings-group-title">边框设置</view>
              <view class="settings-row switch-row">
                <text class="settings-label">显示边框</text>
                <switch checked="{{showWorkBorder}}" bindchange="toggleWorkBorder" color="#6c5ce7"/>
              </view>
              <block wx:if="{{showWorkBorder}}">
                <view class="settings-row">
                  <text class="settings-label">边框颜色</text>
                  <color-picker bindchange="onWorkBorderColorChange" value="{{workBorderColor}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">边框宽度</text>
                  <slider min="1" max="5" value="{{workBorderWidth}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustWorkBorderWidth"
                          show-value/>
                </view>
              </block>
            </view>
            
            <view class="settings-group">
              <view class="settings-group-title">外发光效果</view>
              <view class="settings-row switch-row">
                <text class="settings-label">显示外发光</text>
                <switch checked="{{showWorkGlow}}" bindchange="toggleWorkGlow" color="#6c5ce7"/>
              </view>
              <block wx:if="{{showWorkGlow}}">
                <view class="settings-row">
                  <text class="settings-label">发光颜色</text>
                  <color-picker bindchange="onWorkGlowColorChange" value="{{workGlowColor}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">发光强度</text>
                  <slider min="0" max="20" value="{{workGlowIntensity}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustWorkGlowIntensity"
                          show-value/>
                </view>
              </block>
            </view>

            <view class="settings-group">
              <view class="settings-group-title">作品名称</view>
              <view class="settings-row">
                <text class="settings-label">文字颜色</text>
                <color-picker bindchange="onWorkNameColorChange" value="{{workNameColor}}" />
              </view>
              <view class="settings-row">
                <text class="settings-label">背景颜色</text>
                <color-picker bindchange="onWorkNameBgColorChange" value="{{workNameBgColor}}" />
              </view>
            </view>
          </view>
        </view>
        </view>
        <canvas type="2d" id="highQualityCanvas" style="position: absolute; left: -9999px; width: 1px; height: 1px;"></canvas>
      </view>
      <view class="" wx:if="{{backgroundImage}}">
        <button class="add-artist-btn" bindtap="showAddArtistOptions">
          <text class="add-icon">+</text>
          <text class="add-text">添加画师</text>
        </button>
      </view>

      <!-- 添加画师选项弹窗 -->
      <view class="add-options-popup {{showAddOptions ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="add-options-mask" bindtap="closeAddOptions"></view>
        <view class="add-options-content">
          <!-- <view class="add-options-header">
            <text class="add-options-title">添加画师</text>
            <view class="add-options-close" bindtap="closeAddOptions">×</view>
          </view> -->
          <view class="add-options-body">
            <button class="add-option-btn" bindtap="addArtist" data-type="normal">
              <text class="add-option-icon">+</text>
              <text class="add-option-text">正常添加</text>
              <text class="add-option-desc">添加到画师列表末尾</text>
            </button>
            <button class="add-option-btn" bindtap="showInsertOptions" wx:if="{{artistList.length >= 2}}">
              <text class="add-option-icon">↩</text>
              <text class="add-option-text">中间插入</text>
              <text class="add-option-desc">选择插入位置</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 插入位置选择弹窗 -->
      <view class="insert-options-popup {{showInsertOptions ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="insert-options-mask" bindtap="closeInsertOptions"></view>
        <view class="insert-options-content">
          <view class="insert-options-header">
            <text class="insert-options-title">选择插入位置</text>
            <view class="insert-options-close" bindtap="closeInsertOptions">×</view>
          </view>
          <view class="insert-options-body">
            <view class="insert-options-list">
              <view class="insert-option-item" 
                    wx:for="{{artistList}}" 
                    wx:key="id"
                    bindtap="addArtist"
                    data-type="insert"
                    data-position="{{index}}">
                <text class="insert-option-num">{{index + 1}}</text>
                <text class="insert-option-name">{{item.name || '未命名画师'}}</text>
                <text class="insert-option-hint">插入到此位置后</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="artist-section" wx:if="{{backgroundImage}}" wx:for="{{artistList}}" wx:key="id">
        <view class="artist-tag-num {{item.isSelected ? 'selected' : ''}}" 
              bindtap="toggleArtistSelection" 
              data-artist-id="{{item.id}}">
              <text class="text">{{index + 1}}</text>
          <text class="checkmark" wx:if="{{item.isSelected}}">✓</text>
        </view>
        <view class="artist-list" wx:if="{{backgroundImage}}">
          <view class="artist-item">
            <view class="artist-info">
              <input class="artist-name-input" 
                     value="{{item.name}}" 
                     data-id="{{item.id}}" 
                     bindinput="onArtistNameInput"
                     placeholder="{{item.name || '画师名字'}}"/>
              <view class="artist-buttons">
                <button class="add-work-btn" bindtap="uploadArtistWork" data-artist-id="{{item.id}}">
                  <text class="add-work-text">+ 添加作品</text>
                </button>
                <button class="download-btn" bindtap="previewArtistImage" data-artist-id="{{item.id}}">
                  <image class="download-icon" src="{{constants.STATIC_URL.ICON}}preview.svg" mode="aspectFit"></image>
                  <text class="download-text">预览图鉴</text>
                </button>
                <button class="delete-btn" catchtap="deleteArtist" data-artist-id="{{item.id}}">
                  <text class="delete-icon">×</text>
                </button>
              </view>
            </view>
            <view class="artist-preview" style="{{imageStyle}}">
              <image class="preview-image" src="{{backgroundImage}}" mode="aspectFit" style="{{imageStyle}}"></image>
              <view class="preview-overlay" style="top: {{overlayInfo.top}}; left: {{overlayInfo.left}}; right: {{overlayInfo.right}}; bottom: {{overlayInfo.bottom}}; background-color: {{overlaySettings.backgroundColor}}; border-width: {{overlaySettings.borderWidth}}rpx; border-style: {{overlaySettings.borderStyle}}; border-radius: {{overlaySettings.borderRadius}}rpx; border-color: {{overlaySettings.borderColor}};">
                <view class="works-grid" wx:if="{{item.works.length > 0}}" data-count="{{item.works.length}}">
                  <view class="work-preview-container {{workDragInfo.dragging && workDragInfo.workId === work.id ? 'dragging' : ''}} {{workDragInfo.dragging && workDragInfo.currentIndex === index ? 'target' : ''}}" 
                        wx:for="{{item.works}}" 
                        wx:for-item="work" 
                        wx:key="id"
                        data-artist-id="{{item.id}}"
                        data-work-id="{{work.id}}"
                        data-index="{{index}}"
                        style="{{workDragInfo.dragging && workDragInfo.workId === work.id ? 'transform: translate(' + (workDragInfo.currentX - workDragInfo.startX) + 'px, 0);' : ''}}">
                    <image class="work-preview" 
                           src="{{work.url}}" 
                           mode="aspectFit"
                           style="{{workPreviewStyle}}"
                           catchtap="showWorkPopup"
                           bindload="onWorkImageLoad"
                           data-artist-id="{{item.id}}"
                           data-work-id="{{work.id}}"/>
                    <view class="preview-work-delete" 
                          catchtap="deleteWork"
                          data-artist-id="{{item.id}}"
                          data-work-id="{{work.id}}">×</view>
                    <view class="work-name-overlay" wx:if="{{work.name}}" style="width: {{work.displaySize.width}}px;">
                      <view class="work-name-mask" style="background: linear-gradient(to bottom, transparent, {{workNameBgColor}});"></view>
                      <text class="work-name-text" style="color: {{workNameColor}};">{{work.name}}</text>
                    </view>
                  </view>
                </view>
              </view>
              <view class="artist-name-tag" 
                    style="width: {{artistTagInfo.width}}; height: {{artistTagInfo.height}}; left: {{artistTagInfo.left}}; top: {{artistTagInfo.top}};">
                <text class="artist-name" 
                      style="font-size:{{fontSettings.fontSize}}rpx;border-width:{{fontSettings.borderWidth}}rpx;border-style:{{fontSettings.borderStyle}};border-color:{{fontSettings.borderColor}};background-color:{{fontSettings.backgroundColor}};border-radius:{{fontSettings.borderRadius}}rpx;color:{{fontSettings.color}}">{{item.name || '画师名字'}}</text>
              </view>
            </view>
            
          </view>
        </view>
      </view>
      <!-- <view class="test-c">
        <view wx:for="{{20}}" wx:key="index" class="test-item">测试内容 {{index + 1}}</view>
      </view> -->
      <!-- 图鉴下载区 -->
      <view class="atlas-download-section">
        <view class="atlas-download-header">
          <text class="atlas-download-icon">📥</text>
          <text class="atlas-download-title">图鉴下载区</text>
        </view>
        <view class="atlas-download-buttons">
          <button class="atlas-btn atlas-btn-detect" bindtap="checkArtistStatus">
            <text class="text">检测</text>
          </button>
          <button class="atlas-btn atlas-btn-batch {{canDownload ? '' : 'disabled'}}" disabled="{{!canDownload}}" bindtap="batchDownloadAtlas">
            <text class="text">批量单画师图鉴</text>
          </button>
          <button class="atlas-btn atlas-btn-merge {{canDownload ? '' : 'disabled'}}" disabled="{{!canDownload}}" bindtap="showMergeSettings">
            <text class="text">完整合并图鉴</text>
          </button>
        </view>
      </view>

      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}art_atlas.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">图鉴生成工具</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">自定义布局</text>
                    <text class="feature-desc">支持自由调整作品展示区位置和大小</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">样式设置</text>
                    <text class="feature-desc">可调整展示区和画师名字的样式效果</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">作品管理</text>
                    <text class="feature-desc">支持添加、删除和编辑展示的作品</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">一键导出</text>
                    <text class="feature-desc">快速生成并保存完整的图鉴展示图</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">上传背景</text>
                    <text class="step-desc">选择并上传图鉴的背景图片</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">调整布局</text>
                    <text class="step-desc">设置作品展示区和画师名字的位置</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">添加作品</text>
                    <text class="step-desc">上传并排列需要展示的作品图片</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">生成导出</text>
                    <text class="step-desc">预览效果并导出最终的图鉴图片</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">合理布局</text>
                  <text class="tip-text">注意展示区大小和位置的合理分配</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">样式搭配</text>
                  <text class="tip-text">选择与背景相协调的边框和字体样式</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">图片质量</text>
                  <text class="tip-text">使用清晰度高的图片以确保最佳展示效果</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">预览确认</text>
                  <text class="tip-text">导出前仔细预览整体效果和细节</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
      <!-- 浮动按钮 -->
      <view class="floating-add-btn {{showFloatingBtn ? 'show' : ''}}" 
            style="bottom: calc({{tabBarHeight + safeAreaBottom}}px + 100rpx);" 
            catchtap="showAddArtistOptions"
            data-test="add-btn">
        <text class="floating-add-icon">+</text>
      </view>

      <view class="floating-bg-settings-btn {{showFloatingBgBtn ? 'show' : ''}}" 
            style="bottom: calc({{tabBarHeight + safeAreaBottom}}px + 200rpx);" 
            catchtap="toggleBgSettingsPopup"
            data-test="bg-settings-btn" wx:if="{{backgroundImage}}">
        <text class="floating-bg-settings-icon">🖼️</text>
      </view>

      <view class="help-btn" 
            style="bottom: calc({{tabBarHeight + safeAreaBottom}}px);" 
            catchtap="toggleHelpPopup"
            data-test="help-btn">
        <text class="help-icon">?</text>
      </view>
  
      <!-- 背景设置浮动面板 -->
      <view class="bg-settings-popup {{showBgSettingsPopup ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="bg-settings-mask" bindtap="toggleBgSettingsPopup"style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}"></view>
        <view class="bg-settings-content">
          <view class="bg-settings-header">
            <text class="bg-settings-title">图鉴设置</text>
            <view class="bg-settings-close" bindtap="toggleBgSettingsPopup">×</view>
          </view>
          <!-- 添加TAB导航 -->
          <view class="bg-settings-tabs">
            <view class="tab-item {{currentBgSettingsTab === 'preview' ? 'active' : ''}}" 
                  bindtap="switchBgSettingsTab" 
                  data-tab="preview">
              <text class="tab-text">图鉴背景设置</text>
            </view>
            <view class="tab-item {{currentBgSettingsTab === 'settings' ? 'active' : ''}}" 
                  bindtap="switchBgSettingsTab" 
                  data-tab="settings">
              <text class="tab-text">作品效果设置</text>
            </view>
          </view>
          <!-- TAB内容区域 -->
          <scroll-view scroll-y class="bg-settings-scroll">
            <!-- 背景预览TAB -->
            <view class="tab-content {{currentBgSettingsTab === 'preview' ? '' : 'hidden'}}">
              <view class="background-section popup-section" style="margin:0;padding:0">
                <view class="upload-area" style="{{imageStyle}}">
                  <view class="preview-container" style="{{imageStyle}}">
                    <image class="preview-image" src="{{backgroundImage}}" mode="aspectFit" style="{{imageStyle}}"></image>
                    <view class="preview-overlay" style="top: {{overlayInfo.top}}; left: {{overlayInfo.left}}; right: {{overlayInfo.right}}; bottom: {{overlayInfo.bottom}}; background-color: {{overlaySettings.backgroundColor}}; border-width: {{overlaySettings.borderWidth}}rpx; border-style: {{overlaySettings.borderStyle}}; border-radius: {{overlaySettings.borderRadius}}rpx; border-color: {{overlaySettings.borderColor}};" catchtap="toggleOverlaySettings">
                      <text class="overlay-text">画师作品展示区</text>
                      <view class="resize-handle top" 
                            bindtouchstart="onResizeStart" 
                            bindtouchmove="onResizeMove" 
                            bindtouchend="onResizeEnd"
                            data-direction="top">
                        <view class="resize-arrow">↑</view>
                      </view>
                      <view class="resize-handle right" 
                            bindtouchstart="onResizeStart" 
                            bindtouchmove="onResizeMove" 
                            bindtouchend="onResizeEnd"
                            data-direction="right">
                        <view class="resize-arrow">→</view>
                      </view>
                      <view class="resize-handle bottom" 
                            bindtouchstart="onResizeStart" 
                            bindtouchmove="onResizeMove" 
                            bindtouchend="onResizeEnd"
                            data-direction="bottom">
                        <view class="resize-arrow">↓</view>
                      </view>
                      <view class="resize-handle left" 
                            bindtouchstart="onResizeStart" 
                            bindtouchmove="onResizeMove" 
                            bindtouchend="onResizeEnd"
                            data-direction="left">
                        <view class="resize-arrow">←</view>
                      </view>
                    </view>
                    <view class="artist-name-tag" 
                          bindtouchstart="onTagTouchStart" 
                          bindtouchmove="onTagTouchMove" 
                          bindtouchend="onTagTouchEnd"
                          style="width: {{artistTagInfo.width}}; height: {{artistTagInfo.height}}; left: {{artistTagInfo.left}}; top: {{artistTagInfo.top}};">
                      <text class="artist-name" 
                            style="font-size:{{fontSettings.fontSize}}rpx;border-width:{{fontSettings.borderWidth}}rpx;border-style:{{fontSettings.borderStyle}};border-color:{{fontSettings.borderColor}};background-color:{{fontSettings.backgroundColor}};border-radius:{{fontSettings.borderRadius}}rpx;color:{{fontSettings.color}}"
                            catchtap="toggleFontSettings">画师名字</text>
                    </view>
                  </view>
                </view>
              </view>
              <view class="image-info">
                <!-- 展示区设置面板 -->
                <view class="font-settings settings-panel {{overlaySettings.showSettings ? '' : 'hidden'}}" id="overlaySettings" catchtap="preventBubble">
                  <view class="settings-header">展示区设置</view>
                  <view class="settings-content">
                    <view class="settings-row">
                      <text class="settings-label">背景色</text>
                      <color-picker bindchange="onOverlayColorChange" value="{{overlaySettings.backgroundColor}}" value="{{overlaySettings.backgroundColor}}" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">圆角大小</text>
                      <slider min="0" max="20" value="{{overlaySettings.borderRadius}}" 
                              block-size="16" activeColor="#6c5ce7" 
                              bindchange="adjustOverlayBorderRadius"
                              show-value/>
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">边框宽度</text>
                      <slider min="0" max="5" value="{{overlaySettings.borderWidth}}" 
                              block-size="16" activeColor="#6c5ce7" 
                              bindchange="adjustOverlayBorderWidth"
                              show-value/>
                    </view>
                    <view class="settings-row" wx:if="{{overlaySettings.borderWidth >= 1}}">
                      <text class="settings-label">边框色</text>
                      <color-picker bindchange="onOverlayBorderColorChange" value="{{overlaySettings.borderColor}}" value="{{overlaySettings.borderColor}}" />
                    </view>
                    <view class="settings-row border-style" wx:if="{{overlaySettings.borderWidth >= 1}}">
                      <text class="settings-label">边框样式</text>
                      <radio-group class="border-style-group" bindchange="onOverlayBorderStyleChange">
                        <label class="border-style-item"><radio value="solid" checked="{{overlaySettings.borderStyle === 'solid'}}"/>实线</label>
                        <label class="border-style-item"><radio value="dashed" checked="{{overlaySettings.borderStyle === 'dashed'}}"/>虚线</label>
                        <label class="border-style-item"><radio value="dotted" checked="{{overlaySettings.borderStyle === 'dotted'}}"/>点线</label>
                      </radio-group>
                    </view>
                  </view>
                </view>
                <!-- 画师名字设置面板 -->
                <view class="font-settings settings-panel {{fontSettings.showSettings ? '' : 'hidden'}}" id="fontSettings" catchtap="preventBubble">
                  <view class="settings-header">画师名字设置</view>
                  <view class="settings-content">
                    <view class="settings-row">
                      <text class="settings-label">背景颜色</text>
                      <color-picker bindchange="onColorChange" value="{{fontSettings.backgroundColor}}" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">字体颜色</text>
                      <color-picker bindchange="onFontColorChange" value="{{fontSettings.color}}" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">圆角大小</text>
                      <slider min="0" max="20" value="{{fontSettings.borderRadius}}" bindchange="adjustBorderRadius" activeColor="#6c5ce7" backgroundColor="#eee" block-size="20" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">边框宽度</text>
                      <slider min="0" max="5" value="{{fontSettings.borderWidth}}" 
                              block-size="16" activeColor="#6c5ce7" 
                              bindchange="adjustBorderWidth"
                              show-value/>
                    </view>
                    <view class="settings-row" wx:if="{{fontSettings.borderWidth >= 1}}">
                      <text class="settings-label">边框色</text>
                      <color-picker bindchange="onBorderColorChange" value="{{fontSettings.borderColor}}" value="{{fontSettings.borderColor}}" />
                    </view>
                    <view class="settings-row border-style" wx:if="{{fontSettings.borderWidth >= 1}}">
                      <text class="settings-label">边框样式</text>
                      <radio-group class="border-style-group" bindchange="onBorderStyleChange">
                        <label class="border-style-item"><radio value="solid" checked="{{fontSettings.borderStyle === 'solid'}}"/>实线</label>
                        <label class="border-style-item"><radio value="dashed" checked="{{fontSettings.borderStyle === 'dashed'}}"/>虚线</label>
                        <label class="border-style-item"><radio value="dotted" checked="{{fontSettings.borderStyle === 'dotted'}}"/>点线</label>
                      </radio-group>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 更多设置TAB -->
            <view class="tab-content {{currentBgSettingsTab === 'settings' ? '' : 'hidden'}}">
              <!-- 作品样式设置 -->
              <view class="settings-content">
                <view class="settings-group">
                  <view class="settings-group-title">边框设置</view>
                  <view class="settings-row switch-row">
                    <text class="settings-label">显示边框</text>
                    <switch checked="{{showWorkBorder}}" bindchange="toggleWorkBorder" color="#6c5ce7"/>
                  </view>
                  <block wx:if="{{showWorkBorder}}">
                    <view class="settings-row">
                      <text class="settings-label">边框颜色</text>
                      <color-picker bindchange="onWorkBorderColorChange" value="{{workBorderColor}}" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">边框宽度</text>
                      <slider min="1" max="5" value="{{workBorderWidth}}" 
                              block-size="16" activeColor="#6c5ce7" 
                              bindchange="adjustWorkBorderWidth"
                              show-value/>
                    </view>
                  </block>
                </view>
                
                <view class="settings-group">
                  <view class="settings-group-title">外发光效果</view>
                  <view class="settings-row switch-row">
                    <text class="settings-label">显示外发光</text>
                    <switch checked="{{showWorkGlow}}" bindchange="toggleWorkGlow" color="#6c5ce7"/>
                  </view>
                  <block wx:if="{{showWorkGlow}}">
                    <view class="settings-row">
                      <text class="settings-label">发光颜色</text>
                      <color-picker bindchange="onWorkGlowColorChange" value="{{workGlowColor}}" />
                    </view>
                    <view class="settings-row">
                      <text class="settings-label">发光强度</text>
                      <slider min="0" max="20" value="{{workGlowIntensity}}" 
                              block-size="16" activeColor="#6c5ce7" 
                              bindchange="adjustWorkGlowIntensity"
                              show-value/>
                    </view>
                  </block>
                </view>

                <view class="settings-group">
                  <view class="settings-group-title">作品名称</view>
                  <view class="settings-row">
                    <text class="settings-label">文字颜色</text>
                    <color-picker bindchange="onWorkNameColorChange" value="{{workNameColor}}" />
                  </view>
                  <view class="settings-row">
                    <text class="settings-label">背景颜色</text>
                    <color-picker bindchange="onWorkNameBgColorChange" value="{{workNameBgColor}}" />
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 检测结果弹窗 -->
      <view class="check-result-popup {{showCheckResult ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="check-result-mask" bindtap="closeCheckResult"></view>
        <view class="check-result-content">
          <view class="check-result-header">
            <text class="check-result-title">检测结果</text>
            <view class="check-result-close" bindtap="closeCheckResult">×</view>
          </view>
          <scroll-view scroll-y class="check-result-scroll">
            <!-- 统计信息区域 -->
            <view class="check-result-stats">
              <view class="stat-item">
                <text class="stat-label">总画师数量</text>
                <text class="stat-value">{{checkResult.totalArtists}}</text>
              </view>
              <view class="stat-item {{checkResult.noNameCount > 0 ? 'warning' : ''}}">
                <text class="stat-label">未添加名字</text>
                <text class="stat-value">{{checkResult.noNameCount}}</text>
              </view>
              <view class="stat-item {{checkResult.noWorksCount > 0 ? 'warning' : ''}}">
                <text class="stat-label">未添加作品</text>
                <text class="stat-value">{{checkResult.noWorksCount}}</text>
              </view>
            </view>
            
            <!-- 分割线 -->
            <view class="check-result-divider"></view>
            
            <!-- 详细信息区域 -->
            <view class="check-result-details">
              <view class="details-header">
                <text class="details-title">需要处理的画师</text>
              </view>
              <view class="details-list">
                <view class="detail-item" wx:for="{{checkResult.details}}" wx:key="id">
                  <view class="detail-number">{{item.number}}</view>
                  <view class="detail-issues">
                    <text class="issue-tag" wx:for="{{item.issues}}" wx:key="*this" wx:for-item="issue">{{issue}}</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 帮助按钮和弹窗 -->
      <view class="help-popup {{showHelp ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="help-mask" bindtap="toggleHelpPopup" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}"></view>
        <view class="help-content" style="{{zheight}}">
          <view class="help-header">
            <text class="help-title">使用帮助</text>
            <view class="help-close" bindtap="toggleHelpPopup">×</view>
          </view>
          <scroll-view scroll-y class="help-scroll">
            <!-- 背景操作区 -->
            <view class="help-section">
              <text class="help-section-title">背景操作区</text>
              <view class="help-list">
                <view class="help-item">
                  <text class="help-step">1. 背景设置</text>
                  <view class="help-detail">
                    <text class="text">• 点击中央"+"按钮上传背景图片</text>
                    <text class="text">• 支持重新上传替换当前背景</text>
                    <text class="text">• 可调整展示区域的位置和大小</text>
                    <text class="text">• 可设置展示区域的背景色和边框样式</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">2. 画师名字标签</text>
                  <view class="help-detail">
                    <text class="text">• 拖动半透明标签调整位置</text>
                    <text class="text">• 点击标签可设置样式</text>
                    <text class="text">• 支持自定义字体颜色和背景</text>
                    <text class="text">• 可设置边框和圆角效果</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">3. 作品效果设置</text>
                  <view class="help-detail">
                    <text class="text">• 可设置作品边框样式</text>
                    <text class="text">• 支持作品外发光效果</text>
                    <text class="text">• 可自定义作品名称样式</text>
                    <text class="text">• 所有设置实时预览</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 画师管理区 -->
            <view class="help-section">
              <text class="help-section-title">画师管理区</text>
              <view class="help-list">
                <view class="help-item">
                  <text class="help-step">1. 添加画师</text>
                  <view class="help-detail">
                    <text class="text">• 点击"添加画师"创建新画师</text>
                    <text class="text">• 支持普通添加和中间插入</text>
                    <text class="text">• 输入画师名称自动保存</text>
                    <text class="text">• 可选择多个画师进行操作</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">2. 作品管理</text>
                  <view class="help-detail">
                    <text class="text">• 点击"添加作品"上传图片</text>
                    <text class="text">• 支持同时选择多张图片</text>
                    <text class="text">• 长按作品可拖动排序</text>
                    <text class="text">• 点击作品可查看大图和编辑名称</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">3. 图鉴导出</text>
                  <view class="help-detail">
                    <text class="text">• 支持预览单个画师图鉴</text>
                    <text class="text">• 可批量导出选中画师图鉴</text>
                    <text class="text">• 支持合并导出完整图鉴</text>
                    <text class="text">• 导出前可进行状态检查</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 快捷操作 -->
            <view class="help-section">
              <text class="help-section-title">快捷操作提示</text>
              <view class="help-list">
                <view class="help-item">
                  <text class="help-step">浮动按钮</text>
                  <view class="help-detail">
                    <text class="text">• 右下角显示快捷添加按钮</text>
                    <text class="text">• 背景设置按钮方便调整</text>
                    <text class="text">• 帮助按钮随时查看说明</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">状态检查</text>
                  <view class="help-detail">
                    <text class="text">• 点击"检测"按钮查看状态</text>
                    <text class="text">• 显示未命名画师和空作品</text>
                    <text class="text">• 导出前自动检查完整性</text>
                  </view>
                </view>
                <view class="help-item">
                  <text class="help-step">批量操作</text>
                  <view class="help-detail">
                    <text class="text">• 点击序号可选择多个画师</text>
                    <text class="text">• 支持批量导出图鉴</text>
                    <text class="text">• 合并导出时保持顺序</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 预览浮层 -->
      <view class="preview-popup {{previewPopup.show ? 'show' : ''}}" catchtouchmove="onPreviewTouchMove">
        <view class="preview-mask" bindtap="closePreviewPopup" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}"></view>
        <view class="preview-content" style="{{zheight}}; top: {{navigationHeight}}px">
          <view class="preview-header">
            <text class="preview-title">图鉴预览 （70%收缩展示）</text>
            <view class="preview-close" bindtap="closePreviewPopup">×</view>
          </view>
          <scroll-view class="preview-scroll" scroll-x enable-flex>
            <view class="preview-scroll-inner">
              <image class="preview-image" 
                     src="{{previewPopup.tempFilePath}}" 
                     mode="heightFix" 
                     bindload="onPreviewImageLoad"/>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 进度提示弹窗 -->
      <view class="progress-popup {{showProgress ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="progress-mask"></view>
        <view class="progress-content">
          <view class="progress-header">
            <text class="progress-title">正在生成完整图鉴</text>
          </view>
          <view class="progress-body">
            <view class="progress-bar-wrapper">
              <view class="progress-bar" style="width: {{progress}}%;"></view>
            </view>
            <text class="progress-text">{{progress}}%</text>
            <text class="progress-status">{{progressStatus}}</text>
          </view>
        </view>
      </view>

      <!-- 合并设置弹窗 -->
      <view class="merge-settings-popup {{showMergeSettings ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="merge-settings-mask" bindtap="closeMergeSettings"></view>
        <view class="merge-settings-content">
          <view class="merge-settings-header">
            <text class="merge-settings-title">合并图鉴设置</text>
            <view class="merge-settings-close" bindtap="closeMergeSettings">×</view>
          </view>
          <view class="merge-settings-body">
            <view class="settings-group">
              <view class="settings-row">
                <text class="settings-label">图鉴背景色</text>
                <color-picker bindchange="onMergeBgColorChange" value="{{mergeSettings.backgroundColor}}" />
              </view>
              <view class="settings-row">
                <text class="settings-label">图像间距</text>
                <slider min="0" max="100" value="{{mergeSettings.spacing}}" 
                        block-size="16" activeColor="#6c5ce7" 
                        bindchange="adjustMergeSpacing"
                        show-value/>
              </view>
              <view class="settings-row switch-row">
                <text class="settings-label">显示整体边框</text>
                <switch checked="{{mergeSettings.showBorder}}" bindchange="toggleMergeBorder" color="#6c5ce7"/>
              </view>
              <block wx:if="{{mergeSettings.showBorder}}">
                <view class="settings-row">
                  <text class="settings-label">边框颜色</text>
                  <color-picker bindchange="onMergeBorderColorChange" value="{{mergeSettings.borderColor}}" />
                </view>
                <view class="settings-row">
                  <text class="settings-label">边框宽度</text>
                  <slider min="1" max="10" value="{{mergeSettings.borderWidth}}" 
                          block-size="16" activeColor="#6c5ce7" 
                          bindchange="adjustMergeBorderWidth"
                          show-value/>
                </view>
              </block>
            </view>
            <button class="start-merge-btn" bindtap="startMergeAtlas">开始生成</button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>


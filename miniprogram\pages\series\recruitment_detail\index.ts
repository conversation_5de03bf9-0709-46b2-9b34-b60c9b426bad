// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
Component({
  options: {
    styleIsolation: 'shared',
    pureDataPattern: /^_/,  // 添加纯数据字段模式
    addGlobalClass: true     // 支持全局样式类
  },

  properties: {
    id: {
      type: Number,
      value: 0
    }
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    navMenus: [],
    tools: [],
    loading: false,
    isEmpty: false,
    recruitmentList: [],
    showDetail: false,
    currentDetail: null,
    formData: {
      recruitment_id: 0,
      cards_count: '',
      pen_name: '',
      artwork_types: [],
      art_styles: '',
      shipping_address: '',
      contact_phone: '',
      contact_wechat: '',
      remarks: '',
      portfolio_images: []
    },
    submitting: false,
    recruitmentDetail: null,
    artworkTypes: [], // 画种列表
    selectedArtworkTypes: [], // 已选择的画种名称
    showArtworkPicker: false, // 是否显示画种选择器
    tempSelectedTypes: [], // 临时存储选中的画种
    artStyles: [], // 画风列表
    selectedArtStyles: [], // 已选择的画风名称
    showArtStylePicker: false, // 是否显示画风选择器
    tempSelectedStyles: [], // 临时存储选中的画风
    showLoginModal: false,
    cardCountValidateTimer: null,
    phoneError: '',
    phoneValidateTimer: null,
    penNameError: '',
    artworkTypesError: '',
    artStylesError: '',
    addressError: '',
    // 添加分享相关数据
    _shareParams: {}, // 私有数据，存储分享参数
  },

  lifetimes: {
    attached() {
      this.getStylesAndTypes();
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this)/* 底部导航栏折叠 */);
      
      // 初始化分享参数
      this.initShareParams();
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this)/* 底部导航栏折叠解除 */);
    },
    ready() {
      if (!this.data.recruitmentDetail) {
        this.initRecruitmentDetail();
      }
    }
  },

  pageLifetimes: {
    show() {
      if (!this.data.recruitmentDetail && !this.data.loading) {
        this.initRecruitmentDetail();
      }
    }
  },

  methods: {
    handleTabBarChange(data: { isCollapsed: any; currentHeight: any; }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 加载画风和画种列表
    async getStylesAndTypes() {
      try {
        const res = await Api.recruitment.getStylesAndTypes();
        // console.log('res',res);
        
        // 过滤掉"不限"选项（一般ID为1或名称为"不限"）
        const filteredTypes = (res.types.list || []).filter(item => item.id !== 1 && item.code !== 'unlimited');
        this.setData({ artworkTypes: filteredTypes });

        const filteredStyles = (res.styles.list || []).filter(item => item.id !== 1 && item.code !== 'unlimited');
        this.setData({ artStyles: filteredStyles });

      } catch (error) {
        console.error('获取画风和画种列表失败:', error);
        wx.showToast({
          title: '获取画风和画种列表失败',
          icon: 'error'
        });
      }
    },


    // 初始化招募详情
    async initRecruitmentDetail() {
      if (this.data.loading) return;

      try {
        this.setData({ loading: true });
        
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        
        if (currentPage && currentPage.options && currentPage.options.id) {
          const recruitment_id = parseInt(currentPage.options.id);
          
          this.setData({
            'formData.recruitment_id': recruitment_id
          });

          wx.showLoading({
            title: '加载中...',
            mask: true
          });

          const res = await Api.recruitment.getDetail(recruitment_id);
          
          if (res && res.id) {
            this.setData({
              recruitmentDetail: res
            });
            
            // 更新分享参数
            this.updateShareParams();
          } else {
            throw new Error('获取招募详情失败');
          }
        } else {
          throw new Error('No recruitment_id found in options');
        }
      } catch (error) {
        console.error('初始化失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'error',
          duration: 2000
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      } finally {
        wx.hideLoading();
        this.setData({ loading: false });
      }
    },

    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 初始化分享参数
    initShareParams() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const shareParams: {recruitment_id?: string} = {}; // 定义shareParams的类型
      
      // 收集需要分享的参数
      if (currentPage && currentPage.options) {
        const options = currentPage.options as Record<string, string>;
        if (options.id) shareParams.recruitment_id = options.id;
      }
      
      // 存储分享参数
      this.setData({
        _shareParams: shareParams
      });
    },
    
    // 将参数对象转换为URL查询字符串
    buildQueryString(params: Record<string, string | number | undefined>) {
      return Object.keys(params)
        .filter(key => params[key] !== undefined)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(String(params[key]))}`)
        .join('&');
    },

    // 分享到朋友圈
    onShareTimeline() {
      console.log('onShareTimeline被调用，_shareParams:', this.data._shareParams);
      
      // 构建查询参数
      const queryString = this.data._shareParams ? 
                          this.buildQueryString(this.data._shareParams) : '';
      
      console.log('构建的queryString:', queryString);
      
      // 准备分享标题
      let shareTitle = '系列招募申请';
      if (this.data.recruitmentDetail && this.data.recruitmentDetail.title) {
        shareTitle = `${this.data.recruitmentDetail.title} - 申请`;
      }
      
      return {
        title: shareTitle,
        query: queryString
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      // 构建查询参数
      const queryString = this.buildQueryString(this.data._shareParams);
      
      // 准备分享标题和路径
      let shareTitle = '系列招募申请';
      let sharePath = '/pages/series/recruitment_detail/index';
      
      if (this.data.recruitmentDetail && this.data.recruitmentDetail.title) {
        shareTitle = `${this.data.recruitmentDetail.title} - 申请`;
      }
      
      // 添加查询参数
      if (queryString) {
        sharePath += `?${queryString}`;
      }
      
      return {
        title: shareTitle,
        path: sharePath,
        imageUrl: this.data.recruitmentDetail && this.data.recruitmentDetail.cover_image 
          ? this.data.recruitmentDetail.cover_image 
          : '/assets/default_share_image.jpg'
      };
    },
    
    // 更新分享参数
    updateShareParams() {
      
      // 确保this.data._shareParams存在，如果不存在则初始化为空对象
      const currentShareParams = this.data._shareParams || {};
      
      const shareParams: {recruitment_id?: string | number} = {...currentShareParams}; 
      
      // 如果有详情数据，更新分享参数
      if (this.data.recruitmentDetail && this.data.recruitmentDetail.id) {
        shareParams.recruitment_id = this.data.recruitmentDetail.id;
      } else {
        console.log('recruitmentDetail或id不存在，不更新recruitment_id');
      }
      
      
      this.setData({
        _shareParams: shareParams
      });
    },

    onLoad() {
    },

    // 下拉刷新
    async onPullDownRefresh() {
      wx.stopPullDownRefresh();
    },

    // 输入框变化
    onInputChange(e: any) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;
      console.log('Input changed:', field, value);
      
      // 更新表单数据
      this.setData({
        [`formData.${field}`]: value
      });
      
      // 针对不同字段进行实时验证
      if (field === 'cards_count' && this.data.recruitmentDetail) {
        // 延迟验证卡牌数量
        clearTimeout(this.data.cardCountValidateTimer);
        
        const timer = setTimeout(() => {
          this.validateCardCount();
        }, 500);
        
        this.setData({
          cardCountValidateTimer: timer
        });
      } else if (field === 'contact_phone') {
        // 延迟验证电话号码
        clearTimeout(this.data.phoneValidateTimer);
        
        const timer = setTimeout(() => {
          this.validatePhone();
        }, 800);
        
        this.setData({
          phoneValidateTimer: timer
        });
      } else if (field === 'pen_name') {
        // 延迟验证笔名
        clearTimeout(this.data.penNameValidateTimer);
        
        const timer = setTimeout(() => {
          this.validatePenName();
        }, 500);
        
        this.setData({
          penNameValidateTimer: timer
        });
      } else if (field === 'shipping_address') {
        // 延迟验证地址
        clearTimeout(this.data.addressValidateTimer);
        
        const timer = setTimeout(() => {
          this.validateAddress();
        }, 800);
        
        this.setData({
          addressValidateTimer: timer
        });
      }
    },

    // 显示画种选择器
    showArtworkPicker() {
      // 初始化临时选择
      this.setData({
        showArtworkPicker: true,
        tempSelectedTypes: this.data.formData.artwork_types
      });
    },

    // 隐藏画种选择器
    hideArtworkPicker() {
      this.setData({
        showArtworkPicker: false
      });
    },

    // 画种选择变化
    onArtworkTypesChange(e: any) {
      const selectedIds = e.detail.value;
      console.log('Selected artwork types:', selectedIds);
      
      // 更新选中状态
      const artworkTypes = this.data.artworkTypes.map(item => ({
        ...item,
        checked: selectedIds.includes(item.id.toString())
      }));

      // 获取选中画种的名称
      const selectedNames = artworkTypes
        .filter(item => selectedIds.includes(item.id.toString()))
        .map(item => item.name);

      this.setData({
        artworkTypes,
        tempSelectedTypes: selectedIds
      });
    },

    // 确认画种选择
    confirmArtworkTypes() {
      const selectedIds = this.data.tempSelectedTypes;
      
      // 获取选中画种的名称
      const selectedNames = this.data.artworkTypes
        .filter(item => selectedIds.includes(item.id.toString()))
        .map(item => item.name);

      this.setData({
        selectedArtworkTypes: selectedNames,
        'formData.artwork_types': selectedIds.join(','),
        showArtworkPicker: false,
        artworkTypesError: ''
      });
      
      // 验证选择
      this.validateArtworkTypes();
    },

    // 显示画风选择器
    showArtStylePicker() {
      console.log('showArtStylePicker被调用，formData.art_styles:', this.data.formData.art_styles);
      console.log('art_styles类型:', typeof this.data.formData.art_styles);
      
      // 增强防御性检查
      let tempStyles = [];
      try {
        if (this.data.formData && this.data.formData.art_styles !== undefined && this.data.formData.art_styles !== null) {
          if (typeof this.data.formData.art_styles === 'string' && this.data.formData.art_styles.trim() !== '') {
            tempStyles = this.data.formData.art_styles.split(',');
            console.log('art_styles分割后:', tempStyles);
          } else if (typeof this.data.formData.art_styles !== 'string') {
            // 如果不是字符串类型，尝试转换
            const safeString = String(this.data.formData.art_styles || '').trim();
            tempStyles = safeString ? [safeString] : [];
            console.log('非字符串art_styles转换后:', tempStyles);
          } else {
            console.log('art_styles是空字符串，使用空数组');
          }
        } else {
          console.log('formData.art_styles不存在或为null/undefined，使用空数组');
        }
      } catch (error) {
        console.error('处理art_styles时出错:', error);
        tempStyles = [];
      }
      
      this.setData({
        showArtStylePicker: true,
        tempSelectedStyles: tempStyles
      });
    },

    // 隐藏画风选择器
    hideArtStylePicker() {
      this.setData({
        showArtStylePicker: false
      });
    },

    // 画风选择变化
    onArtStylesChange(e: any) {
      const selectedIds = e.detail.value;
      console.log('Selected art styles:', selectedIds);
      
      // 更新选中状态
      const artStyles = this.data.artStyles.map(item => ({
        ...item,
        checked: selectedIds.includes(item.id.toString())
      }));

      // 获取选中画风的名称
      const selectedNames = artStyles
        .filter((item: { id: { toString: () => any; }; }) => selectedIds.includes(item.id.toString()))
        .map((item: { name: any; }) => item.name);

      this.setData({
        artStyles,
        tempSelectedStyles: selectedIds
      });
    },

    // 确认画风选择
    confirmArtStyles() {
      const selectedIds = this.data.tempSelectedStyles;
      
      // 获取选中画风的名称
      const selectedNames = this.data.artStyles
        .filter(item => selectedIds.includes(item.id.toString()))
        .map(item => item.name);

      this.setData({
        selectedArtStyles: selectedNames,
        'formData.art_styles': selectedIds.join(','),
        showArtStylePicker: false,
        artStylesError: ''
      });
      
      // 验证选择
      this.validateArtStyles();
    },

    // 选择图片
    async chooseImage() {
      // 检查登录状态
      const token = wx.getStorageSync('token');
      console.log('选择图片时的token:', token);
      
      if (!token) {
        console.log('未登录，显示登录弹窗');
        this.setData({ showLoginModal: true });
        return;
      }
      
      if (this.data.loading) return;

      try {
        console.log('开始选择图片');
        const res = await wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        });

        console.log('Chosen image:', res);
        console.log('图片临时路径:', res.tempFiles[0].tempFilePath);
        console.log('图片大小:', res.tempFiles[0].size);
        
        // 使用原始临时文件路径
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const fileSize = res.tempFiles[0].size;
        
        // 显示加载中提示
        wx.showLoading({
          title: '处理中...',
          mask: true
        });
        
        try {
          // 生成唯一ID作为图片标识
          const fileName = res.tempFiles[0].tempFilePath.split('/').pop() || `图片${this.data.formData.portfolio_images.length + 1}`;
          
          // 先添加图片对象，使用占位图
          const newImage = {
            tempFilePath: tempFilePath, // 保留原始路径用于上传
            url: '/assets/bg.jpg', // 临时使用占位图
            size: fileSize,
            fileName: fileName,
            debugInfo: '正在处理图片...',
            createTime: new Date().toLocaleTimeString()
          };
          
          // 添加到图片列表
          this.setData({
            'formData.portfolio_images': [...this.data.formData.portfolio_images, newImage]
          });
          
          // 获取当前图片索引
          const currentIndex = this.data.formData.portfolio_images.length - 1;
          
          // 异步处理图片
          setTimeout(async () => {
            try {
              // 获取canvas并绘制图片
              const query = this.createSelectorQuery();
              query.select(`#previewCanvas`).fields({ node: true, size: true }).exec(async (res) => {
                if (!res || !res[0] || !res[0].node) {
                  console.error('Canvas获取失败');
                  throw new Error('Canvas获取失败');
                }
                
                const canvas = res[0].node;
                const ctx = canvas.getContext('2d');
                
                // 设置画布尺寸
                canvas.width = 200;
                canvas.height = 200;
                
                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                try {
                  // 创建图片对象
                  const img = canvas.createImage();
                  
                  // 使用FileSystemManager读取图片文件
                  const fs = wx.getFileSystemManager();
                  const fileData = await new Promise((resolve, reject) => {
                    fs.readFile({
                      filePath: tempFilePath,
                      success: res => {
                        resolve(res.data);
                      },
                      fail: err => {
                        console.error('读取文件失败:', err);
                        reject(new Error('读取文件失败: ' + err.errMsg));
                      }
                    });
                  });
                  
                  // 将文件数据转换为base64
                  const base64 = wx.arrayBufferToBase64(fileData as ArrayBuffer);
                  const base64Url = `data:image/jpeg;base64,${base64}`;
                  
                  // 加载图片
                  await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = () => {
                      console.error('图片加载到Canvas失败');
                      reject(new Error('图片加载到Canvas失败'));
                    };
                    // 使用base64加载图片
                    img.src = base64Url;
                  });
                  
                  // 计算绘制参数，保持原始宽高比
                  const imgWidth = img.width;
                  const imgHeight = img.height;
                  let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
                  
                  if (imgWidth > imgHeight) {
                    drawHeight = canvas.height;
                    drawWidth = (imgWidth / imgHeight) * canvas.height;
                    offsetX = (canvas.width - drawWidth) / 2;
                  } else {
                    drawWidth = canvas.width;
                    drawHeight = (imgHeight / imgWidth) * canvas.width;
                    offsetY = (canvas.height - drawHeight) / 2;
                  }
                  
                  // 先用背景色填充整个画布
                  ctx.fillStyle = '#f0f0f0';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  // 绘制图片
                  ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
                  
                  // 生成预览图URL
                  const previewUrl = canvas.toDataURL('image/jpeg', 0.8);
                  console.log('预览图生成成功');
                  
                  // 更新图片信息
                  const portfolio_images = [...this.data.formData.portfolio_images];
                  if (portfolio_images[currentIndex]) {
                    portfolio_images[currentIndex].url = previewUrl;
                    portfolio_images[currentIndex].debugInfo = '预览图生成成功';
                    portfolio_images[currentIndex].canvasPreview = true;
                    // 保存base64以备加载失败时使用
                    portfolio_images[currentIndex].base64 = base64;
                    
                    this.setData({
                      'formData.portfolio_images': portfolio_images
                    });
                    console.log('已更新预览图');
                  }
                } catch (canvasError) {
                  console.error('Canvas处理失败:', canvasError);
                  // 更新错误信息
                  const portfolio_images = [...this.data.formData.portfolio_images];
                  if (portfolio_images[currentIndex]) {
                    portfolio_images[currentIndex].debugInfo = '预览图生成失败: ' + (canvasError.message || '未知错误');
                    
                    // 仍然保留占位图，确保显示
                    portfolio_images[currentIndex].url = '/assets/bg.jpg';
                    portfolio_images[currentIndex].canvasPreview = false;
                    
                    this.setData({
                      'formData.portfolio_images': portfolio_images
                    });
                    
                    // 显示轻提示，但不中断流程
                    wx.showToast({
                      title: '预览生成失败，已使用占位图',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                }
              });
            } catch (error) {
              console.error('预览图生成失败:', error);
            }
          }, 100);
          
          // 显示提示
          wx.hideLoading();
          wx.showToast({
            title: '图片已添加',
            icon: 'success'
          });
        } catch (processError) {
          console.error('图片处理失败:', processError);
          wx.hideLoading();
          
          // 显示警告
          wx.showToast({
            title: '图片处理失败',
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('选择图片失败:', error);
        wx.hideLoading();  // 确保loading被关闭
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    },
    
    // 图片加载错误处理
    onImageError(e: any) {
      console.error('Image load error:', e);
      console.error('图片加载失败详情:', {
        index: e.currentTarget.dataset.index,
        errorType: e.type,
        timestamp: e.timeStamp,
        target: e.target
      });
      
      const index = e.currentTarget.dataset.index;
      const portfolio_images = [...this.data.formData.portfolio_images];
      
      if (portfolio_images[index]) {
        console.log(`处理第${index}张图片加载失败，检查是否有base64数据:`, !!portfolio_images[index].base64);
        
        // 添加调试信息
        portfolio_images[index].debugInfo = '加载失败，尝试恢复';
        
        // 如果图片有base64数据但URL加载失败，重新使用base64数据
        if (portfolio_images[index].base64) {
          console.log(`使用base64数据重新设置图片URL，base64长度:`, portfolio_images[index].base64.length);
          portfolio_images[index].url = `data:image/jpeg;base64,${portfolio_images[index].base64}`;
          portfolio_images[index].debugInfo = 'base64替代';
          console.log('重新设置URL后的图片数据:', portfolio_images[index].url.substring(0, 50) + '...');
          this.setData({
            'formData.portfolio_images': portfolio_images
          });
          console.log('已重新设置base64图片URL');
          return;
        }
        
        // 标记图片加载失败
        console.log(`标记第${index}张图片加载失败`);
        portfolio_images[index].loadFailed = true;
        portfolio_images[index].debugInfo = '无法恢复的错误';
        this.setData({
          'formData.portfolio_images': portfolio_images
        });
        console.log('已更新加载失败状态');
      } else {
        console.error(`索引${index}处的图片不存在`);
      }
      
      // 显示加载错误提示
      wx.showToast({
        title: '图片加载失败，请重新上传',
        icon: 'none'
      });
    },

    // 图片加载成功处理
    onImageLoad(e: any) {
      console.log('图片加载成功:', e);
      const index = e.currentTarget.dataset.index;
      const portfolio_images = [...this.data.formData.portfolio_images];
      
      if (portfolio_images[index]) {
        // 记录图片加载成功
        portfolio_images[index].loadFailed = false;
        
        // 添加调试信息
        const isBase64 = portfolio_images[index].url.indexOf('data:image/jpeg;base64,') === 0;
        portfolio_images[index].debugInfo = isBase64 ? 'base64加载成功' : '路径加载成功';
        
        this.setData({
          'formData.portfolio_images': portfolio_images
        });
        console.log(`第${index}张图片加载成功，来源: ${isBase64 ? 'base64' : '文件路径'}`);
      }
    },

    // 删除图片
    deleteImage(e: any) {
      const { index } = e.currentTarget.dataset;
      console.log('Deleting image at index:', index);
      const portfolio_images = [...this.data.formData.portfolio_images];
      portfolio_images.splice(index, 1);
      this.setData({
        'formData.portfolio_images': portfolio_images
      });
    },

    // 验证表单
    validateForm() {
      let isValid = true;
      
      // 验证卡牌数量
      if (!this.validateCardCount()) {
        isValid = false;
      }
      
      // 验证笔名
      if (!this.validatePenName()) {
        wx.showToast({
          title: '请输入您的笔名',
          icon: 'none'
        });
        isValid = false;
      }
      
      // 验证画种
      if (!this.validateArtworkTypes()) {
        wx.showToast({
          title: '请选择可画画种',
          icon: 'none'
        });
        isValid = false;
      }
      
      // 验证画风
      if (!this.validateArtStyles()) {
        wx.showToast({
          title: '请选择擅长画风',
          icon: 'none'
        });
        isValid = false;
      }
      
      // 验证地址
      if (!this.validateAddress()) {
        wx.showToast({
          title: '请输入有效的邮寄地址',
          icon: 'none'
        });
        isValid = false;
      }
      
      // 验证电话
      if (!this.validatePhone()) {
        wx.showToast({
          title: '请输入正确的联系电话',
          icon: 'none'
        });
        isValid = false;
      }
      
      // 验证作品
      const { portfolio_images } = this.data.formData;
      if (portfolio_images.length === 0) {
        wx.showToast({
          title: '请至少上传1张作品',
          icon: 'none'
        });
        isValid = false;
      }
      
      return isValid;
    },

    // 提交申请
    async submitApplication() {
      if (this.data.loading || !this.validateForm()) return;
      
      // 检查登录状态并打印详细信息
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      console.log('提交申请时的认证信息:', {
        hasToken: !!token,
        tokenValue: token,
        hasUserInfo: !!userInfo,
        userId: userInfo ? userInfo.id : null
      });
      
      if (!token) {
        console.log('未登录，显示登录弹窗');
        this.setData({ showLoginModal: true });
        return;
      }

      this.setData({ 
        loading: true,
        submitting: true 
      });

      try {
        wx.showLoading({
          title: '上传作品中...',
          mask: true
        });
        
        console.log('准备提交申请，检查图片状态');
        console.log('当前图片数量:', this.data.formData.portfolio_images.length);

        // 检查是否有图片加载失败
        const failedImages = this.data.formData.portfolio_images.filter(img => img.loadFailed);
        console.log('加载失败的图片数量:', failedImages.length);
        if (failedImages.length > 0) {
          console.error('存在加载失败的图片，无法提交');
          throw new Error('有图片加载失败，请删除后重新上传');
        }

        // 先上传所有图片
        console.log('开始逐个上传图片');
        const uploadPromises = this.data.formData.portfolio_images.map(async (image, index) => {
          try {
            console.log(`准备上传第${index + 1}张图片:`, {
              是否有base64: !!image.base64,
              原始路径: image.tempFilePath,
              文件大小: image.size,
              调试信息: image.debugInfo || '无'
            });
            
            // 再次检查token是否存在
            const currentToken = wx.getStorageSync('token');
            if (!currentToken) {
              console.error(`上传第${index + 1}张图片前，发现token不存在`);
              throw new Error('认证已失效，请重新登录');
            }
            
            // 始终使用tempFilePath，它是选择图片时的原始临时路径
            let fileToUpload = image.tempFilePath;
            
            // 检查文件是否存在并且可用
            try {
              const fs = wx.getFileSystemManager();
              fs.accessSync(fileToUpload);
              console.log(`文件可访问性检查通过: ${fileToUpload}`);
            } catch (accessErr) {
              console.error(`文件访问检查失败: ${accessErr.errMsg || accessErr}`);
              throw new Error(`文件${index + 1}不可访问，请重新上传`);
            }
            
            console.log(`使用路径上传文件: ${fileToUpload}`);
            const uploadRes = await Api.recruitment.uploadPortfolio(fileToUpload);
            console.log('上传结果:', uploadRes);
            return uploadRes;
          } catch (error) {
            console.error(`上传第${index + 1}张图片失败:`, error);
            console.error('错误详情:', error.errMsg || error.message);
            throw new Error(error.message || `第${index + 1}张作品上传失败`);
          }
        });

        const uploadedImages = await Promise.all(uploadPromises);
        console.log('All images uploaded:', uploadedImages);
        
        // 更新formData中的图片信息
        const formData = {
          ...this.data.formData,
          portfolio_images: uploadedImages
        };

        wx.showLoading({
          title: '提交申请中...',
          mask: true
        });

        // 提交前再次检查token
        const submitToken = wx.getStorageSync('token');
        if (!submitToken) {
          console.error('提交申请前，发现token不存在');
          throw new Error('认证已失效，请重新登录');
        }
        console.log('提交申请前的认证信息:', {
          token: submitToken,
          formData: formData
        });

        const res = await Api.recruitment.submitApplication(formData);
        console.log('Submit result:', res);
        
        if (res.code === 1 || res.id >0) {
          wx.showToast({
            title: '申请成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            this.navigateBackWithRefresh();
          }, 1500);
        } else {
          throw new Error(res.msg || '申请失败');
        }
      } catch (error) {
        console.error('Submit failed:', error);
        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        wx.hideLoading();
        this.setData({ 
          loading: false,
          submitting: false 
        });
      }
    },

    // 登录成功回调
    onLoginSuccess(e: any) {
      console.log('登录成功回调:', e.detail);
      // 保存登录信息
      const userInfo = e.detail.userInfo;
      if (userInfo) {
        console.log('保存登录用户信息:', userInfo);
      }
      
      this.setData({ showLoginModal: false });
      
      // 登录成功后继续执行之前的操作
      setTimeout(() => {
        // 重新验证token
        const newToken = wx.getStorageSync('token');
        console.log('登录成功后的token:', newToken);
        
        if (newToken) {
          // 继续提交操作
          this.submitApplication();
        } else {
          wx.showToast({
            title: '登录异常，请重试',
            icon: 'none'
          });
        }
      }, 300);
    },
    
    // 关闭登录弹窗
    onLoginModalClose() {
      console.log('关闭登录弹窗');
      this.setData({ showLoginModal: false });
    },

    // 多选图片上传功能
    async chooseMultipleImages() {
      // 检查登录状态
      const token = wx.getStorageSync('token');
      console.log('选择图片时的token:', token);
      
      if (!token) {
        console.log('未登录，显示登录弹窗');
        this.setData({ showLoginModal: true });
        return;
      }
      
      if (this.data.loading) return;
      
      try {
        // 计算还可以上传的图片数量
        const remainingCount = 3 - this.data.formData.portfolio_images.length;
        if (remainingCount <= 0) return;
        
        const res = await wx.chooseMedia({
          count: remainingCount,
          mediaType: ['image'],
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        });
        
        console.log('Chosen images:', res);
        
        // 显示加载中提示
        wx.showLoading({
          title: '处理中...',
          mask: true
        });
        
        // 处理选中的图片
        for (let i = 0; i < res.tempFiles.length; i++) {
          const tempFile = res.tempFiles[i];
          const tempFilePath = tempFile.tempFilePath;
          const fileSize = tempFile.size;
          
          // 生成唯一ID作为图片标识
          const fileName = tempFilePath.split('/').pop() || `图片${this.data.formData.portfolio_images.length + 1}`;
          
          // 创建图片对象
          const newImage = {
            tempFilePath: tempFilePath,
            url: '/assets/bg.jpg', // 临时使用占位图
            size: fileSize,
            fileName: fileName,
            debugInfo: '正在处理图片...',
            createTime: new Date().toLocaleTimeString()
          };
          
          // 添加到图片列表
          const portfolio_images = [...this.data.formData.portfolio_images];
          portfolio_images.push(newImage);
          
          this.setData({
            'formData.portfolio_images': portfolio_images
          });
          
          // 获取当前图片索引
          const currentIndex = portfolio_images.length - 1;
          
          // 处理这张图片
          await this.processImage(tempFilePath, currentIndex);
        }
        
        wx.hideLoading();
        wx.showToast({
          title: '图片已添加',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('选择图片失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    },

    // 处理单张图片 (从chooseImage方法中提取逻辑为独立方法)
    async processImage(tempFilePath, currentIndex) {
      // 图片处理逻辑，可从原来的chooseImage方法中提取
      try {
        // 获取canvas并绘制图片
        const query = this.createSelectorQuery();
        query.select(`#previewCanvas`).fields({ node: true, size: true }).exec(async (res) => {
          if (!res || !res[0] || !res[0].node) {
            console.error('Canvas获取失败');
            throw new Error('Canvas获取失败');
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 设置画布尺寸
          canvas.width = 200;
          canvas.height = 200;
          
          // 清空画布
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          try {
            // 创建图片对象
            const img = canvas.createImage();
            
            // 使用FileSystemManager读取图片文件
            const fs = wx.getFileSystemManager();
            const fileData = await new Promise((resolve, reject) => {
              fs.readFile({
                filePath: tempFilePath,
                success: res => {
                  resolve(res.data);
                },
                fail: err => {
                  console.error('读取文件失败:', err);
                  reject(new Error('读取文件失败: ' + err.errMsg));
                }
              });
            });
            
            // 将文件数据转换为base64
            const base64 = wx.arrayBufferToBase64(fileData as ArrayBuffer);
            const base64Url = `data:image/jpeg;base64,${base64}`;
            
            // 加载图片
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = () => {
                console.error('图片加载到Canvas失败');
                reject(new Error('图片加载到Canvas失败'));
              };
              // 使用base64加载图片
              img.src = base64Url;
            });
            
            // 计算绘制参数，保持原始宽高比
            const imgWidth = img.width;
            const imgHeight = img.height;
            let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
            
            if (imgWidth > imgHeight) {
              drawHeight = canvas.height;
              drawWidth = (imgWidth / imgHeight) * canvas.height;
              offsetX = (canvas.width - drawWidth) / 2;
            } else {
              drawWidth = canvas.width;
              drawHeight = (imgHeight / imgWidth) * canvas.width;
              offsetY = (canvas.height - drawHeight) / 2;
            }
            
            // 先用背景色填充整个画布
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制图片
            ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
            
            // 生成预览图URL
            const previewUrl = canvas.toDataURL('image/jpeg', 0.8);
            console.log('预览图生成成功');
            
            // 更新图片信息
            const portfolio_images = [...this.data.formData.portfolio_images];
            if (portfolio_images[currentIndex]) {
              portfolio_images[currentIndex].url = previewUrl;
              portfolio_images[currentIndex].debugInfo = '预览图生成成功';
              portfolio_images[currentIndex].canvasPreview = true;
              // 保存base64以备加载失败时使用
              portfolio_images[currentIndex].base64 = base64;
              
              this.setData({
                'formData.portfolio_images': portfolio_images
              });
              console.log('已更新预览图');
            }
          } catch (canvasError) {
            console.error('Canvas处理失败:', canvasError);
            // 更新错误信息
            const portfolio_images = [...this.data.formData.portfolio_images];
            if (portfolio_images[currentIndex]) {
              portfolio_images[currentIndex].debugInfo = '预览图生成失败: ' + (canvasError.message || '未知错误');
              
              // 仍然保留占位图，确保显示
              portfolio_images[currentIndex].url = '/assets/bg.jpg';
              portfolio_images[currentIndex].canvasPreview = false;
              
              this.setData({
                'formData.portfolio_images': portfolio_images
              });
            }
          }
        });
      } catch (error) {
        console.error('预览图生成失败:', error);
      }
    },

    // 增加卡牌数量
    increaseCardCount() {
      if (!this.data.recruitmentDetail) return;
      
      const { min_cards, max_cards } = this.data.recruitmentDetail;
      let currentCount = parseInt(this.data.formData.cards_count || '0');
      
      // 如果当前值不是数字，设为最小值
      if (isNaN(currentCount)) {
        currentCount = min_cards;
      } else if (currentCount < max_cards) {
        // 如果当前值小于最大值，加1
        currentCount += 1;
      }
      
      // 更新数据并验证
      this.setData({
        'formData.cards_count': currentCount.toString(),
        cardsCountError: ''
      });
      
      // 检查是否超出范围
      this.validateCardCount();
    },

    // 减少卡牌数量
    decreaseCardCount() {
      if (!this.data.recruitmentDetail) return;
      
      const { min_cards, max_cards } = this.data.recruitmentDetail;
      let currentCount = parseInt(this.data.formData.cards_count || '0');
      
      // 如果当前值不是数字，设为最小值
      if (isNaN(currentCount)) {
        currentCount = min_cards;
      } else if (currentCount > min_cards) {
        // 如果当前值大于最小值，减1
        currentCount -= 1;
      }
      
      // 更新数据并验证
      this.setData({
        'formData.cards_count': currentCount.toString(),
        cardsCountError: ''
      });
      
      // 检查是否超出范围
      this.validateCardCount();
    },

    // 验证卡牌数量
    validateCardCount() {
      if (!this.data.recruitmentDetail) return;
      
      const { min_cards, max_cards } = this.data.recruitmentDetail;
      const cardsCount = parseInt(this.data.formData.cards_count || '0');
      
      if (isNaN(cardsCount)) {
        this.setData({
          cardsCountError: '请输入有效数字'
        });
        return false;
      }
      
      if (cardsCount < min_cards) {
        this.setData({
          cardsCountError: `申请数量不能少于${min_cards}张`
        });
        return false;
      }
      
      if (cardsCount > max_cards) {
        this.setData({
          cardsCountError: `申请数量不能超过${max_cards}张`
        });
        return false;
      }
      
      // 数量合法，清除错误提示
      this.setData({
        cardsCountError: ''
      });
      return true;
    },

    // 验证电话号码
    validatePhone() {
      const phoneNumber = this.data.formData.contact_phone;
      
      if (!phoneNumber || phoneNumber.trim() === '') {
        this.setData({
          phoneError: '请输入联系电话'
        });
        return false;
      }
      
      // 手机号正则表达式 (支持13,14,15,16,17,18,19开头的11位手机号)
      const mobilePattern = /^1[3-9]\d{9}$/;
      
      // 固定电话正则表达式 (支持区号-电话号码的格式，区号3-4位，电话号码7-8位)
      const telPattern = /^(0\d{2,3}-)?[1-9]\d{6,7}$/;
      
      if (mobilePattern.test(phoneNumber) || telPattern.test(phoneNumber)) {
        // 验证通过，清除错误提示
        this.setData({
          phoneError: ''
        });
        return true;
      } else {
        // 验证失败，显示错误提示
        this.setData({
          phoneError: '请输入正确的手机号或固定电话'
        });
        return false;
      }
    },

    // 验证笔名
    validatePenName() {
      const penName = this.data.formData.pen_name;
      
      if (!penName || penName.trim() === '') {
        this.setData({
          penNameError: '请输入您的笔名'
        });
        return false;
      }
      
      // 清除错误提示
      this.setData({
        penNameError: ''
      });
      return true;
    },

    // 验证画种
    validateArtworkTypes() {
      const artworkTypes = this.data.formData.artwork_types;
      
      if (!artworkTypes || (Array.isArray(artworkTypes) && artworkTypes.length === 0) || 
          (typeof artworkTypes === 'string' && artworkTypes.trim() === '')) {
        this.setData({
          artworkTypesError: '请选择可画画种'
        });
        return false;
      }
      
      // 清除错误提示
      this.setData({
        artworkTypesError: ''
      });
      return true;
    },

    // 验证画风
    validateArtStyles() {
      const artStyles = this.data.formData.art_styles;
      
      if (!artStyles || (typeof artStyles === 'string' && artStyles.trim() === '')) {
        this.setData({
          artStylesError: '请选择擅长画风'
        });
        return false;
      }
      
      // 清除错误提示
      this.setData({
        artStylesError: ''
      });
      return true;
    },

    // 验证地址
    validateAddress() {
      const address = this.data.formData.shipping_address;
      
      if (!address || address.trim() === '') {
        this.setData({
          addressError: '请输入邮寄地址'
        });
        return false;
      }
      
      if (address.length < 8) {
        this.setData({
          addressError: '地址过短，请填写详细地址'
        });
        return false;
      }
      
      // 清除错误提示
      this.setData({
        addressError: ''
      });
      return true;
    },
    
    // 带刷新参数返回上一页
    navigateBackWithRefresh() {
      // 获取上一页面路径
      const pages = getCurrentPages();
      
      if (pages.length >= 2) {
        // 获取上一页的路径和参数
        const prevPage = pages[pages.length - 2];
        const prevRoute = '/' + prevPage.route;
        console.log('当前页面栈:', pages);
        console.log('上一页路径:', prevRoute);
        console.log('上一页参数:', prevPage.options);
        // 构建参数，添加刷新标记
        let params: Record<string, string | number> = {};
        
        // 保留原有参数
        if (prevPage.options) {
          Object.keys(prevPage.options).forEach(key => {
            params[key] = prevPage.options[key];
          });
        }
        
        // 添加刷新标记和时间戳确保刷新
        params.refresh = '1';
        params.timestamp = new Date().getTime();
        
        // 构建查询字符串
        const queryString = Object.keys(params)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
          .join('&');
          console.log('当前页面栈:', pages);
          console.log('上一页路径:', prevRoute);
          console.log('上一页参数:', prevPage.options);
          console.log('queryString:',`${prevRoute}?${queryString}`);
          
        // 带参数跳转回上一页
        wx.redirectTo({
          url: `${prevRoute}?${queryString}`
        });
      } else {
        // 如果没有上一页，返回首页
        wx.reLaunch({
          url: '/pages/series/recruitment/index'
        });
      }
    }
  }
});

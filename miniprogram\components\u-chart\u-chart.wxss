.u-chart-container {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.u-chart-canvas {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  transition: opacity 0.3s ease;
}

.u-chart-canvas-loading {
  opacity: 0.5;
}

.u-chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.7);
}

.u-chart-loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.u-chart-loading-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 
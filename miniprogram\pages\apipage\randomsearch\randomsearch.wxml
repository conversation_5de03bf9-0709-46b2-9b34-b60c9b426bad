<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="随机人设" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view 
      id="scrollView"
      scroll-y 
      scroll-top="{{scrollTop}}"
      class="content-scroll {{showCropper ? 'no-scroll' : ''}}" 
      enhanced="{{true}}" 
      bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="image-container">
        <block wx:if="{{loading}}">
          <view class="image-card">
            <view class="image-wrapper loading-state">
              <view class="loading-progress">
                <view class="loading-step">步骤 {{loadingStep}}/{{loadingStepCount}}</view>
                <view class="progress-bar">
                  <view class="progress-inner" style="width: {{loadingProgress}}%;"></view>
                </view>
                <text class="progress-text">{{loadingProgress}}%</text>
                <view class="loading-message">
                  <text class="loading-text">{{loadingText}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
        <block wx:else>
          <block wx:for="{{currentDetail.images}}" wx:key="image_id">
            <view class="image-card">
              <view class="image-wrapper">
                <!-- 缩略图 -->
                <image class="main-image blur" 
                       src="{{item.url}}?x-oss-process=image/resize,w_200/quality,q_30/format,webp" 
                       mode="aspectFill"
                       lazy-load="{{false}}"
                       hidden="{{item.isLoaded}}" />
                <!-- 高清图 -->
                <image class="main-image" 
                       src="{{item.url}}" 
                       mode="widthFix" 
                       lazy-load="true"
                       bindload="onImageLoad"
                       data-index="{{index}}"
                       hidden="{{!item.isLoaded}}" />
                <view class="image-loading" hidden="{{item.isLoaded}}">
                  <view class="loading-progress">
                    <view class="progress-bar">
                      <view class="progress-inner" style="width: {{loadingProgress}}%;"></view>
                    </view>
                    <text class="progress-text">{{loadingProgress}}%</text>
                  </view>
                  <text class="loading-text">{{loadingText}}</text>
                </view>
              </view>
              <view class="image-info">
                <view class="artist-info" wx:if="{{item.artist.name}}">
                  <text class="label">画师：</text>
                  <text class="value">{{item.artist.name}}</text>
                </view>
                <view class="image-stats">
                  <view class="stat-item">
                    <text class="label">收藏：</text>
                    <text class="value">{{item.favorites}}</text>
                  </view>
                  <view class="stat-item">
                    <text class="label">尺寸：</text>
                    <text class="value">{{item.width}} x {{item.height}}</text>
                  </view>
                </view>
                <view class="tags-container">
                  <text class="tag" wx:for="{{item.tags}}" wx:key="name" wx:for-item="tag">{{tag.name}}</text>
                </view>
                <view class="links">
                  <button class="link-btn" wx:if="{{item.artist && item.artist.pixiv}}" bindtap="openLink" data-url="{{item.artist.pixiv}}">
                    <text class="link-icon">🎨</text>
                    <text>Pixiv</text>
                  </button>
                  <button class="link-btn" wx:if="{{item.artist && item.artist.twitter}}" bindtap="openLink" data-url="{{item.artist.twitter}}">
                    <text class="link-icon">🐦</text>
                    <text>Twitter</text>
                  </button>
                  <!-- <button class="link-btn" bindtap="openLink" data-url="{{item.source}}">
                    <text class="link-icon">🔗</text>
                    <text>原图</text>
                  </button> -->
                  
                </view>
              </view>
              <view class="refresh-button-container links">
                <button class="link-btn download-btn" bindtap="downloadImage">
                    <text class="link-icon">💾</text>
                    <text>保存高清图</text>
                </button>
                <button class="link-btn refresh-button {{countdown > 0 ? 'counting' : ''}}" bindtap="refreshImage" disabled="{{loading || countdown > 0}}">
                  <block wx:if="{{countdown > 0}}">
                    <text class="countdown">{{countdown}}s</text>
                  </block>
                  <block wx:else>
                    <text class="refresh-icon">🔄</text>
                    <text>换一张</text>
                  </block>
                </button>
              </view>
            </view>
          </block>
        </block>
      </view>
      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}randompicture.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">随机人设抽取</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">多样人设</text>
                    <text class="feature-desc">提供丰富多样的角色设定和性格特征</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">智能组合</text>
                    <text class="feature-desc">自动抽取合理的人物性格和背景组合</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">一键抽取</text>
                    <text class="feature-desc">快速获取完整的角色人设方案</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">收藏保存</text>
                    <text class="feature-desc">支持保存喜欢的人设方案供后续使用</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">选择类型</text>
                    <text class="step-desc">选择想要抽取的人设类型和风格</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">设置参数</text>
                    <text class="step-desc">调整性格、背景等具体参数</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">抽取人设</text>
                    <text class="step-desc">点击抽取获取完整的人设方案</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">收藏使用</text>
                    <text class="step-desc">保存喜欢的人设方案以供参考</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">灵活调整</text>
                  <text class="tip-text">可以根据需要调整抽取的人设细节</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">多次尝试</text>
                  <text class="tip-text">不满意可以多次抽取直到获得理想方案</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">合理运用</text>
                  <text class="tip-text">将抽取的人设作为创作灵感的参考来源</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">创意发展</text>
                  <text class="tip-text">基于抽取的人设进行创意延伸和扩展</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 隐藏的画布 -->
<canvas type="2d" id="processCanvas" style="position:fixed;left:-9999px;top:-9999px;width:100px;height:100px;"></canvas>


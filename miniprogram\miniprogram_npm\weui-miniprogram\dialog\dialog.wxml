<template name="body">
  <view class="weui-dialog {{extClass}} {{innerShow ? 'weui-animate-fade-in' : 'weui-animate-fade-out'}}" catchtap="stopEvent">
    <view class="weui-dialog__hd">
      <view class="weui-dialog__title" tabindex="0">{{title}}
        <slot name="title"/>
      </view>
    </view>
    <view class="weui-dialog__bd">
      <slot/>
    </view>
    <view class="weui-dialog__ft">
      <block wx:if="{{buttons && buttons.length}}">
        <view wx:for="{{buttons}}" wx:key="index" class="weui-dialog__btn {{item.className}} {{item.extClass}}" hover-class="weui-active" data-index="{{index}}" bindtap="buttonTap" aria-role="button">{{item.text}}</view>
      </block>
      <slot name="footer" wx:else/>
    </view>
  </view>
  <view wx:if="{{mask}}" bindtap="close" class="weui-mask {{maskClass}} {{innerShow ? 'weui-animate-fade-in' : 'weui-animate-fade-out'}}" aria-role="button" aria-label="关闭"/>
</template>

<root-portal enable="{{true}}" wx:if="{{rootPortal && wrapperShow}}">
  <view aria-role="dialog" aria-modal="true" class="root-portal-box weui-dialog__root">
    <template is="body" data="{{title, maskClosable, maskClass, extClass, mask, buttons, innerShow}}"/>
  </view>
</root-portal>
<view wx:elif="{{!rootPortal && wrapperShow}}" aria-role="dialog" aria-modal="true">
  <template is="body" data="{{title, maskClosable, maskClass, extClass, mask, buttons, innerShow}}"/>
</view>


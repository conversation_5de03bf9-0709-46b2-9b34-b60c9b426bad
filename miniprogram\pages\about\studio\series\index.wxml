<wxs src="../../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="系列申请列表" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" 
      enhanced="{{true}}" 
      bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="application-container">
        <!-- 说明文字 -->
        <view class="header-card">
          <view class="header-content">
            <view class="header-title">我的系列申请</view>
            <view class="header-subtitle">管理您提交的画师系列申请，查看申请状态和详情</view>
          </view>
          <view class="header-stat">
            <view class="stat-item">
              <view class="stat-value">{{applications.length || 0}}</view>
              <view class="stat-label">申请总数</view>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <view class="stat-value">{{pendingCount || 0}}</view>
              <view class="stat-label">审核中</view>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <view class="stat-value">{{approvedCount || 0}}</view>
              <view class="stat-label">已通过</view>
            </view>
          </view>
        </view>
        
        <!-- 标签选项卡 -->
        <view class="tab-container">
          <view 
            class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
            wx:for="{{tabs}}" 
            wx:key="id" 
            bindtap="switchTab" 
            data-id="{{item.id}}"
          >
            <text>{{item.name}}</text>
            <view class="tab-underline" wx:if="{{activeTab === item.id}}"></view>
          </view>
        </view>
        
        <!-- 加载中状态 -->
        <view class="loading-container" wx:if="{{loading}}">
          <view class="loading-icon"></view>
          <view class="loading-text">加载中...</view>
        </view>
        
        <!-- 空数据状态 -->
        <view class="empty-container" wx:if="{{!loading && isEmpty}}">
          <image class="empty-image" src="{{constants.COMMON_ASSETS.DEFAULT_AVATAR}}" mode="aspectFit"></image>
          <view class="empty-text">暂无{{activeTab !== 'all' ? statusMap[activeTab] : ''}}申请记录</view>
          <view class="empty-tips">
            {{activeTab !== 'all' ? '尝试切换到其他标签查看' : '您还没有提交过系列申请'}}
          </view>
          <view class="empty-tips" wx:if="{{activeTab === 'all'}}">
            您可以在招募页面参与系列申请
          </view>
        </view>
        
        <!-- 申请列表 -->
        <view class="application-list" wx:if="{{!loading && !isEmpty}}">
          <view class="application-item" wx:for="{{filteredApplications}}" wx:key="id" data-id="{{item.id}}">
            <view class="application-header">
              <view class="application-title">{{item.recruitment_title}}</view>
              <view class="application-status status-{{item.status}}">
                {{statusMap[item.status]}}
              </view>
            </view>
            
            <view class="application-content">
              <view class="application-row">
                <view class="application-col">
                  <view class="info-group">
                    <text class="info-label">系列名称</text>
                    <text class="info-value series-name">{{item.series_name}}</text>
                  </view>
                  <view class="info-group">
                    <text class="info-label">笔名</text>
                    <text class="info-value">{{item.pen_name}}</text>
                  </view>
                </view>
                <view class="application-col">
                  <view class="info-group">
                    <text class="info-label">卡片数量</text>
                    <text class="info-value">{{item.cards_count}}张</text>
                  </view>
                  <view class="info-group">
                    <text class="info-label">作品类型</text>
                    <text class="info-value">{{item.artwork_types}}</text>
                  </view>
                </view>
              </view>
              
              <view class="time-info-container">
                <view class="time-info">
                  <mp-icon icon="time" color="#999999" size="{{14}}"></mp-icon>
                  <text>提交时间: {{item.createtime}}</text>
                </view>
                <view class="time-info" wx:if="{{item.status !== 'pending'}}">
                  <mp-icon icon="done" color="{{item.status === 'approved' ? '#4caf50' : '#f44336'}}" size="{{14}}"></mp-icon>
                  <text>审核时间: {{item.review_time}}</text>
                </view>
              </view>
            </view>
            
            <view class="application-footer">
              <view class="action-tag" wx:if="{{item.status === 'approved'}}">
                <mp-icon icon="done" color="#4caf50" size="{{16}}"></mp-icon>
                <text>已通过</text>
              </view>
              <view class="action-tag waiting" wx:if="{{item.status === 'pending'}}">
                <mp-icon icon="time" color="#ff9800" size="{{16}}"></mp-icon>
                <text>等待审核</text>
              </view>
              <view class="action-tag rejected" wx:if="{{item.status === 'rejected'}}">
                <mp-icon icon="close" color="#f44336" size="{{16}}"></mp-icon>
                <text>已驳回</text>
              </view>
              <view class="view-detail-btn" bindtap="viewApplicationDetail" data-id="{{item.id}}">
                查看详情
                <mp-icon icon="arrow" color="#666666" size="{{12}}"></mp-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="3"><!-- 底部导航 --></tab-bar>
</view>

<!-- 添加详情弹窗 -->
<view class="detail-modal {{showDetail ? 'show' : ''}}" catchtouchmove="preventTouchMove" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
  <view class="detail-mask" bindtap="hideApplicationDetail"></view>
  <view class="detail-container">
    <view class="detail-header">
      <view class="detail-title">申请详情</view>
      <view class="detail-close" bindtap="hideApplicationDetail">
        <mp-icon icon="close" color="#333333" size="{{20}}"></mp-icon>
      </view>
    </view>
    
    <scroll-view scroll-y class="detail-content">
      <block wx:if="{{currentApplication}}">
        <view class="detail-status-bar status-{{currentApplication.status}}">
          <text>{{statusMap[currentApplication.status]}}</text>
        </view>
        
        <view class="detail-section">
          <view class="detail-section-title">招募信息</view>
          <view class="detail-item">
            <text class="detail-label">招募标题</text>
            <text class="detail-value">{{currentApplication.recruitment_title}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">系列名称</text>
            <text class="detail-value">{{currentApplication.series_name}}</text>
          </view>
        </view>
        
        <view class="detail-section">
          <view class="detail-section-title">申请信息</view>
          <view class="detail-item">
            <text class="detail-label">笔名</text>
            <text class="detail-value">{{currentApplication.pen_name}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">卡片数量</text>
            <text class="detail-value">{{currentApplication.cards_count}}张</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">作品类型</text>
            <text class="detail-value">{{currentApplication.artwork_types}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">艺术风格</text>
            <text class="detail-value">{{currentApplication.art_styles}}</text>
          </view>
        </view>
        
        <view class="detail-section">
          <view class="detail-section-title">联系方式</view>
          <view class="detail-item">
            <text class="detail-label">联系电话</text>
            <text class="detail-value">{{currentApplication.contact_phone}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">微信</text>
            <text class="detail-value">{{currentApplication.contact_wechat}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">QQ</text>
            <text class="detail-value">{{currentApplication.contact_qq}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">邮寄地址</text>
            <text class="detail-value">{{currentApplication.shipping_address}}</text>
          </view>
        </view>
        
        <view class="detail-section">
          <view class="detail-section-title">申请备注</view>
          <view class="detail-remarks">{{currentApplication.remarks || '无备注'}}</view>
        </view>
        
        <view class="detail-section" wx:if="{{currentApplication.status !== 'pending'}}">
          <view class="detail-section-title">审核结果</view>
          <view class="detail-item">
            <text class="detail-label">审核时间</text>
            <text class="detail-value">{{currentApplication.review_time}}</text>
          </view>
          <view class="detail-item" wx:if="{{currentApplication.admin_remarks}}">
            <text class="detail-label">审核备注</text>
            <text class="detail-value">{{currentApplication.admin_remarks}}</text>
          </view>
        </view>
      </block>
      
      <view class="detail-loading" wx:else>
        <view class="detail-loading-icon"></view>
        <text>加载中...</text>
      </view>
    </scroll-view>
    
    <view class="detail-footer">
      <view class="detail-btn primary" bindtap="hideApplicationDetail">关闭</view>
    </view>
  </view>
</view>




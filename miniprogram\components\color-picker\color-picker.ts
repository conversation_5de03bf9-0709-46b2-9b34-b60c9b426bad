/**
 * 颜色选择器组件
 * 
 * 使用说明：
 * 1. 在页面的json文件中引入组件：
 *    {
 *      "usingComponents": {
 *        "color-picker": "/components/color-picker/color-picker"
 *      }
 *    }
 * 
 * 2. 在页面wxml中使用组件：
 *    <color-picker
 *      value="{{color}}"
 *      presetColors="{{presetColors}}"
 *      showPresets="{{true}}"
 *      showColorValue="{{true}}"
 *      zIndex="{{1000}}"
 *      bindchange="onColorChange"
 *      bindconfirm="onColorConfirm"
 *    />
 * 
 * 3. 组件参数说明：
 *    - value: string - 当前颜色值，支持hex格式('#FF0000')和rgba格式('rgba(255,0,0,1)')
 *    - presetColors: string[] - 预设颜色列表，默认提供12种颜色
 *    - showPresets: boolean - 是否显示预设颜色，默认true
 *    - showColorValue: boolean - 是否显示颜色值，默认true
 *    - zIndex: number - 弹出层层级，默认1000
 * 
 * 4. 事件说明：
 *    - bindchange: 颜色变化时触发
 *      event.detail = { color: string } // 当前选择的颜色值
 *    - bindconfirm: 点击确定按钮时触发
 *      event.detail = { color: string } // 最终确认的颜色值
 * 
 * 5. 示例代码：
 *    Page({
 *      data: {
 *        color: '#FF0000',
 *        presetColors: ['#FF0000', '#00FF00', '#0000FF']
 *      },
 *      onColorChange(e) {
 *        console.log('颜色变化:', e.detail.color);
 *      },
 *      onColorConfirm(e) {
 *        this.setData({ color: e.detail.color });
 *      }
 *    })
 * 
 * 6. 注意事项：
 *    - 在弹出层中使用时，建议设置合适的zIndex避免层级问题
 *    - 颜色选择器支持透明度调节，输出格式为rgba
 *    - 组件会自动处理触摸事件，防止滚动穿透
 *    - 在同一页面使用多个选择器时，每个选择器会自动分配唯一的popupId
 */

interface TouchDetail {
  clientX: number;
  clientY: number;
  pageX: number;
  pageY: number;
}

let pickerCount = 0;

Component({
  properties: {
    value: {
      type: String,
      value: '#000000',
      observer(newVal: string) {
        if (newVal !== this.data.currentColor) {
          this.initializeColor(newVal);
        }
      }
    },
    // 预设颜色，可以由外部传入自定义的预设颜色
    presetColors: {
      type: Array,
      value: [
        '#FFFFFF', '#808080', '#000000',  // 基础颜色
        '#FF0000', '#FF8000', '#FFFF00',  // 红橙黄
        '#80FF00', '#00FF00', '#00FF80',  // 黄绿绿青
        '#00FFFF', '#0080FF', '#0000FF',  // 青蓝蓝
        '#8000FF', '#FF00FF', '#FF0080'   // 紫粉红
      ]
    },
    // 是否显示预设颜色
    showPresets: {
      type: Boolean,
      value: true
    },
    // 是否显示颜色值
    showColorValue: {
      type: Boolean,
      value: true
    },
    zIndex: {
      type: Number,
      value: 1000
    }
  },

  data: {
    visible: false,
    currentColor: '#000000',
    pureColor: '#000000', // 不带透明度的颜色
    tempColor: '#000000',
    hue: 0,
    saturation: 0,
    value: 100,
    alpha: 100, // 透明度值 0-100
    svCursorX: 0,
    svCursorY: 0,
    hueCursorX: 0,
    alphaCursorX: 0,
    pickerWidth: 0,
    pickerHeight: 0,
    hueWidth: 0,
    alphaWidth: 0,
    svRect: null as WechatMiniprogram.BoundingClientRectCallbackResult | null,
    hueRect: null as WechatMiniprogram.BoundingClientRectCallbackResult | null,
    alphaRect: null as WechatMiniprogram.BoundingClientRectCallbackResult | null,
    throttleTimer: null as number | null,
    lastUpdateTime: 0,
    popupId: '',
    isMoving: false
  },

  lifetimes: {
    attached() {
      this.data.popupId = `color-picker-${++pickerCount}`;
      
      // 初始化节流函数
      (this as any).throttledUpdateSV = this.throttle(this.updateSV, 16);
      (this as any).throttledUpdateHue = this.throttle(this.updateHue, 16);
      (this as any).throttledUpdateAlpha = this.throttle(this.updateAlpha, 16);
      
      // 延迟初始化颜色
      wx.nextTick(() => {
        this.initializeColor(this.properties.value);
      });
    },
    
    detached() {
      // 清理所有定时器
      if (this.data.throttleTimer) {
        clearTimeout(this.data.throttleTimer);
      }
      
      const context = this as any;
      ['throttledUpdateSV', 'throttledUpdateHue', 'throttledUpdateAlpha'].forEach(key => {
        if (context[key] && context[key].timer) {
          clearTimeout(context[key].timer);
        }
      });
    }
  },

  methods: {
    throttle(fn: Function, delay: number) {
      let lastTime = 0;
      
      return function(this: any, ...args: any[]) {
        const now = Date.now();
        
        if (now - lastTime >= delay) {
          fn.apply(this, args);
          lastTime = now;
        }
      };
    },

    showPicker() {
      if (this.data.visible) return;
      
      this.setData({
        visible: true,
        tempColor: this.data.currentColor
      }, () => {
        this.updatePickerSize();
      });
    },

    hidePicker() {
      if (this.data.isMoving) return;
      
      this.initializeColor(this.data.tempColor);
      this.setData({ visible: false });
    },

    stopPropagation() {
      // 阻止事件冒泡
    },

    onConfirm() {
      this.updateColor(true);
      this.setData({ visible: false });
      this.triggerEvent('confirm', { color: this.data.currentColor });
    },

    preventDefault() {
      return false;
    },

    updatePickerSize() {
      const query = this.createSelectorQuery();
      
      Promise.all([
        new Promise<WechatMiniprogram.BoundingClientRectCallbackResult>(resolve => {
          query.select('.sv-picker').boundingClientRect(resolve);
        }),
        new Promise<WechatMiniprogram.BoundingClientRectCallbackResult>(resolve => {
          query.select('.hue-picker').boundingClientRect(resolve);
        }),
        new Promise<WechatMiniprogram.BoundingClientRectCallbackResult>(resolve => {
          query.select('.alpha-slider').boundingClientRect(resolve);
        })
      ]).then(([svRect, hueRect, alphaRect]) => {
        if (svRect) {
          this.setData({
            pickerWidth: svRect.width || 0,
            pickerHeight: svRect.height || 0,
            svRect
          });
          this.updateCursorPosition();
        }
        
        if (hueRect) {
          this.setData({
            hueWidth: hueRect.width || 0,
            hueRect
          });
          this.updateHueCursor();
        }
        
        if (alphaRect) {
          this.setData({
            alphaWidth: alphaRect.width || 0,
            alphaRect
          });
          this.updateAlphaCursor();
        }
      });
    },

    updateCursorPosition() {
      const { saturation, value, pickerWidth, pickerHeight } = this.data;
      const svCursorX = (saturation / 100) * pickerWidth;
      const svCursorY = ((100 - value) / 100) * pickerHeight;
      
      this.setData({ svCursorX, svCursorY });
    },

    updateHueCursor() {
      const { hue, hueWidth } = this.data;
      this.setData({
        hueCursorX: (hue / 360) * this.data.pickerHeight
      });
    },

    updateAlphaCursor() {
      const { alpha, alphaWidth } = this.data;
      this.setData({
        alphaCursorX: (alpha / 100) * alphaWidth
      });
    },

    initializeColor(color: string) {
      let rgb: {r: number, g: number, b: number, a?: number} | null = null;
      let alpha = 100;

      if (color.startsWith('rgba')) {
        const match = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (match) {
          rgb = {
            r: parseInt(match[1]),
            g: parseInt(match[2]),
            b: parseInt(match[3])
          };
          alpha = Math.round(parseFloat(match[4]) * 100);
        }
      } else {
        rgb = this.hexToRgb(color);
      }

      if (!rgb) return;
      
      const hsv = this.rgbToHsv(rgb.r, rgb.g, rgb.b);
      const pureColor = this.rgbToHex(rgb.r, rgb.g, rgb.b);
      
      // 获取 sv-picker 元素
      const svPicker = this.createSelectorQuery().select('.sv-picker');
      
      this.setData({
        hue: hsv.h,
        saturation: hsv.s,
        value: hsv.v,
        alpha,
        pureColor,
        currentColor: this.getRgbaColor(rgb.r, rgb.g, rgb.b, alpha / 100)
      }, () => {
        // 更新 sv-picker 的背景色
        svPicker.fields({ node: true }, function(res) {
          if (res && res.node) {
            res.node.style.setProperty('--hue', `${hsv.h}`);
          }
        }).exec();
      });

      if (this.data.pickerWidth && this.data.pickerHeight) {
        this.updateCursorPosition();
        this.updateHueCursor();
        this.updateAlphaCursor();
      }
    },

    getRgbaColor(r: number, g: number, b: number, a: number) {
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    },

    hexToRgb(hex: string) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },

    rgbToHex(r: number, g: number, b: number) {
      return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    },

    rgbToHsv(r: number, g: number, b: number) {
      r /= 255;
      g /= 255;
      b /= 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0;
      const v = max;
      const d = max - min;
      const s = max === 0 ? 0 : d / max;

      if (max === min) {
        h = 0;
      } else {
        switch (max) {
          case r:
            h = (g - b) / d + (g < b ? 6 : 0);
            break;
          case g:
            h = (b - r) / d + 2;
            break;
          case b:
            h = (r - g) / d + 4;
            break;
        }
        h /= 6;
      }

      return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        v: Math.round(v * 100)
      };
    },

    hsvToRgb(h: number, s: number, v: number) {
      h /= 360;
      s /= 100;
      v /= 100;

      let r = 0, g = 0, b = 0;
      const i = Math.floor(h * 6);
      const f = h * 6 - i;
      const p = v * (1 - s);
      const q = v * (1 - f * s);
      const t = v * (1 - (1 - f) * s);

      switch (i % 6) {
        case 0: r = v; g = t; b = p; break;
        case 1: r = q; g = v; b = p; break;
        case 2: r = p; g = v; b = t; break;
        case 3: r = p; g = q; b = v; break;
        case 4: r = t; g = p; b = v; break;
        case 5: r = v; g = p; b = q; break;
      }

      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    },

    updateColor(triggerEvent = false) {
      const rgb = this.hsvToRgb(this.data.hue, this.data.saturation, this.data.value);
      const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);
      const rgba = this.getRgbaColor(rgb.r, rgb.g, rgb.b, this.data.alpha / 100);
      
      this.setData({ 
        pureColor: hex,
        currentColor: rgba
      });
      
      if (triggerEvent) {
        this.triggerEvent('change', { color: rgba });
      }
    },

    onSVTouchStart(e: WechatMiniprogram.TouchEvent) {
      this.setData({ isMoving: true });
      this.updateSV(e);
    },

    onSVTouchMove(e: WechatMiniprogram.TouchEvent) {
      ((this as any).throttledUpdateSV).call(this, e);
    },

    onSVTouchEnd() {
      this.setData({ isMoving: false });
      this.updateColor(false);
    },

    updateSV(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0] as TouchDetail;
      const rect = this.data.svRect;
      
      if (!rect) {
        const query = this.createSelectorQuery();
        query.select('.sv-picker').boundingClientRect(rect => {
          if (!rect) return;
          this.data.svRect = rect;
          this.updateSVWithRect(touch, rect);
        }).exec();
      } else {
        this.updateSVWithRect(touch, rect);
      }
    },

    updateSVWithRect(touch: TouchDetail, rect: WechatMiniprogram.BoundingClientRectCallbackResult) {
      let x = touch.clientX - rect.left;
      let y = touch.clientY - rect.top;

      x = Math.max(0, Math.min(x, rect.width));
      y = Math.max(0, Math.min(y, rect.height));

      const saturation = (x / rect.width) * 100;
      const value = 100 - (y / rect.height) * 100;

      this.setData({
        saturation,
        value,
        svCursorX: x,
        svCursorY: y
      });
    },

    onHueTouchStart(e: WechatMiniprogram.TouchEvent) {
      this.setData({ isMoving: true });
      this.updateHue(e);
    },

    onHueTouchMove(e: WechatMiniprogram.TouchEvent) {
      ((this as any).throttledUpdateHue).call(this, e);
    },

    onHueTouchEnd() {
      this.setData({ isMoving: false });
      this.updateColor(false);
    },

    updateHue(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0] as TouchDetail;
      const rect = this.data.hueRect;
      
      if (!rect) {
        const query = this.createSelectorQuery();
        query.select('.hue-picker').boundingClientRect(rect => {
          if (!rect) return;
          this.data.hueRect = rect;
          this.updateHueWithRect(touch, rect);
        }).exec();
      } else {
        this.updateHueWithRect(touch, rect);
      }
    },

    updateHueWithRect(touch: TouchDetail, rect: WechatMiniprogram.BoundingClientRectCallbackResult) {
      let y = touch.clientY - rect.top;
      y = Math.max(0, Math.min(y, rect.height));

      const hue = (y / rect.height) * 360;

      const svPicker = this.createSelectorQuery().select('.sv-picker');
      
      this.setData({
        hue,
        hueCursorX: y
      }, () => {
        svPicker.fields({ node: true }, function(res) {
          if (res && res.node) {
            res.node.style.setProperty('--hue', `${hue}`);
          }
        }).exec();
      });
    },

    onAlphaTouchStart(e: WechatMiniprogram.TouchEvent) {
      this.setData({ isMoving: true });
      this.updateAlpha(e);
    },

    onAlphaTouchMove(e: WechatMiniprogram.TouchEvent) {
      ((this as any).throttledUpdateAlpha).call(this, e);
    },

    onAlphaTouchEnd() {
      this.setData({ isMoving: false });
      this.updateColor(false);
    },

    updateAlpha(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0] as TouchDetail;
      const rect = this.data.alphaRect;
      
      if (!rect) {
        const query = this.createSelectorQuery();
        query.select('.alpha-slider').boundingClientRect(rect => {
          if (!rect) return;
          this.data.alphaRect = rect;
          this.updateAlphaWithRect(touch, rect);
        }).exec();
      } else {
        this.updateAlphaWithRect(touch, rect);
      }
    },

    updateAlphaWithRect(touch: TouchDetail, rect: WechatMiniprogram.BoundingClientRectCallbackResult) {
      let x = touch.clientX - rect.left;
      x = Math.max(0, Math.min(x, rect.width));

      const alpha = Math.round((x / rect.width) * 100);

      this.setData({
        alpha,
        alphaCursorX: x
      });
    },

    onPresetColorTap(e: WechatMiniprogram.CustomEvent) {
      const color = e.currentTarget.dataset.color;
      this.initializeColor(color);
    }
  }
});
{"version": 3, "sources": ["index.js", "lib/body.js", "lib/fetch-error.js", "lib/response.js", "lib/headers.js", "lib/request.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,ACHA,AENA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA,AFMA;AHUA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA,ACHA;ALgBA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA,AGTA;AJaA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n/**\n * index.js\n *\n * a request API compatible with window.fetch\n */\n\nvar parse_url = require('url').parse;\nvar resolve_url = require('url').resolve;\nvar http = require('http');\nvar https = require('https');\nvar zlib = require('zlib');\nvar stream = require('stream');\n\nvar Body = require('./lib/body');\nvar Response = require('./lib/response');\nvar Headers = require('./lib/headers');\nvar Request = require('./lib/request');\nvar FetchError = require('./lib/fetch-error');\n\n// commonjs\nmodule.exports = Fetch;\n// es6 default export compatibility\nmodule.exports.default = module.exports;\n\n/**\n * Fetch class\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */\nfunction Fetch(url, opts) {\n\n\t// allow call as function\n\tif (!(this instanceof Fetch))\n\t\treturn new Fetch(url, opts);\n\n\t// allow custom promise\n\tif (!Fetch.Promise) {\n\t\tthrow new Error('native promise missing, set Fetch.Promise to your favorite alternative');\n\t}\n\n\tBody.Promise = Fetch.Promise;\n\n\tvar self = this;\n\n\t// wrap http.request into fetch\n\treturn new Fetch.Promise(function(resolve, reject) {\n\t\t// build request object\n\t\tvar options = new Request(url, opts);\n\n\t\tif (!options.protocol || !options.hostname) {\n\t\t\tthrow new Error('only absolute urls are supported');\n\t\t}\n\n\t\tif (options.protocol !== 'http:' && options.protocol !== 'https:') {\n\t\t\tthrow new Error('only http(s) protocols are supported');\n\t\t}\n\n\t\tvar send;\n\t\tif (options.protocol === 'https:') {\n\t\t\tsend = https.request;\n\t\t} else {\n\t\t\tsend = http.request;\n\t\t}\n\n\t\t// normalize headers\n\t\tvar headers = new Headers(options.headers);\n\n\t\tif (options.compress) {\n\t\t\theaders.set('accept-encoding', 'gzip,deflate');\n\t\t}\n\n\t\tif (!headers.has('user-agent')) {\n\t\t\theaders.set('user-agent', 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)');\n\t\t}\n\n\t\tif (!headers.has('connection') && !options.agent) {\n\t\t\theaders.set('connection', 'close');\n\t\t}\n\n\t\tif (!headers.has('accept')) {\n\t\t\theaders.set('accept', '*/*');\n\t\t}\n\n\t\t// detect form data input from form-data module, this hack avoid the need to pass multipart header manually\n\t\tif (!headers.has('content-type') && options.body && typeof options.body.getBoundary === 'function') {\n\t\t\theaders.set('content-type', 'multipart/form-data; boundary=' + options.body.getBoundary());\n\t\t}\n\n\t\t// bring node-fetch closer to browser behavior by setting content-length automatically\n\t\tif (!headers.has('content-length') && /post|put|patch|delete/i.test(options.method)) {\n\t\t\tif (typeof options.body === 'string') {\n\t\t\t\theaders.set('content-length', Buffer.byteLength(options.body));\n\t\t\t// detect form data input from form-data module, this hack avoid the need to add content-length header manually\n\t\t\t} else if (options.body && typeof options.body.getLengthSync === 'function') {\n\t\t\t\t// for form-data 1.x\n\t\t\t\tif (options.body._lengthRetrievers && options.body._lengthRetrievers.length == 0) {\n\t\t\t\t\theaders.set('content-length', options.body.getLengthSync().toString());\n\t\t\t\t// for form-data 2.x\n\t\t\t\t} else if (options.body.hasKnownLength && options.body.hasKnownLength()) {\n\t\t\t\t\theaders.set('content-length', options.body.getLengthSync().toString());\n\t\t\t\t}\n\t\t\t// this is only necessary for older nodejs releases (before iojs merge)\n\t\t\t} else if (options.body === undefined || options.body === null) {\n\t\t\t\theaders.set('content-length', '0');\n\t\t\t}\n\t\t}\n\n\t\toptions.headers = headers.raw();\n\n\t\t// http.request only support string as host header, this hack make custom host header possible\n\t\tif (options.headers.host) {\n\t\t\toptions.headers.host = options.headers.host[0];\n\t\t}\n\n\t\t// send request\n\t\tvar req = send(options);\n\t\tvar reqTimeout;\n\n\t\tif (options.timeout) {\n\t\t\treq.once('socket', function(socket) {\n\t\t\t\treqTimeout = setTimeout(function() {\n\t\t\t\t\treq.abort();\n\t\t\t\t\treject(new FetchError('network timeout at: ' + options.url, 'request-timeout'));\n\t\t\t\t}, options.timeout);\n\t\t\t});\n\t\t}\n\n\t\treq.on('error', function(err) {\n\t\t\tclearTimeout(reqTimeout);\n\t\t\treject(new FetchError('request to ' + options.url + ' failed, reason: ' + err.message, 'system', err));\n\t\t});\n\n\t\treq.on('response', function(res) {\n\t\t\tclearTimeout(reqTimeout);\n\n\t\t\t// handle redirect\n\t\t\tif (self.isRedirect(res.statusCode) && options.redirect !== 'manual') {\n\t\t\t\tif (options.redirect === 'error') {\n\t\t\t\t\treject(new FetchError('redirect mode is set to error: ' + options.url, 'no-redirect'));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (options.counter >= options.follow) {\n\t\t\t\t\treject(new FetchError('maximum redirect reached at: ' + options.url, 'max-redirect'));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!res.headers.location) {\n\t\t\t\t\treject(new FetchError('redirect location header missing at: ' + options.url, 'invalid-redirect'));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// per fetch spec, for POST request with 301/302 response, or any request with 303 response, use GET when following redirect\n\t\t\t\tif (res.statusCode === 303\n\t\t\t\t\t|| ((res.statusCode === 301 || res.statusCode === 302) && options.method === 'POST'))\n\t\t\t\t{\n\t\t\t\t\toptions.method = 'GET';\n\t\t\t\t\tdelete options.body;\n\t\t\t\t\tdelete options.headers['content-length'];\n\t\t\t\t}\n\n\t\t\t\toptions.counter++;\n\n\t\t\t\tresolve(Fetch(resolve_url(options.url, res.headers.location), options));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// normalize location header for manual redirect mode\n\t\t\tvar headers = new Headers(res.headers);\n\t\t\tif (options.redirect === 'manual' && headers.has('location')) {\n\t\t\t\theaders.set('location', resolve_url(options.url, headers.get('location')));\n\t\t\t}\n\n\t\t\t// prepare response\n\t\t\tvar body = res.pipe(new stream.PassThrough());\n\t\t\tvar response_options = {\n\t\t\t\turl: options.url\n\t\t\t\t, status: res.statusCode\n\t\t\t\t, statusText: res.statusMessage\n\t\t\t\t, headers: headers\n\t\t\t\t, size: options.size\n\t\t\t\t, timeout: options.timeout\n\t\t\t};\n\n\t\t\t// response object\n\t\t\tvar output;\n\n\t\t\t// in following scenarios we ignore compression support\n\t\t\t// 1. compression support is disabled\n\t\t\t// 2. HEAD request\n\t\t\t// 3. no content-encoding header\n\t\t\t// 4. no content response (204)\n\t\t\t// 5. content not modified response (304)\n\t\t\tif (!options.compress || options.method === 'HEAD' || !headers.has('content-encoding') || res.statusCode === 204 || res.statusCode === 304) {\n\t\t\t\toutput = new Response(body, response_options);\n\t\t\t\tresolve(output);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// otherwise, check for gzip or deflate\n\t\t\tvar name = headers.get('content-encoding');\n\n\t\t\t// for gzip\n\t\t\tif (name == 'gzip' || name == 'x-gzip') {\n\t\t\t\tbody = body.pipe(zlib.createGunzip());\n\t\t\t\toutput = new Response(body, response_options);\n\t\t\t\tresolve(output);\n\t\t\t\treturn;\n\n\t\t\t// for deflate\n\t\t\t} else if (name == 'deflate' || name == 'x-deflate') {\n\t\t\t\t// handle the infamous raw deflate response from old servers\n\t\t\t\t// a hack for old IIS and Apache servers\n\t\t\t\tvar raw = res.pipe(new stream.PassThrough());\n\t\t\t\traw.once('data', function(chunk) {\n\t\t\t\t\t// see http://stackoverflow.com/questions/37519828\n\t\t\t\t\tif ((chunk[0] & 0x0F) === 0x08) {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflate());\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflateRaw());\n\t\t\t\t\t}\n\t\t\t\t\toutput = new Response(body, response_options);\n\t\t\t\t\tresolve(output);\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// otherwise, use response as-is\n\t\t\toutput = new Response(body, response_options);\n\t\t\tresolve(output);\n\t\t\treturn;\n\t\t});\n\n\t\t// accept string, buffer or readable stream as body\n\t\t// per spec we will call tostring on non-stream objects\n\t\tif (typeof options.body === 'string') {\n\t\t\treq.write(options.body);\n\t\t\treq.end();\n\t\t} else if (options.body instanceof Buffer) {\n\t\t\treq.write(options.body);\n\t\t\treq.end()\n\t\t} else if (typeof options.body === 'object' && options.body.pipe) {\n\t\t\toptions.body.pipe(req);\n\t\t} else if (typeof options.body === 'object') {\n\t\t\treq.write(options.body.toString());\n\t\t\treq.end();\n\t\t} else {\n\t\t\treq.end();\n\t\t}\n\t});\n\n};\n\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */\nFetch.prototype.isRedirect = function(code) {\n\treturn code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n}\n\n// expose Promise\nFetch.Promise = global.Promise;\nFetch.Response = Response;\nFetch.Headers = Headers;\nFetch.Request = Request;\n", "\n/**\n * body.js\n *\n * Body interface provides common methods for Request and Response\n */\n\nvar convert = require('encoding').convert;\nvar bodyStream = require('is-stream');\nvar PassThrough = require('stream').PassThrough;\nvar FetchError = require('./fetch-error');\n\nmodule.exports = Body;\n\n/**\n * Body class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nfunction Body(body, opts) {\n\n\topts = opts || {};\n\n\tthis.body = body;\n\tthis.bodyUsed = false;\n\tthis.size = opts.size || 0;\n\tthis.timeout = opts.timeout || 0;\n\tthis._raw = [];\n\tthis._abort = false;\n\n}\n\n/**\n * Decode response as json\n *\n * @return  Promise\n */\nBody.prototype.json = function() {\n\n\t// for 204 No Content response, buffer will be empty, parsing it will throw error\n\tif (this.status === 204) {\n\t\treturn Body.Promise.resolve({});\n\t}\n\n\treturn this._decode().then(function(buffer) {\n\t\treturn JSON.parse(buffer.toString());\n\t});\n\n};\n\n/**\n * Decode response as text\n *\n * @return  Promise\n */\nBody.prototype.text = function() {\n\n\treturn this._decode().then(function(buffer) {\n\t\treturn buffer.toString();\n\t});\n\n};\n\n/**\n * Decode response as buffer (non-spec api)\n *\n * @return  Promise\n */\nBody.prototype.buffer = function() {\n\n\treturn this._decode();\n\n};\n\n/**\n * Decode buffers into utf-8 string\n *\n * @return  Promise\n */\nBody.prototype._decode = function() {\n\n\tvar self = this;\n\n\tif (this.bodyUsed) {\n\t\treturn Body.Promise.reject(new Error('body used already for: ' + this.url));\n\t}\n\n\tthis.bodyUsed = true;\n\tthis._bytes = 0;\n\tthis._abort = false;\n\tthis._raw = [];\n\n\treturn new Body.Promise(function(resolve, reject) {\n\t\tvar resTimeout;\n\n\t\t// body is string\n\t\tif (typeof self.body === 'string') {\n\t\t\tself._bytes = self.body.length;\n\t\t\tself._raw = [new Buffer(self.body)];\n\t\t\treturn resolve(self._convert());\n\t\t}\n\n\t\t// body is buffer\n\t\tif (self.body instanceof Buffer) {\n\t\t\tself._bytes = self.body.length;\n\t\t\tself._raw = [self.body];\n\t\t\treturn resolve(self._convert());\n\t\t}\n\n\t\t// allow timeout on slow response body\n\t\tif (self.timeout) {\n\t\t\tresTimeout = setTimeout(function() {\n\t\t\t\tself._abort = true;\n\t\t\t\treject(new FetchError('response timeout at ' + self.url + ' over limit: ' + self.timeout, 'body-timeout'));\n\t\t\t}, self.timeout);\n\t\t}\n\n\t\t// handle stream error, such as incorrect content-encoding\n\t\tself.body.on('error', function(err) {\n\t\t\treject(new FetchError('invalid response body at: ' + self.url + ' reason: ' + err.message, 'system', err));\n\t\t});\n\n\t\t// body is stream\n\t\tself.body.on('data', function(chunk) {\n\t\t\tif (self._abort || chunk === null) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (self.size && self._bytes + chunk.length > self.size) {\n\t\t\t\tself._abort = true;\n\t\t\t\treject(new FetchError('content size at ' + self.url + ' over limit: ' + self.size, 'max-size'));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself._bytes += chunk.length;\n\t\t\tself._raw.push(chunk);\n\t\t});\n\n\t\tself.body.on('end', function() {\n\t\t\tif (self._abort) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tclearTimeout(resTimeout);\n\t\t\tresolve(self._convert());\n\t\t});\n\t});\n\n};\n\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   String  encoding  Target encoding\n * @return  String\n */\nBody.prototype._convert = function(encoding) {\n\n\tencoding = encoding || 'utf-8';\n\n\tvar ct = this.headers.get('content-type');\n\tvar charset = 'utf-8';\n\tvar res, str;\n\n\t// header\n\tif (ct) {\n\t\t// skip encoding detection altogether if not html/xml/plain text\n\t\tif (!/text\\/html|text\\/plain|\\+xml|\\/xml/i.test(ct)) {\n\t\t\treturn Buffer.concat(this._raw);\n\t\t}\n\n\t\tres = /charset=([^;]*)/i.exec(ct);\n\t}\n\n\t// no charset in content type, peek at response body for at most 1024 bytes\n\tif (!res && this._raw.length > 0) {\n\t\tfor (var i = 0; i < this._raw.length; i++) {\n\t\t\tstr += this._raw[i].toString()\n\t\t\tif (str.length > 1024) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tstr = str.substr(0, 1024);\n\t}\n\n\t// html5\n\tif (!res && str) {\n\t\tres = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// html4\n\tif (!res && str) {\n\t\tres = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n\n\t\tif (res) {\n\t\t\tres = /charset=(.*)/i.exec(res.pop());\n\t\t}\n\t}\n\n\t// xml\n\tif (!res && str) {\n\t\tres = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// found charset\n\tif (res) {\n\t\tcharset = res.pop();\n\n\t\t// prevent decode issues when sites use incorrect encoding\n\t\t// ref: https://hsivonen.fi/encoding-menu/\n\t\tif (charset === 'gb2312' || charset === 'gbk') {\n\t\t\tcharset = 'gb18030';\n\t\t}\n\t}\n\n\t// turn raw buffers into a single utf-8 buffer\n\treturn convert(\n\t\tBuffer.concat(this._raw)\n\t\t, encoding\n\t\t, charset\n\t);\n\n};\n\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */\nBody.prototype._clone = function(instance) {\n\tvar p1, p2;\n\tvar body = instance.body;\n\n\t// don't allow cloning a used body\n\tif (instance.bodyUsed) {\n\t\tthrow new Error('cannot clone body after it is used');\n\t}\n\n\t// check that body is a stream and not form-data object\n\t// note: we can't clone the form-data object without having it as a dependency\n\tif (bodyStream(body) && typeof body.getBoundary !== 'function') {\n\t\t// tee instance body\n\t\tp1 = new PassThrough();\n\t\tp2 = new PassThrough();\n\t\tbody.pipe(p1);\n\t\tbody.pipe(p2);\n\t\t// set instance body to teed body and return the other teed body\n\t\tinstance.body = p1;\n\t\tbody = p2;\n\t}\n\n\treturn body;\n}\n\n// expose Promise\nBody.Promise = global.Promise;\n", "\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */\n\nmodule.exports = FetchError;\n\n/**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */\nfunction FetchError(message, type, systemError) {\n\n\t// hide custom error implementation details from end-users\n\tError.captureStackTrace(this, this.constructor);\n\n\tthis.name = this.constructor.name;\n\tthis.message = message;\n\tthis.type = type;\n\n\t// when err.type is `system`, err.code contains system error code\n\tif (systemError) {\n\t\tthis.code = this.errno = systemError.code;\n\t}\n\n}\n\nrequire('util').inherits(FetchError, Error);\n", "\n/**\n * response.js\n *\n * Response class provides content decoding\n */\n\nvar http = require('http');\nvar Headers = require('./headers');\nvar Body = require('./body');\n\nmodule.exports = Response;\n\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nfunction Response(body, opts) {\n\n\topts = opts || {};\n\n\tthis.url = opts.url;\n\tthis.status = opts.status || 200;\n\tthis.statusText = opts.statusText || http.STATUS_CODES[this.status];\n\tthis.headers = new Headers(opts.headers);\n\tthis.ok = this.status >= 200 && this.status < 300;\n\n\tBody.call(this, body, opts);\n\n}\n\nResponse.prototype = Object.create(Body.prototype);\n\n/**\n * Clone this response\n *\n * @return  Response\n */\nResponse.prototype.clone = function() {\n\treturn new Response(this._clone(this), {\n\t\turl: this.url\n\t\t, status: this.status\n\t\t, statusText: this.statusText\n\t\t, headers: this.headers\n\t\t, ok: this.ok\n\t});\n};\n", "\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */\n\nmodule.exports = Headers;\n\n/**\n * Headers class\n *\n * @param   Object  headers  Response headers\n * @return  Void\n */\nfunction Headers(headers) {\n\n\tvar self = this;\n\tthis._headers = {};\n\n\t// Headers\n\tif (headers instanceof Headers) {\n\t\theaders = headers.raw();\n\t}\n\n\t// plain object\n\tfor (var prop in headers) {\n\t\tif (!headers.hasOwnProperty(prop)) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeof headers[prop] === 'string') {\n\t\t\tthis.set(prop, headers[prop]);\n\n\t\t} else if (typeof headers[prop] === 'number' && !isNaN(headers[prop])) {\n\t\t\tthis.set(prop, headers[prop].toString());\n\n\t\t} else if (headers[prop] instanceof Array) {\n\t\t\theaders[prop].forEach(function(item) {\n\t\t\t\tself.append(prop, item.toString());\n\t\t\t});\n\t\t}\n\t}\n\n}\n\n/**\n * Return first header value given name\n *\n * @param   String  name  Header name\n * @return  Mixed\n */\nHeaders.prototype.get = function(name) {\n\tvar list = this._headers[name.toLowerCase()];\n\treturn list ? list[0] : null;\n};\n\n/**\n * Return all header values given name\n *\n * @param   String  name  Header name\n * @return  Array\n */\nHeaders.prototype.getAll = function(name) {\n\tif (!this.has(name)) {\n\t\treturn [];\n\t}\n\n\treturn this._headers[name.toLowerCase()];\n};\n\n/**\n * Iterate over all headers\n *\n * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n * @param   Boolean   thisArg   `this` context for callback function\n * @return  Void\n */\nHeaders.prototype.forEach = function(callback, thisArg) {\n\tObject.getOwnPropertyNames(this._headers).forEach(function(name) {\n\t\tthis._headers[name].forEach(function(value) {\n\t\t\tcallback.call(thisArg, value, name, this)\n\t\t}, this)\n\t}, this)\n}\n\n/**\n * Overwrite header values given name\n *\n * @param   String  name   Header name\n * @param   String  value  Header value\n * @return  Void\n */\nHeaders.prototype.set = function(name, value) {\n\tthis._headers[name.toLowerCase()] = [value];\n};\n\n/**\n * Append a value onto existing header\n *\n * @param   String  name   Header name\n * @param   String  value  Header value\n * @return  Void\n */\nHeaders.prototype.append = function(name, value) {\n\tif (!this.has(name)) {\n\t\tthis.set(name, value);\n\t\treturn;\n\t}\n\n\tthis._headers[name.toLowerCase()].push(value);\n};\n\n/**\n * Check for header name existence\n *\n * @param   String   name  Header name\n * @return  Boolean\n */\nHeaders.prototype.has = function(name) {\n\treturn this._headers.hasOwnProperty(name.toLowerCase());\n};\n\n/**\n * Delete all header values given name\n *\n * @param   String  name  Header name\n * @return  Void\n */\nHeaders.prototype['delete'] = function(name) {\n\tdelete this._headers[name.toLowerCase()];\n};\n\n/**\n * Return raw headers (non-spec api)\n *\n * @return  Object\n */\nHeaders.prototype.raw = function() {\n\treturn this._headers;\n};\n", "\n/**\n * request.js\n *\n * Request class contains server only options\n */\n\nvar parse_url = require('url').parse;\nvar Headers = require('./headers');\nvar Body = require('./body');\n\nmodule.exports = Request;\n\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */\nfunction Request(input, init) {\n\tvar url, url_parsed;\n\n\t// normalize input\n\tif (!(input instanceof Request)) {\n\t\turl = input;\n\t\turl_parsed = parse_url(url);\n\t\tinput = {};\n\t} else {\n\t\turl = input.url;\n\t\turl_parsed = parse_url(url);\n\t}\n\n\t// normalize init\n\tinit = init || {};\n\n\t// fetch spec options\n\tthis.method = init.method || input.method || 'GET';\n\tthis.redirect = init.redirect || input.redirect || 'follow';\n\tthis.headers = new Headers(init.headers || input.headers || {});\n\tthis.url = url;\n\n\t// server only options\n\tthis.follow = init.follow !== undefined ?\n\t\tinit.follow : input.follow !== undefined ?\n\t\tinput.follow : 20;\n\tthis.compress = init.compress !== undefined ?\n\t\tinit.compress : input.compress !== undefined ?\n\t\tinput.compress : true;\n\tthis.counter = init.counter || input.counter || 0;\n\tthis.agent = init.agent || input.agent;\n\n\tBody.call(this, init.body || this._clone(input), {\n\t\ttimeout: init.timeout || input.timeout || 0,\n\t\tsize: init.size || input.size || 0\n\t});\n\n\t// server request options\n\tthis.protocol = url_parsed.protocol;\n\tthis.hostname = url_parsed.hostname;\n\tthis.port = url_parsed.port;\n\tthis.path = url_parsed.path;\n\tthis.auth = url_parsed.auth;\n}\n\nRequest.prototype = Object.create(Body.prototype);\n\n/**\n * Clone this request\n *\n * @return  Request\n */\nRequest.prototype.clone = function() {\n\treturn new Request(this);\n};\n"]}
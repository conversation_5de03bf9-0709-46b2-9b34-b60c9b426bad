<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="个人中心" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 个人中心内容区域 -->
      <view class="profile-container" wx:if="{{!loading}}">
        <!-- 用户信息卡片 -->
        <view class="user-card">
          <view class="user-info {{isLogin ? '' : 'not-login'}}">
            <view class="avatar-container" bindtap="handleSettingClick" data-id="profile">
              <image class="avatar" src="{{isLogin ? constants.DOMAIN + userInfo.avatar : constants.COMMON_ASSETS.DEFAULT_AVATAR}}" mode="aspectFill"></image>
              <view class="level-tag" wx:if="{{isLogin}}">Lv.{{userInfo.level || 1}}</view>
            </view>
            <view class="user-details" bindtap="handleSettingClick" data-id="profile">
              <view class="nickname">{{isLogin ? userInfo.nickname : '未登录'}}</view>
              <view class="account" wx:if="{{isLogin}}">ID: {{userInfo.username}}</view>
              <view class="login-btn" wx:if="{{!isLogin}}" bindtap="showLoginModal">点击登录</view>
            </view>
            <!-- 添加刷新按钮 -->
            <view class="refresh-btn {{loading ? 'refreshing' : ''}}" bindtap="refreshUserInfo" wx:if="{{isLogin}}" style="display: none;">
              <mp-icon icon="refresh" color="#07c160" size="{{20}}"></mp-icon>
            </view>
          </view>
          
          <!-- 用户数据统计 -->
          <view class="user-stats" wx:if="{{isLogin}}" style="display: none;">
            <view class="stat-item">
              <view class="stat-value">{{userInfo.score || '0'}}</view>
              <view class="stat-label">积分</view>
            </view>
            <view class="stat-item border-item">
              <view class="stat-value">{{userInfo.money || '0.00'}}</view>
              <view class="stat-label">余额</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{userInfo.successions || '1'}}</view>
              <view class="stat-label">连续登录</view>
            </view>
          </view>
        </view>
        
        <!-- 会员信息卡片 -->
        <view class="vip-card" wx:if="{{isLogin}}" style="display: none;">
          <view class="vip-info">
            <view class="vip-left">
              <view class="vip-title">{{userInfo.group_id > 0 ? 'VIP会员' : '普通会员'}}</view>
              <view class="vip-desc">{{userInfo.group_id > 0 ? '尊享会员特权' : '升级会员获取更多特权'}}</view>
            </view>
            <view class="vip-right">
              <view class="vip-btn">{{userInfo.group_id > 0 ? '查看特权' : '立即升级'}}</view>
            </view>
          </view>
        </view>
        
        <!-- 数据统计卡片 -->
        <view class="stats-card" wx:if="{{isLogin && hasUserStats}}" style="display: none;">
          <view class="card-header">
            <view class="section-title">数据统计</view>
            <view class="switch-chart" bindtap="switchChartType" data-type="{{currentChartType === 'ring' ? 'pie' : 'ring'}}">
              切换图表
            </view>
          </view>
          
          <!-- 图表展示区域 -->
          <view class="chart-container">
            <view class="chart-wrapper">
              <qiun-wx-ucharts 
                type="{{currentChartType}}"
                canvas2d="{{true}}"
                canvasId="usage-stats-chart"
                chartData="{{chartData}}"
                opts="{{chartOpts}}"
                loadingType="1"
                errorShow="{{true}}" />
            </view>
            
            <!-- 统计数据预览 -->
            <view class="stats-preview">
              <view class="preview-item">
                <view class="preview-value">{{userStats.used_count || '0'}}</view>
                <view class="preview-label">已使用</view>
              </view>
              <view class="preview-item">
                <view class="preview-value">{{userStats.total_count || '0'}}</view>
                <view class="preview-label">总额度</view>
              </view>
              <view class="preview-item">
                <view class="preview-value">{{userStats.total_count - userStats.used_count || '0'}}</view>
                <view class="preview-label">剩余</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 最近活动卡片 -->
        <view class="activity-card" wx:if="{{isLogin && hasRecentActivities}}" style="display: none;">
          <view class="card-header">
            <view class="section-title">最近活动</view>
            <view class="view-more">查看更多</view>
          </view>
          
          <view class="activity-list">
            <view class="activity-item" wx:for="{{recentActivities}}" wx:key="id">
              <view class="activity-icon">
                <mp-icon icon="{{item.icon}}" color="#07c160" size="{{24}}"></mp-icon>
              </view>
              <view class="activity-content">
                <view class="activity-title">{{item.title}}</view>
                <view class="activity-desc">{{item.description}}</view>
              </view>
              <view class="activity-time">{{item.time}}</view>
            </view>
          </view>
        </view>
        
        <!-- 公告列表 -->
        <view class="announcement-section" wx:if="{{hasAnnouncements}}" style="display: none;">
          <!-- 工作室全体公告 -->
          <view class="section-header">
            <view class="section-title">工作室全体公告</view>
            <view class="view-more" bindtap="viewMoreAnnouncements" data-type="all" wx:if="{{studioAllAnnouncements.length > 1}}">查看更多</view>
          </view>
          <view class="announcement-list" wx:if="{{hasStudioAllAnnouncements}}">
            <view class="announcement-item" wx:for="{{studioAllAnnouncements}}" wx:key="id" wx:if="{{index < 1}}" bindtap="viewAnnouncementDetail" data-type="all" data-index="{{index}}">
              <view class="announcement-header">
                <view class="announcement-title-wrapper">
                  <view class="announcement-title">{{item.title}}</view>
                  <view class="announcement-tag" wx:if="{{item.is_top === 1}}">置顶</view>
                </view>
                <view class="announcement-date">{{item.formattedTime}}</view>
              </view>
              <view class="announcement-content">{{item.content}}</view>
            </view>
            <view class="no-announcement" wx:if="{{studioAllAnnouncements.length === 0}}">
              <mp-icon icon="info" color="#999" size="{{20}}"></mp-icon>
              <text>暂无全体公告</text>
            </view>
          </view>
          
          <!-- 工作室系列公告 -->
          <view class="section-header" style="margin-top: 30rpx;">
            <view class="section-title">工作室系列公告</view>
            <view class="view-more" bindtap="viewMoreAnnouncements" data-type="series" wx:if="{{studioSeriesAnnouncements.length > 1}}">查看更多</view>
          </view>
          <view class="announcement-list" wx:if="{{hasStudioSeriesAnnouncements}}">
            <view class="announcement-item" wx:for="{{studioSeriesAnnouncements}}" wx:key="id" wx:if="{{index < 1}}" bindtap="viewAnnouncementDetail" data-type="series" data-index="{{index}}">
              <view class="announcement-header">
                <view class="announcement-title-wrapper">
                  <view class="announcement-title">{{item.title}}</view>
                  <view class="announcement-tag" wx:if="{{item.is_top === 1}}">置顶</view>
                </view>
                <view class="announcement-date">{{item.formattedTime}}</view>
              </view>
              <view class="announcement-content">{{item.content}}</view>
            </view>
            <view class="no-announcement" wx:if="{{studioSeriesAnnouncements.length === 0}}">
              <mp-icon icon="info" color="#999" size="{{20}}"></mp-icon>
              <text>暂无系列公告</text>
            </view>
          </view>
        </view>
        
        <!-- 公告详情弹窗 -->
        <view class="announcement-detail-modal" wx:if="{{showAnnouncementDetail}}" catchtouchmove="preventTouchMove" style="display: none;">
          <view class="announcement-detail-mask" bindtap="closeAnnouncementDetail"></view>
          <view class="announcement-detail-content">
            <view class="announcement-detail-header">
              <view class="announcement-detail-title">{{currentAnnouncement.title}}</view>
              <view class="announcement-detail-close" bindtap="closeAnnouncementDetail">×</view>
            </view>
            <view class="announcement-detail-info">
              <view class="announcement-detail-date">发布时间：{{currentAnnouncement.formattedTime}}</view>
            </view>
            <scroll-view scroll-y class="announcement-detail-body">
              <view class="announcement-detail-text">{{currentAnnouncement.content}}</view>
            </scroll-view>
          </view>
        </view>
        
        <!-- 更多公告弹窗 -->
        <view class="more-announcements-modal" wx:if="{{showMoreAnnouncements}}" catchtouchmove="preventTouchMove">
          <view class="more-announcements-mask" bindtap="closeMoreAnnouncements"></view>
          <view class="more-announcements-content">
            <view class="more-announcements-header">
              <view class="more-announcements-title">{{currentAnnouncementType === 'all' ? '全体公告' : '系列公告'}}</view>
              <view class="more-announcements-close" bindtap="closeMoreAnnouncements">×</view>
            </view>
            <scroll-view scroll-y class="more-announcements-body">
              <view class="announcement-list">
                <view class="announcement-item" 
                      wx:for="{{currentAnnouncementType === 'all' ? studioAllAnnouncements : studioSeriesAnnouncements}}" 
                      wx:key="id" 
                      bindtap="viewAnnouncementDetail" 
                      data-type="{{currentAnnouncementType}}" 
                      data-index="{{index}}">
                  <view class="announcement-header">
                    <view class="announcement-title-wrapper">
                      <view class="announcement-title">{{item.title}}</view>
                      <view class="announcement-tag" wx:if="{{item.is_top === 1}}">置顶</view>
                    </view>
                    <view class="announcement-date">{{item.formattedTime}}</view>
                  </view>
                  <view class="announcement-content">{{item.content}}</view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
        
        <!-- 功能菜单 -->
        <view class="menu-section">
          <view class="section-header">
            <view class="section-title">我的服务</view>
          </view>
          <view class="menu-grid">
            <view class="menu-item" wx:for="{{menuItems}}" wx:key="id" bindtap="handleMenuClick" data-id="{{item.id}}">
              <view class="menu-icon" style="background: {{item.color}}10;">
                <mp-icon icon="{{item.icon}}" color="{{item.color}}" size="{{25}}"></mp-icon>
              </view>
              <view class="menu-name">{{item.name}}</view>
            </view>
            <!-- uCharts图表菜单项 -->
            <view class="menu-item" bindtap="navigateToUChart" style="display: none;">
              <view class="menu-icon" style="background: #FF572210;">
                <mp-icon icon="info" color="#FF5722" size="{{25}}"></mp-icon>
              </view>
              <view class="menu-name">图表示例</view>
            </view>
          </view>
        </view>
        
        <!-- 账户安全设置 -->
        <view class="settings-section" style="display: none;">
          <view class="section-header">
            <view class="section-title">账户设置</view>
          </view>
          <view class="settings-list">
            <view class="setting-item" wx:for="{{settingItems}}" wx:key="id" bindtap="handleSettingClick" data-id="{{item.id}}">
              <view class="setting-left">
                <mp-icon class="setting-icon" icon="{{item.icon}}" color="{{item.color}}" size="{{20}}"></mp-icon>
                <text class="setting-name">{{item.name}}</text>
              </view>
              <view class="setting-right">
                <text class="setting-value" wx:if="{{item.value}}">{{item.value}}</text>
                <mp-icon icon="arrow" color="#999999" size="{{12}}"></mp-icon>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 退出登录按钮 -->
        <!-- <view class="logout-button" bindtap="logout" wx:if="{{isLogin}}">
          <view class="logout-content">
            <mp-icon icon="close" color="#ff5000" size="{{18}}"></mp-icon>
            <text class="logout-text">退出登录</text>
          </view>
        </view> -->
      </view>
      
      <!-- 骨架屏 -->
      <view class="skeleton" wx:if="{{loading}}">
        <view class="skeleton-card">
          <view class="skeleton-user">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-info">
              <view class="skeleton-name"></view>
              <view class="skeleton-account"></view>
            </view>
          </view>
          <view class="skeleton-stats">
            <view class="skeleton-stat"></view>
            <view class="skeleton-stat"></view>
            <view class="skeleton-stat"></view>
          </view>
        </view>
        
        <view class="skeleton-vip"></view>
        <view class="skeleton-chart"></view>
        <view class="skeleton-activity"></view>
        
        <view class="skeleton-menu">
          <view class="skeleton-title"></view>
          <view class="skeleton-grid">
            <view class="skeleton-item" wx:for="{{8}}" wx:key="index"></view>
          </view>
        </view>
        
        <view class="skeleton-settings">
          <view class="skeleton-title"></view>
          <view class="skeleton-list">
            <view class="skeleton-setting" wx:for="{{4}}" wx:key="index"></view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="3"><!-- 底部导航 --></tab-bar>
</view>

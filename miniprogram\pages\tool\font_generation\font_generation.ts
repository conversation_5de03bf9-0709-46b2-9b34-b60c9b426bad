/// <reference path="../../../../typings/index.d.ts" />
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
// 修改接口为类型
type FontItem = {
  id: number;
  name: string;
  path: string;
  format: string;
  tags?: string[];
  weigh?: number;
  previewImage?: string;
}

type FontCategory = {
  type: string;
  name: string;
  fonts: FontItem[];
}

type ComponentData = {

  isTabBarCollapsed: boolean;
  statusBarHeight: number;
  navBarHeight: number;
  tabBarHeight: number;
  inputText: string;
  selectedFont: string;
  selectedPreviewText: string;
  currentCategory: string;
  fontCategories: FontCategory[];
  isLoading: boolean;
  defaultPreviewText: string;
  canvasSize: number;
  bgColor: string;
  bgOpacity: number;
  fontSize: number;
  scaledFontSize: number;
  maxFontSize: number;
  textDirection: string;
  textColor: string;
  canvasWidth: number;
  canvasHeight: number;
  screenWidth: number;
  isTextOverflow: boolean;
  textWidth: number;
  textHeight: number;
  targetRatio: number;
  minAcceptableRatio: number;
  canvas: WechatMiniprogram.Canvas | null;
  ctx: WechatMiniprogram.CanvasContext | null;
  isExporting: boolean;
}

type ComponentMethods = {
  init(): Promise<void>;
  loadFontsList(): Promise<void>;
  refreshFontsList(): Promise<void>;
  textToHex(text: string): string;
  getFontPreviewUrl(font: FontItem, text: string): string;
  onTextInput(e: WechatMiniprogram.Input): void;
  onSelectFont(e: WechatMiniprogram.TouchEvent): Promise<void>;
  onCategoryChange(e: WechatMiniprogram.TouchEvent): void;
  exportImage(): Promise<void>;
  onBgColorSelect(e: any): void;
  onTextColorSelect(e: any): void;
  onDirectionChange(e: WechatMiniprogram.RadioGroupChange): void;
  onFontSizeChange(e: WechatMiniprogram.SliderChange): void;
  decreaseFontSize(): void;
  increaseFontSize(): void;
  updateCanvasLayout(): void;
  findOptimalFontSize(text: string, maxSize: number): Promise<number>;
  calculateFillRatio(textWidth: number, textHeight: number, isVertical: boolean): number;
  handleTabBarChange(data: { 
    isCollapsed: boolean,
    expandedHeight: number,
    collapsedHeight: number,
    currentHeight: number 
  }): void;
  onShareAppMessage(): WechatMiniprogram.Page.ICustomShareContent;
  onShareTimeline(): WechatMiniprogram.Page.ICustomTimelineContent;
  updateFontPreview(fontPath: string): Promise<void>;
  generateFontPreview(font: FontItem): Promise<string>;
}

import Api from '../../../utils/api';

Component<ComponentData, {}, ComponentMethods>({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20,
    navBarHeight: 44,
    tabBarHeight: 48,
    inputText: '',
    selectedFont: '',
    selectedPreviewText: '',
    currentCategory: '',
    fontCategories: [],
    isLoading: false,
    defaultPreviewText: '创意',
    canvasSize: 600,
    bgColor: '#FFFFFF',
    bgOpacity: 100,
    fontSize: 72,
    scaledFontSize: 72,
    maxFontSize: 200,
    textDirection: 'horizontal',
    textColor: '#000000',
    canvasWidth: 1280,
    canvasHeight: 1024,
    screenWidth: 375,
    isTextOverflow: false,
    textWidth: 0,
    textHeight: 0,
    targetRatio: 0.9,
    minAcceptableRatio: 0.9,
    canvas: null,
    ctx: null,
    isExporting: false
  },

  lifetimes: {
    attached() {
      this.setData({
        layoutStyle: layoutUtil.getContentStyle_nosafeArea()//不包含安全区域的高度 //layoutUtil.getContentStyle()//包含安全区域完整高度
      });
      this.init();
      this.refreshFontsList();
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 分享给朋友
    onShareAppMessage: function () {
      return {
        title: '字体生成工具 - 多种艺术字体任你选择',
        path: '/pages/tool/font_generation/font_generation',
        imageUrl: '/assets/share-image.png'
      };
    },

    // 分享到朋友圈
    onShareTimeline: function () {
      return {
        title: '字体生成工具 - 提供多种艺术字体样式，满足不同创作需求',
        imageUrl: '/assets/share-image.png'
      };
    },

    async init() {
      try {

        // 更新画布布局
        const systemInfo = wx.getSystemInfoSync();
        await this.setData({
          screenWidth: systemInfo.windowWidth,
          statusBarHeight: systemInfo.statusBarHeight || 20,
          isLoading: true
        });

        // 立即加载字体列表
        const response = await Api.font.getCategories();

        if (!Array.isArray(response) || response.length === 0) {
          console.error('无效的字体数据:', response);
          throw new Error('无效的字体数据');
        }

        // 生成预览图
        for (const category of response) {
          for (const font of category.fonts) {
            font.previewImage = await this.generateFontPreview(font);
          }
        }

        // 设置字体分类和默认分类
        const firstCategory = response[0];
        const firstFont = firstCategory.fonts[0];

        // 一次性设置所有相关数据
        await this.setData({
          fontCategories: response,
          currentCategory: firstCategory.type,
          selectedFont: firstFont.path,
          selectedPreviewText: this.data.defaultPreviewText,
          isLoading: false
        });


        this.updateCanvasLayout();

        // 立即加载第一个字体的预览
        await this.updateFontPreview(firstFont.path);

      } catch (error) {
        console.error('初始化失败，详细错误:', error);
        this.setData({ 
          isLoading: false 
        });
        wx.showToast({
          title: '初始化失败',
          icon: 'none'
        });
      }
    },

    // 生成字体预览图
    async generateFontPreview(font: FontItem): Promise<string> {
      const cacheKey = `font_preview_${font.path}`;
      const cached = wx.getStorageSync(cacheKey);
      if (cached) {
        return cached;
      }

      try {
        // 创建离屏画布
        const query = wx.createSelectorQuery();
        const canvas = await new Promise<WechatMiniprogram.Canvas>((resolve) => {
          query.select('#measureCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              const canvas = res[0].node;
              canvas.width = 80;  // 修改宽度以适应两个字
              canvas.height = 40;
              resolve(canvas);
            });
        });

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('获取画布上下文失败');
        }

        // 加载字体
        const previewUrl = this.getFontPreviewUrl(font, '创意');  // 使用"创意"作为预览文字
        const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
          wx.request({
            url: previewUrl,
            method: 'GET',
            responseType: 'arraybuffer',
            header: {
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Origin': 'https://font.chinaz.com',
              'Referer': 'https://font.chinaz.com/'
            },
            success: resolve,
            fail: reject
          });
        });

        if (res.statusCode === 200 && res.data) {
          const buffer = res.data as ArrayBuffer;
          const base64 = wx.arrayBufferToBase64(buffer);
          
          await new Promise<void>((resolve, reject) => {
            wx.loadFontFace({
              family: `Preview_${font.path}`,
              source: `data:font/${font.format};base64,${base64}`,
              success: (result) => {
                resolve();
              },
              fail: (error) => {
                console.error('预览字体加载失败:', error);
                reject(error);
              }
            });
          });

          // 清空画布
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // 绘制文字
          ctx.fillStyle = '#000000';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.font = `28px "Preview_${font.path}"`;  // 调整字体大小
          ctx.fillText('创意', canvas.width / 2, canvas.height / 2);  // 绘制"创意"两个字

          // 导出图片
          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas,
              success: (res) => resolve(res.tempFilePath),
              fail: reject
            });
          });

          // 缓存预览图
          wx.setStorageSync(cacheKey, tempFilePath);
          return tempFilePath;
        }

        throw new Error('加载字体失败');
      } catch (error) {
        console.error('生成预览图失败:', error);
        return ''; // 返回空字符串作为默认值
      }
    },

    // 修改loadFontsList方法
    async loadFontsList() {
      try {
        this.setData({ isLoading: true });

        const response = await Api.font.getCategories();

        if (!Array.isArray(response) || response.length === 0) {
          console.error('无效的API响应格式或空数据:', response);
          throw new Error('无效的数据格式或空数据');
        }

        // 生成预览图
        for (const category of response) {
          for (const font of category.fonts) {
            font.previewImage = await this.generateFontPreview(font);
          }
        }

        // 设置字体分类和默认分类
        const firstCategory = response[0];
        const firstFont = firstCategory.fonts[0];

        await this.setData({
          fontCategories: response,
          currentCategory: firstCategory.type,
          selectedFont: firstFont.path,
          isLoading: false
        });


        // 立即加载第一个字体的预览
        await this.updateFontPreview(firstFont.path);
      } catch (error) {
        console.error('加载字体列表失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载字体失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 刷新字体列表
    async refreshFontsList() {
      try {
        wx.showLoading({ title: '刷新中...' });
        await this.loadFontsList();
        wx.hideLoading();
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('刷新字体列表失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      }
    },

    // 文字转hex
    textToHex(text: string) {
      return text.split('').map(char => char.charCodeAt(0).toString(16)).join(',');
    },

    // 获取字体预览URL
    getFontPreviewUrl(font: FontItem, text: string) {
      const hexText = this.textToHex(text);
      const format = font.format || 'ttf'; // 确保format有默认值
      return `https://font.chinaz.com/api/fonts/FontPreview.ashx?format=${format}&hex=1&font_file=${font.path}&t=ttf&words=${hexText}`;
    },

    // 输入文字变化
    onTextInput(e: WechatMiniprogram.Input) {
      const newText = e.detail.value;
      this.setData({
        inputText: newText
      });

      // 如果已经选择了字体，立即更新预览
      if (this.data.selectedFont) {
        this.updateFontPreview(this.data.selectedFont);
      }
    },

    // 修改选择字体方法
    async onSelectFont(e: WechatMiniprogram.TouchEvent) {
      try {
        const fontPath = e.currentTarget.dataset.font;
        if (!fontPath) {
          throw new Error('字体路径为空');
        }

        // 先设置加载状态
        this.setData({ isLoading: true });

        // 查找字体对象
        let selectedFont: FontItem | undefined;
        for (const category of this.data.fontCategories) {
          selectedFont = category.fonts.find(f => f.path === fontPath);
          if (selectedFont) break;
        }

        if (!selectedFont) {
          throw new Error('找不到字体');
        }

        // 设置选中的字体
        await this.setData({
          selectedFont: fontPath
        });

        // 更新预览
        await this.updateFontPreview(fontPath);

      } catch (error) {
        console.error('选择字体失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '选择字体失败',
          icon: 'none'
        });
      }
    },

    // 切换分类
    onCategoryChange(e: WechatMiniprogram.TouchEvent) {
      const category = e.currentTarget.dataset.category;
      if (!category) {
        console.error('分类为空');
        return;
      }
      
      // 找到该分类的第一个字体
      const targetCategory = this.data.fontCategories.find(c => c.type === category);
      if (targetCategory && targetCategory.fonts.length > 0) {
        const firstFont = targetCategory.fonts[0];
        this.setData({
          currentCategory: category,
          selectedFont: firstFont.path
        });
        this.updateFontPreview(firstFont.path);
      } else {
        this.setData({
          currentCategory: category
        });
      }
    },

    // 导出图片
    async exportImage() {
      console.log('=== 开始导出图片过程 ===');
      if (!this.data.selectedPreviewText || !this.data.selectedFont) {
        console.log('验证失败：', {
          selectedPreviewText: this.data.selectedPreviewText,
          selectedFont: this.data.selectedFont
        });
        wx.showToast({
          title: '请先选择字体并输入文字',
          icon: 'none'
        });
        return;
      }

      try {
        this.setData({ isExporting: true });
        wx.showLoading({ title: '导出中...' });
        console.log('开始查找字体对象...');

        try {
          // 查找当前选中的字体对象
          let selectedFont: FontItem | undefined;
          for (const category of this.data.fontCategories) {
            selectedFont = category.fonts.find(f => f.path === this.data.selectedFont);
            if (selectedFont) break;
          }

          if (!selectedFont) {
            console.error('未找到选中的字体:', this.data.selectedFont);
            throw new Error('找不到选中的字体');
          }
          console.log('找到字体对象:', {
            name: selectedFont.name,
            path: selectedFont.path,
            format: selectedFont.format
          });

          // 重新加载字体以确保字体可用
          const text = this.data.selectedPreviewText.replace(/\r?\n/g, '');  // 移除换行符
          const previewUrl = this.getFontPreviewUrl(selectedFont, text);
          console.log('准备请求字体数据:', {
            text: text,
            url: previewUrl,
            format: selectedFont.format || 'ttf' // 确保有默认格式
          });
          
          console.log('开始请求字体数据...');
          const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
            wx.request({
              url: previewUrl,
              method: 'GET',
              responseType: 'arraybuffer',
              header: {
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Origin': 'https://font.chinaz.com',
                'Referer': 'https://font.chinaz.com/'
              },
              success: (res) => {
                console.log('字体数据请求成功:', {
                  statusCode: res.statusCode,
                  dataSize: res.data ? (res.data as ArrayBuffer).byteLength : 0
                });
                resolve(res);
              },
              fail: (error) => {
                console.error('字体数据请求失败:', error);
                reject(error);
              }
            });
          });

          if (res.statusCode !== 200 || !res.data) {
            console.error('字体数据请求异常:', {
              statusCode: res.statusCode,
              hasData: !!res.data
            });
            throw new Error('获取字体数据失败');
          }

          const buffer = res.data as ArrayBuffer;
          const base64 = wx.arrayBufferToBase64(buffer);
          console.log('字体数据转换完成:', {
            bufferSize: buffer.byteLength,
            base64Length: base64.length
          });

          // 使用时间戳确保字体名称唯一
          const fontFamily = `CustomFont_${Date.now()}`;
          console.log('开始加载字体...', {fontFamily});
          let currentFontFamily = fontFamily;  // 在外层定义变量

          // 确保字体加载完成
          try {
            await new Promise<void>((resolve, reject) => {
              console.log('调用loadFontFace...');
              const fontSource = `data:font/ttf;base64,${base64}`;
              console.log('字体源:', {
                family: fontFamily,
                source: fontSource.substring(0, 50) + '...'
              });
              
              wx.loadFontFace({
                global: true,
                family: fontFamily,
                source: fontSource,
                desc: {
                  style: 'normal',
                  weight: 'normal',
                  variant: 'normal'
                },
                success: (res) => {
                  console.log('字体加载成功:', {
                    res: res,
                    family: fontFamily
                  });
                  resolve();
                },
                fail: (error) => {
                  console.error('字体加载失败:', {
                    error: error,
                    family: fontFamily
                  });
                  reject(error);
                },
                complete: (res) => {
                  console.log('字体加载完成回调:', res);
                }
              });
            });

            // 增加字体加载等待时间
            console.log('等待字体完全加载...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            console.log('字体加载等待完成');

            // 使用实际画布进行验证
            console.log('开始验证字体...');
            const query = wx.createSelectorQuery().in(this);
            query.select('#exportCanvas')
              .fields({ node: true, size: true });
            const [canvasRes] = (await new Promise(resolve => query.exec(resolve))) as WechatMiniprogram.IAnyObject[];
            
            if (!canvasRes || !canvasRes.node) {
              console.error('获取验证画布失败:', {canvasRes});
              throw new Error('获取验证画布失败');
            }

            const testCanvas = canvasRes.node as WechatMiniprogram.Canvas;
            testCanvas.width = 800;  // 增加画布尺寸
            testCanvas.height = 400;
            
            const testCtx = testCanvas.getContext('2d');
            if (!testCtx) {
              console.error('获取验证画布上下文失败');
              throw new Error('获取验证画布上下文失败');
            }

            // 清空画布并设置背景色以便观察
            testCtx.fillStyle = '#FFFFFF';
            testCtx.fillRect(0, 0, testCanvas.width, testCanvas.height);
            
            // 测试多个字符
            console.log('测试默认字体...');
            testCtx.textBaseline = 'middle';
            testCtx.textAlign = 'left';
            testCtx.fillStyle = '#000000';
            
            const testTexts = ['永', '测试', '中文', 'ABC'];  // 使用多个测试文本
            const fontSize = 100;
            let fontVerified = false;
            
            // 先绘制默认字体
            testCtx.font = `${fontSize}px sans-serif`;
            const defaultText = '永';
            const defaultMetrics = testCtx.measureText(defaultText);
            const defaultWidth = defaultMetrics.width;
            const defaultHeight = defaultMetrics.actualBoundingBoxAscent + defaultMetrics.actualBoundingBoxDescent;
            testCtx.fillText(defaultText, 10, 50);
            
            // 等待一段时间再测试自定义字体
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试加载的字体
            console.log('测试自定义字体...');
            const customFontDeclaration = `${fontSize}px "${currentFontFamily}"`;
            testCtx.font = customFontDeclaration;
            const customMetrics = testCtx.measureText(defaultText);
            const customWidth = customMetrics.width;
            const customHeight = customMetrics.actualBoundingBoxAscent + customMetrics.actualBoundingBoxDescent;
            testCtx.fillText(defaultText, 10, 150);
            
            // 计算宽度和高度的差异
            const widthDifference = Math.abs(defaultWidth - customWidth);
            const heightDifference = Math.abs(defaultHeight - customHeight);
            
            console.log('字体验证结果:', {
              testText: defaultText,
              defaultWidth,
              customWidth,
              defaultHeight,
              customHeight,
              widthDifference,
              heightDifference,
              isDifferentWidth: widthDifference > 2,
              isDifferentHeight: heightDifference > 2
            });

            // 如果宽度或高度有明显差异，认为字体已加载
            if (widthDifference > 2 || heightDifference > 2) {
              fontVerified = true;
              console.log('字体验证通过：检测到尺寸差异');
            } else {
              // 尝试使用像素比较
              const defaultImageData = testCtx.getImageData(10, 30, 100, 40);
              const customImageData = testCtx.getImageData(10, 130, 100, 40);
              let differentPixels = 0;
              
              for (let i = 0; i < defaultImageData.data.length; i += 4) {
                if (defaultImageData.data[i] !== customImageData.data[i] ||
                    defaultImageData.data[i + 1] !== customImageData.data[i + 1] ||
                    defaultImageData.data[i + 2] !== customImageData.data[i + 2]) {
                  differentPixels++;
                }
              }
              
              console.log('像素比较结果:', {
                differentPixels,
                totalPixels: (defaultImageData.data.length / 4)
              });
              
              if (differentPixels > 100) {  // 如果有足够多的像素不同
                fontVerified = true;
                console.log('字体验证通过：检测到像素差异');
              }
            }

            // 如果验证失败，尝试重新加载字体
            if (!fontVerified) {
              console.warn('字体验证失败，尝试重新加载...');
              // 重新加载字体，使用不同的字体名称
              const retryFontFamily = `CustomFont_Retry_${Date.now()}`;
              await new Promise<void>((resolve, reject) => {
                const retryFontSource = `data:font/ttf;base64,${base64}`;
                console.log('重试字体源:', {
                  family: retryFontFamily,
                  source: retryFontSource.substring(0, 50) + '...'
                });
                
                wx.loadFontFace({
                  global: true,
                  family: retryFontFamily,
                  source: retryFontSource,
                  desc: {
                    style: 'normal',
                    weight: 'normal',
                    variant: 'normal'
                  },
                  success: (result) => {
                    console.log('重试字体加载成功:', result);
                    currentFontFamily = retryFontFamily;
                    resolve();
                  },
                  fail: (error) => {
                    console.error('重试字体加载失败:', error);
                    reject(error);
                  }
                });
              });

              // 等待字体加载
              await new Promise(resolve => setTimeout(resolve, 5000));
              
              // 再次验证字体
              testCtx.clearRect(0, 0, testCanvas.width, testCanvas.height);
              testCtx.fillStyle = '#FFFFFF';
              testCtx.fillRect(0, 0, testCanvas.width, testCanvas.height);
              
              // 使用相同的测试字符进行重试验证
              fontVerified = false;
              for (const currentTestText of testTexts) {
                // 先用默认字体测试
                testCtx.font = `${fontSize}px sans-serif`;
                const defaultMetrics = testCtx.measureText(currentTestText);
                const defaultWidth = defaultMetrics.width;
                testCtx.fillText(currentTestText, 10, 50);
                
                // 等待一段时间再测试自定义字体
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 测试重试加载的字体
                console.log(`重试测试字符: ${currentTestText}`);
                const customFontDeclaration = `${fontSize}px "${currentFontFamily}"`;
                testCtx.font = customFontDeclaration;
                const customMetrics = testCtx.measureText(currentTestText);
                const customWidth = customMetrics.width;
                testCtx.fillText(currentTestText, 10, 150);
                
                const widthDifference = Math.abs(defaultWidth - customWidth);
                console.log('重试字体验证结果:', {
                  testText: currentTestText,
                  defaultWidth,
                  customWidth,
                  widthDifference,
                  isDifferent: widthDifference > 2
                });

                if (widthDifference > 2) {
                  fontVerified = true;
                  break;
                }
              }
              
              if (!fontVerified) {
                console.warn('重试字体验证也失败，继续使用当前字体');
              } else {
                console.log('重试字体验证成功，使用新字体名称:', currentFontFamily);
              }
            }

          } catch (error) {
            console.error('字体加载过程出错:', error);
            throw new Error('字体加载失败，请重试');
          }

          console.log('开始准备画布...');
          // 使用更稳定的方式获取canvas
          const query = wx.createSelectorQuery().in(this);
          query.select('#exportCanvas')
            .fields({ node: true, size: true });
          const [canvasRes] = (await new Promise(resolve => query.exec(resolve))) as WechatMiniprogram.IAnyObject[];
          
          if (!canvasRes || !canvasRes.node) {
            console.error('获取画布失败:', {canvasRes});
            throw new Error('获取导出画布失败');
          }
          console.log('获取画布成功');

          const canvas = canvasRes.node as WechatMiniprogram.Canvas;
          canvas.width = this.data.canvasWidth;
          canvas.height = this.data.canvasHeight;
          console.log('设置画布尺寸:', {
            width: canvas.width,
            height: canvas.height
          });

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            console.error('获取画布上下文失败');
            throw new Error('获取画布上下文失败');
          }
          console.log('获取画布上下文成功');

          // 绘制背景
          console.log('开始绘制背景:', {
            bgColor: this.data.bgColor,
            opacity: this.data.bgOpacity
          });
          ctx.fillStyle = this.data.bgColor;
          if (this.data.bgOpacity < 100) {
            const alpha = this.data.bgOpacity / 100;
            ctx.globalAlpha = alpha;
          }
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.globalAlpha = 1.0;

          // 修改字体设置部分
          const scaledFontSize = this.data.scaledFontSize * (canvas.width / 750);
          const fontDeclaration = `${scaledFontSize}px "${currentFontFamily}"`;
          console.log('最终字体设置:', {
            size: scaledFontSize,
            family: currentFontFamily,
            declaration: fontDeclaration
          });
          
          ctx.font = fontDeclaration;
          ctx.fillStyle = this.data.textColor;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          
          console.log('设置最终字体样式:', {
            declaration: fontDeclaration,
            resultingFont: ctx.font
          });

          // 绘制文字
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;

          // 在文字位置绘制参考点
          const originalStyle = ctx.fillStyle;
          ctx.fillStyle = '#FF0000';
          ctx.fillRect(centerX - 2, centerY - 2, 4, 4);
          ctx.fillStyle = originalStyle;

          if (this.data.textDirection === 'vertical') {
            console.log('开始绘制竖排文字');
            const chars = text.split('');
            const totalHeight = chars.length * scaledFontSize;
            let y = centerY - totalHeight / 2 + scaledFontSize / 2;

            for (const char of chars) {
              ctx.fillText(char, centerX, y);
              y += scaledFontSize;
              // 每个字符后短暂等待
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          } else {
            console.log('绘制横排文字:', {
              text,
              position: `${centerX},${centerY}`,
              font: ctx.font
            });
            ctx.fillText(text, centerX, centerY);
          }

          // 增加渲染等待时间
          console.log('等待渲染完成...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          console.log('渲染等待完成');

          // 导出前再次确认画布状态
          console.log('导出前画布状态:', {
            width: canvas.width,
            height: canvas.height,
            currentFont: ctx.font,
            currentStyle: ctx.fillStyle,
            currentAlign: ctx.textAlign,
            currentBaseline: ctx.textBaseline
          });

          // 导出图片
          console.log('开始导出图片...');
          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas,
              success: (res) => {
                console.log('导出临时文件成功:', res.tempFilePath);
                resolve(res.tempFilePath);
              },
              fail: (error) => {
                console.error('导出临时文件失败:', error);
                reject(error);
              }
            });
          });

          // 保存图片
          console.log('开始保存图片到相册...');
          await wx.saveImageToPhotosAlbum({
            filePath: tempFilePath
          });
          console.log('图片保存成功');

          wx.showToast({
            title: '导出成功',
            icon: 'success'
          });
        } catch (error) {
          console.error('导出过程发生错误:', error);
          wx.hideLoading();  // 确保在错误时也隐藏loading
          wx.showToast({
            title: error.message || '导出失败',
            icon: 'none'
          });
        }
      } finally {
        console.log('=== 导出图片过程结束 ===');
        wx.hideLoading();  // 最终的loading隐藏
        this.setData({ isExporting: false });
      }
    },

    // 其他方法保持不变...
    onBgColorSelect(e: any) {
      this.setData({ bgColor: e.detail.color });
    },

    onTextColorSelect(e: any) {
      this.setData({ textColor: e.detail.color });
    },

    onDirectionChange(e: WechatMiniprogram.RadioGroupChange) {
      this.setData({
        textDirection: e.detail.value
      });
      this.updateCanvasLayout();
    },

    onFontSizeChange(e: WechatMiniprogram.SliderChange) {
      const fontSize = Math.floor(e.detail.value);
      const scale = this.data.screenWidth / this.data.canvasWidth;
      const scaledFontSize = Math.floor(fontSize * scale);

      this.setData({
        fontSize,
        scaledFontSize
      });
    },

    decreaseFontSize() {
      if (this.data.fontSize > this.data.maxFontSize - 50) {
        const fontSize = this.data.fontSize - 1;
        const scale = this.data.screenWidth / this.data.canvasWidth;
        const scaledFontSize = Math.floor(fontSize * scale);

        this.setData({
          fontSize,
          scaledFontSize
        });
      }
    },

    increaseFontSize() {
      if (this.data.fontSize < this.data.maxFontSize + 100) {
        const fontSize = this.data.fontSize + 1;
        const scale = this.data.screenWidth / this.data.canvasWidth;
        const scaledFontSize = Math.floor(fontSize * scale);

        this.setData({
          fontSize,
          scaledFontSize
        });
      }
    },

    updateCanvasLayout() {
      const { textDirection, screenWidth } = this.data;
      const isVertical = textDirection === 'vertical';

      let canvasWidth, canvasHeight;
      if (isVertical) {
        canvasWidth = Math.floor(screenWidth * 0.8);
        canvasHeight = Math.floor(screenWidth * 1.6);
      } else {
        canvasWidth = Math.floor(screenWidth * 1.5);
        canvasHeight = Math.floor(screenWidth * 0.8);
      }

      const scale = screenWidth / canvasWidth;
      const maxFontSize = isVertical ?
        Math.floor(Math.min(canvasWidth * 0.8, canvasHeight / (this.data.inputText && this.data.inputText.length || 1))) :
        Math.floor(Math.min(canvasWidth / (this.data.inputText && this.data.inputText.length || 1) * 1.5, canvasHeight * 0.8));

      const scaledFontSize = Math.floor(Math.min(this.data.fontSize, maxFontSize) * scale);

      this.setData({
        canvasWidth,
        canvasHeight,
        maxFontSize,
        scaledFontSize
      });
    },

    async findOptimalFontSize(text: string, maxSize: number): Promise<number> {
      const isVertical = this.data.textDirection === 'vertical';
      const canvasWidth = this.data.canvasWidth;
      const canvasHeight = this.data.canvasHeight;
      
      // 获取 canvas 上下文
      const query = wx.createSelectorQuery().in(this);
      const [canvasRes] = (await new Promise(resolve => {
        query.select('#exportCanvas')
          .fields({ node: true, size: true })
          .exec(resolve);
      })) as WechatMiniprogram.IAnyObject[];
      
      if (!canvasRes || !canvasRes.node) {
        throw new Error('获取画布失败');
      }

      const canvas = canvasRes.node as WechatMiniprogram.Canvas;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('获取画布上下文失败');
      }

      // 计算初始字体大小
      const initialFontSize = isVertical ?
        Math.floor(Math.min(canvasWidth * 0.8, canvasHeight / (this.data.inputText && this.data.inputText.length || 1))) :
        Math.floor(Math.min(canvasWidth / (this.data.inputText && this.data.inputText.length || 1) * 1.5, canvasHeight * 0.8));

      let low = 1;
      let high = maxSize;
      let optimal = maxSize;
      
      while (low <= high) {
        const mid = Math.floor((low + high) / 2);
        if (mid > initialFontSize) {
          const scaledFontSize = mid * (canvasWidth / 750);
          ctx.font = `${scaledFontSize}px "PreviewFont"`;
          const metrics = ctx.measureText(text);
          const ratio = this.calculateFillRatio(
            metrics.width,
            mid,
            isVertical
          );
          
          if (ratio >= this.data.targetRatio) {
            optimal = mid;
            low = mid + 1;
          } else {
            high = mid - 1;
          }
        } else {
          low = mid + 1;
        }
      }
      
      return optimal;
    },

    calculateFillRatio(textWidth: number, textHeight: number, isVertical: boolean): number {
      const { canvasWidth, canvasHeight } = this.data;
      
      if (isVertical) {
        return Math.min(textHeight / canvasHeight, textWidth / canvasWidth);
      } else {
        return Math.min(textWidth / canvasWidth, textHeight / canvasHeight);
      }
    },

    // 更新字体预览方法
    async updateFontPreview(fontPath: string) {
      
      if (!fontPath) {
        console.error('字体路径为空');
        return;
      }

      let selectedFont: FontItem | undefined;
      for (const category of this.data.fontCategories) {
        selectedFont = category.fonts.find(f => f.path === fontPath);
        if (selectedFont) break;
      }

      if (!selectedFont) {
        console.error('找不到字体:', fontPath);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '字体加载失败',
          icon: 'none'
        });
        return;
      }

      try {
        // 获取字体数据
        const text = this.data.inputText || this.data.defaultPreviewText;
        const previewUrl = this.getFontPreviewUrl(selectedFont, text);

        const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
          wx.request({
            url: previewUrl,
            method: 'GET',
            responseType: 'arraybuffer',
            header: {
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Origin': 'https://font.chinaz.com',
              'Referer': 'https://font.chinaz.com/'
            },
            success: resolve,
            fail: reject
          });
        });


        if (res.statusCode === 200 && res.data) {
          const buffer = res.data as ArrayBuffer;
          const base64 = wx.arrayBufferToBase64(buffer);

          // 加载字体
          await new Promise<void>((resolve, reject) => {
            wx.loadFontFace({
              family: 'PreviewFont',
              source: `data:font/${selectedFont.format};base64,${base64}`,
              success: (result) => {
                resolve();
              },
              fail: (error) => {
                console.error('预览字体加载失败:', error);
                reject(error);
              }
            });
          });

          // 更新预览文字和状态
          await this.setData({
            selectedPreviewText: text,
            isLoading: false
          });

          // 计算最佳字体大小
          const optimalSize = await this.findOptimalFontSize(
            text,
            this.data.maxFontSize
          );

          // 更新字体大小
          const scale = this.data.screenWidth / this.data.canvasWidth;
        
          
          await this.setData({
            fontSize: optimalSize,
            scaledFontSize: optimalSize * scale
          });
          
        }
      } catch (error) {
        console.error('加载字体失败，详细错误:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    }
  }
});

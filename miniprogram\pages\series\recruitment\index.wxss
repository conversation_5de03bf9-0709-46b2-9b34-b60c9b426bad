/* 背景和容器样式优化 */
.content-scroll {
  padding-bottom: 50rpx;
  box-sizing: border-box;
}

/* 加载指示器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(91, 157, 243, 0.2);
  border-top: 4rpx solid #3C7FD7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #718096;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recruitment-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px) saturate(1.8);
  -webkit-backdrop-filter: blur(25px) saturate(1.8);
  border-radius: 0 32rpx 0 32rpx;
  margin: 32rpx 16rpx;
  padding: 20rpx 36rpx 60rpx 36rpx;
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.04),
    0 12rpx 32rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 即将截止状态 - 7天内 */
.recruitment-card.closing {
  background: linear-gradient(145deg, rgba(255, 244, 222, 0.95), rgba(255, 250, 240, 0.95));
  border: 1px solid rgba(255, 159, 64, 0.15);
  box-shadow: 
    0 4rpx 24rpx rgba(255, 159, 64, 0.08),
    0 12rpx 32rpx rgba(255, 159, 64, 0.04),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
}

/* 已结束状态 */
.recruitment-card.expired {
  background: linear-gradient(145deg, rgba(245, 245, 245, 0.95), rgba(250, 250, 250, 0.95));
  border: 1px solid rgba(160, 174, 192, 0.2);
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.03),
    0 12rpx 32rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  opacity: 0.8;
}

.recruitment-card:active {
  transform: translateY(2rpx) scale(0.985);
  box-shadow: 
    0 2rpx 12rpx rgba(0, 0, 0, 0.03),
    0 8rpx 24rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);
}

.card-number {
  position: absolute;
  top: 0;
  left: 0;
  width: 50rpx;
  height: 50rpx;
  background: linear-gradient(135deg, #9dc5fa, #518fe0);
  color: #fff;
  border-radius:0 0 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  /* box-shadow: 
    0 6rpx 16rpx rgba(91, 157, 243, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4); */
  /* border: 3rpx solid rgba(255, 255, 255, 0.95); */
  /* animation: pulse 2.5s infinite cubic-bezier(0.215, 0.61, 0.355, 1); */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* 序号状态样式 */
.card-number.closing {
  background: linear-gradient(135deg, #FFB347, #FF8C00);
  box-shadow: 
    0 6rpx 16rpx rgba(255, 159, 64, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.9);
}

.card-number.ended {
  background: linear-gradient(135deg, #A0AEC0, #718096);
  box-shadow: 
    0 6rpx 16rpx rgba(113, 128, 150, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.9);
  opacity: 0.8;
}
.card-number.applied {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.2), inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.9;
}
@keyframes pulse {
  0% {
    box-shadow: 
      0 6rpx 16rpx rgba(91, 157, 243, 0.2),
      inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4),
      0 0 0 0 rgba(91, 157, 243, 0.3);
  }
  70% {
    box-shadow: 
      0 6rpx 16rpx rgba(91, 157, 243, 0.2),
      inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4),
      0 0 0 12rpx rgba(91, 157, 243, 0);
  }
  100% {
    box-shadow: 
      0 6rpx 16rpx rgba(91, 157, 243, 0.2),
      inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4),
      0 0 0 0 rgba(91, 157, 243, 0);
  }
}

.card-title {
  padding-left: 44rpx;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 28rpx;
  line-height: 1.4;
  background: linear-gradient(135deg, #2C3E50, #3498DB);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  letter-spacing: 0.5rpx;
}

.card-info {
  margin-bottom: 32rpx;
  background: linear-gradient(145deg, #F8FAFD, #FFFFFF);
  padding: 28rpx;
  border-radius: 24rpx;
  border: 1px solid rgba(91, 157, 243, 0.12);
  box-shadow: 
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.9),
    0 2rpx 8rpx rgba(91, 157, 243, 0.06);
}

.info-item {
  font-size: 28rpx;
  color: #4A5568;
  margin: 8rpx 0;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  padding: 0 10rpx;
}

.info-item:hover {
  transform: translateX(6rpx);
  color: #2D3748;
}

.label {
  color: #718096;
  min-width: 144rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.card-action {
  position: absolute;
  right: 0;
  bottom: 0;
  text-align: right;
  margin-top: 24rpx;
}

.detail-btn {
  width: 100rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14rpx 18rpx;
  background: linear-gradient(135deg, #5B9DF3, #3C7FD7);
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 44rpx 0 0 0;
  border: none;
  box-shadow: 
    0 8rpx 24rpx rgba(91, 157, 243, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 1rpx;
}

.detail-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0.08)
  );
  transform: rotate(45deg);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  opacity: 0;
}

.detail-btn:active {
  transform: scale(0.97);
  box-shadow: 
    0 4rpx 12rpx rgba(91, 157, 243, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #4A90E2, #357ABD);
}

.detail-btn:active::after {
  opacity: 1;
  transform: rotate(45deg) scale(1.5);
}

/* 状态文本样式 */
.status-text {
  margin-top: 16rpx !important;
  padding-top: 16rpx !important;
  border-top: 1px dashed rgba(91, 157, 243, 0.1);
}

.status-text.closing text:not(.label) {
  color: #FF8C00;
  font-weight: 600;
}

.status-text.ended text:not(.label) {
  color: #718096;
}

/* 按钮状态样式 */
.detail-btn.closing {
  background: linear-gradient(135deg, #FFB347, #FF8C00);
  box-shadow: 
    0 8rpx 24rpx rgba(255, 159, 64, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

.detail-btn.closing:active {
  background: linear-gradient(135deg, #FF8C00, #FF7000);
  box-shadow: 
    0 4rpx 12rpx rgba(255, 159, 64, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

.detail-btn.ended {
  background: linear-gradient(135deg, #CBD5E0, #A0AEC0);
  box-shadow: 
    0 8rpx 24rpx rgba(160, 174, 192, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.8;
  cursor: not-allowed;
}

.detail-btn.ended:active {
  transform: none;
  box-shadow: 
    0 8rpx 24rpx rgba(160, 174, 192, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

/* 已申请状态 */
.detail-btn.applied {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  box-shadow: 
    0 8rpx 24rpx rgba(76, 175, 80, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.9;
}

.detail-btn.applied:active {
  background: linear-gradient(135deg, #388E3C, #2E7D32);
  box-shadow: 
    0 4rpx 12rpx rgba(76, 175, 80, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

/* 详情浮层优化 */
.detail-popup {
  /* position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0; */
  background: rgba(255, 255, 255, 1);
  /* backdrop-filter: blur(6px) saturate(1.8);
  -webkit-backdrop-filter: blur(6px) saturate(1.8); */
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  /* transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1); */
}

.detail-popup.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  /* position: fixed;
  bottom: 0;
  left: 0;
  right: 0; */
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px) saturate(1.8);
  -webkit-backdrop-filter: blur(25px) saturate(1.8);
  /* border-radius: 40rpx 40rpx 0 0; */
  padding: 48rpx 36rpx;
  transform: translateY(100%);
  transition: transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  /* display: flex; */
  flex-direction: column;
  box-shadow: 
    0 -8rpx 36rpx rgba(0, 0, 0, 0.08),
    0 -2rpx 8rpx rgba(0, 0, 0, 0.03);
  /* max-height: 80vh; */
  border-top: 1px solid rgba(255, 255, 255, 0.9);
}

.detail-popup.show .popup-content {
  transform: translateY(0);
}

.popup-close {
  position: absolute;
  top: 28rpx;
  right: 28rpx;
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #718096;
  background: rgba(245, 247, 250, 0.9);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  box-shadow: 
    0 2rpx 8rpx rgba(0, 0, 0, 0.05),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.popup-close:active {
  background: #EDF2F7;
  transform: scale(0.95);
  color: #4A5568;
}

.popup-title {
  font-size: 44rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #2C3E50, #3498DB);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 40rpx;
  padding-right: 88rpx;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}

.popup-scroll {
  flex: 1;
  overflow-y: auto;
  margin: 0 -36rpx;
  padding: 0 36rpx;
}

.popup-scroll::-webkit-scrollbar {
  display: none;
}

.detail-section {
  margin-bottom: 20rpx;
  background: linear-gradient(145deg, #F8FAFD, #FFFFFF);
  padding: 16rpx;
  border-radius: 28rpx;
  position: relative;
  border: 1px solid rgba(91, 157, 243, 0.12);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  box-shadow: 
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.9),
    0 2rpx 8rpx rgba(91, 157, 243, 0.06);
}

.detail-section:hover {
  transform: translateY(-2rpx);
  box-shadow: 
    0 8rpx 24rpx rgba(91, 157, 243, 0.08),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.9);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 28rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 28rpx;
  background: linear-gradient(135deg, #2C3E50, #3498DB);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5rpx;
}

.section-content {
  font-size: 28rpx;
  color: #4A5568;
  line-height: 1.8;
  letter-spacing: 0.5rpx;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: #FFFFFF;
  color: #3C7FD7;
  border-radius: 32rpx;
  margin: 12rpx 20rpx 12rpx 0;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  border: 2rpx solid rgba(91, 157, 243, 0.2);
  box-shadow: 
    0 2rpx 8rpx rgba(91, 157, 243, 0.06),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}

.tag::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(91, 157, 243, 0.12),
    rgba(91, 157, 243, 0.06)
  );
  transform: rotate(45deg);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  opacity: 0;
}

.tag:active {
  background: #F8FAFD;
  transform: scale(0.97);
  color: #2D3748;
}

.tag:active::after {
  opacity: 1;
  transform: rotate(45deg) scale(1.5);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  background: #FFFFFF;
  padding: 28rpx;
  border-radius: 20rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.9);
}

.info-grid .info-item {
  position: relative;
  background: #F8FAFD;
  border-radius: 16rpx;
  margin: 0;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  border: 1px solid rgba(91, 157, 243, 0.08);
}

.info-grid .info-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(91, 157, 243, 0.08);
}

.info-grid .label {
  color: #718096;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  min-width: auto;
  letter-spacing: 0.5rpx;
}

.info-grid .info-item text:not(.label) {
  color: #2D3748;
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.popup-footer {
  position: relative;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px) saturate(1.8);
  -webkit-backdrop-filter: blur(25px) saturate(1.8);
  border-top: 1px solid rgba(91, 157, 243, 0.08);
}

.popup-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 15%;
  right: 15%;
  height: 2rpx;
  background: linear-gradient(to right, 
    transparent, 
    rgba(91, 157, 243, 0.2), 
    rgba(91, 157, 243, 0.3), 
    rgba(91, 157, 243, 0.2), 
    transparent
  );
}

.apply-btn {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #5B9DF3, #3C7FD7, #2C5C8F);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 48rpx;
  border: none;
  box-shadow: 
    0 8rpx 24rpx rgba(91, 157, 243, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 4rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.apply-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  border-radius: 48rpx 48rpx 0 0;
}

.apply-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.apply-btn:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 
    0 4rpx 12rpx rgba(91, 157, 243, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #4A90E2, #357ABD, #2C5C8F);
}

.apply-btn:active::after {
  left: 100%;
}

/* 已申请按钮状态 */
.apply-btn.applied:not(.ended):not(.notStarted) {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  box-shadow: 
    0 8rpx 24rpx rgba(76, 175, 80, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.9;
}

.apply-btn.applied:not(.ended):not(.notStarted):active {
  background: linear-gradient(135deg, #388E3C, #2E7D32);
  box-shadow: 
    0 4rpx 12rpx rgba(76, 175, 80, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

/* 申请覆盖层样式 */
.application-overlay {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.4));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.application-message {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
  letter-spacing: 4rpx;
  background: rgba(255,255,255,0.15);
  border-radius: 8rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
}

/* 已禁用的卡片内容 */
.card-content.disabled {
  position: relative;
  pointer-events: none;
}

/* 已申请按钮状态 */
.apply-btn.applied:not(.ended):not(.notStarted) {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  box-shadow: 
    0 8rpx 24rpx rgba(76, 175, 80, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.9;
}

.apply-btn.applied:not(.ended):not(.notStarted):active {
  background: linear-gradient(135deg, #388E3C, #2E7D32);
  box-shadow: 
    0 4rpx 12rpx rgba(76, 175, 80, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

.apply-btn.disabled {
  background: linear-gradient(135deg, #CBD5E0, #A0AEC0, #718096);
  box-shadow: 
    0 8rpx 24rpx rgba(160, 174, 192, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  opacity: 0.8;
  pointer-events: none;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  padding: 24rpx 0;
  margin-top: 10rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #5B9DF3, #3C7FD7);
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx 36rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 
    0 8rpx 16rpx rgba(91, 157, 243, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  overflow: hidden;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  border: none;
  width: auto;
  min-width: 350rpx;
}

.refresh-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  border-radius: 40rpx 40rpx 0 0;
}

.refresh-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  border: none;
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.refresh-btn:active {
  transform: scale(0.97) translateY(2rpx);
  box-shadow: 
    0 4rpx 8rpx rgba(91, 157, 243, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

.refresh-btn:active::after {
  left: 100%;
}

.refresh-btn.active {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  box-shadow: 
    0 8rpx 16rpx rgba(76, 175, 80, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

.refresh-btn.active:active {
  background: linear-gradient(135deg, #388E3C, #2E7D32);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  margin-left: 8rpx;
}

/* 添加未开始状态的样式 */
.card-number.notStarted {
  background: linear-gradient(135deg, #9CA3AF, #6B7280);
  box-shadow: 
    0 6rpx 16rpx rgba(107, 114, 128, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.9);
}

/* 未开始的卡片样式 */
.recruitment-card.notStarted {
  background: linear-gradient(145deg, rgba(240, 240, 245, 0.95), rgba(245, 245, 250, 0.95));
  border: 1px solid rgba(107, 114, 128, 0.2);
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.03),
    0 12rpx 32rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  opacity: 0.9;
}

/* 未开始的状态文本样式 */
.status-text.notStarted text:not(.label) {
  color: #6B7280;
  font-weight: 500;
}

/* 未开始的按钮样式 */
.detail-btn.notStarted {
  background: linear-gradient(135deg, #9CA3AF, #6B7280);
  box-shadow: 
    0 8rpx 24rpx rgba(107, 114, 128, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  opacity: 0.8;
  cursor: not-allowed;
}

.detail-btn.notStarted:active {
  transform: none;
  box-shadow: 
    0 8rpx 24rpx rgba(107, 114, 128, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

/* 未开始状态的卡片内容 */
.card-content.not-started {
  position: relative;
  pointer-events: none;
}

/* 未开始状态的覆盖层样式 */
.not-started-overlay {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.3), rgba(107, 114, 128, 0.4));
}

/* 未开始状态的申请按钮 */
.apply-btn.not-started {
  background: linear-gradient(135deg, #9CA3AF, #6B7280, #4B5563);
  box-shadow: 
    0 8rpx 24rpx rgba(107, 114, 128, 0.25),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  opacity: 0.8;
}

.apply-btn.not-started:active {
  background: linear-gradient(135deg, #6B7280, #4B5563, #374151);
  box-shadow: 
    0 4rpx 12rpx rgba(107, 114, 128, 0.2),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 筛选选项卡样式 */
.filter-tabs {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px) saturate(1.8);
  -webkit-backdrop-filter: blur(10px) saturate(1.8);
  border-radius: 16rpx;
  margin: 20rpx 16rpx 30rpx;
  padding: 12rpx;
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.03),
    0 1rpx 4rpx rgba(0, 0, 0, 0.01),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: sticky;
  top: 0;
  z-index: 10;
  margin-top: 10rpx;
  animation: slideDown 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-origin: top center;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  white-space: nowrap;
  padding: 16rpx 28rpx;
  font-size: 28rpx;
  color: #718096;
  border-radius: 8rpx;
  margin: 0 8rpx;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  font-weight: 500;
}

.filter-tab.active {
  color: #3C7FD7;
  background: rgba(91, 157, 243, 0.1);
  box-shadow: 
    inset 0 1rpx 2rpx rgba(91, 157, 243, 0.1),
    0 2rpx 4rpx rgba(255, 255, 255, 0.9);
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 6rpx;
  left: 28rpx;
  right: 28rpx;
  height: 4rpx;
  background: linear-gradient(to right, #5B9DF3, #3C7FD7);
  border-radius: 4rpx;
  animation: appear 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@keyframes appear {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

.filter-tab:active {
  transform: scale(0.96);
  background: rgba(91, 157, 243, 0.05);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 80rpx auto;
  padding: 40rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
  /* opacity: 0.7; */
}

.empty-text {
  font-size: 30rpx;
  color: #A0AEC0;
  text-align: center;
  font-weight: 500;
  letter-spacing: 2rpx;
}

@keyframes slideDown {
  from {
    transform: scaleY(0.8);
    opacity: 0;
  }
  to {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* URL参数显示样式 */
.url-params-container {
  margin: 30rpx 16rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(91, 157, 243, 0.1);
}

.url-params-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #3C7FD7;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px dashed rgba(91, 157, 243, 0.2);
}

.url-params-content {
  padding: 8rpx 0;
}

.param-item {
  display: flex;
  padding: 12rpx 0;
  font-size: 28rpx;
}

.param-key {
  color: #4A5568;
  font-weight: 500;
  margin-right: 16rpx;
}

.param-value {
  color: #2D3748;
  word-break: break-all;
}
/* 状态边框定义 */
.status-border-green {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #4CAF50, #388E3C) border-box;
}

.status-border-blue {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #5B9DF3, #3C7FD7) border-box;
}

.status-border-gray {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #CBD5E0, #A0AEC0) border-box;
}

.detail-action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.share-button {
  background-color: #f0f0f0;
  color: #333;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  height: 80rpx;
  border-radius: 40rpx;
  flex: 0 0 auto;
  padding: 0 30rpx;
}

.share-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg t='1623312836381' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2367'%3E%3Cpath d='M752 64c88 0 160 72 160 160s-72 160-160 160c-52.8 0-99.2-25.6-128-65.6l-329.6 164.8c0 6.4 1.6 12.8 1.6 20.8 0 8-1.6 14.4-1.6 22.4l329.6 164.8c28.8-40 75.2-65.6 128-65.6 88 0 160 72 160 160s-72 160-160 160c-88 0-160-72-160-160 0-8 1.6-14.4 1.6-22.4l-329.6-164.8c-28.8 40-75.2 65.6-128 65.6-88 0-160-72-160-160s72-160 160-160c52.8 0 99.2 25.6 128 65.6l329.6-164.8c-1.6-8-1.6-14.4-1.6-22.4 0-88 72-160 160-160z' p-id='2368'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
}

.apply-button {
  flex: 1;
  background-color: #0080ff;
  color: #ffffff;
  font-size: 28rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.apply-button[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}




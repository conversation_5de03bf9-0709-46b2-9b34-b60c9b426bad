{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nmodule.exports = (to, from) => {\n\t// TODO: use `Reflect.ownKeys()` when targeting Node.js 6\n\tfor (const prop of Object.getOwnPropertyNames(from).concat(Object.getOwnPropertySymbols(from))) {\n\t\tObject.defineProperty(to, prop, Object.getOwnPropertyDescriptor(from, prop));\n\t}\n\n\treturn to;\n};\n"]}
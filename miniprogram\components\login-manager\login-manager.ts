import eventBus from '../../utils/eventBus';
import Api from '../../utils/api';

Component({
  data: {
    loginModalVisible: false,
    currentCallback: null,
    currentSource: '',
    isChecking: false,
    isLogin: false
  },
  
  lifetimes: {
    attached() {
      // 监听登录弹窗事件
      eventBus.on('loginModalEvent', this.handleLoginModalEvent.bind(this));
      
      // 检查初始登录状态
      this.checkLoginStatus();
    },
    
    detached() {
      eventBus.off('loginModalEvent', this.handleLoginModalEvent.bind(this));
    }
  },
  
  methods: {
    async checkLoginStatus() {
      // 避免重复检查
      if (this.data.isChecking) return;
      
      this.setData({ isChecking: true });
      
      try {
        // 检查本地存储
        const token = wx.getStorageSync('token');
        const userInfo = wx.getStorageSync('userInfo');
        
        if (token && userInfo) {
          // 如果用户没有头像，尝试使用微信头像
          if (!userInfo.avatar) {
            try {
              const wxUserInfo = wx.getStorageSync('wxUserInfo');
              if (wxUserInfo && wxUserInfo.avatarUrl) {
                userInfo.avatar = wxUserInfo.avatarUrl;
                // 更新本地存储
                wx.setStorageSync('userInfo', userInfo);
              }
            } catch (error) {
              console.log('获取微信头像失败:', error);
            }
          }

          // 验证后端登录状态
          const res = await Api.common.logintest();
          if (res && res.code !== 401) {
            this.setData({ isLogin: true });
            // 广播登录状态
            eventBus.emit('loginStatusUpdate', { isLogin: true, userInfo });
            return true;
          }
        }
        
        // 登录失效，清除本地存储
        this.setData({ isLogin: false });
        // 广播登录状态
        eventBus.emit('loginStatusUpdate', { isLogin: false });
        return false;
      } catch (error) {
        console.error('检查登录状态失败:', error);
        this.setData({ isLogin: false });
        // 广播登录状态
        eventBus.emit('loginStatusUpdate', { isLogin: false });
        return false;
      } finally {
        this.setData({ isChecking: false });
      }
    },
    
    handleLoginModalEvent(data: {show: boolean, source?: string, callback?: Function}) {
      if (data.show) {
        // 如果已经显示，不重复显示
        if (this.data.loginModalVisible) return;
        
        this.setData({
          loginModalVisible: true,
          currentSource: data.source || '',
          currentCallback: data.callback || null
        });
      } else {
        this.setData({
          loginModalVisible: false
        });
      }
      
      // 广播登录弹窗状态变化
      eventBus.emit('loginModalVisibilityChange', {
        visible: this.data.loginModalVisible,
        source: this.data.currentSource
      });
    },
    
    handleLoginSuccess(e) {
      const userInfo = e.detail.userInfo;
      
      // 确保userInfo包含头像信息
      if (userInfo && !userInfo.avatar) {
        try {
          const wxUserInfo = wx.getStorageSync('wxUserInfo');
          if (wxUserInfo && wxUserInfo.avatarUrl) {
            userInfo.avatar = wxUserInfo.avatarUrl;
          }
        } catch (error) {
          console.log('获取微信头像失败:', error);
        }
      }
      
      // 更新登录状态
      this.setData({
        isLogin: true,
        loginModalVisible: false
      });
      
      // 执行回调
      if (this.data.currentCallback) {
        this.data.currentCallback(userInfo);
      }
      
      // 广播登录成功事件
      eventBus.emit('loginSuccess', { userInfo });
      
      // 广播登录弹窗已关闭
      eventBus.emit('loginModalVisibilityChange', {
        visible: false,
        source: this.data.currentSource
      });
    },
    
    handleLoginCancel() {
      this.setData({
        loginModalVisible: false
      });
      
      // 广播登录弹窗已关闭
      eventBus.emit('loginModalVisibilityChange', {
        visible: false,
        source: this.data.currentSource
      });
    }
  }
}); 
{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar isWindows = process.platform === 'win32';\nvar trailingSlashRe = isWindows ? /[^:]\\\\$/ : /.\\/$/;\n\n// https://github.com/nodejs/node/blob/3e7a14381497a3b73dda68d05b5130563cdab420/lib/os.js#L25-L43\nmodule.exports = function () {\n\tvar path;\n\n\tif (isWindows) {\n\t\tpath = process.env.TEMP ||\n\t\t\tprocess.env.TMP ||\n\t\t\t(process.env.SystemRoot || process.env.windir) + '\\\\temp';\n\t} else {\n\t\tpath = process.env.TMPDIR ||\n\t\t\tprocess.env.TMP ||\n\t\t\tprocess.env.TEMP ||\n\t\t\t'/tmp';\n\t}\n\n\tif (trailingSlashRe.test(path)) {\n\t\tpath = path.slice(0, -1);\n\t}\n\n\treturn path;\n};\n"]}
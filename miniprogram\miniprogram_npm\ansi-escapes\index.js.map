{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar ESC = '\\u001b[';\nvar x = module.exports;\n\nx.cursorTo = function (x, y) {\n\tif (arguments.length === 0) {\n\t\treturn ESC + 'H';\n\t}\n\n\tif (arguments.length === 1) {\n\t\treturn ESC + (x + 1) + 'G';\n\t}\n\n\treturn ESC + (y + 1) + ';' + (x + 1) + 'H';\n};\n\nx.cursorMove = function (x, y) {\n\tvar ret = '';\n\n\tif (x < 0) {\n\t\tret += ESC + (-x) + 'D';\n\t} else if (x > 0) {\n\t\tret += ESC + x + 'C';\n\t}\n\n\tif (y < 0) {\n\t\tret += ESC + (-y) + 'A';\n\t} else if (y > 0) {\n\t\tret += ESC + y + 'B';\n\t}\n\n\treturn ret;\n};\n\nx.cursorUp = function (count) {\n\treturn ESC + (typeof count === 'number' ? count : 1) + 'A';\n};\n\nx.cursorDown = function (count) {\n\treturn ESC + (typeof count === 'number' ? count : 1) + 'B';\n};\n\nx.cursorForward = function (count) {\n\treturn ESC + (typeof count === 'number' ? count : 1) + 'C';\n};\n\nx.cursorBackward = function (count) {\n\treturn ESC + (typeof count === 'number' ? count : 1) + 'D';\n};\n\nx.cursorLeft = ESC + '1000D';\nx.cursorSavePosition = ESC + 's';\nx.cursorRestorePosition = ESC + 'u';\nx.cursorGetPosition = ESC + '6n';\nx.cursorNextLine = ESC + 'E';\nx.cursorPrevLine = ESC + 'F';\nx.cursorHide = ESC + '?25l';\nx.cursorShow = ESC + '?25h';\n\nx.eraseLines = function (count) {\n\tvar clear = '';\n\n\tfor (var i = 0; i < count; i++) {\n\t\tclear += x.cursorLeft + x.eraseEndLine + (i < count - 1 ? x.cursorUp() : '');\n\t}\n\n\treturn clear;\n};\n\nx.eraseEndLine = ESC + 'K';\nx.eraseStartLine = ESC + '1K';\nx.eraseLine = ESC + '2K';\nx.eraseDown = ESC + 'J';\nx.eraseUp = ESC + '1J';\nx.eraseScreen = ESC + '2J';\nx.scrollUp = ESC + 'S';\nx.scrollDown = ESC + 'T';\n\nx.clearScreen = '\\u001bc';\nx.beep = '\\u0007';\n\nx.image = function (buf, opts) {\n\topts = opts || {};\n\n\tvar ret = '\\u001b]1337;File=inline=1';\n\n\tif (opts.width) {\n\t\tret += ';width=' + opts.width;\n\t}\n\n\tif (opts.height) {\n\t\tret += ';height=' + opts.height;\n\t}\n\n\tif (opts.preserveAspectRatio === false) {\n\t\tret += ';preserveAspectRatio=0';\n\t}\n\n\treturn ret + ':' + buf.toString('base64') + '\\u0007';\n};\n\nx.iTerm = {};\n\nx.iTerm.setCwd = function (cwd) {\n\treturn '\\u001b]50;CurrentDir=' + (cwd || process.cwd()) + '\\u0007';\n};\n"]}
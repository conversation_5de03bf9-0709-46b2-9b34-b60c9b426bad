import { layoutUtil} from '../../utils/layout';
import eventBus from '../../utils/eventBus';
import { API_ENDPOINTS, COMMON_ASSETS } from '../../utils/constants';

// 添加更完整的类型声明
declare namespace WechatMiniprogram {
  interface Wx {
    getPrivacySetting(options: {
      success?: (res: { needAuthorization: boolean, privacyContractName: string }) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    
    openPrivacyContract(options: {
      success?: () => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
  }
  
  // 添加缺失的 CustomEvent 接口
  interface CustomEvent {
    detail: {
      value: any;
      [key: string]: any;
    };
    [key: string]: any;
  }
}

interface IComponentData {
  show: boolean;
  hasUserInfo: boolean;
  userInfo: UserInfo | null;
  openid: string | null;
  tempUserInfo: {
    avatarUrl: string;
    nickName: string;
    avatarBase64: string;
  };
  canLogin: boolean;
  needSetAvatar: boolean;
  needSetNickname: boolean;
  isSettingInfo: boolean;
  modalTitle: string;
  hasAgreedPrivacy: boolean;
  bindUserId: number | null;
  bindSource: string | null;
  defaultAvatar: string;
  [key: string]: any;
}

interface UserInfo {
  avatar: string;
  nickname: string;
  createtime: number;
  expires_in: number;
  expiretime: number;
  id: number;
  mobile: string;
  score: number;
  user_id: number;
  username: string;
}

interface LoginResponse {
  code: number;
  msg: string;
  data: {
    token: string;
    userinfo: UserInfo;
    openid: string;
  };
}

interface LoginSuccessEvent {
  userInfo: {
    avatar: string;
    createtime: number;
    expires_in: number;
    expiretime: number;
    id: number;
    mobile: string;
    nickname: string;
    score: number;
    user_id: number;
  };
}

Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    userId: {
      type: Number,
      value: null
    },
    source: {
      type: String,
      value: ''
    }
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    show: false,
    hasUserInfo: false,
    userInfo: null,
    openid: null,
    tempUserInfo: {
      avatarUrl: '',
      nickName: '',
      avatarBase64: ''
    },
    canLogin: false,
    needSetAvatar: false,
    needSetNickname: false,
    isSettingInfo: false,
    modalTitle: '当前内容需要登录后才能使用',
    hasAgreedPrivacy: false,
    bindUserId: null,
    bindSource: null,
    defaultAvatar: COMMON_ASSETS.DEFAULT_AVATAR
  } as IComponentData,

  lifetimes: {
    attached() {
      // 检查本地存储的登录状态
      const userInfo = wx.getStorageSync('userInfo');
      const openid = wx.getStorageSync('openid');
      
      // 只有当 userInfo 和 openid 都存在时才设置
      if (userInfo && openid) {
        this.setData({
          hasUserInfo: true,
          userInfo,
          openid  // 只在确定有值时设置
        });
      } else {
        this.setData({
          hasUserInfo: false,
          userInfo: null,
          openid: ''  // 使用空字符串代替 undefined
        });
      }
      
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      
      // 只保留这一个事件监听，删除旧的事件监听
      eventBus.on('loginModalEvent', this.handleLoginModal.bind(this));
      
      // 检查隐私协议状态
      if (wx.canIUse('getPrivacySetting')) {
        (wx as any).getPrivacySetting({
          success: (res) => {
            this.setData({
              hasAgreedPrivacy: res.needAuthorization === false
            });
          }
        });
      }
      
      // 检查是否有从 redirect 页面传递的 user_id
      const redirectData = wx.getStorageSync('redirectUserIdData');
      if (redirectData && redirectData.user_id) {
        this.setData({
          bindUserId: redirectData.user_id,
          bindSource: redirectData.source || 'redirect',
          show: true,
          modalTitle: '请重新登录绑定账号'
        });
        // 使用后清除本地存储
        wx.removeStorageSync('redirectUserIdData');
      }
    },

    detached() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      
      // 只保留这一个事件解绑，删除旧的事件解绑
      eventBus.off('loginModalEvent', this.handleLoginModal.bind(this));
    },

    ready: function() {
      // 检查是否有从 redirect 页面传递的 user_id
      const redirectData = wx.getStorageSync('redirectUserIdData');
      if (redirectData && redirectData.user_id) {
        this.setData({
          bindUserId: redirectData.user_id,
          bindSource: redirectData.source || 'redirect',
          show: true,
          modalTitle: '请重新登录绑定账号'
        });
        // 使用后清除本地存储
        wx.removeStorageSync('redirectUserIdData');
      }
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    preventDefault() {
      // 阻止触摸事件
      return;
    },

    // 关闭弹窗
    onClose() {
      this.setData({ 
        show: false,
        isSettingInfo: false,
        modalTitle: '当前内容需要登录后才能使用',
        tempUserInfo: {
          avatarUrl: '',
          nickName: '',
          avatarBase64: ''
        },
        hasAgreedPrivacy: false,
        bindUserId: null,
        bindSource: null
      });
      // 设置登录弹窗状态为未显示
      eventBus.setLoginModalStatus(false);
      this.triggerEvent('close');
    },

    // 选择头像
    async onChooseAvatar(e: any) {
      const { avatarUrl } = e.detail;
      
      try {
        wx.showLoading({ title: '处理中...' });
  
        // 将临时文件转为base64
        const base64 = await new Promise<string>((resolve, reject) => {
          wx.getFileSystemManager().readFile({
            filePath: avatarUrl,
            encoding: 'base64',
            success: res => {
              resolve(res.data as string);
            },
            fail: err => {
              reject(err);
            }
          });
        });
        
        // 缓存微信头像信息，以便后续使用
        try {
          const wxUserInfo = wx.getStorageSync('wxUserInfo') || {};
          wxUserInfo.avatarUrl = avatarUrl;
          wx.setStorageSync('wxUserInfo', wxUserInfo);
        } catch (error) {
          console.log('保存微信头像信息失败:', error);
        }
  
        this.setData({
          'tempUserInfo.avatarUrl': avatarUrl,
          'tempUserInfo.avatarBase64': base64
        });
  
        // 检查是否可以登录
        this.checkCanLogin();
        
        wx.hideLoading();
      } catch (error) {
        console.error('头像处理失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '头像处理失败',
          icon: 'none',
          duration: 2000
        });
      }
    },
  
    // 输入昵称
    onInputNickname(e: any) {
      const nickName = e.detail.value.trim();
      this.setData({
        'tempUserInfo.nickName': nickName
      }, () => {
        this.checkCanLogin();
      });
    },
  
    // 检查是否可以登录
    checkCanLogin() {
      const { tempUserInfo, needSetAvatar, needSetNickname } = this.data;
      let canLogin = true;
      
      if (needSetAvatar && !tempUserInfo.avatarUrl) {
        canLogin = false;
      }
      if (needSetNickname && !tempUserInfo.nickName) {
        canLogin = false;
      }
      
      this.setData({ canLogin });
    },
  
    // 统一的登录处理函数
    async handleLogin() {
      if (!this.data.hasAgreedPrivacy) {
        wx.showToast({
          title: '请先阅读并同意隐私协议',
          icon: 'none'
        });
        return;
      }

      wx.showLoading({ title: '处理中...' });
      if (this.data.isSettingInfo) {
        if (!this.data.canLogin) {
          wx.showToast({
            title: '请完善信息',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        try {
          // 重新登录并上传新信息
          const loginResult = await this.doLogin();
          
          if (loginResult) {
            // 登录成功后，如果有新设置的头像则上传
            if (this.data.needSetAvatar && this.data.tempUserInfo.avatarBase64) {
              try {
                const uploadRes = await this.uploadAvatar(this.data.tempUserInfo.avatarBase64);
                
                // 更新本地存储的用户信息
                const storedUserInfo = wx.getStorageSync('userInfo');
                storedUserInfo.avatarUrl = uploadRes.data.url;
                wx.setStorageSync('userInfo', storedUserInfo);
              } catch (error) {
                console.error('头像上传失败:', error);
              }
            }
            
            // 触发登录成功事件，返回用户信息
            const userInfo = wx.getStorageSync('userInfo');
            const loginSuccessData: LoginSuccessEvent = {
              userInfo: {
                avatar: userInfo.avatar || '',
                createtime: userInfo.createtime,
                expires_in: userInfo.expires_in,
                expiretime: userInfo.expiretime,
                id: userInfo.id,
                mobile: userInfo.mobile || '',
                nickname: userInfo.nickname,
                score: userInfo.score || 0,
                user_id: userInfo.user_id
              }
            };
            
            // 调用 onLoginSuccess 处理回调
            this.onLoginSuccess(loginSuccessData.userInfo);
            
            this.triggerEvent('loginsuccess', loginSuccessData);
            this.onClose();
            wx.hideLoading();
          }
        } catch (error) {
          console.error('登录过程失败:', error);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        // 首次登录
        try {
          const loginResult = await this.doLogin();
          
          if (loginResult) {
            // 如果不需要设置任何信息，直接关闭弹窗
            if (!this.data.needSetAvatar && !this.data.needSetNickname) {
              const userInfo = wx.getStorageSync('userInfo');
              const loginSuccessData: LoginSuccessEvent = {
                userInfo: {
                  avatar: userInfo.avatar || '',
                  createtime: userInfo.createtime,
                  expires_in: userInfo.expires_in,
                  expiretime: userInfo.expiretime,
                  id: userInfo.id,
                  mobile: userInfo.mobile || '',
                  nickname: userInfo.nickname,
                  score: userInfo.score || 0,
                  user_id: userInfo.user_id
                }
              };
              this.triggerEvent('loginsuccess', loginSuccessData);
              this.onClose();
            } else {
              // 需要设置信息，直接跳转到艺术家资料页面
              const userInfo = wx.getStorageSync('userInfo');
              const loginSuccessData: LoginSuccessEvent = {
                userInfo: {
                  avatar: userInfo.avatar || '',
                  createtime: userInfo.createtime,
                  expires_in: userInfo.expires_in,
                  expiretime: userInfo.expiretime,
                  id: userInfo.id,
                  mobile: userInfo.mobile || '',
                  nickname: userInfo.nickname,
                  score: userInfo.score || 0,
                  user_id: userInfo.user_id
                }
              };
              
              this.triggerEvent('loginsuccess', loginSuccessData);
              this.onClose();
              
              // 跳转到艺术家资料页面
              wx.navigateTo({
                url: '/pages/about/artistProfile/index'
              });
            }
          }
        } catch (error) {
          console.error('登录过程失败:', error);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      }
    },
  
    // 执行登录
    async doLogin(): Promise<boolean> {
      return new Promise((resolve, reject) => {
        wx.login({
          success: (loginRes) => {
            if (loginRes.code) {
              // 获取微信头像，尝试从缓存或当前会话中获取
              let wxAvatarUrl = '';
              try {
                // 尝试从缓存中获取微信头像
                const wxUserInfo = wx.getStorageSync('wxUserInfo');
                if (wxUserInfo && wxUserInfo.avatarUrl) {
                  wxAvatarUrl = wxUserInfo.avatarUrl;
                }
              } catch (error) {
                console.log('获取微信头像失败:', error);
              }

              // 准备登录数据
              const loginData: any = {
                code: loginRes.code,
                nickname: this.data.tempUserInfo.nickName || undefined,
                avatar: this.data.tempUserInfo.avatarUrl || wxAvatarUrl || undefined
              };
              
              // 如果有绑定用户ID，添加到请求中
              if (this.data.bindUserId) {
                loginData.user_id = this.data.bindUserId;
              }
              
              // 如果有来源信息，添加到请求中
              if (this.data.bindSource) {
                loginData.source = this.data.bindSource;
              }
              
              wx.request({
                url: API_ENDPOINTS.WX_LOGIN,
                method: 'POST',
                data: loginData,
                success: (response) => {
                  console.log('登录响应:', response.data);
                  const result = response.data as LoginResponse;
                  if (result.code === 1) {
                    // 确保 openid 存在且正确存储
                    if (result.data.openid) {
                      // 如果用户没有头像但有微信头像，尝试使用微信头像
                      if (!result.data.userinfo.avatar) {
                        try {
                          const wxUserInfo = wx.getStorageSync('wxUserInfo');
                          if (wxUserInfo && wxUserInfo.avatarUrl) {
                            result.data.userinfo.avatar = wxUserInfo.avatarUrl;
                          }
                        } catch (error) {
                          console.log('获取微信头像失败:', error);
                        }
                      }

                      wx.setStorageSync('openid', result.data.openid);
                      wx.setStorageSync('token', result.data.token);
                      wx.setStorageSync('userInfo', result.data.userinfo);
                      
                      this.setData({
                        hasUserInfo: true,
                        userInfo: result.data.userinfo,
                        openid: result.data.openid,
                        needSetAvatar: !result.data.userinfo.avatar || 
                                      !result.data.userinfo.avatar.includes('/uploads/avatar/'),
                        needSetNickname: !result.data.userinfo.nickname
                      });
                      
                      console.log('存储的用户信息:', {
                        openid: result.data.openid,
                        userInfo: result.data.userinfo
                      });
                    } else {
                      console.error('登录响应中没有 openid');
                      wx.showToast({
                        title: '登录异常',
                        icon: 'none'
                      });
                      return;
                    }
  
                    // 根据返回的用户信息判断是否需要设置头像和昵称
                    const needSetAvatar = !result.data.userinfo.avatar || 
                                       !result.data.userinfo.avatar.includes('/uploads/avatar/');
                    const needSetNickname = !result.data.userinfo.nickname;
                    
                    this.setData({
                      hasUserInfo: true,
                      userInfo: result.data.userinfo,
                      openid: result.data.openid,
                      needSetAvatar,
                      needSetNickname
                    });
  
                    // 如果不需要设置任何信息，直接关闭弹窗
                    if (!needSetAvatar && !needSetNickname) {
                      const userInfo = wx.getStorageSync('userInfo');
                      const loginSuccessData: LoginSuccessEvent = {
                        userInfo: {
                          avatar: userInfo.avatar || '',
                          createtime: userInfo.createtime,
                          expires_in: userInfo.expires_in,
                          expiretime: userInfo.expiretime,
                          id: userInfo.id,
                          mobile: userInfo.mobile || '',
                          nickname: userInfo.nickname,
                          score: userInfo.score || 0,
                          user_id: userInfo.user_id
                        }
                      };
                      
                      // 调用 onLoginSuccess 处理回调
                      this.onLoginSuccess(loginSuccessData.userInfo);
                      
                      this.triggerEvent('loginsuccess', loginSuccessData);
                      this.onClose();
                    } else {
                      // 需要设置信息，直接跳转到艺术家资料页面
                      const userInfo = wx.getStorageSync('userInfo');
                      const loginSuccessData: LoginSuccessEvent = {
                        userInfo: {
                          avatar: userInfo.avatar || '',
                          createtime: userInfo.createtime,
                          expires_in: userInfo.expires_in,
                          expiretime: userInfo.expiretime,
                          id: userInfo.id,
                          mobile: userInfo.mobile || '',
                          nickname: userInfo.nickname,
                          score: userInfo.score || 0,
                          user_id: userInfo.user_id
                        }
                      };
                      
                      // 调用 onLoginSuccess 处理回调
                      this.onLoginSuccess(loginSuccessData.userInfo);
                      
                      this.triggerEvent('loginsuccess', loginSuccessData);
                      this.onClose();
                      
                      // 跳转到艺术家资料页面
                      wx.navigateTo({
                        url: '/pages/about/artistProfile/index'
                      });
                    }
  
                    wx.showToast({
                      title: '登录成功',
                      icon: 'success',
                      duration: 2000
                    });
                    
                    resolve(true);
                  } else {
                    console.error('登录失败:', result);
                    wx.showToast({
                      title: result.msg || '登录失败',
                      icon: 'none',
                      duration: 2000
                    });
                    resolve(false);
                  }
                },
                fail: (error) => {
                  console.error('登录请求失败:', error);
                  wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                    duration: 2000
                  });
                  reject(error);
                }
              });
            } else {
              wx.showToast({
                title: '获取登录凭证失败',
                icon: 'none',
                duration: 2000
              });
              reject(new Error('获取登录凭证失败'));
            }
          },
          fail: (loginErr) => {
            console.error('wx.login 失败:', loginErr);
            reject(loginErr);
          }
        });
      });
    },
  
    // 上传头像
    async uploadAvatar(base64: string): Promise<{code: number; data: {url: string}}> {
      return new Promise((resolve, reject) => {
        wx.request<{code: number; msg?: string; data: {url: string}}>({
          url: API_ENDPOINTS.UPLOAD_AVATAR,
          method: 'POST',
          data: {
            file: base64,
            type: 'base64'
          },
          header: {
            'content-type': 'application/json',
            'Authorization': wx.getStorageSync('token')
          },
          success: (res) => {
            const data = res.data;
            if (data.code === 1) {
              resolve(data);
            } else {
              reject(new Error(data.msg || '上传失败'));
            }
          },
          fail: (error) => {
            reject(error);
          }
        });
      });
    },
  
    // 处理用户协议点击
    handleUserAgreement() {
      wx.navigateTo({
        url: '/pages/agreement/user'
      });
    },
  
    // 处理隐私政策点击
    handlePrivacyPolicy() {
      wx.navigateTo({
        url: '/pages/agreement/privacy'
      });
    },

    // 处理隐私协议勾选
    onPrivacyCheckboxChange(e: WechatMiniprogram.CustomEvent) {
      const hasAgreed = e.detail.value.length > 0;
      this.setData({ hasAgreedPrivacy: hasAgreed });
    },

    // 显示隐私协议
    handlePrivacyShow() {
      if (wx.canIUse('openPrivacyContract')) {
        (wx as any).openPrivacyContract({
          success: () => {
            console.log('用户查看了隐私协议');
          },
          fail: (err) => {
            console.error('打开隐私协议失败:', err);
          }
        });
      } else {
        wx.showModal({
          title: '提示',
          content: '当前微信版本过低，请升级后再使用',
          showCancel: false
        });
      }
    },

    // 添加新的统一处理 loginModalEvent 的方法
    handleLoginModal(data: {show: boolean, source?: string, callback?: Function}) {
      if (data.show) {
        // 登录弹窗显示逻辑
        eventBus.setLoginModalStatus(true);
        
        this.setData({
          show: true,
          modalTitle: '当前内容需要登录后才能使用',
          // 如果有来源，设置来源
          bindSource: data.source || null
        });
        
        // 存储回调函数，以便登录成功后调用
        if (data.callback) {
          this.loginCallback = data.callback;
        }
      } else {
        // 登录弹窗隐藏逻辑
        eventBus.setLoginModalStatus(false);
        this.setData({ 
          show: false,
          isSettingInfo: false,
          modalTitle: '当前内容需要登录后才能使用',
          tempUserInfo: {
            avatarUrl: '',
            nickName: '',
            avatarBase64: ''
          },
          hasAgreedPrivacy: false,
          bindUserId: null,
          bindSource: null
        });
        this.triggerEvent('close');
      }
    },

    // 登录成功后的处理，触发回调
    onLoginSuccess(userInfo) {
      // 隐藏登录弹窗
      this.setData({
        show: false
      });
      
      // 确保userInfo包含头像信息
      if (userInfo && !userInfo.avatar) {
        try {
          const wxUserInfo = wx.getStorageSync('wxUserInfo');
          if (wxUserInfo && wxUserInfo.avatarUrl) {
            userInfo.avatar = wxUserInfo.avatarUrl;
          }
        } catch (error) {
          console.log('获取微信头像失败:', error);
        }
      }
      
      // 执行回调
      if (this.loginCallback) {
        this.loginCallback(userInfo);
        this.loginCallback = null;
      }
      
      // 关闭弹窗
      this.onClose();
      
      // 广播登录成功事件 - 修正格式为 { userInfo }
      eventBus.emit('loginSuccess', { userInfo });
    },

    // 处理显示登录弹窗事件
    handleShowLoginModal(data: { fromRedirect?: boolean, user_id?: number, source?: string }) {
      // 兼容旧的调用方式
      this.handleLoginModal({
        show: true, 
        source: data.source || 'redirect',
        callback: null
      });
    },

    // 处理隐藏登录浮窗事件
    handleHideLoginModal: function() {
      // 兼容旧的调用方式
      this.handleLoginModal({
        show: false
      });
    }
  }
}); 
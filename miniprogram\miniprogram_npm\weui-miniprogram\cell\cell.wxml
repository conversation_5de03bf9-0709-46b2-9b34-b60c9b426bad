<view bindtap="navigateTo" class="weui-cell {{link ? 'weui-cell_access' : ''}} {{extClass}} {{outerClass}} {{inForm ? ' weui-cell-inform' : ''}}{{inline ? '' : 'weui-cell_label-block'}}" hover-class="{{hover ? 'weui-hover-active' : extHoverClass}}" aria-role="{{ariaRole}}">
    <view wx:if="{{hasHeader}}" class="weui-cell__hd {{iconClass}}">
        <block wx:if="{{icon}}">
            <image src="{{icon}}" class="weui-cell__icon" mode="aspectFit"/>
        </block>
        <block wx:else>
            <slot name="icon"/>
        </block>
        <block wx:if="{{inForm}}">
            <block wx:if="{{title}}"><view class="weui-label">{{title}}</view></block>
            <block wx:else>
                <slot name="title"/>
            </block>
        </block>
        <block wx:else>
            <block wx:if="{{title}}">{{title}}</block>
            <block wx:else>
                <slot name="title"/>
            </block>
        </block>
    </view>
    <view wx:if="{{hasBody}}" class="weui-cell__bd">
        <block wx:if="{{value}}">{{value}}</block>
        <block wx:else>
            <slot/>
        </block>
    </view>
    <view wx:if="{{hasFooter}}" class="weui-cell__ft weui-cell__ft_in-access {{footerClass}}">
        <block wx:if="{{footer}}">{{footer}}</block>
        <block wx:else>
            <slot name="footer"/>
        </block>
        <icon wx:if="{{showError && error}}" type="warn" size="23" color="#E64340"/>
    </view>
</view>


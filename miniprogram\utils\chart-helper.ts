/**
 * 图表数据生成工具类
 * 为各种类型的图表提供数据生成方法
 */

interface SeriesItem {
  name: string;
  data?: number[];
  value?: number;
  color?: string;
  type?: string;
  size?: number;
}

interface ChartData {
  categories?: string[];
  series: SeriesItem[];
  indicators?: string[];
  [key: string]: any;
}

// 雷达图指标配置
interface RadarIndicator {
  text: string;
  max: number;
  color?: string;
}

const ChartHelper = {
  /**
   * 创建柱状图数据
   * @param {string[]} categories 类别标签
   * @param {number[]} values 数值数组
   * @param {string} seriesName 系列名称
   * @returns {ChartData} 图表数据对象
   */
  createColumnData(categories: string[], values: number[], seriesName: string = '数据'): ChartData {
    return {
      categories,
      series: [
        {
          name: seriesName,
          data: values
        }
      ]
    };
  },
  
  /**
   * 创建多系列数据（适用于柱状图、折线图等）
   * @param {string[]} categories 类别标签
   * @param {number[][]} seriesData 二维数组，每个子数组为一个系列的数据
   * @param {string[]} seriesNames 系列名称数组
   * @returns {ChartData} 图表数据对象
   */
  createMultiSeriesData(categories: string[], seriesData: number[][], seriesNames: string[] = []): ChartData {
    const series = seriesData.map((data, index) => {
      return {
        name: seriesNames[index] || `系列${index + 1}`,
        data
      };
    });
    
    return {
      categories,
      series
    };
  },
  
  /**
   * 创建饼图数据
   * @param {string[]} names 饼图各部分名称
   * @param {number[]} values 各部分对应的值
   * @returns {ChartData} 图表数据对象
   */
  createPieData(names: string[], values: number[]): ChartData {
    const series = names.map((name, index) => {
      return {
        name,
        value: values[index] || 0,
        color: this.getRandomColor()
      };
    });
    
    return {
      series
    };
  },
  
  /**
   * 创建雷达图数据
   * @param {string[]} indicators 雷达图各维度名称
   * @param {number[][]} seriesData 二维数组，每个子数组为一个系列的数据
   * @param {string[]} seriesNames 系列名称数组
   * @param {number} maxValue 最大值，用于缩放数据
   * @returns {ChartData} 图表数据对象
   */
  createRadarData(
    indicators: string[], 
    seriesData: number[][], 
    seriesNames: string[] = [],
    maxValue: number = 100
  ): ChartData {
    const series = seriesData.map((data, index) => {
      return {
        name: seriesNames[index] || `系列${index + 1}`,
        data
      };
    });
    
    // 创建雷达图指标配置，每个维度的最大值都设为传入的maxValue
    const radarIndicators = indicators.map(name => ({
      text: name,
      max: maxValue
    }));
    
    return {
      categories: indicators, // 兼容旧版API
      series,
      radarIndicators, // 新增雷达图指标配置
      extra: {
        radar: {
          gridType: 'polygon', // 雷达图网格类型：polygon-多边形，circle-圆形
          gridColor: '#CCCCCC', // 雷达图网格线颜色
          gridCount: 5, // 雷达图网格线数量
          opacity: 0.2, // 数据区域透明度
          labelColor: '#666666', // 标签文字颜色
          max: maxValue, // 雷达图数据最大值
          border: true, // 显示数据区域边框
          borderWidth: 2, // 数据区域边框宽度
          fontSize: 13 // 标签文字大小
        }
      }
    };
  },
  
  /**
   * 创建强化版雷达图数据，支持更详细的配置
   * @param {RadarIndicator[]} indicators 雷达图指标配置，包含文本、最大值和颜色
   * @param {number[][]} seriesData 二维数组，每个子数组为一个系列的数据
   * @param {string[]} seriesNames 系列名称数组
   * @param {object} extraConfig 额外配置项
   * @returns {ChartData} 图表数据对象
   */
  createEnhancedRadarData(
    indicators: RadarIndicator[], 
    seriesData: number[][], 
    seriesNames: string[] = [],
    extraConfig: any = {}
  ): ChartData {
    const series = seriesData.map((data, index) => {
      return {
        name: seriesNames[index] || `系列${index + 1}`,
        data
      };
    });
    
    // 提取指标名称列表
    const indicatorNames = indicators.map(item => item.text);
    
    // 默认配置
    const defaultConfig = {
      gridType: 'polygon', // 雷达图网格类型：polygon-多边形，circle-圆形
      gridColor: '#CCCCCC', // 雷达图网格线颜色
      gridCount: 5, // 雷达图网格线数量
      opacity: 0.2, // 数据区域透明度
      labelColor: '#666666', // 标签文字颜色
      border: true, // 显示数据区域边框
      borderWidth: 2, // 数据区域边框宽度
      fontSize: 13 // 标签文字大小
    };
    
    // 合并配置
    const radarConfig = {...defaultConfig, ...extraConfig};
    
    return {
      categories: indicatorNames, // 兼容旧版API
      series,
      radarIndicators: indicators, // 详细的雷达图指标配置
      extra: {
        radar: radarConfig
      }
    };
  },
  
  /**
   * 创建仪表盘数据
   * @param {number} value 仪表盘值(0-1)
   * @param {string} name 仪表盘名称
   * @returns {ChartData} 图表数据对象
   */
  createGaugeData(value: number, name: string = '完成率'): ChartData {
    return {
      series: [
        {
          name,
          value,
          color: value < 0.4 ? '#FF5E5E' : value < 0.6 ? '#FFB93B' : '#5AD8A6'
        }
      ]
    };
  },
  
  /**
   * 创建漏斗图数据
   * @param {string[]} names 漏斗图各层名称
   * @param {number[]} values 各层对应的值
   * @returns {ChartData} 图表数据对象
   */
  createFunnelData(names: string[], values: number[]): ChartData {
    const series = names.map((name, index) => {
      return {
        name,
        value: values[index] || 0,
        color: this.getRandomColor()
      };
    });
    
    return {
      series
    };
  },
  
  /**
   * 创建山峰图数据
   * @param {string[]} categories 类别标签
   * @param {number[]} values 数值数组
   * @param {string} seriesName 系列名称
   * @returns {ChartData} 图表数据对象
   */
  createMountData(categories: string[], values: number[], seriesName: string = '数据'): ChartData {
    return {
      categories,
      series: [
        {
          name: seriesName,
          data: values
        }
      ]
    };
  },
  
  /**
   * 创建词云图数据
   * @param {string[]} words 词语数组
   * @param {number[]} values 词语权重值
   * @param {number[]} sizes 字体大小数组
   * @returns {ChartData} 图表数据对象
   */
  createWordCloudData(words: string[], values: number[], sizes: number[]): ChartData {
    const series = words.map((name, index) => {
      return {
        name,
        value: values[index] || 0,
        size: sizes[index] || 12,
        color: this.getRandomColor()
      };
    });
    
    return {
      series
    };
  },
  
  /**
   * 创建混合图表数据 (柱状图+折线图)
   * @param {string[]} categories 类别标签
   * @param {number[]} columnValues 柱状图数值
   * @param {string} columnName 柱状图系列名称
   * @param {number[]} lineValues 折线图数值
   * @param {string} lineName 折线图系列名称
   * @returns {ChartData} 图表数据对象
   */
  createMixData(categories: string[], columnValues: number[], columnName: string, lineValues: number[], lineName: string): ChartData {
    return {
      categories,
      series: [
        {
          name: columnName,
          type: 'column',
          data: columnValues
        },
        {
          name: lineName,
          type: 'line',
          data: lineValues
        }
      ]
    };
  },
  
  /**
   * 生成随机颜色
   * @returns {string} 颜色值
   */
  getRandomColor(): string {
    const colors = [
      '#5AD8A6', '#5B8FF9', '#5D7092', '#64D2FF', '#65789B',
      '#6DC8EC', '#6F5EF9', '#72CCFF', '#737300', '#7B9292',
      '#85DA47', '#8C6AC4', '#9270CA', '#9661BC', '#9D87FB',
      '#F6BD16', '#F6903D', '#FF6B3B', '#FF8C00', '#FF9845'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },
  
  /**
   * 生成一组随机数据
   * @param {number} len 数据长度
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {number[]} 随机数据数组
   */
  generateRandomData(len: number = 5, min: number = 10, max: number = 100): number[] {
    const data: number[] = [];
    for (let i = 0; i < len; i++) {
      data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
  }
};

export default ChartHelper; 
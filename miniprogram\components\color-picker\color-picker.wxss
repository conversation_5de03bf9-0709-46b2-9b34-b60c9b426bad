.color-picker {
  padding: 12rpx;
  border-radius: 12rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  display: inline-block;
  position: relative;
  z-index: 1;
  touch-action: none;
}

.picker-main {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.picker-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.sv-hue-container {
  display: flex;
  gap: 12rpx;
  width: 100%;
}

.sv-picker {
  position: relative;
  width: 100%;
  height: 180rpx;
  border-radius: 8rpx;
  overflow: hidden;
  touch-action: none;
}

.sv-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sv-white {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, #fff, transparent);
  pointer-events: none;
}

.sv-black {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, #000);
  pointer-events: none;
}

.sv-cursor {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 0 1rpx rgba(0, 0, 0, 0.2);
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.hue-picker {
  position: relative;
  width: 60rpx;
  height: 180rpx;
  border-radius: 8rpx;
  background: linear-gradient(to bottom,
    #f00 0%, #ff0 17%, #0f0 33%,
    #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
  touch-action: none;
}

.hue-cursor {
  position: absolute;
  left: -2rpx;
  right: -2rpx;
  height: 6rpx;
  background: #fff;
  border-radius: 3rpx;
  box-shadow: 0 0 0 1rpx rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.alpha-container {
  position: relative;
  width: 100%;
  height: 24rpx;
  margin: 8rpx 0;
  border-radius: 8rpx;
  overflow: hidden;
}

.alpha-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  touch-action: none;
}

.alpha-cursor {
  position: absolute;
  top: 50%;
  width: 20rpx;
  height: 180%;
  background: #fff;
  border: 1rpx solid rgba(0, 0, 0, 0.2);
  border-radius: 3rpx;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.color-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  width: 120rpx;
}

.current-color {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.color-value {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #666;
}

.alpha-value {
  color: #666;
  font-size: 24rpx;
  text-align: center;
}

.preset-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

.preset-colors {
  display: flex;
  gap: 0;
  margin: 12rpx 0;
  padding: 4rpx 8rpx;
  white-space: nowrap;
  overflow-x: auto;
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8rpx;
  height: 52rpx;
  align-items: center;
}

.preset-colors::-webkit-scrollbar {
  display: none;
}

.preset-color {
  position: relative;
  width: 44rpx;
  height: 44rpx;
  flex-shrink: 0;
  border-radius: 6rpx;
  transition: all 0.2s ease;
  cursor: pointer;
  box-sizing: border-box;
  margin: 0 4rpx;
  display: inline-block;
  vertical-align: middle;
}

.preset-color::after {
  content: '';
  position: absolute;
  right: -4rpx;
  top: 6rpx;
  bottom: 6rpx;
  width: 1rpx;
  background: rgba(0, 0, 0, 0.05);
}

.preset-color:last-child::after {
  display: none;
}

.preset-color:active {
  transform: scale(0.9);
}

.preset-color.active {
  position: relative;
  transform: scale(1.15);
  z-index: 1;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.preset-color.active::after {
  display: none;
}

/* 白色预设颜色特殊处理 */
.preset-color .transparency-grid {
  border-radius: 6rpx;
  background-size: 8rpx 8rpx;
  background-position: 0 0, 0 4rpx, 4rpx -4rpx, -4rpx 0;
}

.slider-container {
  margin: 20rpx 0;
}

.slider-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.confirm-button {
  margin-top: 20rpx;
  background: #2575fc;
  color: #fff;
  border-radius: 8rpx;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
}

.selected-color-display {
  margin-top: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  text-align: center;
}

.selected-color-display .text {
  color: #fff;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
  font-size: 28rpx;
}

/* 触发按钮 */
.color-trigger {
  display: inline-flex;
  align-items: center;
  padding: 8rpx;
  background: #fff;
  border-radius: 8rpx;
  border: 2rpx solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-trigger:active {
  opacity: 0.8;
}

/* 颜色预览 */
.color-preview-wrapper {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  border-radius: 6rpx;
  overflow: hidden;
}

.transparency-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
    linear-gradient(-45deg, #ccc 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ccc 75%),
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 12rpx 12rpx;
  background-position: 0 0, 0 6rpx, 6rpx -6rpx, -6rpx 0;
}

.color-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 弹出层 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 400rpx;
  max-width: 90vw;
}

.popup-content.show {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

/* 选择器面板 */
.picker-panel {
  padding: 16rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.popup-footer {
  margin-top: 20rpx;
}

.confirm-btn {
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  font-size: 26rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.confirm-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.section-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  padding-left: 4rpx;
}

@keyframes slideUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 确保弹出的颜色选择面板在最顶层 */
.color-picker-panel {
  z-index: 10001;
}

.color-picker-container {
  width: 100%;
  transition: padding-bottom 0.3s ease;
}

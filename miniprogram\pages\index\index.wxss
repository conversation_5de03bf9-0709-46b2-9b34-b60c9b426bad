.tools-container {
  margin: 20rpx;
  margin-bottom: 30rpx;
}

.tool-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 12rpx 8rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #6a11cb, #2575fc);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tool-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.95);
}

.tool-card:active::before {
  opacity: 1;
}

.tool-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.tool-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

.tool-info {
  flex: 1;
  margin-right: 16rpx;
}

.tool-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.tool-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  display: block;
}

.tool-arrow {
  color: #999;
  font-size: 32rpx;
  margin-left: 16rpx;
}

.tool-intro {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx 30rpx;
  margin: 0 20rpx 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.tool-intro::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6a11cb, #2575fc, #6a11cb);
  background-size: 200% 100%;
  animation: gradientFlow 3s linear infinite;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #2575fc 0%, #6a11cb 100%);
  /* 定义标准属性和兼容性属性 */
  background-clip: text; /* 标准属性 */
  -webkit-background-clip: text; /* 兼容性属性 */
  color: transparent;
  margin-left: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-section {
  margin-bottom: 48rpx;
  animation: fadeInUp 0.6s ease-out;
}

.intro-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.section-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}

.feature-item {
  font-size: 24rpx;
  color: #2575fc;
  background: rgba(37, 117, 252, 0.08);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.feature-item:active {
  background: rgba(37, 117, 252, 0.15);
  transform: scale(0.96);
}

.animate-item {
  opacity: 0;
  transform: translateY(30rpx);
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.tool-card:nth-child(1) { animation-delay: 0.2s; }
.tool-card:nth-child(2) { animation-delay: 0.3s; }
.tool-card:nth-child(3) { animation-delay: 0.4s; }

.tool-intro { animation-delay: 0.3s; }

.feature-item:nth-child(1) { animation-delay: 0.4s; }
.feature-item:nth-child(2) { animation-delay: 0.5s; }
.feature-item:nth-child(3) { animation-delay: 0.6s; }
.feature-item:nth-child(4) { animation-delay: 0.7s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmerIcon {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

@keyframes gradientFlow {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 0%; }
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  filter: blur(2px) brightness(1.1);
  opacity: 0.8;
}

.guide-list {
  margin-top: 24rpx;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 24rpx;
}

.guide-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 30rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.guide-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(37, 117, 252, 0.1);
}

.guide-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.guide-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.tools-used {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tool-tag {
  font-size: 24rpx;
  color: #2575fc;
  background: rgba(37, 117, 252, 0.08);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.tool-tag:active {
  background: rgba(37, 117, 252, 0.15);
  transform: scale(0.96);
}

.tip-list {
  margin-top: 24rpx;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
  border: 1px solid rgba(37, 117, 252, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.tip-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(37, 117, 252, 0.1);
}

.tip-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.intro-section:nth-child(1) { animation-delay: 0.1s; }
.intro-section:nth-child(2) { animation-delay: 0.2s; }
.intro-section:nth-child(3) { animation-delay: 0.3s; }
.intro-section:nth-child(4) { animation-delay: 0.4s; }

.guide-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.guide-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.guide-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.guide-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.tip-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.6s both; }

.container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
}

.category-section {
  margin: 24rpx 20rpx;
  animation: slideIn 0.6s ease-out forwards;
  opacity: 0;
}

.category-section:nth-child(1) { animation-delay: 0.1s; }
.category-section:nth-child(2) { animation-delay: 0.2s; }
.category-section:nth-child(3) { animation-delay: 0.3s; }

.category-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.category-header:active {
  background: rgba(255, 255, 255, 0.98);
  transform: scale(0.98);
}

.category-title {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.category-title .text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.category-arrow {
  width: 20rpx;
  height: 20rpx;
  border-right: 3rpx solid #666;
  border-bottom: 3rpx solid #666;
  transform: rotate(45deg);
  transition: transform 0.3s ease;
  margin-left: 16rpx;
}

.category-arrow.expanded {
  transform: rotate(-135deg) translateY(6rpx);
}

.tools-container {
  height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;
  opacity: 0;
}

.tools-container.expanded {
  height: auto;
  opacity: 1;
  padding-top: 16rpx;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subscribe-btn {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.subscribe-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 订阅弹窗样式 */
.subscribe-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.subscribe-modal.show {
  visibility: visible;
  opacity: 1;
}

.subscribe-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.subscribe-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.subscribe-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
}

.subscribe-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.template-list {
  max-height: 60vh;
  overflow-y: auto;
}

.template-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}

.template-info {
  flex: 1;
  margin-right: 20rpx;
}

.template-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.template-desc {
  font-size: 26rpx;
  color: #666;
}

.template-checkbox {
  width: 40rpx;
  height: 40rpx;
}

.checkbox {
  width: 100%;
  height: 100%;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  position: relative;
}

.checkbox.checked {
  background: #07c160;
  border-color: #07c160;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 8rpx;
  height: 16rpx;
  border: 2rpx solid #fff;
  border-width: 0 2rpx 2rpx 0;
}

.subscribe-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  gap: 20rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #07c160;
  color: #fff;
}

/* 在现有样式基础上添加 */
.time-picker {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
}

.picker-value {
  font-size: 26rpx;
  color: #07c160;
  padding: 8rpx 16rpx;
  background: rgba(7, 193, 96, 0.1);
  border-radius: 8rpx;
}

.picker-value:active {
  opacity: 0.8;
}

.test-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
  color: white;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-top: 20rpx;
}

.test-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.subscribe-tip {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  text-align: center;
}

.video-parser-entry {
  margin: 30rpx;
  margin-top: 150rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 26rpx;
  display: flex;
  flex-direction: column;
}

.video-parser-button {
  display: flex;
  align-items: center;
}

.parser-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.parser-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.parser-desc {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
  margin-left: 80rpx;
}

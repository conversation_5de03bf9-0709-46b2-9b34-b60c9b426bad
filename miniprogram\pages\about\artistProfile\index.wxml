<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="编辑个人资料" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 个人头像和昵称编辑区 -->
      <view class="profile-header">
        <view class="avatar-container">
          <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image class="avatar" src="{{userInfo.avatar_url || constants.COMMON_ASSETS.DEFAULT_AVATAR}}" mode="aspectFill"></image>
            <view class="avatar-edit-hint">
              <mp-icon icon="camera" size="12" color="#ffffff"></mp-icon>
            </view>
          </button>
        </view>
        <view class="user-info">
          <view class="nickname-wrapper">
            <input class="nickname-input" value="{{userInfo.nickname}}" placeholder="请输入昵称" bindinput="handleNicknameInput" />
            <view class="type-tag {{userInfo.is_artist == 1 ? 'artist' : (userInfo.is_artist == 2 ? 'studio' : 'user')}}">
              {{artistTypes[userInfo.is_artist].name}}
            </view>
          </view>
          <!-- 多个签名标签 -->
          <view class="signature-tags">
            <view class="tags-container">
              <view class="tag signature-tag" wx:for="{{signatureList}}" wx:key="index">
                {{item}}
                <text class="tag-delete" data-index="{{index}}" bindtap="handleDeleteSignature">×</text>
              </view>
              <!-- 添加签名按钮放在标签行 -->
              <view class="tag add-signature-tag" bindtap="showAddSignatureInput" wx:if="{{!showSignatureInput}}">
                <mp-icon icon="add" size="16" color="#4a90e2"></mp-icon>
                <text>添加签名</text>
              </view>
            </view>
            <view class="signature-tip">添加签名，可在参与系列时快捷选择</view>
          </view>
          <view class="signature-input-wrapper" wx:if="{{showSignatureInput}}">
            <input class="signature-input" 
                  value="{{newSignature}}" 
                  placeholder="请输入常用签名，可添加多个" 
                  placeholder-style="color: #999; line-height: 80rpx;"
                  placeholder-class="signature-placeholder"
                  cursor-spacing="10"
                  adjust-position="{{true}}"
                  bindinput="handleNewSignatureInput" 
                  focus="{{showSignatureInput}}" 
                  bindblur="hideAddSignatureInput" />
            <view class="signature-input-actions">
              <view class="signature-input-confirm" bindtap="confirmAddSignature">添加</view>
              <view class="signature-input-cancel" bindtap="hideAddSignatureInput">取消</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 基本信息编辑卡片 -->
      <view class="card" style="display: none;">
        <view class="card-header">
          <text class="card-title">邮寄信息</text>
          <text class="card-subtitle">请真实准确填写，以便工作室邮寄</text>
        </view>
        <view class="card-content">
          <view class="form-item">
            <text class="form-label">收件人：</text>
            <input class="form-input" type="text" value="{{userInfo.addname}}" placeholder="请输入收件人" bindinput="handleAddnameInput" />
          </view>
          <view class="form-item">
            <text class="form-label">地址：</text>
            <input class="form-input" type="text" value="{{userInfo.address}}" placeholder="请输入地址" bindinput="handleAddressInput" />
          </view>
          <view class="form-item">
            <text class="form-label">手机号码：</text>
            <input class="form-input" type="number" value="{{userInfo.mobile}}" placeholder="请输入手机号码" bindinput="handleMobileInput" />
          </view>
          <!-- 展开/收起按钮 -->
          <view class="expand-btn" bindtap="toggleMoreInfo">
            <text>{{showMoreInfo ? '收起' : '展开'}}详细信息</text>
            <mp-icon icon="arrow" size="16" color="#666666" class="{{showMoreInfo ? 'arrow-up' : 'arrow-down'}}"></mp-icon>
          </view>
          
          <!-- 可展开的详细信息 -->
          <view class="more-info" wx:if="{{showMoreInfo}}">
            <view class="form-item">
              <text class="form-label">电子邮箱：</text>
              <input class="form-input" type="text" value="{{userInfo.email}}" placeholder="请输入电子邮箱" bindinput="handleEmailInput" />
            </view>
            <view class="form-item">
              <text class="form-label">性别：</text>
              <picker bindchange="handleGenderChange" value="{{userInfo.gender}}" range="{{genderRange}}" range-key="name">
                <view class="picker-view">
                  <text>{{genderRange[userInfo.gender].name}}</text>
                  <mp-icon icon="arrow" size="16" color="#666666"></mp-icon>
                </view>
              </picker>
            </view>
            <view class="form-item">
              <text class="form-label">生日：</text>
              <picker mode="date" value="{{userInfo.birthday}}" start="1900-01-01" end="2050-12-31" bindchange="handleBirthdayChange">
                <view class="picker-view">
                  <text>{{userInfo.birthday || '请选择生日'}}</text>
                  <mp-icon icon="arrow" size="16" color="#666666"></mp-icon>
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- 画风画种编辑卡片 -->
      <view class="card" wx:if="{{userInfo.is_artist > 0}}">
        <view class="card-header">
          <text class="card-title">擅长内容</text>
          <text class="card-subtitle">请仔细选择，将自动推荐适合您的系列</text>
        </view>
        <view class="card-content">
          <view class="form-item">
            <text class="form-label">擅长画风：</text>
            <view class="tags-edit-container">
              <view class="tags-container">
                <view class="tag" wx:for="{{artStylesList}}" wx:key="index">
                  {{item}}
                  <text class="tag-delete" data-index="{{index}}" data-type="style" bindtap="handleDeleteTag">×</text>
                </view>
              </view>
              <view class="add-tag" bindtap="showArtStylePicker">
                <mp-icon icon="add" size="18" color="#666666"></mp-icon>
                <text>添加画风</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">擅长画种：</text>
            <view class="tags-edit-container">
              <view class="tags-container">
                <view class="tag" wx:for="{{artworkTypesList}}" wx:key="index">
                  {{item}}
                  <text class="tag-delete" data-index="{{index}}" data-type="artwork" bindtap="handleDeleteTag">×</text>
                </view>
              </view>
              <view class="add-tag" bindtap="showArtworkTypePicker">
                <mp-icon icon="add" size="18" color="#666666"></mp-icon>
                <text>添加画种</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 合同信息编辑卡片 -->
      <view class="card" wx:if="{{userInfo.is_artist > 0}}">
        <view class="card-header">
          <text class="card-title">签约信息</text>
          <text class="card-subtitle">如已签约，请仔细填写，为您规避风险</text>
        </view>
        <view class="card-content">
          <!-- 展开/收起按钮 -->
          <view class="expand-btn" bindtap="toggleContractInfo">
            <text>{{showContractInfo ? '收起' : '展开'}}合同信息</text>
            <mp-icon icon="arrow" size="16" color="#666666" class="{{showContractInfo ? 'arrow-up' : 'arrow-down'}}"></mp-icon>
          </view>
          <view class="form-item" wx:if="{{showContractInfo}}">
            <text class="form-label">合同状态：</text>
            <picker bindchange="handleContractStatusChange" value="{{contractStatusIndex}}" range="{{contractStatusOptions}}" range-key="name">
              <view class="picker-view">
                <text class="contract-status {{userInfo.contract_status}}">{{contractStatusMap[userInfo.contract_status] || '未签约'}}</text>
                <mp-icon icon="arrow" size="16" color="#666666"></mp-icon>
              </view>
            </picker>
          </view>
          
          
          
          <!-- 可展开的详细信息 -->
          <view class="more-info" wx:if="{{showContractInfo && userInfo.contract_status === 'active'}}">
            <view class="form-item">
              <text class="form-label">开始日期：</text>
              <picker mode="date" value="{{userInfo.contract_start_date}}" start="2020-01-01" end="2050-12-31" bindchange="handleContractStartChange">
                <view class="picker-view">
                  <text>{{userInfo.contract_start_date || '请选择日期'}}</text>
                  <mp-icon icon="arrow" size="16" color="#666666"></mp-icon>
                </view>
              </picker>
            </view>
            <view class="form-item">
              <text class="form-label">结束日期：</text>
              <picker mode="date" value="{{userInfo.contract_end_date}}" start="2020-01-01" end="2050-12-31" bindchange="handleContractEndChange">
                <view class="picker-view">
                  <text>{{userInfo.contract_end_date || '请选择日期'}}</text>
                  <mp-icon icon="arrow" size="16" color="#666666"></mp-icon>
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- 银行账户信息 -->
      <view class="card" wx:if="{{userInfo.is_artist > 0}}" style="display: none;">
        <view class="card-header">
          <text class="card-title">线下结算信息</text>
          <text class="card-subtitle">如无法使用线上结算，请填写以下信息</text>
        </view>
        <view class="card-content">
          <!-- 展开/收起按钮 -->
          <view class="expand-btn" bindtap="toggleBankInfo">
            <text>{{showBankInfo ? '收起' : '展开'}}账户信息</text>
            <mp-icon icon="arrow" size="16" color="#666666" class="{{showBankInfo ? 'arrow-up' : 'arrow-down'}}"></mp-icon>
          </view>
          
          <!-- 可展开的详细信息 -->
          <view class="more-info" wx:if="{{showBankInfo}}">
            <view class="form-item">
              <text class="form-label">开户行：</text>
              <input class="form-input" type="text" value="{{userInfo.bank_name}}" placeholder="请输入开户行" bindinput="handleBankNameInput" />
            </view>
            <view class="form-item">
              <text class="form-label">银行账号：</text>
              <input class="form-input" type="number" value="{{userInfo.bank_account}}" placeholder="请输入银行账号" bindinput="handleBankAccountInput" />
            </view>
            <view class="form-item">
              <text class="form-label">开户人：</text>
              <input class="form-input" type="text" value="{{userInfo.account_holder}}" placeholder="请输入开户人姓名" bindinput="handleAccountHolderInput" />
            </view>
            <view class="form-item">
              <text class="form-label">身份证号：</text>
              <input class="form-input" type="idcard" value="{{userInfo.id_card_number}}" placeholder="请输入身份证号码" bindinput="handleIdCardInput" />
            </view>
          </view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="action-buttons">
        <button class="save-button" bindtap="handleSaveProfile">保存资料</button>
      </view>
      
      <!-- 隐藏的Canvas用于图片处理 -->
      <canvas canvas-id="avatarCanvas" style="width: 200px; height: 200px; position: absolute; left: -1000px; top: -1000px;"></canvas>
      
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="3"><!-- 底部导航 --></tab-bar>
</view>

<!-- 图片裁剪组件 -->
<wxmy-cropper wx:if="{{showCropper}}" 
  src="{{cropperSrc}}" 
  bindcancel="hideCropper" 
  bindconfirm="handleCropperConfirm"
  cropperRatio="1"
  cropperWidth="250"
  cropperShape="circle">
</wxmy-cropper>

<!-- 画风选择弹窗 -->
<view class="picker-popup" wx:if="{{showArtStylePickerPopup}}">
  <view class="picker-mask" bindtap="hideArtStylePicker"></view>
  <view class="picker-content">
    <view class="picker-header">
      <text class="picker-title">选择画风</text>
      <view class="picker-close" bindtap="hideArtStylePicker">×</view>
    </view>
    <view class="picker-body">
      <checkbox-group bindchange="handleArtStyleCheck">
        <view class="checkbox-item" wx:for="{{allArtStyles}}" wx:key="code">
          <checkbox value="{{item.name}}" checked="{{artStylesList.indexOf(item.name) > -1}}" />
          <text>{{item.name}}</text>
        </view>
      </checkbox-group>
    </view>
    <view class="picker-footer">
      <button class="picker-confirm" bindtap="confirmArtStyleSelection">确定</button>
    </view>
  </view>
</view>

<!-- 画种选择弹窗 -->
<view class="picker-popup" wx:if="{{showArtworkTypePickerPopup}}">
  <view class="picker-mask" bindtap="hideArtworkTypePicker"></view>
  <view class="picker-content">
    <view class="picker-header">
      <text class="picker-title">选择画种</text>
      <view class="picker-close" bindtap="hideArtworkTypePicker">×</view>
    </view>
    <view class="picker-body">
      <checkbox-group bindchange="handleArtworkTypeCheck">
        <view class="checkbox-item" wx:for="{{allArtworkTypes}}" wx:key="code">
          <checkbox value="{{item.name}}" checked="{{artworkTypesList.indexOf(item.name) > -1}}" />
          <text>{{item.name}}</text>
        </view>
      </checkbox-group>
    </view>
    <view class="picker-footer">
      <button class="picker-confirm" bindtap="confirmArtworkTypeSelection">确定</button>
    </view>
  </view>
</view>

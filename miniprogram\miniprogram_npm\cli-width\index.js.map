{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nexports = module.exports = cliWidth;\n\nfunction normalizeOpts(options) {\n  var defaultOpts = {\n    defaultWidth: 0,\n    output: process.stdout,\n    tty: require('tty')\n  };\n\n  if (!options) {\n    return defaultOpts;\n  }\n\n  Object.keys(defaultOpts).forEach(function (key) {\n    if (!options[key]) {\n      options[key] = defaultOpts[key];\n    }\n  });\n\n  return options;\n}\n\nfunction cliWidth(options) {\n  var opts = normalizeOpts(options);\n\n  if (opts.output.getWindowSize) {\n    return opts.output.getWindowSize()[0] || opts.defaultWidth;\n  }\n\n  if (opts.tty.getWindowSize) {\n    return opts.tty.getWindowSize()[1] || opts.defaultWidth;\n  }\n\n  if (opts.output.columns) {\n    return opts.output.columns;\n  }\n\n  if (process.env.CLI_WIDTH) {\n    var width = parseInt(process.env.CLI_WIDTH, 10);\n\n    if (!isNaN(width) && width !== 0) {\n      return width;\n    }\n  }\n\n  return opts.defaultWidth;\n};\n"]}
{"version": 3, "sources": ["tmp.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["/*!\n * Tmp\n *\n * Copyright (c) 2011-2017 KARASZI Istvan <<EMAIL>>\n *\n * MIT Licensed\n */\n\n/*\n * Module dependencies.\n */\nconst fs = require('fs');\nconst path = require('path');\nconst crypto = require('crypto');\nconst osTmpDir = require('os-tmpdir');\nconst _c = process.binding('constants');\n\n/*\n * The working inner variables.\n */\nconst\n  /**\n   * The temporary directory.\n   * @type {string}\n   */\n  tmpDir = osTmpDir(),\n\n  // the random characters to choose from\n  RANDOM_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n\n  TEMPLATE_PATTERN = /XXXXXX/,\n\n  DEFAULT_TRIES = 3,\n\n  CREATE_FLAGS = (_c.O_CREAT || _c.fs.O_CREAT) | (_c.O_EXCL || _c.fs.O_EXCL) | (_c.O_RDWR || _c.fs.O_RDWR),\n\n  EBADF = _c.EBADF || _c.os.errno.EBADF,\n  ENOENT = _c.ENOENT || _c.os.errno.ENOENT,\n\n  DIR_MODE = 448 /* 0o700 */,\n  FILE_MODE = 384 /* 0o600 */,\n\n  // this will hold the objects need to be removed on exit\n  _removeObjects = [];\n\nvar\n  _gracefulCleanup = false,\n  _uncaughtException = false;\n\n/**\n * Random name generator based on crypto.\n * Adapted from http://blog.tompawlak.org/how-to-generate-random-values-nodejs-javascript\n *\n * @param {number} howMany\n * @returns {string} the generated random name\n * @private\n */\nfunction _randomChars(howMany) {\n  var\n    value = [],\n    rnd = null;\n\n  // make sure that we do not fail because we ran out of entropy\n  try {\n    rnd = crypto.randomBytes(howMany);\n  } catch (e) {\n    rnd = crypto.pseudoRandomBytes(howMany);\n  }\n\n  for (var i = 0; i < howMany; i++) {\n    value.push(RANDOM_CHARS[rnd[i] % RANDOM_CHARS.length]);\n  }\n\n  return value.join('');\n}\n\n/**\n * Checks whether the `obj` parameter is defined or not.\n *\n * @param {Object} obj\n * @returns {boolean} true if the object is undefined\n * @private\n */\nfunction _isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\n\n/**\n * Parses the function arguments.\n *\n * This function helps to have optional arguments.\n *\n * @param {(Options|Function)} options\n * @param {Function} callback\n * @returns {Array} parsed arguments\n * @private\n */\nfunction _parseArguments(options, callback) {\n  if (typeof options == 'function') {\n    return [callback || {}, options];\n  }\n\n  if (_isUndefined(options)) {\n    return [{}, callback];\n  }\n\n  return [options, callback];\n}\n\n/**\n * Generates a new temporary name.\n *\n * @param {Object} opts\n * @returns {string} the new random name according to opts\n * @private\n */\nfunction _generateTmpName(opts) {\n  if (opts.name) {\n    return path.join(opts.dir || tmpDir, opts.name);\n  }\n\n  // mkstemps like template\n  if (opts.template) {\n    return opts.template.replace(TEMPLATE_PATTERN, _randomChars(6));\n  }\n\n  // prefix and postfix\n  const name = [\n    opts.prefix || 'tmp-',\n    process.pid,\n    _randomChars(12),\n    opts.postfix || ''\n  ].join('');\n\n  return path.join(opts.dir || tmpDir, name);\n}\n\n/**\n * Gets a temporary file name.\n *\n * @param {(Options|tmpNameCallback)} options options or callback\n * @param {?tmpNameCallback} callback the callback function\n */\nfunction tmpName(options, callback) {\n  var\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1],\n    tries = opts.name ? 1 : opts.tries || DEFAULT_TRIES;\n\n  if (isNaN(tries) || tries < 0)\n    return cb(new Error('Invalid tries'));\n\n  if (opts.template && !opts.template.match(TEMPLATE_PATTERN))\n    return cb(new Error('Invalid template provided'));\n\n  (function _getUniqueName() {\n    const name = _generateTmpName(opts);\n\n    // check whether the path exists then retry if needed\n    fs.stat(name, function (err) {\n      if (!err) {\n        if (tries-- > 0) return _getUniqueName();\n\n        return cb(new Error('Could not get a unique tmp filename, max tries reached ' + name));\n      }\n\n      cb(null, name);\n    });\n  }());\n}\n\n/**\n * Synchronous version of tmpName.\n *\n * @param {Object} options\n * @returns {string} the generated random name\n * @throws {Error} if the options are invalid or could not generate a filename\n */\nfunction tmpNameSync(options) {\n  var\n    args = _parseArguments(options),\n    opts = args[0],\n    tries = opts.name ? 1 : opts.tries || DEFAULT_TRIES;\n\n  if (isNaN(tries) || tries < 0)\n    throw new Error('Invalid tries');\n\n  if (opts.template && !opts.template.match(TEMPLATE_PATTERN))\n    throw new Error('Invalid template provided');\n\n  do {\n    const name = _generateTmpName(opts);\n    try {\n      fs.statSync(name);\n    } catch (e) {\n      return name;\n    }\n  } while (tries-- > 0);\n\n  throw new Error('Could not get a unique tmp filename, max tries reached');\n}\n\n/**\n * Creates and opens a temporary file.\n *\n * @param {(Options|fileCallback)} options the config options or the callback function\n * @param {?fileCallback} callback\n */\nfunction file(options, callback) {\n  var\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  opts.postfix = (_isUndefined(opts.postfix)) ? '.tmp' : opts.postfix;\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    if (err) return cb(err);\n\n    // create and open the file\n    fs.open(name, CREATE_FLAGS, opts.mode || FILE_MODE, function _fileCreated(err, fd) {\n      if (err) return cb(err);\n\n      if (opts.discardDescriptor) {\n        return fs.close(fd, function _discardCallback(err) {\n          if (err) {\n            // Low probability, and the file exists, so this could be\n            // ignored.  If it isn't we certainly need to unlink the\n            // file, and if that fails too its error is more\n            // important.\n            try {\n              fs.unlinkSync(name);\n            } catch (e) {\n              if (!isENOENT(e)) {\n                err = e;\n              }\n            }\n            return cb(err);\n          }\n          cb(null, name, undefined, _prepareTmpFileRemoveCallback(name, -1, opts));\n        });\n      }\n      if (opts.detachDescriptor) {\n        return cb(null, name, fd, _prepareTmpFileRemoveCallback(name, -1, opts));\n      }\n      cb(null, name, fd, _prepareTmpFileRemoveCallback(name, fd, opts));\n    });\n  });\n}\n\n/**\n * Synchronous version of file.\n *\n * @param {Options} options\n * @returns {FileSyncObject} object consists of name, fd and removeCallback\n * @throws {Error} if cannot create a file\n */\nfunction fileSync(options) {\n  var\n    args = _parseArguments(options),\n    opts = args[0];\n\n  opts.postfix = opts.postfix || '.tmp';\n\n  const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n  const name = tmpNameSync(opts);\n  var fd = fs.openSync(name, CREATE_FLAGS, opts.mode || FILE_MODE);\n  if (opts.discardDescriptor) {\n    fs.closeSync(fd); \n    fd = undefined;\n  }\n\n  return {\n    name: name,\n    fd: fd,\n    removeCallback: _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts)\n  };\n}\n\n/**\n * Removes files and folders in a directory recursively.\n *\n * @param {string} root\n * @private\n */\nfunction _rmdirRecursiveSync(root) {\n  const dirs = [root];\n\n  do {\n    var\n      dir = dirs.pop(),\n      deferred = false,\n      files = fs.readdirSync(dir);\n\n    for (var i = 0, length = files.length; i < length; i++) {\n      var\n        file = path.join(dir, files[i]),\n        stat = fs.lstatSync(file); // lstat so we don't recurse into symlinked directories\n\n      if (stat.isDirectory()) {\n        if (!deferred) {\n          deferred = true;\n          dirs.push(dir);\n        }\n        dirs.push(file);\n      } else {\n        fs.unlinkSync(file);\n      }\n    }\n\n    if (!deferred) {\n      fs.rmdirSync(dir);\n    }\n  } while (dirs.length !== 0);\n}\n\n/**\n * Creates a temporary directory.\n *\n * @param {(Options|dirCallback)} options the options or the callback function\n * @param {?dirCallback} callback\n */\nfunction dir(options, callback) {\n  var\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    if (err) return cb(err);\n\n    // create the directory\n    fs.mkdir(name, opts.mode || DIR_MODE, function _dirCreated(err) {\n      if (err) return cb(err);\n\n      cb(null, name, _prepareTmpDirRemoveCallback(name, opts));\n    });\n  });\n}\n\n/**\n * Synchronous version of dir.\n *\n * @param {Options} options\n * @returns {DirSyncObject} object consists of name and removeCallback\n * @throws {Error} if it cannot create a directory\n */\nfunction dirSync(options) {\n  var\n    args = _parseArguments(options),\n    opts = args[0];\n\n  const name = tmpNameSync(opts);\n  fs.mkdirSync(name, opts.mode || DIR_MODE);\n\n  return {\n    name: name,\n    removeCallback: _prepareTmpDirRemoveCallback(name, opts)\n  };\n}\n\n/**\n * Prepares the callback for removal of the temporary file.\n *\n * @param {string} name the path of the file\n * @param {number} fd file descriptor\n * @param {Object} opts\n * @returns {fileCallback}\n * @private\n */\nfunction _prepareTmpFileRemoveCallback(name, fd, opts) {\n  const removeCallback = _prepareRemoveCallback(function _removeCallback(fdPath) {\n    try {\n      if (0 <= fdPath[0]) {\n        fs.closeSync(fdPath[0]);\n      }\n    }\n    catch (e) {\n      // under some node/windows related circumstances, a temporary file\n      // may have not be created as expected or the file was already closed\n      // by the user, in which case we will simply ignore the error\n      if (!isEBADF(e) && !isENOENT(e)) {\n        // reraise any unanticipated error\n        throw e;\n      }\n    }\n    try {\n      fs.unlinkSync(fdPath[1]);\n    }\n    catch (e) {\n      if (!isENOENT(e)) {\n        // reraise any unanticipated error\n        throw e;\n      }\n    }\n  }, [fd, name]);\n\n  if (!opts.keep) {\n    _removeObjects.unshift(removeCallback);\n  }\n\n  return removeCallback;\n}\n\n/**\n * Prepares the callback for removal of the temporary directory.\n *\n * @param {string} name\n * @param {Object} opts\n * @returns {Function} the callback\n * @private\n */\nfunction _prepareTmpDirRemoveCallback(name, opts) {\n  const removeFunction = opts.unsafeCleanup ? _rmdirRecursiveSync : fs.rmdirSync.bind(fs);\n  const removeCallback = _prepareRemoveCallback(removeFunction, name);\n\n  if (!opts.keep) {\n    _removeObjects.unshift(removeCallback);\n  }\n\n  return removeCallback;\n}\n\n/**\n * Creates a guarded function wrapping the removeFunction call.\n *\n * @param {Function} removeFunction\n * @param {Object} arg\n * @returns {Function}\n * @private\n */\nfunction _prepareRemoveCallback(removeFunction, arg) {\n  var called = false;\n\n  return function _cleanupCallback(next) {\n    if (!called) {\n      const index = _removeObjects.indexOf(_cleanupCallback);\n      if (index >= 0) {\n        _removeObjects.splice(index, 1);\n      }\n\n      called = true;\n      removeFunction(arg);\n    }\n\n    if (next) next(null);\n  };\n}\n\n/**\n * The garbage collector.\n *\n * @private\n */\nfunction _garbageCollector() {\n  if (_uncaughtException && !_gracefulCleanup) {\n    return;\n  }\n\n  // the function being called removes itself from _removeObjects,\n  // loop until _removeObjects is empty\n  while (_removeObjects.length) {\n    try {\n      _removeObjects[0].call(null);\n    } catch (e) {\n      // already removed?\n    }\n  }\n}\n\n/**\n * Helper for testing against EBADF to compensate changes made to Node 7.x under Windows.\n */\nfunction isEBADF(error) {\n  return isExpectedError(error, -EBADF, 'EBADF');\n}\n\n/**\n * Helper for testing against ENOENT to compensate changes made to Node 7.x under Windows.\n */\nfunction isENOENT(error) {\n  return isExpectedError(error, -ENOENT, 'ENOENT');\n}\n\n/**\n * Helper to determine whether the expected error code matches the actual code and errno,\n * which will differ between the supported node versions.\n *\n * - Node >= 7.0:\n *   error.code {String}\n *   error.errno {String|Number} any numerical value will be negated\n *\n * - Node >= 6.0 < 7.0:\n *   error.code {String}\n *   error.errno {Number} negated\n *\n * - Node >= 4.0 < 6.0: introduces SystemError\n *   error.code {String}\n *   error.errno {Number} negated\n *\n * - Node >= 0.10 < 4.0:\n *   error.code {Number} negated\n *   error.errno n/a\n */\nfunction isExpectedError(error, code, errno) {\n  return error.code == code || error.code == errno;\n}\n\n/**\n * Sets the graceful cleanup.\n *\n * Also removes the created files and directories when an uncaught exception occurs.\n */\nfunction setGracefulCleanup() {\n  _gracefulCleanup = true;\n}\n\nconst version = process.versions.node.split('.').map(function (value) {\n  return parseInt(value, 10);\n});\n\nif (version[0] === 0 && (version[1] < 9 || version[1] === 9 && version[2] < 5)) {\n  process.addListener('uncaughtException', function _uncaughtExceptionThrown(err) {\n    _uncaughtException = true;\n    _garbageCollector();\n\n    throw err;\n  });\n}\n\nprocess.addListener('exit', function _exit(code) {\n  if (code) _uncaughtException = true;\n  _garbageCollector();\n});\n\n/**\n * Configuration options.\n *\n * @typedef {Object} Options\n * @property {?number} tries the number of tries before give up the name generation\n * @property {?string} template the \"mkstemp\" like filename template\n * @property {?string} name fix name\n * @property {?string} dir the tmp directory to use\n * @property {?string} prefix prefix for the generated name\n * @property {?string} postfix postfix for the generated name\n */\n\n/**\n * @typedef {Object} FileSyncObject\n * @property {string} name the name of the file\n * @property {string} fd the file descriptor\n * @property {fileCallback} removeCallback the callback function to remove the file\n */\n\n/**\n * @typedef {Object} DirSyncObject\n * @property {string} name the name of the directory\n * @property {fileCallback} removeCallback the callback function to remove the directory\n */\n\n/**\n * @callback tmpNameCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n */\n\n/**\n * @callback fileCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallback\n * @param {simpleCallback} [next] function to call after entry was removed\n */\n\n/**\n * Callback function for function composition.\n * @see {@link https://github.com/raszi/node-tmp/issues/57|raszi/node-tmp#57}\n *\n * @callback simpleCallback\n */\n\n// exporting all the needed methods\nmodule.exports.tmpdir = tmpDir;\n\nmodule.exports.dir = dir;\nmodule.exports.dirSync = dirSync;\n\nmodule.exports.file = file;\nmodule.exports.fileSync = fileSync;\n\nmodule.exports.tmpName = tmpName;\nmodule.exports.tmpNameSync = tmpNameSync;\n\nmodule.exports.setGracefulCleanup = setGracefulCleanup;\n"]}
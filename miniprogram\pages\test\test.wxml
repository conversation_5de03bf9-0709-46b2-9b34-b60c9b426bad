<wxs src="../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="页面标题" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="test-c">
        <!-- 添加控制登录浮窗的按钮 -->
        <view class="auth-buttons">
          <button class="auth-btn" bindtap="showLoginModal">显示登录浮窗</button>
          <button class="auth-btn auth-btn-hide" bindtap="hideLoginModal">隐藏登录浮窗</button>
        </view>
        <view class="auth-buttons" style="margin-top: 20rpx;">
          <button class="auth-btn auth-btn-params" bindtap="showLoginWithParams">带参数登录</button>
        </view>
        
        <view wx:for="{{50}}" wx:key="index" class="test-item">
        测试内容 {{index + 1}}</view>
      </view>
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 引入登录浮窗组件 -->
<login-modal></login-modal>




/**
 * 工具函数模块
 * 
 * 注意：此模块当前仅在少数文件中使用，主要是index.ts
 * 将来可考虑整合到其他工具文件中
 */

/**
 * 格式化日期时间为 YYYY/MM/DD HH:MM:SS 格式
 * 
 * @param date - 要格式化的日期对象
 * @returns 格式化后的日期时间字符串
 */
export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

/**
 * 将数字格式化为两位，不足两位前面补零
 * 
 * @param n - 要格式化的数字
 * @returns 格式化后的字符串
 */
const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

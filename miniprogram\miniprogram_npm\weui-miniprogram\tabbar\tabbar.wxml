<view class="weui-tabbar {{reactive ? 'weui-tabbar__reactive' : ''}} {{extClass}}" aria-role="tablist">
  <!-- 选中的时候往 weui-tabbar__item 加 class:weui-bar__item_on -->
  <view wx:for="{{list}}" wx:key="index" data-index="{{index}}" bindtap="tabChange" class="weui-tabbar__item {{index === current ? 'weui-bar__item_on' : ''}}" aria-role="tab" aria-labelledby="t{{index}}_title" aria-describedby="t{{index}}_tips" aria-selected="{{index === current}}">
    <view id="t{{index}}_tips" aria-hidden="true" style="position: relative;display:inline-block;">
      <image src="{{current === index ? item.selectedIconPath : item.iconPath}}" class="weui-tabbar__icon"/>
      <mp-badge wx:if="{{item.badge || item.dot}}" content="{{item.badge}}" aria-label="{{item.ariaLabel || ''}}" style="position: absolute;top:-2px;left:calc(100% - 3px)"/>
    </view>
    <view id="t{{index}}_title" aria-hidden="true" class="weui-tabbar__label">{{item.text}}</view>
  </view>
</view>

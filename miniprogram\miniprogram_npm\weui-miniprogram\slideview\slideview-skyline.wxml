<!-- slide-view/slide-view.wxml -->
<view class="weui-slideview weui-movable-view {{icon ? 'weui-slideview_icon' : ''}} {{extClass}}" style="width: 100%;height: 100%;">
    <view bindtouchstart="touchstart" bindtouchmove="touchmove" bindtouchend="touchend" class="weui-slideview__left left" style="width:100%;">
      <slot/>
    </view>
    <view class="weui-slideview__right right">
      <view class="weui-slideview__buttons" style="height:100%;width:100%;" wx:if="{{buttons && buttons.length}}">
        <view wx:for="{{buttons}}" wx:key="index" class="btn weui-slideview__btn__wrp btn-{{index}} {{item.className}}">
          <view bind:tap="buttonTap" data-index="{{index}}" class="weui-slideview__btn {{item.extClass}}" aria-role="button">
            <view wx:if="{{!icon}}">{{item.text}}</view>
            <image class="weui-slideview__btn__icon" wx:else src="{{item.src}}"/>
          </view>
        </view>
      </view>
    </view>
</view>


<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="{{pagetitle}}"><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="gallery-header">
        <!-- 标题背景图 -->
        <view class="header-bg-wrapper">
          <image class="header-bg" src="{{baseUrl}}{{galleryData.series.cover_image}}" mode="aspectFill"></image>
          <view class="header-bg-overlay"></view>
        </view>
        
        <view class="header-content">
          <!-- 左侧信息 -->
          <view class="header-left">
            <view class="gallery-title-wrapper">
              <view class="gallery-title">{{galleryData.gallery.name}}</view>
            </view>
            <!-- <view class="gallery-subtitle">{{galleryData.series.name}}</view> -->
            
            <!-- 主题和类型 -->
            <view class="theme-container">
              <view class="theme-tag">{{galleryData.series.theme}}</view>
            </view>
            
            <!-- 基本信息始终显示 -->
            <view class="selection-info">
              <text class="info-icon">📋</text>
              <text class="info-text">可选：{{galleryData.gallery.min_selections}}-{{galleryData.gallery.max_selections}}张</text>
            </view>
          </view>
          
          <!-- 右侧封面图 -->
          <view class="header-right">
            <!-- <image class="series-cover" src="{{baseUrl}}{{galleryData.studio.logo_url}}" mode="aspectFill"></image> -->
          </view>
        </view>
        
        <!-- 可展开/折叠的部分 -->
        <block wx:if="{{isHeaderExpanded}}">
          <!-- 工作室信息 -->
          <view class="studio-info">
            <text class="info-icon">🏢</text>
            <text class="info-text">工作室：{{galleryData.studio.name}}</text>
          </view>
          
          <!-- 时间信息 -->
          <view class="dates-container" wx:if="{{galleryData.series.dates}}">
            <view class="date-item">
              <text class="date-label">系列时间</text>
              <text class="date-value">{{galleryData.series.dates.series.start}} 至 {{galleryData.series.dates.series.end}}</text>
            </view>
            <view class="date-item">
              <text class="date-label">提交时间</text>
              <text class="date-value">{{galleryData.series.dates.submission.start}} 至 {{galleryData.series.dates.submission.end}}</text>
            </view>
          </view>
          
          <!-- 系列描述 -->
          <view class="series-description" wx:if="{{galleryData.series.description}}">
            <text class="description-text">{{galleryData.series.description}}</text>
            <view class="tags-row">
              <view class="type-tag">{{galleryData.series.creation_type}}</view>
              <view class="type-tag {{galleryData.gallery.selection_mode==0?'red':'green'}}">{{galleryData.gallery.selection_mode==0?'禁止重复选图':'允许重复选图'}}</view>
              <view class="type-tag {{galleryData.gallery.show_selected_artist==1?'green':'red'}}">{{galleryData.gallery.show_selected_artist==1?'允许查看画师':'不允许查看画师'}}</view>
            </view>
          </view>
        </block>
        
        <!-- 展开/折叠按钮 -->
        <view class="header-toggle" bindtap="toggleHeader">
          <text>{{isHeaderExpanded ? '收起' : '展开更多'}}</text>
          <text class="header-toggle-icon {{isHeaderExpanded ? 'expanded' : ''}}">▼</text>
        </view>
      </view>
      
      <!-- 添加加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">正在加载图库数据...</view>
      </view>
      
      <!-- 只在非加载状态且有数据时显示内容 -->
      <view wx:else>
        <view class="image-grid" wx:if="{{galleryData && galleryData.images && galleryData.images.list && galleryData.images.list.length > 0}}">
          <block wx:for="{{galleryData.images.list}}" wx:key="id">
            <view class="photo-card {{item.selected ? 'selected' : ''}} {{(galleryData.gallery.selection_mode == 0 && item.hasOtherSelections) ? 'nos' : ''}}" 
                  data-index="{{index}}" 
                  data-id="{{item.id}}">
              <!-- 图片部分 -->
              <view class="photo-image-container" bindtap="onImageTap" data-index="{{index}}">
                <image class="photo-image" 
                       src="{{baseUrl}}{{item.thumbnail_url}}" 
                       mode="aspectFill" 
                       lazy-load="true"
                       data-index="{{index}}"
                       catchlongpress="preventImageAction"
                        data-url="{{baseUrl}}{{item.url}}"
                       show-menu-by-longpress="{{false}}"></image>
                 
                <!-- 图片遮罩层：显示已被他人选择 -->
                <view class="image-mask" wx:if="{{galleryData.gallery.selection_mode === '0' && item.hasOtherSelections}}" 
                  catchtap="onImageTap" data-index="{{index}}">
                  <text class="mask-text" wx:if="{{galleryData.gallery.show_selected_artist === '1'}}" catchtap="showImageArtistsList" data-index="{{index}}">已被他人选择<text class="mask-hint">(点击查看)</text>
                  </text>
                  <text class="mask-text" wx:else>已被他人选择</text>
                </view>
                
                <!-- 新增：已提交但未选择的图片遮罩 -->
                <view class="image-mask submitted-mask" wx:elif="{{(userHasSubmitted || galleryData.userHasSubmitted) && !item.selected}}" catchtap="showImageArtistsList" data-index="{{index}}">
                  <text class="mask-text">已提交选择
                    无法再次选择
                  </text>
                </view>
                
                <!-- 已选择标记 - 右上角 -->
                <view class="selection-number" wx:if="{{item.selected}}">
                  <text>{{item.selectionIndex}}</text>
                </view>
              </view>
              
              <!-- 白色标签区域 -->
              <view class="photo-info">
                <!-- 媒材类型标签 -->
                <view class="artwork-types">{{item.artwork_types || '未指定媒材'}}</view>
                
                <!-- 右下角选择按钮 -->
                <view class="select-button {{item.selected ? 'active' : ''}} {{(galleryData.gallery.selection_mode == 0 && item.hasOtherSelections) ? 'disabled' : ''}} {{userHasSubmitted || galleryData.userHasSubmitted ? 'disabled' : ''}}" 
                      catchtap="toggleSelection" 
                      data-index="{{index}}">
                  {{item.selected ? '✓' : '+'}}
                </view>
              </view>
            </view>
          </block>
        </view>
        
        <view class="empty-container" wx:else>
          <text class="empty-text">暂无图片数据</text>
        </view>
      </view>
      
      <!-- 页面底部区域 - 浮动版 -->
      <view class="gallery-footer">
        <view class="footer-top">
          <view class="selection-count">
            已选择：<text class="count-num">{{selectedCount}}</text>/{{galleryData.gallery.max_selections}}
          </view>
          
          <view class="artists-list-btn" bindtap="showArtistsList" wx:if="{{galleryData.gallery.show_selected_artist == 1}}">
            <text>画师列表</text>
            <text class="btn-icon">></text>
          </view>
        </view>
        
        <button class="submit-btn" bindtap="submitSelection" 
                disabled="{{selectedCount < galleryData.gallery.min_selections || userHasSubmitted || galleryData.userHasSubmitted}}">
          {{userHasSubmitted || galleryData.userHasSubmitted ? '已提交选择' : '确认选择'}}
        </button>
      </view>
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 画师列表弹窗 -->
<view class="artists-popup" wx:if="{{showArtistsPopup}}" bindtap="hideArtistsList">
  <view class="artists-popup-content" catchtap="preventBubble">
    <view class="popup-header">
      <text class="popup-title">已选择画师列表</text>
      <view class="close-btn" bindtap="hideArtistsList">×</view>
    </view>
    <scroll-view scroll-y class="artists-list-scroll">
      <view class="artists-list">
        <!-- 使用合并后的画师列表 -->
        <block wx:for="{{mergedArtistsList}}" wx:key="artist_id" wx:for-index="artistIndex">
          <view class="artist-item-popup">
            <!-- 添加序号显示 -->
            <view class="artist-index">{{artistIndex + 1}}</view>
            
            <view class="artist-item-content">
              <!-- 画师信息和图片放在同一行 -->
              <view class="artist-row-header">
                <view class="artist-info">
                  <text class="artist-name">{{item.artist_name}}</text>
                  <view class="artist-detail-info">
                    <text class="selection-time">{{item.selection_time}}</text>
                    <!-- 添加状态标签 - 中文显示 -->
                    <text class="status-tag {{item.status === 'cancelled' ? 'cancelled' : (item.status === 'approved' ? 'approved' : 'pending')}}">{{item.status === 'cancelled' ? '已驳回' : (item.status === 'approved' ? '已通过' : '审核中')}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 显示画师选择的所有图像 -->
              <view class="artist-images">
                <block wx:for="{{item.images}}" wx:for-item="image" wx:key="id">
                  <image class="small-image" src="{{baseUrl}}{{image.thumbnail_url || image.url}}" mode="aspectFill"></image>
                </block>
              </view>
            </view>
          </view>
        </block>
        
        <!-- 显示提示，如果没有任何画师选择 -->
        <view class="no-artists" wx:if="{{hasNoArtistSelections}}">
          <text>暂无其他画师选择信息</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 单张图片的画师列表弹窗 -->
<view class="artists-popup" wx:if="{{showImageArtistsPopup}}" bindtap="hideImageArtistsPopup">
  <view class="artists-popup-content" catchtap="preventBubble">
    <view class="popup-header">
      <text class="popup-title">已选择该图片的画师</text>
      <view class="close-btn" bindtap="hideImageArtistsPopup">×</view>
    </view>
    <view class="popup-image-container">
      <image src="{{baseUrl}}{{currentViewingImage.thumbnail_url}}" 
             mode="aspectFit" 
             class="popup-image"
             catchlongpress="preventImageAction"
             show-menu-by-longpress="{{false}}"></image>
    </view>
    <view class="artists-list">
      <!-- 使用预处理的画师列表数据 -->
      <block wx:if="{{currentViewingImage.artistSelections && currentViewingImage.artistSelections.length > 0}}">
        <block wx:for="{{currentViewingImage.artistSelections}}" wx:key="artist_id" wx:for-index="artistIndex">
          <view class="artist-row-with-status">
            <view class="artist-index mini">{{artistIndex + 1}}</view>
            <view class="artist-info-container">
              <text class="artist-name">{{item.artist_name}}</text>
              <view class="artist-detail-row">
                <text class="selection-time">{{item.selection_time}}</text>
                <!-- 添加状态标签 - 中文显示 -->
                <text class="status-tag {{item.status === 'cancelled' ? 'cancelled' : (item.status === 'approved' ? 'approved' : 'pending')}}">{{item.status === 'cancelled' ? '已驳回' : (item.status === 'approved' ? '已通过' : '审核中')}}</text>
              </view>
            </view>
          </view>
        </block>
      </block>
      
      <!-- 没有画师选择的提示 -->
      <view class="no-artists" wx:if="{{!currentViewingImage.artistSelections || currentViewingImage.artistSelections.length === 0}}">
        <text>暂无画师选择该图片</text>
      </view>
    </view>
  </view>
</view>

// 导航项类型
export interface TabItem {
  id: number
  text: string
  icon: string
  url: string
  visible: boolean
  showInHome: boolean
  description?: string
  features?: string[]
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  fontSize: {
    small: string
    normal: string
    large: string
  }
  spacing: {
    small: string
    normal: string
    large: string
  }
}

// 导航配置
export interface NavigationConfig {
  topBar: {
    backgroundColor: string
    titleColor: string
    titleSize: string
    height: string
  }
  tabBar: {
    backgroundColor: string
    borderColor: string
    selectedColor: string
    color: string
    height: string
  }
}

// 图片处理配置
export interface ImageProcessingConfig {
  enabled: boolean
  maxSize: number
  supportFormats: string[]
  cropSettings: {
    defaultAspectRatio: number
    maxWidth: number
    maxHeight: number
    quality: number
  }
  gridLayout: {
    columns: number
    spacing: number
    padding: number
    defaultLayout: string
    maxImages: number
    backgroundColor: string
  }
  watermark: {
    enabled: boolean
    text: string
    position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
    opacity: number
    fontSize: number
    fontColor: string
  }
  compression: {
    enabled: boolean
    quality: number
    maxSize: number
  }
}

// 字体生成配置
export interface FontGenerationConfig {
  enabled: boolean
  maxLength: number
  defaultFontSize: number
  supportedFonts: string[]
  defaultOptions: {
    fontSize: number
    lineHeight: number
    letterSpacing: number
    textAlign: string
    fontFamily: string
  }
  text: {
    defaultSize: number
    defaultColor: string
    defaultFont: string
    alignment: 'left' | 'center' | 'right'
  }
  background: {
    defaultColor: string
    opacity: number
    padding: number
  }
  export: {
    format: 'png' | 'jpg'
    quality: number
  }
  exportOptions: {
    formats: string[]
    defaultFormat: string
    quality: number
  }
  templates: Array<{
    id: number
    name: string
    background: string
    textColor: string
    padding: string
    settings?: Record<string, any>
  }>
}

// 页面配置
export interface PageConfig {
  header: {
    height: number
    backgroundColor: string
    textColor: string
    fontSize: number
  }
  footer: {
    height: number
    backgroundColor: string
    textColor: string
  }
  transitions: {
    duration: number
    type: string
  }
}

// 组件样式配置
export interface ComponentStyleConfig {
  button: {
    primaryColor: string
    secondaryColor: string
    borderRadius: number
    fontSize: number
  }
  input: {
    height: number
    backgroundColor: string
    borderColor: string
    fontSize: number
  }
  list: {
    itemHeight: number
    separatorColor: string
    backgroundColor: string
  }
}

// 工具配置
export interface UtilsConfig {
  imageProcess: {
    cacheEnabled: boolean
    cacheExpiration: number
    maxCacheSize: number
  }
  request: {
    timeout: number
    retryTimes: number
    retryDelay: number
  }
}

// 缓存配置
export interface CacheConfig {
  imageCache: {
    maxSize: number
    expires: number
  }
  configCache: {
    expires: number
  }
  fontCache: {
    maxItems: number
    expires: number
  }
}

// 错误处理配置
export interface ErrorHandlingConfig {
  retryTimes: number
  retryDelay: number
  errorMessages: {
    networkError: string
    serverError: string
    uploadError: string
    downloadError: string
  }
}

// 纸张规格配置
export interface PaperSpecification {
  paper: string
  width: string
  height: string
  unit: string
}

export interface PaperCategory {
  category: string
  specifications: PaperSpecification[]
}

// UI配置
export interface UIConfig {
  animations: {
    pageTransition: boolean
    duration: number
  }
  loading: {
    text: string
    icon: string
  }
  toast: {
    duration: number
    position: string
  }
  dialog: {
    confirmColor: string
    cancelColor: string
  }
}

// 使用场景类型
export interface UsageScenario {
  title: string
  description: string
  tools: string[]
}

// 使用提示类型
export interface Tip {
  title: string
  content: string
}

// 工具介绍类型
export interface ArtToolsIntro {
  title: string
  description: string
  usageScenarios: UsageScenario[]
  tips: Tip[]
}

// 图片工具配置
export interface ImageToolsConfig {
  enabled: boolean
  maxSize: number
  supportFormats: string[]
  cropperOptions: {
    width: number
    height: number
    maxScale: number
    minScale: number
    quality: number
  }
  gridOptions: {
    defaultLayout: string
    maxImages: number
    spacing: number
    backgroundColor: string
  }
  watermarkOptions: {
    defaultText: string
    defaultPosition: string
    fontSize: number
    fontColor: string
    opacity: number
  }
}

// 网格工具配置
export interface GridToolConfig {
  enabled: boolean
  maxGridSize: number
  defaultGridSize: number
  spacing: number
}

// 功能特性配置
export interface FeaturesConfig {
  imageTools: ImageToolsConfig
  gridTool: GridToolConfig
  fontGeneration: FontGenerationConfig
}

// 全局配置接口
export interface WxConfig {
  version: string
  theme: ThemeConfig
  navigation: NavigationConfig
  tabList: TabItem[]
  categoryList: CategoryItem[]
  imageProcessing: ImageProcessingConfig
  fontGeneration: FontGenerationConfig
  pageConfig: PageConfig
  componentStyle: ComponentStyleConfig
  utils: UtilsConfig
  ui: UIConfig
  cache: CacheConfig
  errorHandling: ErrorHandlingConfig
  paperSizeInformation: PaperCategory[]
  artToolsIntro: ArtToolsIntro
  features: FeaturesConfig
}

export interface ToolItem extends TabItem {
  description: string
  features: string[]
}

export interface CategoryItem {
  id: string
  name: string
  icon: string
  tools: ToolItem[]
} 

// 全局配置接口 (用于config-manager.ts)
export interface GlobalConfig {
  version: string
  theme: ThemeConfig
  ui: UIConfig
  cache: CacheConfig
  errorHandling: ErrorHandlingConfig
  features: FeaturesConfig
}
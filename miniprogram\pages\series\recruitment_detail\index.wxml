<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="系列申请" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view id="canvas-container" style="position: fixed; width: 0; height: 0; overflow: hidden; opacity: 0; left: -9999px;">
    <canvas type="2d" id="previewCanvas" style="width: 200px; height: 200px; visibility: hidden;" wx:for="{{formData.portfolio_images}}" wx:key="id" canvas-id="previewCanvas{{index}}"></canvas>
  </view>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="form-container">
        <!-- 招募详情展示 -->
        <view class="recruitment-card" wx:if="{{recruitmentDetail}}">
          <view class="recruitment-card-header">
            <view class="recruitment-card-title">{{recruitmentDetail.title}}</view>
            <view class="recruitment-card-desc">{{recruitmentDetail.description}}</view>
          </view>
          <view class="recruitment-card-grid">
            <view class="recruitment-card-item">
              <view class="recruitment-card-label">限领数量</view>
              <view class="recruitment-card-value">{{recruitmentDetail.min_cards}} - {{recruitmentDetail.max_cards}}张</view>
              <view class="recruitment-card-value" wx:if="{{recruitmentDetail.joined_count > 0}}">{{recruitmentDetail.joined_count}} / {{recruitmentDetail.total_limit==0?'不限人数':recruitmentDetail.total_limit+'人'}}</view>
            </view>
            <view class="recruitment-card-item">
              <view class="recruitment-card-label">结算方式</view>
              <view class="recruitment-card-value">{{recruitmentDetail.settlement_method}}</view>
            </view>
            <view class="recruitment-card-item">
              <view class="recruitment-card-label">报名时间</view>
              <view class="recruitment-card-value">{{recruitmentDetail.recruitment_start_date}} 至 {{recruitmentDetail.recruitment_end_date}}</view>
            </view>
            <view class="recruitment-card-item">
              <view class="recruitment-card-label">单价范围</view>
              <view class="recruitment-card-value">{{recruitmentDetail.price_min}} 至 {{recruitmentDetail.price_max}}</view>
            </view>
          </view>
        </view>
        
        <!-- 作品上传 -->
        <view class="form-group-container">
          <view class="form-group-title required">作品展示</view>
          <view class="form-group-content">
            <view class="upload-container">
              <view class="upload-hint">请上传1-3张您的代表作品</view>
              <view class="upload-grid">
                <view class="upload-card" wx:for="{{formData.portfolio_images}}" wx:key="index">
                  <image src="{{item.url}}" mode="aspectFill" class="upload-card-image" lazy-load="true" binderror="onImageError" data-index="{{index}}" bindload="onImageLoad" webp="false"/>
                  <view class="placeholder-text" wx:if="{{item.loadFailed}}">图片加载失败</view>
                  <view class="upload-delete" bindtap="deleteImage" data-index="{{index}}">×</view>
                </view>
                <view class="upload-card upload-card-add" bindtap="chooseMultipleImages" wx:if="{{formData.portfolio_images.length < 3}}">
                  <text class="upload-add-icon">+</text>
                  <text class="upload-add-text">上传图片({{3 - formData.portfolio_images.length}})</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 申请信息 -->
        <view class="form-group-container">
          <view class="form-group-title required">申请信息</view>
          <view class="form-group-content">
            
            
            <view class="form-row">
              <view class="form-row-label required">笔名</view>
              <view class="form-row-content">
                <input type="text" 
                       class="form-input {{penNameError ? 'error' : ''}}" 
                       value="{{formData.pen_name}}" 
                       bindinput="onInputChange" 
                       bindblur="validatePenName"
                       data-field="pen_name" 
                       placeholder="请输入您的笔名"/>
                <view class="{{penNameError ? 'input-error' : 'input-tip'}}">
                  {{penNameError || '笔名会显示在您的作品上'}}
                </view>
              </view>
            </view>
            <view class="form-row">
              <view class="form-row-label required">申请数量</view>
              <view class="form-row-content">
                <view class="counter-wrapper">
                  <view class="counter-btn minus {{formData.cards_count <= recruitmentDetail.min_cards ? 'disabled' : ''}}" 
                        catchtap="decreaseCardCount">-</view>
                  <input type="number" 
                         class="form-input counter-input {{cardsCountError ? 'error' : ''}}" 
                         value="{{formData.cards_count}}" 
                         bindinput="onInputChange" 
                         bindblur="validateCardCount"
                         data-field="cards_count" 
                         placeholder="{{recruitmentDetail.min_cards}}-{{recruitmentDetail.max_cards}}"
                         min="{{recruitmentDetail.min_cards}}"
                         max="{{recruitmentDetail.max_cards}}"/>
                  <view class="counter-btn plus {{formData.cards_count >= recruitmentDetail.max_cards ? 'disabled' : ''}}" 
                        catchtap="increaseCardCount">+</view>
                </view>
                <view class="{{cardsCountError ? 'input-error' : 'input-tip'}}">
                  {{cardsCountError || '可申请范围: ' + recruitmentDetail.min_cards + ' - ' + recruitmentDetail.max_cards + '张'}}
                </view>
              </view>
            </view>
            <view class="form-row">
              <view class="form-row-label required">可画画种</view>
              <view class="form-row-content" bindtap="showArtworkPicker">
                <view class="form-picker {{artworkTypesError ? 'error' : ''}}">
                  <view class="{{selectedArtworkTypes.length ? 'picker-value' : 'picker-placeholder'}}">
                    {{selectedArtworkTypes.length ? selectedArtworkTypes : '请选择可画画种'}}
                  </view>
                  <text class="form-arrow">></text>
                </view>
                <view class="{{artworkTypesError ? 'input-error' : 'input-tip'}}">
                  {{artworkTypesError || '请选择您擅长的画种类型'}}
                </view>
              </view>
            </view>
            
            <view class="form-row">
              <view class="form-row-label required">擅长画风</view>
              <view class="form-row-content" bindtap="showArtStylePicker">
                <view class="form-picker {{artStylesError ? 'error' : ''}}">
                  <view class="{{selectedArtStyles.length ? 'picker-value' : 'picker-placeholder'}}">
                    {{selectedArtStyles.length ? selectedArtStyles : '请选择擅长画风'}}
                  </view>
                  <text class="form-arrow">></text>
                </view>
                <view class="{{artStylesError ? 'input-error' : 'input-tip'}}">
                  {{artStylesError || '请选择您的创作风格'}}
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 联系信息 -->
        <view class="form-group-container">
          <view class="form-group-title required">联系方式</view>
          <view class="form-group-content">
            <view class="form-row">
              <view class="form-row-label required">邮寄地址</view>
              <view class="form-row-content">
                <input type="text" 
                       class="form-input {{addressError ? 'error' : ''}}" 
                       value="{{formData.shipping_address}}" 
                       bindinput="onInputChange" 
                       bindblur="validateAddress"
                       data-field="shipping_address" 
                       placeholder="请输入邮寄地址"/>
                <view class="{{addressError ? 'input-error' : 'input-tip'}}">
                  {{addressError || '请填写详细地址，确保可以收到作品'}}
                </view>
              </view>
            </view>
            
            <view class="form-row">
              <view class="form-row-label required">联系电话</view>
              <view class="form-row-content">
                <input type="text" 
                       class="form-input {{phoneError ? 'error' : ''}}" 
                       value="{{formData.contact_phone}}" 
                       bindinput="onInputChange" 
                       bindblur="validatePhone"
                       data-field="contact_phone" 
                       placeholder="请输入手机号或固定电话" />
                <view class="{{phoneError ? 'input-error' : 'input-tip'}}">
                  {{phoneError || '支持手机号或固定电话(区号-电话号码)'}}
                </view>
              </view>
            </view>
            
            <view class="form-row">
              <view class="form-row-label">微信号</view>
              <view class="form-row-content">
                <input type="text" 
                       class="form-input" 
                       value="{{formData.contact_wechat}}" 
                       bindinput="onInputChange" 
                       data-field="contact_wechat" 
                       placeholder="请输入微信号（选填）"/>
                <view class="input-tip">方便联系，提高沟通效率</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 备注信息 -->
        <view class="form-group-container">
          <view class="form-group-title">备注说明</view>
          <view class="form-group-content">
            <view class="form-row">
              <view class="form-row-content">
                <textarea class="form-textarea" 
                          value="{{formData.remarks}}" 
                          bindinput="onInputChange" 
                          data-field="remarks" 
                          placeholder="请输入备注说明（选填）" 
                          maxlength="200" />
                <view class="input-tip">还有其他想说的？请在这里补充说明 ({{formData.remarks.length || 0}}/200)</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-button">
          <button class="{{submitting ? 'disabled' : ''}}" bindtap="submitApplication" disabled="{{submitting}}">
            {{submitting ? '提交中...' : '提交申请'}}
          </button>
        </view>
      </view>
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 画种选择弹窗 -->
<view class="picker-popup {{showArtworkPicker ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="picker-mask" bindtap="hideArtworkPicker" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}"></view>
  <view class="picker-panel" style="{{zheight}}">
    <view class="picker-header">
      <view class="picker-title">选择画种</view>
      <view class="picker-close" bindtap="hideArtworkPicker">×</view>
    </view>
    <view class="picker-body">
      <checkbox-group bindchange="onArtworkTypesChange">
        <label class="checkbox-item {{item.checked ? 'checked' : ''}}" 
              wx:for="{{artworkTypes}}" 
              wx:key="id">
          <checkbox value="{{item.id}}" 
                  checked="{{item.checked}}" 
                  color="#4a90e2"/>
          <text class="checkbox-text">{{item.name}}</text>
        </label>
      </checkbox-group>
    </view>
    <view class="picker-footer">
      <button class="picker-btn cancel" bindtap="hideArtworkPicker">取消</button>
      <button class="picker-btn confirm" bindtap="confirmArtworkTypes">确定</button>
    </view>
  </view>
</view>

<!-- 画风选择弹窗 -->
<view class="picker-popup {{showArtStylePicker ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="picker-mask" bindtap="hideArtStylePicker" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}"></view>
  <view class="picker-panel" style="{{zheight}}">
    <view class="picker-header">
      <view class="picker-title">选择画风</view>
      <view class="picker-close" bindtap="hideArtStylePicker">×</view>
    </view>
    <view class="picker-body">
      <checkbox-group bindchange="onArtStylesChange">
        <label class="checkbox-item {{item.checked ? 'checked' : ''}}" 
              wx:for="{{artStyles}}" 
              wx:key="id">
          <checkbox value="{{item.id}}" 
                  checked="{{item.checked}}" 
                  color="#4a90e2"/>
          <text class="checkbox-text">{{item.name}}</text>
        </label>
      </checkbox-group>
    </view>
    <view class="picker-footer">
      <button class="picker-btn cancel" bindtap="hideArtStylePicker">取消</button>
      <button class="picker-btn confirm" bindtap="confirmArtStyles">确定</button>
    </view>
  </view>
</view>

<!-- 登录弹窗 -->
<login-modal show="{{showLoginModal}}" bind:loginsuccess="onLoginSuccess" bind:close="onLoginModalClose"></login-modal>


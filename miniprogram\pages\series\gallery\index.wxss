/* 全局样式 */
.content-scroll {
  padding-bottom: 20px;
}

/* 背景图 */
.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* 美化后的画廊标题区域 */
.gallery-header {
  position: relative;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 标题背景图效果 */
.header-bg-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  overflow: hidden;
  z-index: 0;
}

.header-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(2px);
  transform: scale(1.05);
}

.header-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(142, 166, 247, 1) 0%, rgba(255,255,255,0.95) 100%);
}

.header-content {
  position: relative;
  z-index: 1;
  display: flex;
  margin-bottom: 20rpx;
}

.header-left {
  flex: 1;
  padding-right: 20rpx;
}

.header-right {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  overflow: hidden;
  /* box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15); */
  /* border: 3rpx solid white; */
}

.series-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.gallery-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  margin-right: 12rpx;
  text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.gallery-badge {
  font-size: 20rpx;
  background: #ff5252;
  color: white;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
  font-weight: normal;
}

.gallery-subtitle {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.theme-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.theme-tag, .type-tag {
  display: inline-block;
  padding: 6rpx 14rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.theme-tag {
  background: #e1f5fe;
  color: #0288d1;
  border: 1rpx solid #b3e5fc;
}

.type-tag {
  background: #f3e5f5;
  color: #9c27b0;
  border: 1rpx solid #e1bee7;
}

.type-tag.green {
  background: #e8f5e9;
  color: #4caf50;
  border: 1rpx solid #c8e6c9;
}

.type-tag.red {
  background: #ffebee;
  color: #f44336;
  border: 1rpx solid #ffcdd2;
}


.selection-info, .studio-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  background: rgba(0, 0, 0, 0.03);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.info-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.dates-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 16rpx 0;
  border-top: 1rpx solid #eee;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
}

.date-item {
  min-width: 48%;
  margin-bottom: 10rpx;
  padding: 0 10rpx;
}

.date-label {
  font-size: 22rpx;
  color: #888;
  display: block;
  margin-bottom: 4rpx;
  position: relative;
  padding-left: 16rpx;
}

.date-label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #3498db;
}

.date-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.series-description {
  background: #f9f9f9;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.description-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.tags-row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

/* 添加展开/收起逻辑的样式 */
.header-toggle {
  text-align: center;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #3498db;
  padding: 8rpx 0;
  position: relative;
  z-index: 2;
}

.header-toggle:after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  height: 1rpx;
  background: #e0e0e0;
  z-index: -1;
}

.header-toggle text {
  background: white;
  padding: 0 20rpx;
}

.header-toggle-icon {
  display: inline-block;
  transition: all 0.3s;
  font-size: 20rpx;
  margin-left: 6rpx;
}

.header-toggle-icon.expanded {
  transform: rotate(180deg);
}

.gallery-info {
  display: flex;
  font-size: 24rpx;
  color: #888;
  flex-wrap: wrap;
}

.info-item {
  margin-right: 20rpx;
}

/* 图片网格 - 修改为两列布局 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 10rpx;
  
}

/* 照片卡片样式 - 调整为两列宽度 */
.photo-card {
  width: 48%; /* 调整为48%使一行容纳两张图片，并留有间距 */
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
}
.photo-card .no-selected {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
.photo-card .no-selected .title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  color: white;
  font-size: 24rpx;
  text-align: center;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.photo-card.nos {
  opacity: 0.7;
}
.photo-card.selected {
  border: 2rpx solid #0ab63e;
  box-shadow: 0 6rpx 16rpx rgba(52, 152, 219, 0.3);
  transform: translateY(-2rpx);
}

/* 图片容器 */
.photo-image-container {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.photo-image {
  width: 100%;
  height: 280rpx; /* 调整高度适应两列布局 */
  display: block;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  pointer-events: none;
}

/* 选择数字标记 - 右上角 */
.selection-number {
  position: absolute;
  top: 0;
  left: 0;
  width: 50rpx;
  height: 50rpx;
  background: #0ab63e;
  color: white;
  border-radius: 0 0 50% 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  /* border: 2rpx solid white; */
  z-index: 20;
  opacity: 0.75;
}

/* 白色标签区域 */
.photo-info {
  padding: 12rpx;
  position: relative;
  height: 60rpx;
  display: flex;
  align-items: center;
}

/* 媒材类型显示 */
.artwork-types {
  font-size: 22rpx;
  color: #666;
  width: calc(100% - 60rpx);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右下角选择按钮 */
.select-button {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50% 0 0 0;
  background: #3498db;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
  transition: all 0.2s ease;
  z-index: 9; /* 确保按钮在最上层 */
}

.select-button.disabled {
  background: #ccc;
  pointer-events: none; /* 禁用交互 */
}
.select-button.active {
  /* transform: scale(0.92); */
  background: #0ab63e;
}
/* 添加按钮点击效果 */
.select-button:active {
  transform: scale(0.92);
  background: #2980b9;
}

.unselected-icon, .selected-icon {
  color: white;
  font-size: 30rpx;
  font-weight: bold;
}

/* 已被他人选择遮罩 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.mask-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 30rpx;
  background: rgba(0, 0, 0, 0.4);
}

/* 添加点击查看提示样式 */
.mask-hint {
  display: block;
  font-size: 20rpx;
  font-weight: normal;
  margin-top: 6rpx;
  opacity: 0.8;
}

/* 页脚区域 - 浮动到右侧 */
.gallery-footer {
  position: fixed;
  right: 20rpx;
  top: 110px; /* 底部间距，考虑tabBar高度 */
  width: auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 90;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.footer-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.selection-count {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.count-num {
  color: #3498db;
  font-weight: bold;
  font-size: 32rpx;
}

/* 画师列表按钮 */
.artists-list-btn {
  padding: 10rpx 20rpx;
  background: #f0f4f8;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  color: #666;
  font-size: 24rpx;
  font-weight: bold;
  margin-top: 10rpx;
}

.btn-icon {
  margin-left: 8rpx;
  font-size: 24rpx;
}

.submit-btn {
  background: #3498db;
  color: white;
  align-items: center;
  font-size: 28rpx;
}


.submit-btn[disabled] {
  background: #ccc;
  color: #999;
  box-shadow: none;
}
.gallery-footer wx-button:not([size=mini]) {
  margin-left: auto;
  margin-right: auto;
  width:auto;
}
.gallery-footer wx-button {
  border-radius: 4px;
  font-size: 17px;
  font-weight: 700;
  line-height: 1.41176471;
  padding: 8px 15px;
}
/* 画师列表弹窗 */
.artists-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.artists-popup-content {
  width: 80%;
  max-height: 80%;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 0 10rpx;
}

/* 添加滚动视图样式 */
.artists-list-scroll {
  flex: 1;
  height: 700rpx;  /* 设置最大高度 */
}

.artists-list {
  padding: 20rpx;
}

.artist-item-popup {
  display: flex;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 添加序号样式 */
.artist-index {
  width: 40rpx;
  height: 40rpx;
  background-color: #3498db;
  color: white;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 15rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.artist-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.artist-row-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx dashed #eee;
}

.artist-detail-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.artist-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.status-tag {
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 8rpx;
  margin-top: 6rpx;
  font-weight: normal;
  display: inline-block;
}

.status-tag.pending {
  background-color: #e3f2fd;
  color: #2196f3;
  border: 1rpx solid #bbdefb;
}

.status-tag.approved {
  background-color: #e8f5e9;
  color: #4caf50;
  border: 1rpx solid #c8e6c9;
}

.status-tag.cancelled {
  background-color: #ffebee;
  color: #f44336;
  border: 1rpx solid #ffcdd2;
}

.artist-images {
  display: flex;
  flex-wrap: wrap;
}

.small-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.artist-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.selection-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  width: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空数据状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  width: 100%;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

/* 单张图片的画师列表弹窗样式 */
.popup-image-container {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  margin-bottom: 20rpx;
}

.popup-image {
  max-width: 100%;
  max-height: 280rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  pointer-events: none;
}

/* 无画师选择状态 */
.no-artists {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

/* 图片遮罩层样式 */
.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  z-index: 10;
}

/* 已提交未选择的遮罩样式 */
.image-mask.submitted-mask {
  background-color: rgba(100, 100, 100, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.submitted-mask .mask-text {
  color: #f5f5f5;
  background: rgba(0, 0, 0, 0.2);
}

.submitted-mask .mask-hint {
  color: #ffcdd2;
}

.mask-text {
  color: white;
  font-size: 28rpx;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.mask-hint {
  display: block;
  font-size: 22rpx;
  color: #e1f5fe;
  margin-top: 6rpx;
  opacity: 0.9;
  font-weight: normal;
}

/* 单张图片的画师列表样式更新 */
.artist-row-with-status {
  display: flex;
  padding: 15rpx 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: center;
}

.artist-index.mini {
  width: 30rpx;
  height: 30rpx;
  font-size: 18rpx;
  margin-right: 10rpx;
}

.artist-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.artist-detail-row {
  display: flex;
  justify-content: space-between;
  margin-top: 5rpx;
}

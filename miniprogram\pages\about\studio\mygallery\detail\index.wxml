<wxs src="../../../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="已选图片" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      
      <!-- 基本信息卡片 -->
      <view class="info-card" wx:if="{{galleryInfo}}">
        <view class="card-title">{{galleryInfo.series_name || '图库信息'}}</view>
        <view class="card-info">
          <view class="info-item">
            <text class="label">图库名称：</text>
            <text>{{galleryInfo.gallery_name}}</text>
          </view>
          <view class="info-item">
            <text class="label">已选数量：</text>
            <text>{{selectedImages.length || 0}}张</text>
          </view>
          <view class="info-item">
            <text class="label">选择限制：</text>
            <text>{{galleryInfo.min_selections || 0}} - {{galleryInfo.max_selections || 0}}张</text>
          </view>
          <view class="info-item">
            <text class="label">状态：</text>
            <text class="status {{galleryInfo.selection_status === 'confirmed' ? 'status-completed' : galleryInfo.selection_status === 'pending' ? 'status-pending' : galleryInfo.selection_status === 'rejected' ? 'status-rejected' : ''}}">{{galleryInfo.selection_status === 'confirmed' ? '已通过' : 
                galleryInfo.selection_status === 'pending' ? '审核中' : 
                galleryInfo.selection_status === 'rejected' ? '已驳回' : '待选择'}}
            </text>
          </view>
          <view class="info-item" wx:if="{{galleryInfo.selection_time}}">
            <text class="label">选择时间：</text>
            <text>{{galleryInfo.selection_time}}</text>
          </view>
        </view>
      </view>
      
      <!-- 未审核通过提示 -->
      <view class="protection-notice" wx:if="{{showStealProtection && galleryInfo}}">
        <view class="notice-icon">!</view>
        <view class="notice-text">
          <view class="notice-title">未通过审核的图片受保护</view>
          <view class="notice-desc">此图库尚未通过审核，图片受到保护，禁止下载和截屏</view>
        </view>
      </view>
      
      <!-- 下载全部按钮：仅在审核通过状态显示 -->
      <view class="download-all-container" wx:if="{{galleryInfo.selection_status === 'confirmed' && selectedImages && selectedImages.length > 0}}">
        <button class="download-all-btn" bindtap="downloadAllImages" disabled="{{isDownloadingAll}}">
          <text class="download-all-icon">↓</text>
          <text>下载全部图片</text>
        </button>
      </view>
      
      <!-- 下载进度条：当正在批量下载时显示 -->
      <view class="download-progress-container" wx:if="{{isDownloadingAll}}">
        <view class="progress-info">
          <text class="progress-text">正在下载 ({{downloadedImages}}/{{totalImages}})</text>
          <text class="progress-percent">{{downloadProgress.toFixed(0)}}%</text>
        </view>
        <view class="progress-bar-container">
          <view class="progress-bar" style="width: {{downloadProgress}}%;"></view>
        </view>
        <view class="progress-tip">
          <text>下载中请勿离开页面，部分图片可能因网络原因下载失败</text>
        </view>
      </view>
      
      <!-- 加载指示器 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 图片网格 -->
      <view class="image-grid" wx:if="{{selectedImages && selectedImages.length > 0}}">
        <!-- 下载提示文本，仅在已通过状态时显示 -->
        <view class="download-tip" wx:if="{{galleryInfo.selection_status === 'confirmed'}}">
          <text>点击图片可预览，点击右下角<text class="download-icon-small">↓</text>按钮可下载到相册</text>
        </view>
        
        <view wx:for="{{selectedImages}}" wx:key="id" class="image-item">
          <image 
            class="gallery-image {{showStealProtection ? 'protected-image' : ''}}" 
            src="{{baseUrl}}{{item.thumbnail_url}}" 
            mode="aspectFill" 
            bindtap="previewImage" 
            data-url="{{baseUrl}}{{item.url}}" 
            data-index="{{index}}"
            show-menu-by-longpress="{{!showStealProtection}}"
          ></image>
          
          <!-- 图片序号 -->
          <view class="image-number">{{index + 1}}</view>
          
          <!-- 未通过审核状态下的水印 -->
          <view class="watermark" wx:if="{{showStealProtection}}">
            <view class="watermark-text">审核中</view>
            <view class="watermark-text">禁止下载</view>
          </view>
          
          <!-- 下载按钮：仅在已通过状态时显示 -->
          <view class="download-btn" wx:if="{{galleryInfo.selection_status === 'confirmed'}}" catchtap="downloadImage" data-url="{{baseUrl}}{{item.url}}" data-filename="{{item.filename || ('image_' + index + '.jpg')}}">
            <text class="download-icon">↓</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态展示 -->
      <view class="empty-state" wx:if="{{!loading && (!selectedImages || selectedImages.length === 0)}}">
        <image class="empty-image" src="/assets/empty.png" mode="aspectFit"></image>
        <text class="empty-text">暂无已选图片</text>
      </view>
      
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view> 
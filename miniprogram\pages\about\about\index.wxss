.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 添加按钮样式 */
.auth-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.auth-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  background-color: #07c160;
  color: #fff;
  text-align: center;
  padding: 0;
}

.auth-btn::after {
  border: none;
}

.auth-btn-hide {
  background-color: #ff6347;
}

.auth-btn:active {
  opacity: 0.8;
}

.auth-btn-params {
  background-color: #1e90ff;
  width: 100%;
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  filter: brightness(0.9);
}

/* 个人中心容器 */
.profile-container {
  padding: 30rpx 24rpx 120rpx;
}

/* 用户卡片 */
.user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  padding-bottom: 30rpx;
}

.not-login {
  padding-bottom: 0;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
  animation: avatar-glow 3s infinite ease-in-out;
}

.level-tag {
  position: absolute;
  bottom: -6rpx;
  right: -6rpx;
  background: linear-gradient(135deg, #ff9000, #ff5000);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  border: 2rpx solid #ffffff;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.account {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.login-btn {
  display: inline-block;
  background: linear-gradient(135deg, #07c160, #0bab53);
  color: white;
  font-size: 28rpx;
  padding: 12rpx 40rpx;
  border-radius: 100rpx;
  margin-top: 10rpx;
}

/* 用户数据统计 */
.user-stats {
  display: flex;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.border-item::before,
.border-item::after {
  content: '';
  position: absolute;
  top: 15%;
  width: 1rpx;
  height: 70%;
  background-color: rgba(0, 0, 0, 0.05);
}

.border-item::before {
  left: 0;
}

.border-item::after {
  right: 0;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

/* 会员卡片 */
.vip-card {
  background: linear-gradient(135deg, #ffdc9d, #ffc107, #ffd54f, #ffc107);
  background-size: 300% 300%;
  animation: gradientBG 8s ease infinite;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.vip-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.vip-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vip-left {
  flex: 1;
}

.vip-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #5e3f00;
  margin-bottom: 8rpx;
}

.vip-desc {
  font-size: 24rpx;
  color: #7e5400;
}

.vip-right {
  display: flex;
  align-items: center;
}

.vip-btn {
  background: rgba(94, 63, 0, 0.2);
  color: #5e3f00;
  font-size: 24rpx;
  padding: 10rpx 24rpx;
  border-radius: 100rpx;
  border: 2rpx solid rgba(94, 63, 0, 0.3);
}

/* 公告列表样式 */
.announcement-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 20rpx 30rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding-left: 10rpx;
  border-left: 6rpx solid #07c160;
}

.view-more {
  font-size: 24rpx;
  color: #07c160;
  padding: 6rpx 12rpx;
  border-radius: 100rpx;
  background-color: rgba(7, 193, 96, 0.1);
}

.announcement-list {
  background: #fff;
  border-radius: 12rpx;
}

.announcement-item {
  padding: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.announcement-title-wrapper {
  display: flex;
  align-items: center;
  max-width: 75%;
}

.announcement-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.announcement-date {
  font-size: 24rpx;
  color: #999;
}

.announcement-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.announcement-tag {
  background-color: #ff5722;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.no-announcement {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

.no-announcement mp-icon {
  margin-bottom: 10rpx;
}

/* 公告详情弹窗样式 */
.announcement-detail-modal, .more-announcements-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.announcement-detail-mask, .more-announcements-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.announcement-detail-content, .more-announcements-content {
  position: relative;
  width: 85%;
  max-width: 600rpx;
  max-height: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: modal-in 0.3s ease-out;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.announcement-detail-header, .more-announcements-header {
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-detail-title, .more-announcements-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding-right: 24rpx;
}

.announcement-detail-close, .more-announcements-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 0 12rpx;
}

.announcement-detail-info {
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.announcement-detail-date {
  font-size: 24rpx;
  color: #999;
}

.announcement-detail-body, .more-announcements-body {
  padding: 24rpx;
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

.announcement-detail-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: justify;
}

.more-announcements-body .announcement-list {
  background: none;
  padding: 0;
}

.more-announcements-body .announcement-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.more-announcements-body .announcement-item:last-child {
  margin-bottom: 0;
}

/* 功能菜单区域 */
.menu-section, .settings-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 20rpx 30rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  transition: all 0.2s ease;
}

.menu-item:active {
  transform: scale(0.9);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.menu-item:active .menu-icon {
  animation: icon-bounce 0.5s;
}

.icon-text {
  font-size: 40rpx;
  font-weight: bold;
}

.menu-name {
  font-size: 24rpx;
  color: #333;
}

/* 设置列表 */
.settings-list {
  background: #fff;
  border-radius: 12rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 10rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.setting-icon-text {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.setting-name {
  font-size: 28rpx;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 26rpx;
  color: #999;
  margin-right: 8rpx;
}

.arrow-icon {
  font-size: 28rpx;
  color: #999;
}

/* 退出登录按钮 */
.logout-button {
  background: #fff;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-top: 60rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.logout-button:active {
  transform: scale(0.98);
  background-color: #fff5f5;
}

.logout-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-text {
  color: #ff5000;
  margin-left: 10rpx;
}

.test-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

/* 背景动画效果 */
@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 添加头像闪光效果 */
@keyframes avatar-glow {
  0% {
    box-shadow: 0 0 5rpx rgba(7, 193, 96, 0.5);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(7, 193, 96, 0.8);
  }
  100% {
    box-shadow: 0 0 5rpx rgba(7, 193, 96, 0.5);
  }
}

/* 菜单图标动画效果 */
@keyframes icon-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
}

/* 添加刷新按钮 */
.refresh-btn {
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  background-color: rgba(7, 193, 96, 0.1);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.refreshing {
  animation: spin 1s linear infinite;
}

/* 骨架屏样式 */
.skeleton {
  padding: 30rpx 24rpx 120rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton-card, .skeleton-vip, .skeleton-menu, .skeleton-settings {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.skeleton-user {
  display: flex;
  align-items: center;
  padding-bottom: 30rpx;
}

.skeleton-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-info {
  flex: 1;
}

.skeleton-name {
  height: 34rpx;
  width: 150rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

.skeleton-account {
  height: 24rpx;
  width: 200rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-stats {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.skeleton-stat {
  height: 60rpx;
  width: 30%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-vip {
  height: 100rpx;
  background: linear-gradient(90deg, #f5f5f5 25%, #e5e5e5 50%, #f5f5f5 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-title {
  height: 28rpx;
  width: 120rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 20rpx;
}

.skeleton-grid {
  display: flex;
  flex-wrap: wrap;
}

.skeleton-item {
  width: 25%;
  height: 120rpx;
  padding: 10rpx;
  box-sizing: border-box;
}

.skeleton-item::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-setting {
  height: 60rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

/* 数据统计卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 20rpx 30rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.switch-chart {
  font-size: 24rpx;
  color: #07c160;
  padding: 6rpx 12rpx;
  border-radius: 100rpx;
  background-color: rgba(7, 193, 96, 0.1);
}

.chart-container {
  display: flex;
  flex-direction: column;
}

.chart-wrapper {
  width: 100%;
  height: 320rpx;
}

.stats-preview {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  margin-top: 20rpx;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.preview-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

/* 最近活动卡片 */
.activity-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 20rpx 30rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.activity-list {
  background: #fff;
  border-radius: 12rpx;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(7, 193, 96, 0.1);
  border-radius: 50%;
  margin-right: 20rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

/* 骨架屏新增部分 */
.skeleton-chart {
  height: 380rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.skeleton-chart::after {
  content: '';
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 30rpx solid #f0f0f0;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-activity {
  height: 300rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.skeleton-activity::after {
  content: '';
  display: block;
  height: 240rpx;
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite;
}

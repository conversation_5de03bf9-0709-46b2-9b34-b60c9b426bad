{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar escapeStringRegexp = require('escape-string-regexp');\nvar ansiStyles = require('ansi-styles');\nvar stripAnsi = require('strip-ansi');\nvar hasAnsi = require('has-ansi');\nvar supportsColor = require('supports-color');\nvar defineProps = Object.defineProperties;\nvar isSimpleWindowsTerm = process.platform === 'win32' && !/^xterm/i.test(process.env.TERM);\n\nfunction Chalk(options) {\n\t// detect mode if not set manually\n\tthis.enabled = !options || options.enabled === undefined ? supportsColor : options.enabled;\n}\n\n// use bright blue on Windows as the normal blue color is illegible\nif (isSimpleWindowsTerm) {\n\tansiStyles.blue.open = '\\u001b[94m';\n}\n\nvar styles = (function () {\n\tvar ret = {};\n\n\tObject.keys(ansiStyles).forEach(function (key) {\n\t\tansiStyles[key].closeRe = new RegExp(escapeStringRegexp(ansiStyles[key].close), 'g');\n\n\t\tret[key] = {\n\t\t\tget: function () {\n\t\t\t\treturn build.call(this, this._styles.concat(key));\n\t\t\t}\n\t\t};\n\t});\n\n\treturn ret;\n})();\n\nvar proto = defineProps(function chalk() {}, styles);\n\nfunction build(_styles) {\n\tvar builder = function () {\n\t\treturn applyStyle.apply(builder, arguments);\n\t};\n\n\tbuilder._styles = _styles;\n\tbuilder.enabled = this.enabled;\n\t// __proto__ is used because we must return a function, but there is\n\t// no way to create a function with a different prototype.\n\t/* eslint-disable no-proto */\n\tbuilder.__proto__ = proto;\n\n\treturn builder;\n}\n\nfunction applyStyle() {\n\t// support varags, but simply cast to string in case there's only one arg\n\tvar args = arguments;\n\tvar argsLen = args.length;\n\tvar str = argsLen !== 0 && String(arguments[0]);\n\n\tif (argsLen > 1) {\n\t\t// don't slice `arguments`, it prevents v8 optimizations\n\t\tfor (var a = 1; a < argsLen; a++) {\n\t\t\tstr += ' ' + args[a];\n\t\t}\n\t}\n\n\tif (!this.enabled || !str) {\n\t\treturn str;\n\t}\n\n\tvar nestedStyles = this._styles;\n\tvar i = nestedStyles.length;\n\n\t// Turns out that on Windows dimmed gray text becomes invisible in cmd.exe,\n\t// see https://github.com/chalk/chalk/issues/58\n\t// If we're on Windows and we're dealing with a gray color, temporarily make 'dim' a noop.\n\tvar originalDim = ansiStyles.dim.open;\n\tif (isSimpleWindowsTerm && (nestedStyles.indexOf('gray') !== -1 || nestedStyles.indexOf('grey') !== -1)) {\n\t\tansiStyles.dim.open = '';\n\t}\n\n\twhile (i--) {\n\t\tvar code = ansiStyles[nestedStyles[i]];\n\n\t\t// Replace any instances already present with a re-opening code\n\t\t// otherwise only the part of the string until said closing code\n\t\t// will be colored, and the rest will simply be 'plain'.\n\t\tstr = code.open + str.replace(code.closeRe, code.open) + code.close;\n\t}\n\n\t// Reset the original 'dim' if we changed it to work around the Windows dimmed gray issue.\n\tansiStyles.dim.open = originalDim;\n\n\treturn str;\n}\n\nfunction init() {\n\tvar ret = {};\n\n\tObject.keys(styles).forEach(function (name) {\n\t\tret[name] = {\n\t\t\tget: function () {\n\t\t\t\treturn build.call(this, [name]);\n\t\t\t}\n\t\t};\n\t});\n\n\treturn ret;\n}\n\ndefineProps(Chalk.prototype, init());\n\nmodule.exports = new Chalk();\nmodule.exports.styles = ansiStyles;\nmodule.exports.hasColor = hasAnsi;\nmodule.exports.stripColor = stripAnsi;\nmodule.exports.supportsColor = supportsColor;\n"]}
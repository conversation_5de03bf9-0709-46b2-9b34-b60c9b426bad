// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api from '../../../utils/api';
import eventBus from '../../../utils/eventBus';
import { DOMAIN, COMMON_ASSETS } from '../../../utils/constants';

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    tabBarHeight: 0,
    userInfo: {},
    artStylesList: [],  // 画风列表
    artworkTypesList: [], // 画种列表
    showCropper: false,
    cropperSrc: '',
    showMoreInfo: false, // 是否显示更多基本信息
    showContractInfo: false, // 是否显示签约信息详情
    showBankInfo: false, // 是否显示线下结算信息详情
    defaultAvatar: COMMON_ASSETS.DEFAULT_AVATAR, // 添加默认头像常量
    contractStatusMap: {
      'pending': '待签约',
      'active': '已签约',
      'expired': '已过期',
      'terminated': '已终止'
    },
    // 签名相关
    signatureList: [], // 多个签名的列表
    showSignatureInput: false, // 是否显示添加签名输入框
    newSignature: '', // 新签名输入值
    // 编辑页面需要的数据
    artistTypes: [
      { id: 0, name: '用户' },
      { id: 1, name: '画师' },
      { id: 2, name: '工作室' }
    ],
    genderRange: [
      { id: 0, name: '未设置' },
      { id: 1, name: '男' },
      { id: 2, name: '女' }
    ],
    contractStatusOptions: [
      { value: 'pending', name: '待签约' },
      { value: 'active', name: '已签约' },
      { value: 'expired', name: '已过期' },
      { value: 'terminated', name: '已终止' }
    ],
    contractStatusIndex: 0,
    showArtStylePickerPopup: false,
    showArtworkTypePickerPopup: false,
    allArtStyles: [],
    allArtworkTypes: [],
    selectedArtStyles: [],
    selectedArtworkTypes: [],
    artStyleIDs: [],
    artworkTypeIDs: []
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      try {
        // 先获取用户ID
        Api.common.logintest().then((loginResult: any) => {
          // 获取用户ID
          const userId = loginResult.id;
          // 使用获取到的ID请求艺术家资料
          return Api.user.getArtistProfile(userId);
        }).then((res: any) => {
          
          // API返回了画风和画种列表，直接使用
          this.setData({
            allArtStyles: res.all_art_styles || [],
            allArtworkTypes: res.all_artwork_types || []
          });
          
          // 处理用户数据，包括画风画种映射
          this.processUserData(res);
          
          // 更新画风和画种的显示名称
          this.updateArtStyleNames();
          this.updateArtworkTypeNames();
          
        }).catch(error => {
          console.error('API调用失败:', error);
          wx.showToast({
            title: '获取艺术家信息失败',
            icon: 'none'
          });
          
          // 无法获取艺术家信息时，返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        });
      } catch (error) {
        console.error('API调用异常:', error);
        wx.showToast({
          title: '获取艺术家信息失败',
          icon: 'none'
        });
        
        // 无法获取艺术家信息时，返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    // 处理从API获取的用户数据
    processUserData: function(userData: any) {
      // 找到合同状态对应的索引
      const contractStatusIndex = this.data.contractStatusOptions.findIndex(
        item => item.value === userData.contract_status
      );

      // 处理art_styles数据，可能是逗号分隔的字符串、数组或ID数组
      let artStylesList: string[] = [];
      let artStyleIDs: number[] = [];
      
      if (userData.art_styles !== undefined && userData.art_styles !== null) {
        if (typeof userData.art_styles === 'string') {
          // 如果是字符串，可能是逗号分隔的名称或ID
          const artStylesArr = userData.art_styles.trim() !== '' ? 
                              userData.art_styles.split(',').filter((item: string) => item !== '不限') : [];
          
          // 检查是否为数字ID字符串
          if (artStylesArr.length > 0 && !isNaN(Number(artStylesArr[0]))) {
            // 是ID字符串，转为数字并保存
            artStyleIDs = artStylesArr.map((id: string) => parseInt(id));
            // 在allArtStyles中查找名称
            if (this.data.allArtStyles.length > 0) {
              artStylesList = artStyleIDs.map(id => {
                const style = this.data.allArtStyles.find(item => item.id === id);
                return style ? style.name : `未知(${id})`;
              });
            } else {
              // 暂存ID，后续获取到allArtStyles后再更新
              artStylesList = artStylesArr.map(id => `ID:${id}`);
            }
          } else {
            // 是名称字符串
            artStylesList = artStylesArr;
          }
        } else if (Array.isArray(userData.art_styles)) {
          // 如果是对象数组，提取name字段
          if (userData.art_styles.length > 0 && typeof userData.art_styles[0] === 'object') {
            artStylesList = userData.art_styles.map((item: any) => item.name);
            if (userData.art_styles[0].id) {
              artStyleIDs = userData.art_styles.map((item: any) => item.id);
            }
          } else if (userData.art_styles.length > 0 && typeof userData.art_styles[0] === 'number') {
            // 是纯ID数组
            artStyleIDs = userData.art_styles;
            // 在allArtStyles中查找名称
            if (this.data.allArtStyles.length > 0) {
              artStylesList = artStyleIDs.map(id => {
                const style = this.data.allArtStyles.find(item => item.id === id);
                return style ? style.name : `未知(${id})`;
              });
            } else {
              // 暂存ID，后续获取到allArtStyles后再更新
              artStylesList = userData.art_styles.map((id: number) => `ID:${id}`);
            }
          } else {
            // 是名称数组
            artStylesList = userData.art_styles;
          }
        }
      }

      // 处理artwork_types数据，可能是逗号分隔的字符串、数组或ID数组
      let artworkTypesList: string[] = [];
      let artworkTypeIDs: number[] = [];
      
      if (userData.artwork_types !== undefined && userData.artwork_types !== null) {
        if (typeof userData.artwork_types === 'string') {
          // 如果是字符串，可能是逗号分隔的名称或ID
          const artworkTypesArr = userData.artwork_types.trim() !== '' ? 
                              userData.artwork_types.split(',').filter((item: string) => item !== '不限') : [];
          
          // 检查是否为数字ID字符串
          if (artworkTypesArr.length > 0 && !isNaN(Number(artworkTypesArr[0]))) {
            // 是ID字符串，转为数字并保存
            artworkTypeIDs = artworkTypesArr.map((id: string) => parseInt(id));
            // 在allArtworkTypes中查找名称
            if (this.data.allArtworkTypes.length > 0) {
              artworkTypesList = artworkTypeIDs.map(id => {
                const type = this.data.allArtworkTypes.find(item => item.id === id);
                return type ? type.name : `未知(${id})`;
              });
            } else {
              // 暂存ID，后续获取到allArtworkTypes后再更新
              artworkTypesList = artworkTypesArr.map(id => `ID:${id}`);
            }
          } else {
            // 是名称字符串
            artworkTypesList = artworkTypesArr;
          }
        } else if (Array.isArray(userData.artwork_types)) {
          // 如果是对象数组，提取name字段
          if (userData.artwork_types.length > 0 && typeof userData.artwork_types[0] === 'object') {
            artworkTypesList = userData.artwork_types.map((item: any) => item.name);
            if (userData.artwork_types[0].id) {
              artworkTypeIDs = userData.artwork_types.map((item: any) => item.id);
            }
          } else if (userData.artwork_types.length > 0 && typeof userData.artwork_types[0] === 'number') {
            // 是纯ID数组
            artworkTypeIDs = userData.artwork_types;
            // 在allArtworkTypes中查找名称
            if (this.data.allArtworkTypes.length > 0) {
              artworkTypesList = artworkTypeIDs.map(id => {
                const type = this.data.allArtworkTypes.find(item => item.id === id);
                return type ? type.name : `未知(${id})`;
              });
            } else {
              // 暂存ID，后续获取到allArtworkTypes后再更新
              artworkTypesList = userData.artwork_types.map((id: number) => `ID:${id}`);
            }
          } else {
            // 是名称数组
            artworkTypesList = userData.artwork_types;
          }
        }
      }

      // 处理签名
      const signatureList = userData.signature !== undefined && userData.signature !== null ? 
                           (typeof userData.signature === 'string' && userData.signature.trim() !== '' ? 
                            userData.signature.split(',').filter(item => item.trim() !== '') : 
                            (userData.signature ? [userData.signature] : [])) : [];

      this.setData({
        userInfo: userData,
        artStylesList,
        artworkTypesList,
        signatureList,
        artStyleIDs,
        artworkTypeIDs,
        contractStatusIndex: contractStatusIndex !== -1 ? contractStatusIndex : 0,
        selectedArtStyles: [...artStylesList],
        selectedArtworkTypes: [...artworkTypesList]
      });
    },

    // 微信头像选择器回调
    onChooseAvatar(e: any) {
      console.log('用户头像选择器回调');
      
      const { avatarUrl } = e.detail;
      console.log('用户头像avatarUrl', avatarUrl);
      
      // 如果是临时文件路径，尝试转换为base64
      if (avatarUrl && (avatarUrl.startsWith('wxfile://') || avatarUrl.startsWith('http://tmp/') || avatarUrl.startsWith('https://tmp/'))) {
        try {
          // 获取图片信息
          wx.getImageInfo({
            src: avatarUrl,
            success: (imgInfo) => {
              // 读取临时文件并转为base64
              wx.getFileSystemManager().readFile({
                filePath: avatarUrl,
                encoding: 'base64',
                success: (res) => {
                  const base64Data = `data:image/jpeg;base64,${res.data}`;
                  this.setData({
                    'userInfo.avatar': base64Data,
                    'userInfo.avatar_url': base64Data
                  });
                },
                fail: (err) => {
                  console.error('读取文件失败:', err);
                  // 如果读取失败，则直接使用URL
                  this.setData({
                    'userInfo.avatar': avatarUrl,
                    'userInfo.avatar_url': avatarUrl
                  });
                }
              });
            },
            fail: (err) => {
              console.error('获取图片信息失败:', err);
              // 失败时直接使用URL
              this.setData({
                'userInfo.avatar': avatarUrl,
                'userInfo.avatar_url': avatarUrl
              });
            }
          });
        } catch (error) {
          console.error('处理头像失败:', error);
          // 出现异常时直接使用URL
          this.setData({
            'userInfo.avatar': avatarUrl,
            'userInfo.avatar_url': avatarUrl
          });
        }
      } else if (avatarUrl) {
        // 如果已经是base64数据或普通URL，直接使用
        this.setData({
          'userInfo.avatar': avatarUrl,
          'userInfo.avatar_url': avatarUrl
        });
      }
    },

    // 处理各种输入变化
    handleNicknameInput: function(e: any) {
      this.setData({
        'userInfo.nickname': e.detail.value
      });
    },

    // 签名相关处理
    showAddSignatureInput: function() {
      this.setData({
        showSignatureInput: true,
        newSignature: ''
      });
    },

    hideAddSignatureInput: function() {
      this.setData({
        showSignatureInput: false
      });
    },

    handleNewSignatureInput: function(e: any) {
      this.setData({
        newSignature: e.detail.value
      });
    },

    confirmAddSignature: function() {
      const { newSignature, signatureList } = this.data;
      
      if (newSignature.trim()) {
        // 添加新签名到列表
        const updatedList = [...signatureList, newSignature.trim()];
        
        this.setData({
          signatureList: updatedList,
          'userInfo.signature': updatedList.join(','),
          showSignatureInput: false,
          newSignature: ''
        });
      } else {
        wx.showToast({
          title: '签名不能为空',
          icon: 'none'
        });
      }
    },

    handleDeleteSignature: function(e: any) {
      const { index } = e.currentTarget.dataset;
      const signatureList = [...this.data.signatureList];
      
      signatureList.splice(index, 1);
      
      this.setData({
        signatureList,
        'userInfo.signature': signatureList.join(',')
      });
    },

    handleMobileInput: function(e: any) {
      this.setData({
        'userInfo.mobile': e.detail.value
      });
    },

    handleEmailInput: function(e: any) {
      this.setData({
        'userInfo.email': e.detail.value
      });
    },
    handleAddnameInput: function(e: any) {
      this.setData({
        'userInfo.addname': e.detail.value
      });
    },
    handleAddressInput: function(e: any) {
      this.setData({
        'userInfo.address': e.detail.value
      });
    },

    handleBankNameInput: function(e: any) {
      this.setData({
        'userInfo.bank_name': e.detail.value
      });
    },

    handleBankAccountInput: function(e: any) {
      this.setData({
        'userInfo.bank_account': e.detail.value
      });
    },

    handleAccountHolderInput: function(e: any) {
      this.setData({
        'userInfo.account_holder': e.detail.value
      });
    },

    handleIdCardInput: function(e: any) {
      this.setData({
        'userInfo.id_card_number': e.detail.value
      });
    },

    // 切换显示更多基本信息
    toggleMoreInfo: function() {
      this.setData({
        showMoreInfo: !this.data.showMoreInfo
      });
    },

    // 切换显示签约信息详情
    toggleContractInfo: function() {
      this.setData({
        showContractInfo: !this.data.showContractInfo
      });
    },

    // 切换显示线下结算信息详情
    toggleBankInfo: function() {
      this.setData({
        showBankInfo: !this.data.showBankInfo
      });
    },

    // 处理选择器变化
    handleGenderChange: function(e: any) {
      this.setData({
        'userInfo.gender': parseInt(e.detail.value)
      });
    },

    handleBirthdayChange: function(e: any) {
      this.setData({
        'userInfo.birthday': e.detail.value
      });
    },

    handleContractStatusChange: function(e: any) {
      const index = parseInt(e.detail.value);
      const contractStatus = this.data.contractStatusOptions[index].value;
      
      // 准备更新数据
      const updateData: any = {
        contractStatusIndex: index,
        'userInfo.contract_status': contractStatus
      };
      
      // 如果状态不是"已签约"，清空日期字段
      if (contractStatus !== 'active') {
        updateData['userInfo.contract_start_date'] = '';
        updateData['userInfo.contract_end_date'] = '';
      }
      
      // 更新数据
      this.setData(updateData);
    },

    handleContractStartChange: function(e: any) {
      this.setData({
        'userInfo.contract_start_date': e.detail.value
      });
    },

    handleContractEndChange: function(e: any) {
      this.setData({
        'userInfo.contract_end_date': e.detail.value
      });
    },

    // 画风画种相关操作
    showArtStylePicker: function() {
      this.setData({
        showArtStylePickerPopup: true
      });
    },

    hideArtStylePicker: function() {
      this.setData({
        showArtStylePickerPopup: false
      });
    },

    handleArtStyleCheck: function(e: any) {
      // 存储选中的画风名称
      this.setData({
        selectedArtStyles: e.detail.value
      });
    },

    confirmArtStyleSelection: function() {
      const { selectedArtStyles, allArtStyles } = this.data;
      const filteredStyles = selectedArtStyles.filter(item => item !== '不限');
      
      // 根据选中的名称找到对应的ID
      const styleIDs = filteredStyles.map(name => {
        const style = allArtStyles.find(s => s.name === name);
        return style ? style.id : null;
      }).filter(id => id !== null);
      
      this.setData({
        artStylesList: filteredStyles,
        artStyleIDs: styleIDs,
        'userInfo.art_styles': filteredStyles.join(','),
        showArtStylePickerPopup: false
      });
    },

    showArtworkTypePicker: function() {
      this.setData({
        showArtworkTypePickerPopup: true
      });
    },

    hideArtworkTypePicker: function() {
      this.setData({
        showArtworkTypePickerPopup: false
      });
    },

    handleArtworkTypeCheck: function(e: any) {
      // 存储选中的画种名称
      this.setData({
        selectedArtworkTypes: e.detail.value
      });
    },

    confirmArtworkTypeSelection: function() {
      const { selectedArtworkTypes, allArtworkTypes } = this.data;
      const filteredTypes = selectedArtworkTypes.filter(item => item !== '不限');
      
      // 根据选中的名称找到对应的ID
      const typeIDs = filteredTypes.map(name => {
        const type = allArtworkTypes.find(t => t.name === name);
        return type ? type.id : null;
      }).filter(id => id !== null);
      
      this.setData({
        artworkTypesList: filteredTypes,
        artworkTypeIDs: typeIDs,
        'userInfo.artwork_types': filteredTypes.join(','),
        showArtworkTypePickerPopup: false
      });
    },

    handleDeleteTag: function(e: any) {
      const { type, index } = e.currentTarget.dataset;
      if (type === 'style') {
        // 删除画风
        const artStylesList = [...this.data.artStylesList];
        const removedStyle = artStylesList[index];
        artStylesList.splice(index, 1);
        
        // 同步更新IDs
        const artStyleIDs = [...this.data.artStyleIDs];
        // 找到对应项的索引
        const style = this.data.allArtStyles.find(s => s.name === removedStyle);
        if (style) {
          const idIndex = artStyleIDs.indexOf(style.id);
          if (idIndex !== -1) {
            artStyleIDs.splice(idIndex, 1);
          }
        }
        
        // 安全处理: 确保userInfo对象存在
        const userInfo = this.data.userInfo || {};
        
        this.setData({
          artStylesList,
          selectedArtStyles: artStylesList,
          artStyleIDs,
          'userInfo.art_styles': artStylesList.length > 0 ? artStylesList.join(',') : ''
        });
        
        console.log('更新后的画风列表:', artStylesList);
        console.log('更新后的画风IDs:', artStyleIDs);
        console.log('更新后的userInfo.art_styles:', this.data.userInfo && this.data.userInfo.art_styles);
      } else if (type === 'artwork') {
        // 删除画种
        const artworkTypesList = [...this.data.artworkTypesList];
        const removedType = artworkTypesList[index];
        artworkTypesList.splice(index, 1);
        
        // 同步更新IDs
        const artworkTypeIDs = [...this.data.artworkTypeIDs];
        // 找到对应项的索引
        const type = this.data.allArtworkTypes.find(t => t.name === removedType);
        if (type) {
          const idIndex = artworkTypeIDs.indexOf(type.id);
          if (idIndex !== -1) {
            artworkTypeIDs.splice(idIndex, 1);
          }
        }
        
        this.setData({
          artworkTypesList,
          selectedArtworkTypes: artworkTypesList,
          artworkTypeIDs,
          'userInfo.artwork_types': artworkTypesList.join(',')
        });
      }
    },

    // 头像相关
    handleEditAvatar: function() {
      wx.showActionSheet({
        itemList: ['拍照', '从手机相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拍照
            this.chooseImage('camera');
          } else if (res.tapIndex === 1) {
            // 从相册选择
            this.chooseImage('album');
          }
        }
      });
    },

    // 选择图片
    chooseImage: function(sourceType: 'album' | 'camera') {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: [sourceType],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          // 获取图片信息
          wx.getImageInfo({
            src: tempFilePath,
            success: (imgInfo) => {
              // 创建一个Canvas，用于将图片转换为base64
              const ctx = wx.createCanvasContext('avatarCanvas', this);
              
              // 计算裁剪参数，确保图片居中裁剪为正方形
              const size = Math.min(imgInfo.width, imgInfo.height);
              const x = (imgInfo.width - size) / 2;
              const y = (imgInfo.height - size) / 2;
              
              // 在Canvas上绘制图片
              ctx.drawImage(tempFilePath, x, y, size, size, 0, 0, 200, 200);
              ctx.draw(false, () => {
                // 将Canvas内容转换为base64
                wx.canvasToTempFilePath({
                  canvasId: 'avatarCanvas',
                  fileType: 'jpg',
                  quality: 0.8,
                  success: (res) => {
                    // 读取临时文件内容并转换为base64
                    wx.getFileSystemManager().readFile({
                      filePath: res.tempFilePath,
                      encoding: 'base64',
                      success: (res) => {
                        const base64Data = `data:image/jpeg;base64,${res.data}`;
                        // 直接设置base64数据作为头像
                        this.setData({
                          'userInfo.avatar': base64Data,
                          'userInfo.avatar_url': base64Data
                        });
                      },
                      fail: (err) => {
                        console.error('转换base64失败:', err);
                        // 失败时仍然使用原始路径，以保证功能不中断
                        this.setData({
                          cropperSrc: tempFilePath,
                          showCropper: true
                        });
                      }
                    });
                  },
                  fail: (err) => {
                    console.error('Canvas导出失败:', err);
                    // 失败时仍然使用原始路径，以保证功能不中断
                    this.setData({
                      cropperSrc: tempFilePath,
                      showCropper: true
                    });
                  }
                }, this);
              });
            },
            fail: (err) => {
              console.error('获取图片信息失败:', err);
              // 失败时仍然使用原始路径，以保证功能不中断
              this.setData({
                cropperSrc: tempFilePath,
                showCropper: true
              });
            }
          });
        }
      });
    },

    // 隐藏裁剪器
    hideCropper: function() {
      this.setData({
        showCropper: false
      });
    },

    // 处理裁剪完成
    handleCropperConfirm: function(e: any) {
      const { tempFilePath } = e.detail;
      
      try {
        // 读取裁剪后的临时文件并转换为base64
        wx.getFileSystemManager().readFile({
          filePath: tempFilePath,
          encoding: 'base64',
          success: (res) => {
            const base64Data = `data:image/jpeg;base64,${res.data}`;
            
            this.setData({
              'userInfo.avatar': base64Data,
              'userInfo.avatar_url': base64Data,
              showCropper: false
            });
          },
          fail: (err) => {
            console.error('转换base64失败:', err);
            // 如果转换失败，仍使用临时文件路径
            this.setData({
              'userInfo.avatar': tempFilePath,
              'userInfo.avatar_url': tempFilePath,
              showCropper: false
            });
          }
        });
      } catch (error) {
        console.error('处理裁剪图片失败:', error);
        // 异常情况下仍使用临时文件路径
        this.setData({
          'userInfo.avatar': tempFilePath,
          'userInfo.avatar_url': tempFilePath,
          showCropper: false
        });
      }
    },

    // 上传头像
    uploadAvatar: function(filePath: string) {
      // TODO: 接入上传API
      wx.showLoading({
        title: '上传中...',
      });

      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        });
        
        // 上传成功后，设置远程URL
        // 这里模拟一个远程URL，实际应该使用服务器返回的URL
        const remoteUrl = DOMAIN + filePath;
        this.setData({
          'userInfo.avatar_url': remoteUrl
        });
      }, 1500);
    },

    // 保存资料
    handleSaveProfile: function() {
      // 表单验证
      if (!this.data.userInfo.nickname) {
        wx.showToast({
          title: '请输入昵称',
          icon: 'none'
        });
        return;
      }

      // 显示加载提示
      wx.showLoading({
        title: '保存中...',
      });

      // 确保画风和画种已转换为ID
      this.mapUserArtStylesWithIDs();
      this.mapUserArtworkTypesWithIDs();

      // 检查头像是否是base64格式，并处理过长的base64数据
      let isBase64Avatar = 0;
      let avatarData = this.data.userInfo.avatar;
      
      if (avatarData && typeof avatarData === 'string' && avatarData.startsWith('data:image')) {
        isBase64Avatar = 1;
        
        // 有些情况下base64数据可能过长，需要进行裁剪处理
        if (avatarData.length > 2 * 1024 * 1024) {  // 如果超过2MB
          
          // 这里可以添加压缩逻辑，比如使用canvas重新绘制并导出较小的图片
          // 由于实现较复杂，本例中我们仅提示并继续使用原始数据
          wx.showToast({
            title: '头像数据较大，可能会影响保存速度',
            icon: 'none',
            duration: 2000
          });
        }
      }

      // 准备要提交的数据
      const formData = {
        id: this.data.userInfo.id,
        nickname: this.data.userInfo.nickname,
        avatar: avatarData, // 可能是base64数据或URL
        signature: this.data.signatureList.join(','),
        mobile: this.data.userInfo.mobile,
        email: this.data.userInfo.email,
        gender: this.data.userInfo.gender,
        birthday: this.data.userInfo.birthday,
        addname: this.data.userInfo.addname,
        address: this.data.userInfo.address,
        // 提交画风和画种ID而不是名称
        art_styles_ids: this.data.artStyleIDs.join(','),
        artwork_types_ids: this.data.artworkTypeIDs.join(','),
        // 保留名称字段，以保持兼容性
        art_styles: this.data.artStylesList.join(','),
        artwork_types: this.data.artworkTypesList.join(','),
        contract_status: this.data.userInfo.contract_status,
        contract_start_date: this.data.userInfo.contract_start_date,
        contract_end_date: this.data.userInfo.contract_end_date,
        bank_name: this.data.userInfo.bank_name,
        bank_account: this.data.userInfo.bank_account,
        account_holder: this.data.userInfo.account_holder,
        id_card_number: this.data.userInfo.id_card_number,
        is_base64_avatar: isBase64Avatar // 标记是否为base64头像
      };

      // 调用API提交数据
      try {
        Api.user.artistProfile(formData)
          .then(res => {
            // 保存成功后，重新获取远程用户信息
            const userId = this.data.userInfo.id;
            
            // 获取最新的艺术家资料
            return Api.user.getArtistProfile(userId);
          })
          .then(res => {
            // 获取当前storage中的userInfo
            let userInfo = wx.getStorageSync('userInfo') || {};
            
            // 更新需要同步的字段
            userInfo.nickname = res.nickname || userInfo.nickname;
            userInfo.mobile = res.mobile || userInfo.mobile;
            
            // 更新头像信息
            if (res.avatar) {
              userInfo.avatar = res.avatar;
            }
            
            // 记录一下更新时间
            userInfo.updatetime = Math.floor(Date.now() / 1000);
            
            console.log('更新storage中的用户信息:', userInfo);
            
            // 保存更新后的userInfo到storage
            wx.setStorageSync('userInfo', userInfo);
            
            wx.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
            
            // 延迟后重定向到about页面，而不是简单地返回
            setTimeout(() => {
              // 使用reLaunch而不是redirectTo，确保about页面会完全重新加载
              wx.reLaunch({
                url: '/pages/about/about/index'
              });
            }, 1500);
          })
          .catch(err => {
            wx.hideLoading();
            wx.showToast({
              title: err.message || '保存失败',
              icon: 'none'
            });
          });
      } catch (error) {
        console.error('API调用异常:', error);
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    },


    // 将用户的画风名称映射为ID
    mapUserArtStylesWithIDs: function() {
      const { artStylesList, allArtStyles } = this.data;
      // 将用户的画风名称转换为ID列表
      const artStyleIDs = artStylesList.map(styleName => {
        const style = allArtStyles.find(item => item.name === styleName);
        return style ? style.id : null;
      }).filter(id => id !== null);

      this.setData({
        artStyleIDs: artStyleIDs
      });
    },

    // 将用户的画种名称映射为ID
    mapUserArtworkTypesWithIDs: function() {
      const { artworkTypesList, allArtworkTypes } = this.data;
      // 将用户的画种名称转换为ID列表
      const artworkTypeIDs = artworkTypesList.map(typeName => {
        const type = allArtworkTypes.find(item => item.name === typeName);
        return type ? type.id : null;
      }).filter(id => id !== null);

      this.setData({
        artworkTypeIDs: artworkTypeIDs
      });
    },
    
    // 通过画风ID更新画风名称显示
    updateArtStyleNames: function() {
      const { artStyleIDs, allArtStyles } = this.data;
      
      if (artStyleIDs.length > 0 && allArtStyles.length > 0) {
        // 根据ID查找名称
        const artStylesList = artStyleIDs.map(id => {
          const style = allArtStyles.find(item => item.id === id);
          return style ? style.name : `未知(${id})`;
        });
        
        this.setData({
          artStylesList,
          selectedArtStyles: [...artStylesList]
        });
      }
    },
    
    // 通过画种ID更新画种名称显示
    updateArtworkTypeNames: function() {
      const { artworkTypeIDs, allArtworkTypes } = this.data;
      
      if (artworkTypeIDs.length > 0 && allArtworkTypes.length > 0) {
        // 根据ID查找名称
        const artworkTypesList = artworkTypeIDs.map(id => {
          const type = allArtworkTypes.find(item => item.id === id);
          return type ? type.name : `未知(${id})`;
        });
        
        this.setData({
          artworkTypesList,
          selectedArtworkTypes: [...artworkTypesList]
        });
      }
    },
  }
});

/**
 * 布局工具类 - 用于精确计算小程序各个区域的尺寸
 * 
 * 布局区域划分：
 * +--------------------+
 * |    状态栏          |  -> statusBarHeight
 * +--------+---+-------+
 * | 返回   |胶囊|  标题 |  -> navigationBarHeight (包含胶囊按钮)
 * +--------+---+-------+ 
 * |                    |
 * |     可视区域       |  -> viewportHeight (不含导航栏和底部导航)
 * |                    |  -> visibleHeight (包含导航栏，不含底部导航)
 * |                    |  -> fullHeight (全屏高度)
 * +--------------------+
 * |    TabBar内容      |  -> tabBarContentHeight
 * +--------------------+
 * |    安全区域        |  -> safeAreaBottom
 * +---+------------+---+
 * +-----------------+
 * |   状态栏        |  -> statusBarHeight
 * +-----------------+
 * |   导航栏        |  -> navigationBarHeight (包含胶囊按钮)
 * +-----------------+ 
 * |                 |
 * |   内容区域      |  -> contentHeight
 * |                 |
 * +-----------------+
 * |   TabBar内容    |  -> tabBarContentHeight
 * +-----------------+
 * |   安全区域      |  -> safeAreaBottom
 * +-----------------+
 * 
 * 使用示例：
 * 
 * 1. 基础布局设置
 * ```typescript
 * // 页面 WXML
 * <view style="{{layoutStyle}}">
 *   <view class="content">页面内容</view>
 * </view>
 * 
 * // 页面 TS
 * Page({
 *   data: {
 *     layoutStyle: layoutUtil.getContentStyle()
 *   }
 * })
 * ```
 * 
 * 2. 自定义导航栏
 * ```typescript
 * // 导航栏 WXML
 * <view style="{{navStyle}}">
 *   <view style="{{navContentStyle}}">
 *     <view class="title">页面标题</view>
 *   </view>
 * </view>
 * 
 * // 导航栏 TS
 * Component({
 *   data: {
 *     navStyle: layoutUtil.getNavigationStyle(),
 *     navContentStyle: layoutUtil.getNavigationContentStyle()
 *   }
 * })
 * ```
 * 
 * 3. 底部TabBar
 * ```typescript
 * // TabBar WXML
 * <view style="{{tabBarStyle}}">
 *   <view style="{{contentStyle}}">TabBar内容</view>
 * </view>
 * 
 * // TabBar TS
 * Component({
 *   data: {
 *     tabBarStyle: layoutUtil.getTabBarStyle(),
 *     contentStyle: layoutUtil.getTabBarContentStyle()
 *   }
 * })
 * ```
 * 
 * 4. 特殊场景
 * ```typescript
 * // 无导航栏布局
 * const fullPageStyle = layoutUtil.getContentWithoutNavStyle();
 * 
 * // 无底部导航布局
 * const noTabBarStyle = layoutUtil.getContentWithoutTabBarStyle();
 * 
 * // 获取布局信息
 * const info = layoutUtil.getLayoutInfo();
 * console.log('状态栏高度：', info.statusBarHeight);
 * console.log('导航栏高度：', info.navigationHeight);
 * ```
 * 
 * 5. 安全区域适配
 * ```typescript
 * // 容器 WXML
 * <view style="{{containerStyle}}">
 *   <view class="safe-content">安全区域内的内容</view>
 * </view>
 * 
 * // 容器 TS
 * Component({
 *   data: {
 *     containerStyle: layoutUtil.getSafeAreaContainerStyle()
 *   }
 * })
 * ```
 * 
 * 6. 胶囊按钮周围布局
 * ```typescript
 * // 导航栏 WXML
 * <view class="nav-bar" style="{{navStyle}}">
 *   <view style="{{leftStyle}}">返回</view>
 *   <view style="{{rightStyle}}">菜单</view>
 * </view>
 * 
 * // 导航栏 TS
 * Component({
 *   data: {
 *     navStyle: layoutUtil.getNavigationStyle(),
 *     leftStyle: layoutUtil.getMenuButtonAdjacentStyle('left'),
 *     rightStyle: layoutUtil.getMenuButtonAdjacentStyle('right')
 *   }
 * })
 * ```
 * 
 * 7. 滚动区域优化
 * ```typescript
 * // 列表页面 TS
 * Page({
 *   onScroll(e) {
 *     const { scrollTop } = e.detail;
 *     const itemHeight = 100; // 列表项高度
 *     
 *     // 检查元素是否在可视区域内
 *     const isVisible = layoutUtil.isInViewport(scrollTop, itemHeight);
 *     if (isVisible) {
 *       // 执行元素可见时的逻辑
 *     }
 *   }
 * })
 * ```
 */

// layout.ts

interface MenuButtonBounding {
  width: number;
  height: number;
  top: number;
  right: number;
  bottom: number;
  left: number;
}

export interface ILayoutInfo {
  statusBarHeight: number;
  navigationHeight: number;
  navBarHeight: number;
  menuButtonHeight: number;
  menuButtonTop: number;
  tabBarHeight: number;
  safeAreaBottom: number;
  menuButtonBounding: MenuButtonBounding;
  systemInfo: {
    windowWidth: number;
    windowHeight: number;
    screenWidth: number;
    screenHeight: number;
    statusBarHeight: number;
    safeArea: WechatMiniprogram.SafeArea;
    platform: string;
    brand: string;
    model: string;
    system: string;
    version: string;
  };
}
export interface myinfo {
  any: any
  systemInfo: {any: any};
}

interface WxNewAPI {
  getWindowInfo(): WxWindowInfo;
  getDeviceInfo(): WxDeviceInfo;
  getAppBaseInfo(): WxAppBaseInfo;
}

interface WxWindowInfo {
  pixelRatio: number;
  windowWidth: number;
  windowHeight: number;
  screenWidth: number;
  screenHeight: number;
  statusBarHeight: number;
  safeArea: WechatMiniprogram.SafeArea;
}

interface WxDeviceInfo {
  brand: string;
  model: string;
  system: string;
  platform: string;
}

interface WxAppBaseInfo {
  version: string;
  platform: string;
}

declare global {
  interface Wx extends WxNewAPI {}
}

class LayoutUtil {
  private static instance: LayoutUtil;
  
  // 基础尺寸配置
  private readonly DEFAULT_TAB_BAR_HEIGHT: number;
  private readonly MIN_SAFE_AREA_BOTTOM: number;
  private readonly DEFAULT_STATUS_BAR_HEIGHT: number;
  private readonly DEFAULT_NAV_BAR_HEIGHT: number;
  private readonly DEFAULT_MENU_BUTTON_HEIGHT: number;
  private readonly DEFAULT_MENU_BUTTON_TOP: number;

  // 布局相关属性
  private _statusBarHeight: number;
  private _navigationHeight: number;
  private _navBarHeight: number;
  private _menuButtonHeight: number;
  private _menuButtonTop: number;
  private _tabBarContentHeight: number;
  private _tabBarHeight: number;
  private _safeAreaBottom: number;
  private _menuButtonBounding: MenuButtonBounding | null;
  private _systemInfo: ILayoutInfo['systemInfo'] | null;

  private constructor() {
    // 初始化常量
    this.DEFAULT_TAB_BAR_HEIGHT = 98;
    this.MIN_SAFE_AREA_BOTTOM = 34;
    this.DEFAULT_STATUS_BAR_HEIGHT = 20;
    this.DEFAULT_NAV_BAR_HEIGHT = 88;
    this.DEFAULT_MENU_BUTTON_HEIGHT = 32;
    this.DEFAULT_MENU_BUTTON_TOP = 24;

    // 初始化变量
    this._statusBarHeight = 0;
    this._navigationHeight = 0;
    this._navBarHeight = 0;
    this._menuButtonHeight = 0;
    this._menuButtonTop = 0;
    this._tabBarContentHeight = this.DEFAULT_TAB_BAR_HEIGHT;
    this._tabBarHeight = 0;
    this._safeAreaBottom = 0;
    this._menuButtonBounding = null;
    this._systemInfo = null;

    this.initLayoutInfo();
  }

  public static getInstance(): LayoutUtil {
    if (!LayoutUtil.instance) {
      LayoutUtil.instance = new LayoutUtil();
    }
    return LayoutUtil.instance;
  }

  private initLayoutInfo() {
    try {
      // 1. 获取系统信息
      this.initSystemInfo();

      // 2. 获取胶囊按钮信息
      const menuButtonBounding = wx.getMenuButtonBoundingClientRect();
      
      if (!this._systemInfo || !menuButtonBounding) {
        throw new Error('获取系统信息或胶囊按钮信息失败');
      }

      // 3. 计算导航栏相关高度
      this._menuButtonBounding = menuButtonBounding;
      this._statusBarHeight = this._systemInfo.statusBarHeight;
      this._menuButtonHeight = menuButtonBounding.height;
      this._menuButtonTop = menuButtonBounding.top;
      
      // 计算自定义导航栏高度
      // 公式: 导航栏总高度 = 状态栏高度 + (胶囊顶部距离 - 状态栏高度) * 2 + 胶囊高度
      const capsuleGap = (menuButtonBounding.top - this._systemInfo.statusBarHeight);
      this._navigationHeight = this._systemInfo.statusBarHeight + capsuleGap * 2 + menuButtonBounding.height;
      this._navBarHeight = this._navigationHeight - this._statusBarHeight; // 纯导航内容高度
      
      // 4. 计算底部区域高度
      // 获取安全区域底部高度
      this._safeAreaBottom = Math.max(
        this._systemInfo.screenHeight - this._systemInfo.safeArea.bottom,
        this.MIN_SAFE_AREA_BOTTOM
      );

      // 特殊机型兼容
      if (this._systemInfo.brand.toLowerCase() === 'xiaomi') {
        this._safeAreaBottom = Math.max(this._safeAreaBottom, this.MIN_SAFE_AREA_BOTTOM);
      }

      // 计算底部导航总高度
      this._tabBarHeight = this._tabBarContentHeight + this._safeAreaBottom;

      // 调试信息输出
      this.logLayoutInfo();
    } catch (error) {
      console.error('初始化布局信息失败：', error);
      this.setDefaultValues();
    }
  }

  private setDefaultValues() {
    this._statusBarHeight = this.DEFAULT_STATUS_BAR_HEIGHT;
    this._navigationHeight = this.DEFAULT_NAV_BAR_HEIGHT;
    this._navBarHeight = this.DEFAULT_NAV_BAR_HEIGHT - this.DEFAULT_STATUS_BAR_HEIGHT;
    this._menuButtonHeight = this.DEFAULT_MENU_BUTTON_HEIGHT;
    this._menuButtonTop = this.DEFAULT_MENU_BUTTON_TOP;
    this._safeAreaBottom = this.MIN_SAFE_AREA_BOTTOM;
    this._tabBarContentHeight = this.DEFAULT_TAB_BAR_HEIGHT;
    this._tabBarHeight = this._tabBarContentHeight + this._safeAreaBottom;
  }

  private fallbackToOldAPI() {
    console.warn('当前基础库版本过低或API不可用，使用旧版API');
    const systemInfo = wx.getSystemInfoSync();
    
    if (!systemInfo) {
      throw new Error('获取系统信息失败');
    }

    this._systemInfo = {
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      statusBarHeight: systemInfo.statusBarHeight,
      safeArea: systemInfo.safeArea,
      platform: systemInfo.platform,
      brand: systemInfo.brand,
      model: systemInfo.model,
      system: systemInfo.system,
      version: systemInfo.version
    };
  }

  /**
   * 获取内容区域的完整样式
   * 确保内容区域精确填充在导航栏和底部导航之间,底部导航包括安全区域
   */
  public getContentStyle(): string {
    const style = [
      `top: ${this._navigationHeight}px`,
      `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarHeight}px)`,
      'position: absolute',
      'left: 0',
      'right: 0',
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }
  /**
   * 获取内容区域的完整样式 (底部自定义导航栏展开状态)
   * 确保内容区域精确填充在导航栏和底部导航之间，不包括底部导航安全区域
   */
  public getContentStyle_nosafeArea(): string {
    const style = [
      `top: ${this._navigationHeight}px`,
      `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px)`,
      'position: absolute',
      'left: 0',
      'right: 0',
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }
  /**
   * 获取内容区域的完整样式 (底部自定义导航栏收缩状态)
   * 确保内容区域精确填充在导航栏和底部导航之间，不包括底部导航安全区域
   */
  public getContentStyle_nosafeArea_collapsed(): string {
    const style = [
      `top: ${this._navigationHeight}px`,
      `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px)`,
      'position: absolute',
      'left: 0',
      'right: 0',
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }

  /**
   * 裁剪框 wxmy-cropper - 获取内容区域的完整样式
   * 确保内容区域精确填充在导航栏和底部导航之间，不包括底部导航安全区域
   * top: 87px; height: calc(100vh - 87px - 132px - 0px);
   */
  public getContentStyle_cropper(): string {
    const style = [
      `top: ${this._navigationHeight}px`,
      `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px -20px)`,
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }



  /**
   * 获取内容区域的顶部样式
   * 用于只需要设置顶部偏移的场景
   */
  public getContentStyleTop(): string {
    return `top: ${this._navigationHeight}px;`;
  }

  /**
   * 获取内容区域的底部样式
   * 用于只需要设置底部偏移的场景
   */
  public getContentStyleBottom(): string {
    return `bottom: ${this._tabBarHeight}px;`;
  }

  /**
   * 获取内容区域不包含导航栏的样式
   * 用于不需要顶部导航栏的场景
   */
  public getContentWithoutNavStyle(): string {
    const style = [
      'top: 0',
      `height: calc(100vh - ${this._tabBarHeight}px)`,
      'position: absolute',
      'left: 0',
      'right: 0',
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }

  /**
   * 获取内容区域不包含底部导航的样式
   * 用于不需要底部导航栏的场景
   */
  public getContentWithoutTabBarStyle(): string {
    const style = [
      `top: ${this._navigationHeight}px`,
      `height: calc(100vh - ${this._navigationHeight}px)`,
      'position: absolute',
      'left: 0',
      'right: 0',
      'overflow-y: auto',
      '-webkit-overflow-scrolling: touch'
    ];
    return style.join('; ') + ';';
  }

  /**
   * 获取底部导航栏完整样式
   * 包含内容区域和安全区域的精确高度
   */
  public getTabBarStyle(): string {
    const style = [
      `height: ${this._tabBarContentHeight}px`,
      `padding-bottom: ${this._safeAreaBottom}px`,
      'position: fixed',
      'left: 0',
      'right: 0',
      'bottom: 0',
      'z-index: 100'
    ];
    return style.join('; ') + ';';
  }

  

  /**
   * 获取底部导航内容区域样式
   * 不包含安全区域的纯内容区域高度
   */
  public getTabBarContentStyle(): string {
    return `height: ${this._tabBarContentHeight}px;`;
  }

  /**
   * 获取导航栏完整样式
   * 包含状态栏和导航内容的精确高度
   */
  public getNavigationStyle(): string {
    const style = [
      `height: ${this._navigationHeight}px`,
      'position: fixed',
      'top: 0',
      'left: 0',
      'right: 0',
      'z-index: 100'
    ];
    return style.join('; ') + ';';
  }

  /**
   * 获取导航栏内容区域样式
   * 不包含状态栏的纯导航内容区域
   */
  public getNavigationContentStyle(): string {
    const style = [
      `height: ${this._navBarHeight}px`,
      `margin-top: ${this._statusBarHeight}px`,
      'position: relative'
    ];
    return style.join('; ') + ';';
  }

  /**
   * 获取可视区域宽度（不包括导航栏等系统装饰）
   * 可视区域宽度（单位：px）
   */
  public windowWidth(): string {
    return `width: ${this._systemInfo && this._systemInfo.windowWidth || 0}px`
  }

  /**
   * 获取屏幕物理宽度
   * 屏幕物理宽度（单位：px）
   */
  public screenWidth(): string {
    return `width: ${this._systemInfo && this._systemInfo.screenWidth || 0}px`
  }
  
  private logLayoutInfo() {
    // console.log('布局信息：', {
    //   statusBarHeight: this._statusBarHeight,
    //   navigationHeight: this._navigationHeight,
    //   navBarHeight: this._navBarHeight,
    //   menuButtonTop: this._menuButtonTop,
    //   menuButtonHeight: this._menuButtonHeight,
    //   safeAreaBottom: this._safeAreaBottom,
    //   tabBarContentHeight: this._tabBarContentHeight,
    //   tabBarHeight: this._tabBarHeight,
    //   systemInfo: this._systemInfo
    // });
  }

  private initSystemInfo() {
    try {
      let windowInfo: WxWindowInfo;
      let deviceInfo: WxDeviceInfo;
      let appBaseInfo: WxAppBaseInfo;

      // 检查是否支持新API
      const canUseNewAPI = wx.canIUse('getWindowInfo') && 
                          wx.canIUse('getDeviceInfo') && 
                          wx.canIUse('getAppBaseInfo');

      if (canUseNewAPI) {
        // 使用新API
        try {
          windowInfo = (wx as any).getWindowInfo();
          deviceInfo = (wx as any).getDeviceInfo();
          appBaseInfo = (wx as any).getAppBaseInfo();
          
          if (!windowInfo || !deviceInfo || !appBaseInfo) {
            throw new Error('获取系统信息失败');
          }

          this._systemInfo = {
            windowWidth: windowInfo.windowWidth,
            windowHeight: windowInfo.windowHeight,
            screenWidth: windowInfo.screenWidth,
            screenHeight: windowInfo.screenHeight,
            statusBarHeight: windowInfo.statusBarHeight,
            safeArea: windowInfo.safeArea,
            platform: appBaseInfo.platform,
            brand: deviceInfo.brand,
            model: deviceInfo.model,
            system: deviceInfo.system,
            version: appBaseInfo.version
          };
        } catch (e) {
          console.warn('新API调用失败，回退到旧API', e);
          this.fallbackToOldAPI();
        }
      } else {
        this.fallbackToOldAPI();
      }
    } catch (error) {
      console.error('初始化系统信息失败：', error);
      this.setDefaultValues();
    }
  }

  /**
   * 获取所有布局信息
   * @returns 包含所有布局参数的对象
   */
  public getLayoutInfo(): ILayoutInfo {
    return {
      statusBarHeight: this._statusBarHeight,
      navigationHeight: this._navigationHeight,
      navBarHeight: this._navBarHeight,
      menuButtonHeight: this._menuButtonHeight,
      menuButtonTop: this._menuButtonTop,
      tabBarHeight: this._tabBarHeight,
      safeAreaBottom: this._safeAreaBottom,
      menuButtonBounding: this._menuButtonBounding as MenuButtonBounding,
      systemInfo: this._systemInfo as ILayoutInfo['systemInfo']
    };
  }
/**
   * 自定义输出内容
   */
  public All_Size(): myinfo {
    const safeDistance = 30;
    const tabbat_diy_height = 75;//增加底部自定义导航栏伸缩高度
    return {
      zheight: `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px)`,//安全区域底部高度 工作室图鉴
      zheight2: `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px - 100px)`,//安全区域底部高度 招募详情
      zheight3: `height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px +20px)`,//安全区域底部高度 招募详情浮窗
      systemInfo: this._systemInfo as myinfo['systemInfo'],
      login:`top: ${this._navigationHeight}px;height: calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px);-webkit-overflow-scrolling: touch`,
      
      layoutStyle:`top:${this._navigationHeight}px;height:calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px);position: absolute;left:0;right:0;overflow-y:auto;-webkit-overflow-scrolling:touch`,//底部自定义导航栏展开状态
      
      layoutStyle_cropper:`top:${this._navigationHeight}px;height:calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px + ${tabbat_diy_height}px);position: absolute;left:0;right:0;overflow-y:auto;-webkit-overflow-scrolling:touch`,//底部自定义导航栏收缩状态
      
      layoutStyle_noposition:`top:${this._navigationHeight}px;height:calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px);overflow-y:auto;-webkit-overflow-scrolling:touch`,//底部自定义导航栏展开状态

      layoutStyle_cropper_noposition:`top:${this._navigationHeight}px;height:calc(100vh - ${this._navigationHeight}px - ${this._tabBarContentHeight}px + ${tabbat_diy_height}px);overflow-y:auto;-webkit-overflow-scrolling:touch`//底部自定义导航栏收缩状态
    };
  }

  // Getter 方法
  public get statusBarHeight(): number { return this._statusBarHeight; }
  public get navigationHeight(): number { return this._navigationHeight; }
  public get navBarHeight(): number { return this._navBarHeight; }
  public get menuButtonHeight(): number { return this._menuButtonHeight; }
  public get menuButtonTop(): number { return this._menuButtonTop; }
  public get tabBarHeight(): number { return this._tabBarHeight; }
  public get safeAreaBottom(): number { return this._safeAreaBottom; }
  public get tabBarContentHeight(): number { return this._tabBarContentHeight; }

  
}

const layoutUtil = LayoutUtil.getInstance();

export {
  layoutUtil
}; 
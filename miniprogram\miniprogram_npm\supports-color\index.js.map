{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar argv = process.argv;\n\nvar terminator = argv.indexOf('--');\nvar hasFlag = function (flag) {\n\tflag = '--' + flag;\n\tvar pos = argv.indexOf(flag);\n\treturn pos !== -1 && (terminator !== -1 ? pos < terminator : true);\n};\n\nmodule.exports = (function () {\n\tif ('FORCE_COLOR' in process.env) {\n\t\treturn true;\n\t}\n\n\tif (hasFlag('no-color') ||\n\t\thasFlag('no-colors') ||\n\t\thasFlag('color=false')) {\n\t\treturn false;\n\t}\n\n\tif (hasFlag('color') ||\n\t\thasFlag('colors') ||\n\t\thasFlag('color=true') ||\n\t\thasFlag('color=always')) {\n\t\treturn true;\n\t}\n\n\tif (process.stdout && !process.stdout.isTTY) {\n\t\treturn false;\n\t}\n\n\tif (process.platform === 'win32') {\n\t\treturn true;\n\t}\n\n\tif ('COLORTERM' in process.env) {\n\t\treturn true;\n\t}\n\n\tif (process.env.TERM === 'dumb') {\n\t\treturn false;\n\t}\n\n\tif (/^screen|^xterm|^vt100|color|ansi|cygwin|linux/i.test(process.env.TERM)) {\n\t\treturn true;\n\t}\n\n\treturn false;\n})();\n"]}
.ai-analysis-container {
  margin: 0 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  /* padding: 40rpx; */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.button-group {
  display: flex;
  gap: 20rpx;
  width: 100%;
  justify-content: center;
  margin-top: 20rpx;
}

.upload-btn, .reanalyze-btn {
  flex: 1;
  max-width: 300rpx;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
}

.reanalyze-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.2);
}

.upload-btn::after, .reanalyze-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

.upload-btn:active, .reanalyze-btn:active {
  transform: scale(0.98);
}

.reanalyze-btn:active {
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.1);
}

.preview-image {
  width: 100%;
  max-height: 450rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.progress-container {
  width: 100%;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx;
  margin: 24rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.progress-text {
  font-size: 26rpx;
  color: #555;
  text-align: center;
  margin-top: 12rpx;
  font-weight: 500;
}

.loading {
  text-align: center;
  color: #555;
  padding: 24rpx 0;
  font-size: 28rpx;
  font-weight: 500;
}

.result-section {
  margin-top: 40rpx;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  color: #333;
  text-align: center;
  position: relative;
  padding-bottom: 20rpx;
}

.result-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: 2rpx;
}

.result-item {
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: slideIn 0.5s ease-out;
  animation-fill-mode: both;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.result-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
}

.subtitle {
  font-size: 30rpx;
  color: #4CAF50;
  margin-bottom: 16rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.subtitle::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(180deg, #4CAF50, #45a049);
  margin-right: 12rpx;
  border-radius: 4rpx;
}

.content {
  font-size: 28rpx;
  color: #444;
  line-height: 1.8;
  white-space: pre-wrap;
  word-break: break-all;
  letter-spacing: 0.5rpx;
}

.content text {
  display: block;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 24rpx;
}

.content text::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #4CAF50;
}

.content-scroll {
  padding: 20rpx;
  box-sizing: border-box;
  height: 100%;
  transition: all 0.3s ease;
}

.content-scroll.no-scroll {
  overflow: hidden;
}

/* 评分条通用样式 */
.score-container {
  margin-top: 20rpx;
}

.score-bar {
  width: 100%;
  height: 24rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.score-fill {
  height: 100%;
  border-radius: 12rpx;
  transition: width 1s ease-out;
}

.score-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-align: right;
}

/* 原创度评分条 */
.originality-fill {
  background: linear-gradient(90deg, #4CAF50, #45a049);
}

/* 精致度评分条 */
.refinement-fill {
  background: linear-gradient(90deg, #2196F3, #1976D2);
}

/* AI概率评分条 */
.ai-fill {
  background: linear-gradient(90deg, #FF9800, #F57C00);
}

/* 为每个result-item添加延迟动画 */
.result-item:nth-child(1) { animation-delay: 0.1s; }
.result-item:nth-child(2) { animation-delay: 0.2s; }
.result-item:nth-child(3) { animation-delay: 0.3s; }
.result-item:nth-child(4) { animation-delay: 0.4s; }
.result-item:nth-child(5) { animation-delay: 0.5s; }
.result-item:nth-child(6) { animation-delay: 0.6s; }

/* 评分区域样式 */
.scores-section {
  margin: 30rpx 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.score-items {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  gap: 20rpx;
}

.score-item {
  flex: 1;
  background: rgba(248, 248, 248, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.score-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.2), transparent);
}

.score-item.active {
  background: #e8f5e9;
}

.score-item.active::before {
  background: linear-gradient(90deg, transparent, #4CAF50, transparent);
}

.score-main {
  text-align: center;
}

.score-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.score-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  font-family: 'DIN Alternate', Arial, sans-serif;
  line-height: 1.2;
  background: linear-gradient(135deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.score-item[data-type="originality"].active .score-value {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.score-item[data-type="refinement"].active .score-value {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 分析文本区域样式 */
.analysis-section {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.95);
}

.analysis-text {
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  animation: slideDown 0.3s ease-out;
}

.analysis-content {
  white-space: pre-wrap;
  word-break: break-all;
  padding: 20rpx;
  background: rgba(248, 248, 248, 0.5);
  border-radius: 12rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条美化 */
progress {
  border-radius: 999rpx;
  overflow: hidden;
  height: 30rpx;
}

/* 自定义进度条颜色 */
progress .wx-progress-inner-bar {
  border-radius: 999rpx;
  transition: all 0.3s ease;
}

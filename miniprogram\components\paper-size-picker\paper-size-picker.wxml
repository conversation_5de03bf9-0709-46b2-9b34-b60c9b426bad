<!-- 纸张选择器组件 - 紧凑版 -->
<view class="paper-picker">
  <!-- 触发按钮 -->
  <view class="paper-trigger" bindtap="showPicker">
    <view class="paper-preview-wrapper">
      <text class="paper-icon">📄</text>
      <text class="paper-value">{{displayButtonText}}</text>
    </view>
  </view>

  <!-- 弹出层 -->
  <block wx:if="{{visible}}">
    <!-- 遮罩层 -->
    <view 
      class="popup-mask {{visible ? 'show' : ''}}" 
      style="z-index: {{zIndex}}"
      catch:tap="onClose"
      catch:touchmove="preventTouchMove"
    ></view>
    
    <!-- 弹出内容 -->
    <view 
      class="popup-content {{visible ? 'show' : ''}}" 
      style="z-index: {{zIndex + 1}}"
      catch:tap="preventBubble"
      catch:touchmove="preventTouchMove"
    >
      <!-- 顶部标题栏 -->
      <view class="popup-header">
        <!-- 搜索框 -->
      <view class="search-wrapper">
        <input 
          class="search-input" 
          placeholder="搜索纸张名称或尺寸" 
          value="{{searchKeyword}}" 
          bindinput="onSearchInput" 
          confirm-type="search"
        />
      </view>
        <!-- <text class="popup-title">选择纸张尺寸</text> -->
        <view class="refresh-btn" catch:tap="onRefresh">↻</view>
        <text class="close-btn" catch:tap="onClose">×</text>
      </view>

      <!-- 即时搜索结果列表 -->
      <block wx:if="{{searchKeyword && instantSearchResults.length > 0}}">
        <scroll-view 
          scroll-y
          class="instant-search-list"
          enhanced="{{true}}"
          bounces="{{false}}"
        >
          <view 
            wx:for="{{instantSearchResults}}" 
            wx:key="size.name + size.width + size.height"
            class="instant-search-item"
            bindtap="onInstantSearchResultSelect"
            data-item="{{item}}"
          >
            <view class="item-info">
              <view class="paper-name">{{item.size.name}}</view>
              <view class="paper-size">{{item.size.width}}×{{item.size.height}}{{item.size.unit}}</view>
            </view>
            <view class="item-category">{{item.categoryName}}分类</view>
          </view>
        </scroll-view>
      </block>

      <!-- 分类标签 - 横向滚动 -->
      <scroll-view scroll-x class="category-tabs" enhanced="{{true}}" show-scrollbar="{{false}}">
        <view class="tabs-content">
          <view 
            wx:for="{{categories}}" 
            wx:key="id"
            class="tab-item {{currentCategory === item.name ? 'active' : ''}}"
            bindtap="switchCategory"
            data-category="{{item.name}}"
          >
            {{item.name}}
          </view>
        </view>
      </scroll-view>

      <!-- 纸张列表 -->
      <view 
        class="paper-list"
        wx:if="{{!searchKeyword}}"
      >
        <view class="paper-container">
          <!-- 横版尺寸 -->
          <view class="paper-column">
            <view class="column-title">
              <text class="title-text">横版尺寸</text>
            </view>
            <scroll-view 
              scroll-y
              class="paper-column-scroll"
              enhanced="{{true}}"
              bounces="{{false}}"
            >
              <view 
                wx:for="{{searchKeyword ? filteredLandscapeSizes : landscapeSizes}}" 
                wx:key="id"
                class="paper-item {{selectedSize.name === item.name ? 'active' : ''}}"
                bindtap="selectSize"
                data-size="{{item}}"
              >
                <view class="paper-name">{{item.name}}</view>
                <view class="paper-size">{{item.width}}×{{item.height}}{{item.unit}}</view>
              </view>
            </scroll-view>
          </view>

          <!-- 竖版尺寸 -->
          <view class="paper-column">
            <view class="column-title">
              <text class="title-text">竖版尺寸</text>
            </view>
            <scroll-view 
              scroll-y
              class="paper-column-scroll"
              enhanced="{{true}}"
              bounces="{{false}}"
            >
              <view 
                wx:for="{{searchKeyword ? filteredPortraitSizes : portraitSizes}}" 
                wx:key="id"
                class="paper-item {{selectedSize.name === item.name ? 'active' : ''}}"
                bindtap="selectSize"
                data-size="{{item}}"
              >
                <view class="paper-name">{{item.name}}</view>
                <view class="paper-size">{{item.width}}×{{item.height}}{{item.unit}}</view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>
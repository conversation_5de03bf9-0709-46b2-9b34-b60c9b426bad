// index.ts
import { layoutUtil } from '../../../../utils/layout';
import ai from '../../../../utils/ai';
const { pollinations, zhipu } = ai;
import eventBus from '../../../../utils/eventBus';
Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    navMenus: [],
    tools: [],
    loading: false,
    isEmpty: false,
    page: 1,
    limit: 5,
    total: 0,
    lastPage: 1,
    tasks: [],
    limitOptions: [10, 15, 30],
    allTasks: []
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      this.loadTasks(1);
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '工具箱',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '工具箱',
        path: '/pages/tool/test/test'
      };
    },

    async loadTasks(page: number) {
      this.setData({ loading: true, page });
      try {
        const userInfo = wx.getStorageSync('userInfo');
        const user_id = (userInfo && userInfo.user_id) || (userInfo && userInfo.id);
        if (!user_id) {
          wx.showToast({ title: '未获取到用户信息', icon: 'none' });
          this.setData({ loading: false });
          return;
        }
        const { limit, allTasks } = this.data;
        // 优先从allTasks取数据
        const startIdx = (page - 1) * limit;
        const endIdx = startIdx + limit;
        if (allTasks.length >= endIdx) {
          // 已缓存，直接取
          let tasks = allTasks.slice(startIdx, endIdx);
          // 确保从缓存取出的任务的图片 Base64 已转换为路径 (如果之前未转换)
          tasks = await Promise.all(tasks.map(item => this.convertTaskImageToBase64Path(item)));
          this.setData({
            tasks,
            loading: false,
            isEmpty: !tasks.length
          });
          return;
        }
        // 未缓存，发起请求
        const result = await pollinations.getTasks({ user_id, page, limit });
        console.log(result);
        
        // 处理获取到的原始任务数据，将 Base64 转换为临时文件路径
        const rawTasks = result.data || [];
        const processedRawTasks = await Promise.all(rawTasks.map(item => this.convertTaskImageToBase64Path(item)));

        const pageTasks = processedRawTasks.map(item => {
          let parameters = {};
          try {
            parameters = JSON.parse(item.parameters);
          } catch (e) {}
          // 这里不再需要根据当前页移除Base64，因为convertTaskImageToBase64Path已经处理了
          return { ...item, parameters };
        });

        // 合并到allTasks
        let newAllTasks = allTasks.slice();
        newAllTasks.splice(startIdx, pageTasks.length, ...pageTasks);
        this.setData({
          tasks: pageTasks,
          total: result.total || 0,
          page: result.current_page || page,
          limit: result.per_page || limit,
          lastPage: result.last_page || 1,
          loading: false,
          isEmpty: !(result.data && result.data.length),
          allTasks: newAllTasks
        });
        // 加载第一页后，后台加载剩余页
        if (page === 1 && result.last_page > 1) {
          this.backgroundLoadAllTasks(user_id, result.last_page, limit, newAllTasks);
        }
      } catch (e) {
        this.setData({ loading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        console.error('获取任务列表失败:', e);
      }
    },

    async backgroundLoadAllTasks(user_id, lastPage, limit, allTasks) {
      for (let p = 2; p <= lastPage; p++) {
        try {
          const result = await pollinations.getTasks({ user_id, page: p, limit });
          
          // 处理获取到的原始任务数据，将 Base64 转换为临时文件路径
          const rawTasks = result.data || [];
          const processedRawTasks = await Promise.all(rawTasks.map(item => this.convertTaskImageToBase64Path(item)));

          const pageTasks = processedRawTasks.map(item => {
            let parameters = {};
            try {
              parameters = JSON.parse(item.parameters);
            } catch (e) {}
            // 这里不再需要移除Base64，因为convertTaskImageToBase64Path已经处理了
            return { ...item, parameters };
          });

          const startIdx = (p - 1) * limit;
          allTasks.splice(startIdx, pageTasks.length, ...pageTasks);
          this.setData({ allTasks: allTasks.slice() });
        } catch (e) {
          console.error('后台加载第' + p + '页失败:', e);
        }
      }
    },

    onPageChange(e) {
      const page = e.currentTarget.dataset.page;
      if (page && page !== this.data.page) {
        this.loadTasks(page);
      }
    },

    onLimitChange(e) {
      const value = this.data.limitOptions[e.detail.value];
      this.setData({ limit: value, allTasks: [] });
      this.loadTasks(1);
    },

    async convertTaskImageToBase64Path(item) {
      // 如果 item.result.image_base64 已经是文件路径，则不再处理
      if (item.status === 'completed' && item.result && item.result.image_base64 && !item.result.image_base64.startsWith(wx.env.USER_DATA_PATH)) {
        const base64Data = item.result.image_base64;
        const fs = wx.getFileSystemManager();
        const filePath = `${wx.env.USER_DATA_PATH}/temp_image_${item.id}.png`;
        return new Promise((resolve) => {
          fs.writeFile({
            filePath: filePath,
            data: base64Data,
            encoding: 'base64',
            success: () => {
              const newItem = { ...item };
              // 确保 result 对象存在
              if (!newItem.result) {
                newItem.result = {};
              }
              newItem.result.image_base64 = filePath; // 将 Base64 替换为临时文件路径
              resolve(newItem);
            },
            fail: (err) => {
              console.error('写入临时文件失败:', err);
              resolve(item); // 写入失败也继续，不影响其他任务显示
            }
          });
        });
      }
      return Promise.resolve(item); // 没有 Base64 数据、未完成的任务或已是文件路径的任务直接返回
    }
  }
});

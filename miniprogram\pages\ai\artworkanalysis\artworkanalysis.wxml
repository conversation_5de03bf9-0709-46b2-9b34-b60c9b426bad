<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="作品分析" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="ai-analysis-container">
        <view class="upload-section">
          
          <view wx:if="{{analyzing}}" class="progress-container">
            <progress 
              percent="{{progressPercent}}" 
              stroke-width="10" 
              color="#4CAF50" 
              active="true" 
              duration="20"
              show-info="true"
            />
            <view class="progress-text">{{progressText}}</view>
          </view>
          <image 
            wx:if="{{tempImagePath}}" 
            src="{{tempImagePath}}" 
            mode="aspectFit" 
            class="preview-image"
            binderror="handleImageError"
            webp="{{false}}"
          ></image>
          <view class="button-group">
            <button class="upload-btn" bindtap="chooseImage">上传作品图片</button>
            <button wx:if="{{tempImagePath && !analyzing}}" class="reanalyze-btn" bindtap="analyzeImage">重新分析</button>
          </view>
        </view>
        
        
        <view wx:if="{{analysisResult}}" class="result-section">
          <view class="result-title">分析结果</view>

          <!-- 评分区域 -->
          <view class="scores-section">
            <view class="score-items">
              <!-- 原创度评分 -->
              <view class="score-item {{activeAnalysis === 'originality' ? 'active' : ''}}" bindtap="toggleAnalysis" data-type="originality">
                <view class="score-main">
                  <view class="score-label">原创度评估</view>
                  <view class="score-value">{{analysisResult.originalityScore}}%</view>
                </view>
              </view>

              <!-- 精致度评分 -->
              <view class="score-item {{activeAnalysis === 'refinement' ? 'active' : ''}}" bindtap="toggleAnalysis" data-type="refinement">
                <view class="score-main">
                  <view class="score-label">精致度评估</view>
                  <view class="score-value">{{analysisResult.refinementScore}}%</view>
                </view>
              </view>
            </view>

            <!-- 分析文本区域 -->
            <view class="analysis-section">
              <view class="analysis-text {{activeAnalysis === 'originality' ? 'show' : ''}}" wx:if="{{activeAnalysis === 'originality'}}">
                <view class="analysis-content">{{analysisResult.originalityAnalysis}}</view>
              </view>
              <view class="analysis-text {{activeAnalysis === 'refinement' ? 'show' : ''}}" wx:if="{{activeAnalysis === 'refinement'}}">
                <view class="analysis-content">{{analysisResult.refinementAnalysis}}</view>
              </view>
            </view>
          </view>

          <view class="result-item">
            <view class="subtitle">作品优点</view>
            <view class="content">{{analysisResult.advantages}}</view>
          </view>
          <view class="result-item">
            <view class="subtitle">需要改进</view>
            <view class="content">{{analysisResult.improvements}}</view>
          </view>
          <view class="result-item">
            <view class="subtitle">修改建议</view>
            <view class="content">{{analysisResult.suggestions}}</view>
          </view>
        </view>
      </view>
      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}artworkanalysis.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">作品分析</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">原创度评估</text>
                    <text class="feature-desc">专业分析作品的原创性和创新程度</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">精致度评分</text>
                    <text class="feature-desc">全面评估作品的完成度和细节表现</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">优点分析</text>
                    <text class="feature-desc">详细指出作品的亮点和成功之处</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">改进建议</text>
                    <text class="feature-desc">提供专业的修改意见和具体建议</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">上传作品</text>
                    <text class="step-desc">选择需要分析的绘画作品图片</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">等待分析</text>
                    <text class="step-desc">AI系统自动分析作品的各个方面</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">查看结果</text>
                    <text class="step-desc">获取详细的分析报告和评分</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">参考改进</text>
                    <text class="step-desc">根据建议对作品进行优化</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">清晰图片</text>
                  <text class="tip-text">上传高清晰度的作品图片以获得更准确的分析</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">完整构图</text>
                  <text class="tip-text">确保上传完整的作品，避免局部截图</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">多次分析</text>
                  <text class="tip-text">可以多次分析同一作品，获取更全面的建议</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">对比提升</text>
                  <text class="tip-text">对比修改前后的分析结果，追踪进步</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>




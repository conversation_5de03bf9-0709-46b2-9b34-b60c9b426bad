<wxs src="../../utils/constants.wxs" module="constants" />

<view class="fp-font-picker" style="{{styleVars}}">
  <!-- 触发按钮 -->
   
  <view class="fp-trigger-button {{visible ? 'fp-active' : ''}}" bindtap="showPicker">
    <block wx:if="{{selectedFont}}">
      <view class="fp-selected-font">
        <view class="fp-font-content">
          <text class="fp-font-name">{{selectedFont.name}}</text>
          <text class="fp-font-tags">{{selectedFont.tags}}</text>
        </view>
        <view class="fp-select-area">
          <text class="fp-select-text">更换字体</text>
          <view class="fp-arrow"></view>
        </view>
      </view>
    </block>
    <view class="fp-placeholder" wx:else>
      <text class="fp-placeholder-text">选择字体</text>
      <view class="fp-select-area">
        <text class="fp-select-text">点击选择</text>
        <view class="fp-arrow"></view>
      </view>
    </view>
  </view>

  <!-- 弹出层 -->
  <view class="fp-popup-mask {{visible ? 'fp-show' : ''}}" bindtap="onMaskClick" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}"></view>
  <view class="fp-popup-content {{visible ? 'fp-show' : ''}}" style="bottom:10px">
    <view class="fp-popup-header">
      <text class="fp-popup-title">选择字体</text>
      <view class="fp-header-actions">
        <view class="fp-refresh-btn {{isLoading ? 'fp-loading' : ''}}" bindtap="refreshFontsList">
          <image src="{{constants.STATIC_URL.ICON}}refresh.svg" mode="aspectFit"></image>
        </view>
        <view class="fp-popup-close" bindtap="hidePicker">
          <text class="fp-close-icon">×</text>
        </view>
      </view>
    </view>

    <!-- 分类切换 -->
    <scroll-view 
      scroll-x 
      class="fp-category-tabs" 
      enhanced="{{true}}" 
      show-scrollbar="{{false}}"
    >
      <view class="fp-category-tabs-content">
        <view 
          wx:for="{{fontCategories}}" 
          wx:key="type"
          class="fp-category-tab {{currentCategory === item.type ? 'fp-active' : ''}}"
          data-category="{{item.type}}"
          bindtap="onCategoryChange"
        >
          <image class="fp-category-icon" src="{{constants.STATIC_URL.ICON}}{{item.icon}}.svg" mode="aspectFit"></image>
          <text>{{item.name}}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 字体列表 -->
    <scroll-view 
      scroll-y 
      class="fp-font-list-container" 
      enhanced="{{true}}" 
      show-scrollbar="{{false}}"
    >
      <view class="fp-font-list">
        <block wx:for="{{fontCategories}}" wx:key="type" wx:for-item="category">
          <block wx:if="{{category.type === currentCategory}}">
            <view class="fp-font-item {{selectedFont.id === font.id ? 'fp-active' : ''}}" 
              wx:for="{{category.fonts}}" 
              wx:key="id"
              wx:for-item="font"
              data-font="{{font}}"
              bindtap="onSelectFont">
              <text class="fp-font-name">{{font.name}}</text>
              <text class="fp-font-tags">{{font.tags}}</text>
            </view>
          </block>
        </block>
      </view>
    </scroll-view>

    <!-- 加载中遮罩 -->
    <view class="fp-loading-mask" wx:if="{{isLoading}}">
      <view class="fp-loading-content">
        <view class="fp-loading-spinner"></view>
        <text class="text">加载中...</text>
      </view>
    </view>
  </view>
</view> 
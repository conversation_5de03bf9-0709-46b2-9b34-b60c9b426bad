<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="组件展示" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">

      <!-- 浮动层按钮 -->
      <view class="float-btn-wrapper">
        <button class="float-btn" bindtap="openFloatLayer">打开浮动层</button>
      </view>

      <!-- 浮动层 -->
      <view class="float-layer-container {{showFloatLayer ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closeFloatLayer" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">浮动层标题</text>
            <view class="close-btn" bindtap="closeFloatLayer">×</view>
          </view>
          <view class="float-body">
            <text class="text">这里是浮动层内容</text>
          </view>
        </view>
      </view>

      <!-- 颜色选择器展示区域 -->
      <view class="color_demo">
        <view class="color_demo_header">
          <text class="color_demo_icon">🎨</text>
          <text class="color_demo_title">颜色选择器</text>
        </view>
        
        <!-- 基础展示区域 -->
        <scroll-view scroll-x class="color_demo_scroll">
          <view class="color_demo_list">
            <!-- 默认样式 -->
            <view class="color_demo_item">
              <text class="color_demo_item_title">默认样式</text>
              <color-picker bindchange="onColorChange1"/>
            </view>
            <!-- 预设颜色 -->
            <view class="color_demo_item">
              <text class="color_demo_item_title">预设颜色</text>
              <color-picker bindchange="onColorChange2" presetColors="{{presetColors}}"/>
            </view>
            <!-- 隐藏预设 -->
            <view class="color_demo_item">
              <text class="color_demo_item_title">隐藏预设</text>
              <color-picker bindchange="onColorChange3" showPresets="{{false}}"/>
            </view>
          </view>
        </scroll-view>

        <!-- 浮层展示按钮 -->
        <view class="color_demo_float">
          <button class="color_demo_btn" bindtap="openColorPicker">在浮层中打开</button>
        </view>
      </view>

      <!-- 颜色选择器浮层 -->
      <view class="float-layer-container {{showColorPicker ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closeColorPicker" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">颜色选择</text>
            <view class="close-btn" bindtap="closeColorPicker">×</view>
          </view>
          <view class="float-body">
            <color-picker bindchange="onColorChange4"/>
          </view>
        </view>
      </view>

      <!-- 字体选择器展示区域 -->
      <view class="font_demo">
        <view class="font_demo_header">
          <text class="font_demo_icon">📝</text>
          <text class="font_demo_title">字体选择器</text>
        </view>
        
        <!-- 基础展示区域 -->
        <scroll-view scroll-x class="font_demo_scroll">
          <view class="font_demo_list">
            <!-- 默认样式 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">默认样式</text>
              <font-picker bindchange="onFontChange1"/>
            </view>
            <!-- 带预设字体 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">预设字体</text>
              <font-picker bindchange="onFontChange2" presetFonts="{{presetFonts}}"/>
            </view>
            <!-- 自定义大小 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">自定义大小</text>
              <font-picker bindchange="onFontChange3" fontSize="{{28}}"/>
            </view>
          </view>
        </scroll-view>

        <!-- 浮层展示按钮 -->
        <view class="font_demo_float">
          <button class="font_demo_btn" bindtap="openFontPicker">在浮层中打开</button>
        </view>
      </view>

      <!-- 字体选择器浮层 -->
      <view class="float-layer-container {{showFontPicker ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closeFontPicker" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">字体选择</text>
            <view class="close-btn" bindtap="closeFontPicker">×</view>
          </view>
          <view class="float-body">
            <font-picker bindchange="onFontChange4"/>
          </view>
        </view>
      </view>

      <!-- 紧凑版字体选择器展示区域 -->
      <view class="font_demo">
        <view class="font_demo_header">
          <text class="font_demo_icon">📝</text>
          <text class="font_demo_title">紧凑版字体选择器</text>
        </view>
        
        <!-- 基础展示区域 -->
        <scroll-view scroll-x class="font_demo_scroll">
          <view class="font_demo_list">
            <!-- 默认样式 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">默认样式</text>
              <compact-font-picker bindconfirm="onCompactFontChange1" tabBarHeight="{{tabBarHeight}}"/>
            </view>
            <!-- 自定义预览文字 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">自定义预览文字</text>
              <compact-font-picker bindconfirm="onCompactFontChange2" preview-text="创意" tabBarHeight="{{tabBarHeight}}"/>
            </view>
            <!-- 默认选中字体 -->
            <view class="font_demo_item">
              <text class="font_demo_item_title">默认选中字体</text>
              <compact-font-picker bindconfirm="onCompactFontChange3" default-font="{{defaultCompactFont}}" tabBarHeight="{{tabBarHeight}}"/>
            </view>
          </view>
        </scroll-view>

        <!-- 浮层展示按钮 -->
        <view class="font_demo_float">
          <button class="font_demo_btn" bindtap="openCompactFontPicker">在浮层中打开</button>
        </view>
      </view>

      <!-- 紧凑版字体选择器浮层 -->
      <view class="float-layer-container {{showCompactFontPicker ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closeCompactFontPicker" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">紧凑版字体选择</text>
            <view class="close-btn" bindtap="closeCompactFontPicker">×</view>
          </view>
          <view class="float-body">
            <compact-font-picker bindconfirm="onCompactFontChange4" tabBarHeight="{{tabBarHeight}}"/>
          </view>
        </view>
      </view>

      <!-- 纸张选择器展示区域 -->
      <view class="paper_demo">
        <view class="paper_demo_header">
          <text class="paper_demo_icon">📄</text>
          <text class="paper_demo_title">纸张选择器</text>
        </view>
        
        <!-- 基础展示区域 -->
        <scroll-view scroll-x class="paper_demo_scroll">
          <view class="paper_demo_list">
            <!-- 默认样式 -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">默认样式</text>
              <paper-size-picker bindchange="onPaperChange1"/>
            </view>
            <!-- 预设纸张 -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">预设纸张</text>
              <paper-size-picker bindchange="onPaperChange2" presetSizes="{{presetPaperSizes}}"/>
            </view>
            <!-- 自定义单位 -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">自定义单位</text>
              <paper-size-picker bindchange="onPaperChange3" unit="cm"/>
            </view>
          </view>
        </scroll-view>

        <!-- 浮层展示按钮 -->
        <view class="paper_demo_float">
          <button class="paper_demo_btn" bindtap="openPaperPicker">在浮层中打开</button>
        </view>
      </view>

      <!-- 纸张选择器浮层 -->
      <view class="float-layer-container {{showPaperPicker ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closePaperPicker" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">纸张选择</text>
            <view class="close-btn" bindtap="closePaperPicker">×</view>
          </view>
          <view class="float-body">
            <paper-size-picker bindchange="onPaperChange4"/>
          </view>
        </view>
      </view>

      <!-- 紧凑版纸张选择器展示区域 -->
      <view class="paper_demo">
        <view class="paper_demo_header">
          <text class="paper_demo_icon">📑</text>
          <text class="paper_demo_title">紧凑版纸张选择器</text>
        </view>
        
        <!-- 基础展示区域 -->
        <scroll-view scroll-x class="paper_demo_scroll">
          <view class="paper_demo_list">
            <!-- 默认样式 -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">默认样式</text>
              <paper-size-picker bindselect="onPaperCompactChange1"/>
            </view>
            <!-- 预设纸张 -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">自定义按钮文字</text>
              <paper-size-picker bindselect="onPaperCompactChange2" buttonText="选择尺寸"/>
            </view>
            <!-- 自定义Z-Index -->
            <view class="paper_demo_item">
              <text class="paper_demo_item_title">自定义Z-Index</text>
              <paper-size-picker bindselect="onPaperCompactChange3" zIndex="2000"/>
            </view>
          </view>
        </scroll-view>

        <!-- 浮层展示按钮 -->
        <view class="paper_demo_float">
          <button class="paper_demo_btn" bindtap="openPaperCompactPicker">在浮层中打开</button>
        </view>
      </view>

      <!-- 紧凑版纸张选择器浮层 -->
      <view class="float-layer-container {{showPaperCompactPicker ? 'show' : ''}}" style="{{isTabBarCollapsed?layoutStyle_cropper_noposition:layoutStyle_noposition}}">
        <view class="mask" bindtap="closePaperCompactPicker" catchtouchmove="preventTouchMove"></view>
        <view class="float-content">
          <view class="float-header">
            <text class="text">紧凑版纸张选择</text>
            <view class="close-btn" bindtap="closePaperCompactPicker">×</view>
          </view>
          <view class="float-body">
            <paper-size-picker bindselect="onPaperCompactChange4"/>
          </view>
        </view>
      </view>

      <!-- 裁剪工具展示区域 -->
      <view class="cropper_demo">
        <view class="cropper_demo_header">
          <text class="cropper_demo_icon">✂️</text>
          <text class="cropper_demo_title">图片裁剪工具</text>
        </view>
        
        <!-- 基础展示区域 -->
        <view class="cropper_demo_content">
          <!-- 上传按钮 -->
          <button class="upload-btn" bindtap="handleUpload" wx:if="{{!originalImage && !croppedImage}}">
            选择图片进行裁剪
          </button>
          
          <!-- 裁剪结果对比展示 -->
          <view class="compare-area" wx:if="{{originalImage || croppedImage}}">
            <!-- 原图展示 -->
            <view class="image-section">
              <view class="section-title">原始图片</view>
              <image 
                class="preview-image" 
                src="{{originalImage}}" 
                mode="aspectFit" 
                bindtap="previewOriginalImage"
              ></image>
            </view>

            <!-- 裁剪结果展示 -->
            <view class="image-section" wx:if="{{croppedImage}}">
              <view class="section-title">裁剪结果</view>
              <image 
                class="preview-image" 
                src="{{croppedImage}}" 
                mode="aspectFit" 
                bindtap="previewImage"
              ></image>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <button class="action-btn" bindtap="handleUpload">重新选择</button>
              <button class="action-btn primary" bindtap="handleUpload" wx:if="{{!showCropper}}">重新裁剪</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 裁剪工具浮层 -->
      <view class="cropper-container {{showCropper ? 'show' : ''}}">
        <wxmy-cropper
          wx:if="{{showCropper}}"
          imageSrc="{{originalImage}}"
          bindcropped="handleCropperComplete"
          bindclose="handleCropperCancel"
        ></wxmy-cropper>
      </view>

      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <text class="intro-icon">🎯</text>
            <text class="intro-title">工具说明</text>
          </view>
          <view class="intro-content">
            <view class="intro-section">
              <text class="section-title">主要功能</text>
              <text class="section-text">本工具可以帮助你将图片快速划分为等比例的网格，适用于：绘画临摹、像素画制作、设计稿分割等场景。</text>
            </view>
            <view class="intro-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <text class="step-num">1</text>
                  <text class="step-text">上传并裁剪你的图片</text>
                </view>
                <view class="step-item">
                  <text class="step-num">2</text>
                  <text class="step-text">调整网格、坐标样式</text>
                </view>
                <view class="step-item">
                  <text class="step-num">3</text>
                  <text class="step-text">点击格子可放大查看</text>
                </view>
                <view class="step-item">
                  <text class="step-num">4</text>
                  <text class="step-text">导出网格图或完整图</text>
                </view>
              </view>
            </view>
            <view class="intro-section">
              <text class="section-title">特色功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <text class="feature-dot">•</text>
                  <text class="feature-text">支持多种纸张尺寸计算</text>
                </view>
                <view class="feature-item">
                  <text class="feature-dot">•</text>
                  <text class="feature-text">网格样式自由调整</text>
                </view>
                <view class="feature-item">
                  <text class="feature-dot">•</text>
                  <text class="feature-text">单格放大高清导出</text>
                </view>
                <view class="feature-item">
                  <text class="feature-dot">•</text>
                  <text class="feature-text">坐标系统辅助定位</text>
                </view>
              </view>
            </view>
        </view>
      </view>
        <!-- 说明结束 -->
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>


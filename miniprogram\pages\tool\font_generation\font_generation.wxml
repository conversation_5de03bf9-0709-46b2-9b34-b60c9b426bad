<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="字体生成" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <!-- 添加隐藏的测量canvas -->
    <canvas type="2d" 
      id="measureCanvas"
      class="measure-canvas"
      style="position: absolute; left: -9999px; visibility: hidden;"
    ></canvas>
    <!-- 添加离屏画布 -->
    <canvas type="2d" id="offscreenCanvas" style="position: absolute; left: -9999px; visibility: hidden;"></canvas>
    <!-- 添加导出用的画布 -->
    <canvas type="2d" id="exportCanvas" style="position: absolute; left: -9999px; visibility: hidden;"></canvas>
    <!-- 添加用于测量文字的离屏画布 -->
    <canvas type="2d" id="measureCanvas" style="position: absolute; left: -9999px; top: -9999px;"></canvas>
    
    <scroll-view scroll-y class="content-scroll" enhanced="{{true}}" bounces="{{true}}" show-scrollbar="{{true}}">
      <view class="font-generator">
        <!-- 输入区域 -->
        <view class="input-section">
          <textarea class="text-input" 
            placeholder="请输入要转换的文字..." 
            value="{{inputText}}"
            bindinput="onTextInput"
            maxlength="100"
          ></textarea>
        </view>
        
        <!-- 字体选择区域 -->
        <view class="font-selector">
          <view class="section-title">
            选择字体
            <view class="refresh-btn" bindtap="refreshFontsList">
              <image src="{{constants.STATIC_URL.ICON}}refresh.svg" mode="aspectFit"></image>
            </view>
          </view>
          <!-- 添加分类切换 -->
          <view class="category-tabs">
            <view 
              wx:for="{{fontCategories}}" 
              wx:key="type"
              class="category-tab {{currentCategory === item.type ? 'active' : ''}}"
              data-category="{{item.type}}"
              bindtap="onCategoryChange"
            >
              {{item.name}}
            </view>
          </view>
          <!-- 字体列表 -->
          <scroll-view scroll-x class="font-list">
            <block wx:for="{{fontCategories}}" wx:key="type" wx:for-item="category">
              <block wx:if="{{category.type === currentCategory}}">
                <view class="font-item {{selectedFont === font.path ? 'active' : ''}}" 
                  wx:for="{{category.fonts}}" 
                  wx:key="path"
                  wx:for-item="font"
                  data-font="{{font.path}}"
                  bindtap="onSelectFont">
                  <image class="preview-char" src="{{font.previewImage}}" mode="aspectFit"></image>
                  <text class="font-name">{{font.name}}</text>
                </view>
              </block>
            </block>
          </scroll-view>
        </view>
        <!-- 预览区域 -->
        <view class="preview-section">
          <view class="preview-container">
            <view class="preview-text {{isLoading ? 'loading' : ''}}" 
              style="background-color: {{bgColor}}{{bgOpacity < 100 ? bgOpacity/100 : ''}}; aspect-ratio: {{textDirection === 'vertical' ? '1024/1280' : '1280/1024'}};">
              <block wx:if="{{!selectedPreviewText}}">
                <text class="placeholder">请选择字体查看预览效果</text>
              </block>
              <block wx:else>
                <text style="font-family: PreviewFont; font-size: {{scaledFontSize}}rpx; color: {{textColor}}; writing-mode: {{textDirection === 'vertical' ? 'vertical-rl' : 'horizontal-tb'}};">{{selectedPreviewText}}</text>
              </block>
              <view class="loading-mask" wx:if="{{isLoading}}">
                <view class="loading-content">加载中...</view>
              </view>
            </view>
          </view>
          <!-- 添加导出按钮 -->
          <view class="button-group">
            <button class="export-btn {{isExporting ? 'loading' : ''}}" bindtap="exportImage">
              <image class="export-icon" src="{{constants.STATIC_URL.ICON}}download.svg" mode="aspectFit"></image>
              <text class="text">{{isExporting ? '导出中...' : '导出图片'}}</text>
              <view class="export-btn-bg"></view>
            </button>
          </view>
        </view>
        <!-- 字体设置面板 -->
        <view class="settings-panel">
          <view class="settings-section">
            <view class="section-title">画布设置</view>
            <view class="setting-item">
              <text class="text">背景颜色</text>
              <color-picker value="{{bgColor}}" bindchange="" bindconfirm="onBgColorSelect"></color-picker>
            </view>
          </view>
          
          <view class="settings-section">
            <view class="section-title">文字设置</view>
            <view class="setting-item">
              <text class="text">文字大小</text>
              <view class="font-size-control">
                <view class="size-btn" bindtap="decreaseFontSize">-</view>
                <slider value="{{fontSize}}" min="{{maxFontSize-50}}" max="{{maxFontSize+100}}" show-value block-size="20" block-color="#1890ff" bindchange="onFontSizeChange"/>
                <view class="size-btn" bindtap="increaseFontSize">+</view>
              </view>
            </view>
            <view class="setting-item">
              <text class="text">排列方向</text>
              <radio-group bindchange="onDirectionChange">
                <radio value="horizontal" checked="{{textDirection === 'horizontal'}}">横排</radio>
                <radio value="vertical" checked="{{textDirection === 'vertical'}}">竖排</radio>
              </radio-group>
            </view>
            <view class="setting-item">
              <text class="text">文字颜色</text>
              <color-picker value="{{textColor}}" bindchange="" bindconfirm="onTextColorSelect"></color-picker>
            </view>
          </view>
        </view>

        

        <!-- 工具说明开始 -->
        <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}editor-text.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">字体生成</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <view class="section-title">功能特点</view>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">1</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">多种字体</text>
                    <text class="feature-desc">提供多种艺术字体样式，满足不同创作需求</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">2</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">自定义设置</text>
                    <text class="feature-desc">支持调整字体大小、颜色、间距等属性</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">3</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">导出分享</text>
                    <text class="feature-desc">可导出高清图片，方便保存和分享使用</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <view class="section-title">使用步骤</view>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">输入文字</text>
                    <text class="step-desc">在文本输入框中输入需要生成的文字内容</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">选择字体</text>
                    <text class="step-desc">从字体列表中选择喜欢的艺术字体样式</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">调整样式</text>
                    <text class="step-desc">设置字体大小、颜色、间距等具体参数</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">导出保存</text>
                    <text class="step-desc">预览效果满意后，导出并保存生成的图片</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <view class="section-title">使用提示</view>
              <view class="tip-list">
                <view class="tip-item">
                  <text class="tip-icon">💡</text>
                  <text class="tip-title">文字长度</text>
                  <text class="tip-text">建议控制文字长度，过长可能影响整体效果</text>
                </view>
                <view class="tip-item">
                  <text class="tip-icon">🎨</text>
                  <text class="tip-title">样式搭配</text>
                  <text class="tip-text">注意字体与颜色的搭配，确保清晰易读</text>
                </view>
                <view class="tip-item">
                  <text class="tip-icon">📱</text>
                  <text class="tip-title">预览效果</text>
                  <text class="tip-text">调整参数时注意预览实时效果再确认</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 工具说明结束 -->
      </view>
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="4"><!-- 底部导航 --></tab-bar>
</view>


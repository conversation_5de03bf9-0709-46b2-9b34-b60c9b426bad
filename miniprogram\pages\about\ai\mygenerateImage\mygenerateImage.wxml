<wxs src="../../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="我的资产" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view wx:if="{{loading}}" class="loading">
        <image class="loading-icon" src="/assets/icons/loading.png" mode="aspectFit"></image>
        加载中...
      </view>
      <view wx:elif="{{isEmpty}}" class="empty">
        <image class="empty-icon" src="/assets/icons/empty.png" mode="aspectFit"></image>
        暂无数据
      </view>
      <view wx:else class="task-list">
        <view wx:for="{{tasks}}" wx:key="id" class="task-item">
          <view class="task-header">
            <text class="task-id">任务ID: {{item.id}}</text>
            <text class="status-tag {{item.status === 'success' ? 'success' : (item.status === 'failed' ? 'failed' : 'processing')}}">
              {{item.status === 'success' ? '已完成' : (item.status === 'failed' ? '失败' : '进行中')}}
            </text>
          </view>
          <text class="task-time">创建时间: {{item.created_at}}</text>
          <text class="task-prompt">提示词: {{item.parameters.prompt}}</text>
          <!-- 可根据实际数据结构补充展示内容 -->
          <image class="task-image" src="{{item.result.image_base64}}" mode="aspectFit"></image>
        </view>
      </view>
      <!-- 分页和每页条数选择 -->
      <view class="pagination-bar">
        <picker mode="selector" range="{{limitOptions}}" value="{{limitOptions.indexOf(limit)}}" bindchange="onLimitChange">
          <view class="picker">
            每页{{limit}}条
            <image class="picker-icon" src="/assets/icons/arrow-down.png" mode="aspectFit"></image>
          </view>
        </picker>
        <view class="pagination">
          <button wx:for="{{lastPage}}" wx:key="index" size="mini" data-page="{{index+1}}" bindtap="onPageChange" class="{{(index+1) === page ? 'active' : ''}}" disabled="{{(index+1) === page}}">{{index+1}}</button>
        </view>
      </view>
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>




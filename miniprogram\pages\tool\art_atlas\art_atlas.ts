// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';

// 工具函数：节流
function throttle<T extends (...args: any[]) => any>(fn: T, delay: number): T {
  let last = 0;
  return function (this: any, ...args: any[]) {
    const now = Date.now();
    if (now - last > delay) {
      fn.apply(this, args);
      last = now;
    }
  } as T;
}

// 定义基准尺寸常量
const BASE_SIZES = {
  MARGIN: 30, // 展示区边距
  TAG_PADDING: 16, // 标签内边距
  TAG_TEXT_WIDTH: 120, // 标签文字宽度
  TAG_HEIGHT: 44, // 标签高度
  CENTER_TEXT_SIZE: 32, // 中心文字大小
  ARTIST_TEXT_SIZE: 28, // 画师名字大小
  FONT_SIZE: 28, // 默认字体大小
  DASH_SIZE: 5, // 虚线大小
  BORDER_WIDTH: 1, // 边框宽度
  CONTAINER_HEIGHT: 300, // 容器高度
  DESIGN_WIDTH: 750, // 设计稿宽度
} as const;

interface IComponentData {
  backgroundImage: string;
  imageStyle: string;
  containerWidth: number;
  displaySize: { width: number; height: number };
  originalSize: { width: number; height: number };
  scaleRatio: number;
  tagPosition: { x: number; y: number };
  foldStatus: {
    sizeCalc: boolean;
    overlayCalc: boolean;
    tagCalc: boolean;
    fontSettings: boolean;
    overlaySettings: boolean;
  };
  fontSettings: {
    showSettings: boolean;
    backgroundColor: string;
    borderWidth: number;
    borderStyle: 'solid' | 'dashed' | 'dotted';
    borderRadius: number;
    borderColor: string;
    color: string;
  };
  overlaySettings: {
    showSettings: boolean;
    backgroundColor: string;
    borderWidth: number;
    borderStyle: 'solid' | 'dashed' | 'dotted';
    borderRadius: number;
    borderColor: string;
  };
  dragInfo: {
    dragging: boolean;
    startX: number;
    startY: number;
    currentX: number;
    currentY: number;
  };
  resizeInfo: {
    resizing: boolean;
    direction: string;
    startX: number;
    startY: number;
    startTop: number;
    startRight: number;
    startBottom: number;
    startLeft: number;
  };
  overlayInfo: {
    top: string;
    right: string;
    bottom: string;
    left: string;
    width: string;
    height: string;
  };
  artistTagInfo: {
    width: string;
    height: string;
    left: string;
    top: string;
  };
  artistList: Array<{
    id: string;
    name: string;
    isSelected: boolean;
    works: Array<{
      id: string;
      url: string;
      name: string;
      displaySize?: {
        width: number;
        height: number;
      };
    }>;
  }>;
  workPopup: {
    show: boolean;
    artistId: string;
    workId: string;
    url: string;
    name: string;
  };
  workDragInfo: {
    dragging: boolean;
    artistId: string;
    workId: string;
    sourceIndex: number;
    currentIndex: number;
    startX: number;
    startY: number;
    currentX: number;
    currentY: number;
  };
  showHelp: boolean;
  workBorderColor: string;
  workGlowColor: string;
  workGlowIntensity: number;
  workBorderWidth: number;
  workPreviewStyle: string;
  showWorkBorder: boolean;
  showWorkGlow: boolean;
  workNameColor: string;
  workNameBgColor: string;
  showFloatingBtn: boolean;
  showFloatingBgBtn: boolean;
  showBgSettingsPopup: boolean;
  currentBgSettingsTab: string;
  currentBgTab: string;
  showCheckResult: boolean;
  checkResult: {
    totalArtists: number;
    noNameCount: number;
    noWorksCount: number;
    details: Array<{
      number: number;
      id: string;
      issues: string[];
    }>;
  };
  canDownload: boolean;
  previewPopup: {
    show: boolean;
    tempFilePath: string;
    width: number;
  };
  showAddOptions: boolean;
  showInsertOptions: boolean;
  showProgress: boolean;
  progress: number;
  progressStatus: string;
  showMergeSettings: boolean;
  mergeSettings: {
    backgroundColor: string;
    spacing: number;
    showBorder: boolean;
    borderColor: string;
    borderWidth: number;
  };
}

// 定义 Canvas 相关类型
interface CanvasImage {
  src: string;
  onload: () => void;
  onerror: () => void;
  width: number;
  height: number;
}

interface Canvas2D {
  width: number;
  height: number;
  createImage(): CanvasImage;
  getContext(contextType: '2d'): CanvasRenderingContext2D;
}

interface CanvasRenderingContext2D {
  fillStyle: string;
  strokeStyle: string;
  lineWidth: number;
  font: string;
  textAlign: 'left' | 'right' | 'center' | 'start' | 'end';
  textBaseline: 'top' | 'hanging' | 'middle' | 'alphabetic' | 'ideographic' | 'bottom';
  drawImage(image: CanvasImage, dx: number, dy: number, dWidth: number, dHeight: number): void;
  fillRect(x: number, y: number, w: number, h: number): void;
  strokeRect(x: number, y: number, w: number, h: number): void;
  fillText(text: string, x: number, y: number): void;
  setLineDash(segments: number[]): void;
  measureText(text: string): TextMetrics;
}

interface IComponentInstance extends WechatMiniprogram.Component.TrivialInstance {
  data: IComponentData;
  setData: WechatMiniprogram.Component.InstanceMethods<IComponentData>['setData'];
  _canvas: Canvas2D | null;
  _ctx: CanvasRenderingContext2D | null;
  _moveTimer: number | null;
}

Component({
  options: {
    pureDataPattern: /^_/ // 指定纯数据字段
  },

  properties: {},

  data: { 
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    safeAreaInsetBottom: layoutUtil.getLayoutInfo().safeAreaBottom,
    // layoutStyle: layoutUtil.getContentStyle(),
    navBarHeight: layoutUtil.getLayoutInfo().navBarHeight,
    statusBarHeight: layoutUtil.getLayoutInfo().statusBarHeight,
    tabBarHeight: layoutUtil.getLayoutInfo().tabBarHeight,
    safeAreaBottom: layoutUtil.getLayoutInfo().safeAreaBottom,
    backgroundImage: '',
    imageStyle: '',
    containerWidth: 0,
    displaySize: { width: 0, height: 0 },
    originalSize: { width: 0, height: 0 },
    scaleRatio: 0,
    tagPosition: { x: 0, y: 0 },
    foldStatus: {
      sizeCalc: true,
      overlayCalc: true,
      tagCalc: true,
      fontSettings: true,
      overlaySettings: true,
    },
    fontSettings: {
      showSettings: false,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      borderWidth: 0,
      borderStyle: 'solid',
      borderRadius: 4,
      borderColor: '#333333',
      color: '#333333'
    },
    overlaySettings: {
      showSettings: false,
      backgroundColor: 'rgba(255, 255, 255, 0.5)',
      borderWidth: 0,
      borderStyle: 'solid',
      borderRadius: 0,
      borderColor: '#333333'
    },
    dragInfo: {
      dragging: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0
    },
    resizeInfo: {
      resizing: false,
      direction: '',
      startX: 0,
      startY: 0,
      startTop: 0,
      startRight: 0,
      startBottom: 0,
      startLeft: 0
    },
    overlayInfo: {
      top: '30rpx',
      right: '30rpx',
      bottom: '30rpx',
      left: '30rpx',
      width: '0px',
      height: '0px'
    },
    artistTagInfo: {
      width: '0px',
      height: '0px',
      left: '0px',
      top: '0px'
    },
    artistList: [],
    workPopup: {
      show: false,
      artistId: '',
      workId: '',
      url: '',
      name: ''
    },
    workDragInfo: {
      dragging: false,
      artistId: '',
      workId: '',
      sourceIndex: -1,
      currentIndex: -1,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0
    },
    showHelp: false,
    workBorderColor: 'rgba(255, 255, 255, 0.5)',
    workGlowColor: 'rgba(0, 128, 255, 0.8)',
    workGlowIntensity: 2,
    workBorderWidth: 1,
    workPreviewStyle: '',
    showWorkBorder: true,
    showWorkGlow: true,
    workNameColor: '#FFFFFF',
    workNameBgColor: 'rgba(0, 128, 255, 0.6)',
    showFloatingBtn: false,
    showFloatingBgBtn: false,
    showBgSettingsPopup: false,
    currentBgSettingsTab: 'preview',
    currentBgTab: 'preview',
    showCheckResult: false,
    checkResult: {
      totalArtists: 0,
      noNameCount: 0,
      noWorksCount: 0,
      details: [] as Array<{
        number: number,
        id: string,
        issues: string[]
      }>,
    },
    canDownload: false,
    previewPopup: {
      show: false,
      tempFilePath: '',
      width: 0
    },
    showAddOptions: false,
    showInsertOptions: false,
    showProgress: false,
    progress: 0,
    progressStatus: '',
    showMergeSettings: false,
    mergeSettings: {
      backgroundColor: '#FFFFFF',
      spacing: 20,
      showBorder: false,
      borderColor: '#000000',
      borderWidth: 2
    },
  } as IComponentData,

  // 私有属性
  _canvas: null as Canvas2D | null,
  _ctx: null as CanvasRenderingContext2D | null,
  _moveTimer: null as number | null,

  lifetimes: {
    attached() {
      // 获取上传区域的宽度
      const query = wx.createSelectorQuery().in(this);
      query.select('.upload-area').boundingClientRect(rect => {
        if (rect) {
        this.setData({
          containerWidth: rect.width
        });
        }
      }).exec();

      // 初始化 canvas
      const canvasQuery = wx.createSelectorQuery().in(this);
      canvasQuery.select('#highQualityCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            this._canvas = res[0].node as unknown as Canvas2D;
            this._ctx = this._canvas.getContext('2d');
            console.log('Canvas 初始化成功');
          } else {
            console.error('Canvas 节点获取失败');
          }
        });
      this.updateWorkStyles();
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },

    detached() {
      // 清理资源
      if (this._moveTimer) {
        wx.nextTick(() => {
          if (this._moveTimer) {
            this._moveTimer = 0;
          }
        });
      }
      // 清理 canvas 相关资源
      this._canvas = null;
      this._ctx = null;
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 防止滚动穿透
    preventTouchMove() {
      return false
    },

    // 阻止事件冒泡
    preventBubble() {
      return false
    },

    // 将rpx转换为实际像素
    rpxToPx(rpx: number, baseWidth: number): number {
      return (rpx / BASE_SIZES.DESIGN_WIDTH) * baseWidth;
    },

    // 计算图片显示尺寸
    calculateImageSize(originalWidth: number, originalHeight: number) {
      const containerWidth = this.data.containerWidth;
      const containerHeight = BASE_SIZES.CONTAINER_HEIGHT; // 容器高度 300rpx
      
      // 计算宽高比
      const imageRatio = originalWidth / originalHeight;
      const containerRatio = containerWidth / containerHeight;
      
      let width, height;
      
      if (imageRatio > containerRatio) {
        // 图片较宽，以容器宽度为准
        width = containerWidth;
        height = containerWidth / imageRatio;
      } else {
        // 图片较高，以容器高度为准
        height = containerHeight;
        width = containerHeight * imageRatio;
      }

      // 计算缩放比例
      const scaleRatio = width / originalWidth;
      
      // 计算标签实际宽度（文字宽度 + 两边内边距 + border）
      const displayTagPaddingXInPx = this.rpxToPx(16, containerWidth); // 水平内边距16rpx
      const displayTagPaddingYInPx = this.rpxToPx(8, containerWidth);  // 垂直内边距8rpx
      const displayBorderWidthInPx = this.rpxToPx(1, containerWidth);  // 边框宽度1rpx
      const displayTextWidthInPx = this.rpxToPx(BASE_SIZES.TAG_TEXT_WIDTH, containerWidth);
      const displayTagHeightInPx = this.rpxToPx(BASE_SIZES.TAG_HEIGHT, containerWidth);
      
      // 计算标签总宽度（文字宽度 + 两边padding + 两边border）
      const tagWidth = displayTextWidthInPx + (displayTagPaddingXInPx * 2) + (displayBorderWidthInPx * 2);
      // 计算标签总高度（文字高度 + 上下padding + 上下border）
      const tagHeight = displayTagHeightInPx + (displayTagPaddingYInPx * 2) + (displayBorderWidthInPx * 2);

      // 计算展示区的实际尺寸
      const marginInPx = this.rpxToPx(BASE_SIZES.MARGIN, containerWidth);
      const overlayWidth = width - (marginInPx * 2);
      const overlayHeight = height - (marginInPx * 2);
      
      this.setData({
        displaySize: { width, height },
        originalSize: { width: originalWidth, height: originalHeight },
        scaleRatio,
        overlayInfo: {
          top: BASE_SIZES.MARGIN + 'rpx',
          right: BASE_SIZES.MARGIN + 'rpx',
          bottom: BASE_SIZES.MARGIN + 'rpx',
          left: BASE_SIZES.MARGIN + 'rpx',
          width: Math.round(overlayWidth) + 'px',
          height: Math.round(overlayHeight) + 'px'
        },
        artistTagInfo: {
          width: tagWidth + 'px',
          height: tagHeight + 'px',
          left: '0px',
          top: '0px'
        },
        tagPosition: { x: 0, y: 0 }
      });
      
      return {
        width: width + 'px',
        height: height + 'px'
      };
    },

    // 选择图片
    async chooseImage() {
      try {
        wx.showLoading({ title: '加载中...' });
        
        const res = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
          sourceType: ['album', 'camera']
        });
        
          const tempFilePath = res.tempFiles[0].tempFilePath;
        const imgInfo = await wx.getImageInfo({ src: tempFilePath });
        
        // 检查图片大小
        if (imgInfo.width * imgInfo.height > 4096 * 4096) {
          wx.showToast({
            title: '图片尺寸过大，请选择较小的图片',
            icon: 'none'
          });
          return;
        }
        
              const size = this.calculateImageSize(imgInfo.width, imgInfo.height);
              this.setData({
                backgroundImage: tempFilePath,
                imageStyle: `width: ${size.width}; height: ${size.height};`
              });
      } catch (err) {
        wx.showToast({
          title: '图片加载失败',
          icon: 'error'
        });
      } finally {
        wx.hideLoading();
      }
    },


    // 开始拖动
    onTagTouchStart(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0];
      this.setData({
        'dragInfo.dragging': true,
        'dragInfo.startX': touch.clientX - this.data.tagPosition.x,
        'dragInfo.startY': touch.clientY - this.data.tagPosition.y
      });
    },

    // 拖动中
    onTagTouchMove: throttle(function(this: IComponentInstance, e: WechatMiniprogram.TouchEvent) {
      if (!this.data.dragInfo.dragging) return;
      
      const touch = e.touches[0];
      
      // 计算新位置
      let newX = touch.clientX - this.data.dragInfo.startX;
      let newY = touch.clientY - this.data.dragInfo.startY;

      // 获取标签和容器的尺寸（移除px单位并转换为数字）
      const tagWidth = parseFloat(this.data.artistTagInfo.width.replace('px', ''));
      const tagHeight = parseFloat(this.data.artistTagInfo.height.replace('px', ''));
      const containerWidth = this.data.displaySize.width;
      const containerHeight = this.data.displaySize.height;

      // 限制在显示区域内（考虑标签的完整尺寸，包括padding和border）
      const maxX = Math.max(0, containerWidth - tagWidth);
      const maxY = Math.max(0, containerHeight - tagHeight);

      // 限制在显示区域内
      newX = Math.max(0, Math.min(maxX, newX));
      newY = Math.max(0, Math.min(maxY, newY));

      // 更新标签位置和样式
      this.setData({
        'tagPosition.x': newX,
        'tagPosition.y': newY,
        'artistTagInfo.left': newX + 'px',
        'artistTagInfo.top': newY + 'px'
      });
    }, 16), // 约60fps的刷新率

    // 结束拖动
    onTagTouchEnd() {
      this.setData({
        'dragInfo.dragging': false
      });
    },

    // 切换折叠状态
    toggleFold(e: WechatMiniprogram.TouchEvent) {
      const section = e.currentTarget.dataset.section;
      const key = `foldStatus.${section}`;
      this.setData({
        [key]: !this.data.foldStatus[section]
      });
    },

    // 开始调整大小
    onResizeStart(e: WechatMiniprogram.TouchEvent) {
      const touch = e.touches[0];
      const direction = e.currentTarget.dataset.direction as string;
      
      // 获取当前边距值（去掉rpx单位）
      const currentTop = parseInt(this.data.overlayInfo.top);
      const currentRight = parseInt(this.data.overlayInfo.right);
      const currentBottom = parseInt(this.data.overlayInfo.bottom);
      const currentLeft = parseInt(this.data.overlayInfo.left);

      this.setData({
        'resizeInfo.resizing': true,
        'resizeInfo.direction': direction,
        'resizeInfo.startX': touch.clientX,
        'resizeInfo.startY': touch.clientY,
        'resizeInfo.startTop': currentTop,
        'resizeInfo.startRight': currentRight,
        'resizeInfo.startBottom': currentBottom,
        'resizeInfo.startLeft': currentLeft
      });
    },

    // 调整大小中
    onResizeMove(e: WechatMiniprogram.TouchEvent) {
      if (!this.data.resizeInfo.resizing) return;

      const touch = e.touches[0];
      const direction = this.data.resizeInfo.direction;
      const deltaX = touch.clientX - this.data.resizeInfo.startX;
      const deltaY = touch.clientY - this.data.resizeInfo.startY;

      // 转换为rpx
      const rpxDeltaX = (deltaX / this.data.containerWidth) * BASE_SIZES.DESIGN_WIDTH;
      const rpxDeltaY = (deltaY / this.data.containerWidth) * BASE_SIZES.DESIGN_WIDTH;

      const minMargin = 10; // 最小边距改为10rpx
      const maxMargin = 150; // 最大边距改为150rpx

      let newTop = this.data.resizeInfo.startTop;
      let newRight = this.data.resizeInfo.startRight;
      let newBottom = this.data.resizeInfo.startBottom;
      let newLeft = this.data.resizeInfo.startLeft;

      // 确保对边的总边距不超过显示区域的70%
      const maxTotalMargin = (BASE_SIZES.DESIGN_WIDTH * 0.7);

      switch (direction) {
        case 'top':
          newTop = Math.max(minMargin, Math.min(maxMargin, this.data.resizeInfo.startTop + rpxDeltaY));
          // 确保上下边距总和不超过限制
          if (newTop + newBottom > maxTotalMargin) {
            newTop = maxTotalMargin - newBottom;
          }
          break;
        case 'right':
          newRight = Math.max(minMargin, Math.min(maxMargin, this.data.resizeInfo.startRight - rpxDeltaX));
          // 确保左右边距总和不超过限制
          if (newLeft + newRight > maxTotalMargin) {
            newRight = maxTotalMargin - newLeft;
          }
          break;
        case 'bottom':
          newBottom = Math.max(minMargin, Math.min(maxMargin, this.data.resizeInfo.startBottom - rpxDeltaY));
          // 确保上下边距总和不超过限制
          if (newTop + newBottom > maxTotalMargin) {
            newBottom = maxTotalMargin - newTop;
          }
          break;
        case 'left':
          newLeft = Math.max(minMargin, Math.min(maxMargin, this.data.resizeInfo.startLeft + rpxDeltaX));
          // 确保左右边距总和不超过限制
          if (newLeft + newRight > maxTotalMargin) {
            newLeft = maxTotalMargin - newRight;
          }
          break;
      }

      // 计算展示区的实际尺寸
      const marginInPx = this.rpxToPx(BASE_SIZES.MARGIN, this.data.containerWidth);
      const overlayWidth = this.data.displaySize.width - (marginInPx * 2);
      const overlayHeight = this.data.displaySize.height - (marginInPx * 2);

      this.setData({
        'overlayInfo.top': newTop + 'rpx',
        'overlayInfo.right': newRight + 'rpx',
        'overlayInfo.bottom': newBottom + 'rpx',
        'overlayInfo.left': newLeft + 'rpx',
        'overlayInfo.width': Math.round(overlayWidth) + 'px',
        'overlayInfo.height': Math.round(overlayHeight) + 'px'
      });
    },

    // 结束调整大小
    onResizeEnd() {
      this.setData({
        'resizeInfo.resizing': false,
        'resizeInfo.direction': ''
      });
    },

    // 生成唯一ID
    generateId(): string {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // 添加画师
    addArtist(e: WechatMiniprogram.TouchEvent) {
      const type = e.currentTarget.dataset.type;
      const position = e.currentTarget.dataset.position;
      
      const newArtist = {
        id: this.generateId(),
        name: '',
        isSelected: true,
        works: []
      };

      if (type === 'insert' && typeof position === 'number') {
        // 在指定位置后插入
        const newArtistList = [...this.data.artistList];
        newArtistList.splice(position + 1, 0, newArtist);
        
        this.setData({
          artistList: newArtistList,
          showInsertOptions: false
        }, () => {
          this.scrollToNewArtist(position + 1);
        });
      } else {
        // 正常添加到末尾
        this.setData({
          artistList: [...this.data.artistList, newArtist],
          showAddOptions: false
        }, () => {
          this.scrollToNewArtist(this.data.artistList.length - 1);
        });
      }
    },

    // 滚动到新添加的画师位置
    scrollToNewArtist(index: number) {
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);
        
        Promise.all([
          new Promise(resolve => {
            query.select('.content-scroll').boundingClientRect(rect => resolve(rect)).exec();
          }),
          new Promise(resolve => {
            query.select('.content-scroll').scrollOffset(scroll => resolve(scroll)).exec();
          }),
          new Promise(resolve => {
            query.selectAll('.artist-section').boundingClientRect(rects => resolve(rects)).exec();
          })
        ]).then(([scrollRect, scrollOffset, rects]: [any, any, any]) => {
          if (!scrollRect || !scrollOffset || !rects || !rects[index]) return;
          
          const targetArtist = rects[index];
          const scrollTop = targetArtist.top - scrollRect.top + scrollOffset.scrollTop - 15;
          
          wx.createSelectorQuery().select('.content-scroll').node(res => {
            if (!res.node) return;
            const scrollView = res.node;
            scrollView.scrollTo({
              top: scrollTop,
              behavior: 'smooth',
              duration: 300
            });
          }).exec();
        });
      });
    },

    // 切换画师菜单
    toggleArtistMenu(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      
      // 先关闭所有其他菜单
      const newArtistList = this.data.artistList.map(artist => ({
        ...artist,
        isSelected: artist.id === artistId ? !artist.isSelected : false
      }));
      
      this.setData({
        artistList: newArtistList
      });
    },

    // 点击空白处关闭菜单
    onTapBackground() {
      // 关闭所有菜单
      const newArtistList = this.data.artistList.map(artist => ({
        ...artist,
        isSelected: false
      }));
      
      this.setData({
        artistList: newArtistList
      });
    },

    // 删除画师
    deleteArtist(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个画师吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              artistList: this.data.artistList.filter(artist => artist.id !== artistId)
            });
          }
        }
      });
    },

    // 更新画师名字
    onArtistNameInput(e: WechatMiniprogram.Input) {
      const artistId = e.currentTarget.dataset.id;
      const newName = e.detail.value;
      
      this.setData({
        artistList: this.data.artistList.map(artist => 
          artist.id === artistId ? { ...artist, name: newName } : artist
        )
      });
    },

    // 上传画师作品
    async uploadArtistWork(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      
      try {
        const res = await wx.chooseMedia({
          count: 6, // 允许一次选择最多9张图片
          mediaType: ['image'],
          sourceType: ['album', 'camera']
        });
        
        // 处理多张图片上传
        const newWorks = res.tempFiles.map(file => ({
          id: this.generateId(),
          url: file.tempFilePath,
          name: ''
        }));
        
        this.setData({
          artistList: this.data.artistList.map(artist => 
            artist.id === artistId 
              ? { ...artist, works: [...artist.works, ...newWorks] }
              : artist
          )
        });
      } catch (err) {
        wx.showToast({
          title: '上传失败',
          icon: 'error'
        });
      }
    },

    // 删除作品
    deleteWork(e: WechatMiniprogram.TouchEvent) {
      const { artistId, workId } = e.currentTarget.dataset;
      
      this.setData({
        artistList: this.data.artistList.map(artist => 
          artist.id === artistId
            ? { ...artist, works: artist.works.filter(work => work.id !== workId) }
            : artist
        )
      });
    },

    // 显示作品浮窗
    showWorkPopup(e: WechatMiniprogram.TouchEvent) {
      const { artistId, workId } = e.currentTarget.dataset;
      const artist = this.data.artistList.find(a => a.id === artistId);
      if (!artist) return;
      
      const work = artist.works.find(w => w.id === workId);
      if (!work) return;
      
      this.setData({
        workPopup: {
          show: true,
          artistId,
          workId,
          url: work.url,
          name: work.name || ''
        }
      });
    },

    // 关闭作品浮窗
    closeWorkPopup() {
      this.setData({
        'workPopup.show': false
      });
    },

    // 从浮窗中删除作品
    deleteWorkFromPopup() {
      const { artistId, workId } = this.data.workPopup;
      
      this.setData({
        artistList: this.data.artistList.map(artist => 
          artist.id === artistId
            ? { ...artist, works: artist.works.filter(work => work.id !== workId) }
            : artist
        ),
        'workPopup.show': false
      });
    },

    // 下载画师展示图
    async downloadArtistImage(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      const artist = this.data.artistList.find(a => a.id === artistId);
      
      if (!artist || !this.data.backgroundImage) {
        wx.showToast({
          title: '无法生成图片',
          icon: 'none'
        });
        return;
      }
      
      try {
        wx.showLoading({ title: '正在生成画师展示图...' });
        
        // 检查 canvas 是否已初始化
        if (!this._canvas || !this._ctx) {
          console.error('Canvas 未初始化');
          return;
        }

        const canvas = this._canvas;
        const ctx = this._ctx;
        
        // 设置画布尺寸为原始图片尺寸
        canvas.width = this.data.originalSize.width;
        canvas.height = this.data.originalSize.height;
        
        // 加载背景图
        const backgroundImage = canvas.createImage();
        await new Promise((resolve, reject) => {
          backgroundImage.onload = resolve;
          backgroundImage.onerror = () => reject(new Error('背景图加载失败'));
          backgroundImage.src = this.data.backgroundImage;
        });

        // 计算预览和实际图片的比例（在开始时就计算所有需要的尺寸和比例）
        const displayWidth = this.data.displaySize.width;
        const displayHeight = this.data.displaySize.height;
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const ratio = canvasWidth / displayWidth;
        const widthScale = this.data.originalSize.width / this.data.displaySize.width;
        const scaleX = canvasWidth / displayWidth;

        // 绘制背景
        ctx.drawImage(backgroundImage, 0, 0, canvasWidth, canvasHeight);
        
        // 计算展示区边距
        const topMargin = parseInt(this.data.overlayInfo.top) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const rightMargin = parseInt(this.data.overlayInfo.right) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const bottomMargin = parseInt(this.data.overlayInfo.bottom) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const leftMargin = parseInt(this.data.overlayInfo.left) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);

        // 绘制半透明展示区
        ctx.fillStyle = this.data.overlaySettings.backgroundColor;
        if (this.data.overlaySettings.borderRadius > 0) {
          // 绘制圆角矩形背景
          const radius = this.data.overlaySettings.borderRadius * scaleX;
          ctx.beginPath();
          ctx.moveTo(leftMargin + radius, topMargin);
          ctx.lineTo(canvas.width - (rightMargin + radius), topMargin);
          ctx.quadraticCurveTo(canvas.width - rightMargin, topMargin, canvas.width - rightMargin, topMargin + radius);
          ctx.lineTo(canvas.width - rightMargin, canvas.height - (bottomMargin + radius));
          ctx.quadraticCurveTo(canvas.width - rightMargin, canvas.height - bottomMargin, canvas.width - (rightMargin + radius), canvas.height - bottomMargin);
          ctx.lineTo(leftMargin + radius, canvas.height - bottomMargin);
          ctx.quadraticCurveTo(leftMargin, canvas.height - bottomMargin, leftMargin, canvas.height - (bottomMargin + radius));
          ctx.lineTo(leftMargin, topMargin + radius);
          ctx.quadraticCurveTo(leftMargin, topMargin, leftMargin + radius, topMargin);
          ctx.closePath();
          ctx.fill();
        } else {
          ctx.fillRect(
            leftMargin,
            topMargin,
            canvas.width - (leftMargin + rightMargin),
            canvas.height - (topMargin + bottomMargin)
          );
        }

        // 如果设置了边框，绘制边框
        if (this.data.overlaySettings.borderWidth > 0) {
          ctx.strokeStyle = this.data.overlaySettings.borderColor;
          ctx.lineWidth = this.data.overlaySettings.borderWidth;
          
          if (this.data.overlaySettings.borderStyle === 'dashed') {
            ctx.setLineDash([10, 10]);
          } else if (this.data.overlaySettings.borderStyle === 'dotted') {
            ctx.setLineDash([2, 2]);
          } else {
            ctx.setLineDash([]);
          }

          if (this.data.overlaySettings.borderRadius > 0) {
            // 绘制圆角矩形边框
            const radius = this.data.overlaySettings.borderRadius * scaleX;
            ctx.beginPath();
            ctx.moveTo(leftMargin + radius, topMargin);
            ctx.lineTo(canvas.width - (rightMargin + radius), topMargin);
            ctx.quadraticCurveTo(canvas.width - rightMargin, topMargin, canvas.width - rightMargin, topMargin + radius);
            ctx.lineTo(canvas.width - rightMargin, canvas.height - (bottomMargin + radius));
            ctx.quadraticCurveTo(canvas.width - rightMargin, canvas.height - bottomMargin, canvas.width - (rightMargin + radius), canvas.height - bottomMargin);
            ctx.lineTo(leftMargin + radius, canvas.height - bottomMargin);
            ctx.quadraticCurveTo(leftMargin, canvas.height - bottomMargin, leftMargin, canvas.height - (bottomMargin + radius));
            ctx.lineTo(leftMargin, topMargin + radius);
            ctx.quadraticCurveTo(leftMargin, topMargin, leftMargin + radius, topMargin);
            ctx.closePath();
            ctx.stroke();
          } else {
            ctx.strokeRect(
              leftMargin,
              topMargin,
              canvas.width - (leftMargin + rightMargin),
              canvas.height - (topMargin + bottomMargin)
            );
          }
        }
        
        // 计算作品布局
        const works = artist.works;
        if (works.length > 0) {
          // 计算三列布局的尺寸
          const overlayWidth = canvas.width - (leftMargin + rightMargin);
          const overlayHeight = canvas.height - (topMargin + bottomMargin);
          
          // 设置列宽比例（左:中:右 = 1:2:1）
          const totalColumns = 4;
          const centerColumnWidth = overlayWidth / 2; // 中间列占50%
          const sideColumnWidth = overlayWidth / 4; // 两侧列各占25%
          
          // 计算作品间距和内边距
          const padding = 10 * widthScale; // 内边距
          const gap = 20 * widthScale; // 作品间距
          
          // 计算中间列可用宽度（减去内边距和间距）
          const usableWidth = centerColumnWidth - (padding * 2);
          const maxWorkWidth = usableWidth - (gap * (works.length - 1)) / works.length;
          
          // 计算每个作品的最大高度（保持等比缩放）
          const maxWorkHeight = overlayHeight - (padding * 2);

          // 加载并绘制作品
          for (let i = 0; i < works.length; i++) {
            const work = works[i];
            const workImage = canvas.createImage();
            
            await new Promise((resolve, reject) => {
              workImage.onload = resolve;
              workImage.onerror = () => reject(new Error(`作品${i + 1}加载失败`));
              workImage.src = work.url;
            });
            
            // 计算作品实际尺寸（等比缩放）
            const scale = Math.min(
              maxWorkWidth / workImage.width,
              maxWorkHeight / workImage.height
            );
            const scaledWidth = workImage.width * scale;
            const scaledHeight = workImage.height * scale;
            
            // 计算作品位置
            const totalWorksWidth = (scaledWidth * works.length) + (gap * (works.length - 1));
            const startX = leftMargin + sideColumnWidth + (centerColumnWidth - totalWorksWidth) / 2;
            const x = startX + (scaledWidth + gap) * i;
            const y = topMargin + (overlayHeight - scaledHeight) / 2;
            
            // 在画布上绘制作品
            if (this.data.showWorkGlow) {
              // 先绘制外发光效果
              ctx.shadowColor = this.data.workGlowColor;
              ctx.shadowBlur = this.data.workGlowIntensity * ratio * 4; // 增加强度倍数
              ctx.shadowOffsetX = 0;
              ctx.shadowOffsetY = 0;
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              
              // 重置阴影后再次绘制图片本身
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
            } else {
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
            }

            // 绘制边框
            if (this.data.showWorkBorder) {
              ctx.strokeStyle = this.data.workBorderColor;
              ctx.lineWidth = this.data.workBorderWidth * ratio;
              ctx.strokeRect(x, y, scaledWidth, scaledHeight);
            }

            // 重置阴影效果
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;

            // 绘制作品名称
            if (work.name) {
              // 设置文字样式
              const fontSize = Math.round(16 * ratio * 0.5); // 基于16rpx的字体大小
              ctx.font = `${fontSize}px sans-serif`;
              ctx.textAlign = 'center';
              ctx.textBaseline = 'bottom';

              // 绘制半透明背景
              const textPadding = 8 * ratio;
              const textMetrics = ctx.measureText(work.name);
              const textWidth = Math.min(scaledWidth, textMetrics.width + textPadding * 2);
              const textHeight = fontSize + textPadding * 2;
              const textX = x;
              const textY = y + scaledHeight - textHeight;

              // 绘制渐变背景
              const gradient = ctx.createLinearGradient(textX, textY, textX, y + scaledHeight);
              gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
              gradient.addColorStop(1, this.data.workNameBgColor);
              ctx.fillStyle = gradient;
              ctx.fillRect(textX, textY, scaledWidth, textHeight);

              // 绘制文字
              ctx.fillStyle = this.data.workNameColor;
              ctx.fillText(
                work.name,
                x + scaledWidth / 2,
                y + scaledHeight - textPadding / 2
              );
            }
          }
        }

        // 绘制画师名字标签
        // 计算标签的实际位置（使用之前计算的 ratio）
        let finalTagX = this.data.tagPosition.x * ratio;
        let finalTagY = this.data.tagPosition.y * ratio;
        
        // 计算字体大小和内边距
        const baseFontSize = 28; // 基础字体大小(rpx)
        const fontSize = Math.round(baseFontSize * ratio * 0.5); // 添加0.5的缩放因子使字体更合适
        ctx.font = `${fontSize}px sans-serif`;
        const text = artist.name || '画师名字';
        const textMetrics = ctx.measureText(text);
        
        // 计算标签框的尺寸
        const padding = {
          x: 8 * ratio, // 水平内边距
          y: 4 * ratio  // 垂直内边距
        };
        
        // 计算标签框的总宽度和高度
        const boxWidth = textMetrics.width + (padding.x * 2);
        const boxHeight = fontSize + (padding.y * 2);

        // 检查是否在预览时贴近底部
        const previewBottomThreshold = 10; // 预览时的底部阈值（像素）
        const isNearBottomInPreview = (displayHeight - (this.data.tagPosition.y + parseFloat(this.data.artistTagInfo.height))) < previewBottomThreshold;
        
        // 如果在预览时贴近底部，则在导出时也贴近底部
        if (isNearBottomInPreview) {
            finalTagY = canvasHeight - boxHeight;
        }

        // 确保标签不会超出画布边界
        finalTagX = Math.max(0, Math.min(finalTagX, canvasWidth - boxWidth));
        finalTagY = Math.max(0, Math.min(finalTagY, canvasHeight - boxHeight));

        // 绘制背景
        if (this.data.fontSettings.backgroundBlur > 0) {
          // 创建一个临时 canvas 用于模糊效果
          const tempCanvas = wx.createOffscreenCanvas({ width: boxWidth, height: boxHeight });
          const tempCtx = tempCanvas.getContext('2d');
          
          // 绘制原始背景
          tempCtx.fillStyle = this.data.fontSettings.backgroundColor;
          tempCtx.fillRect(0, 0, boxWidth, boxHeight);
          
          // 应用模糊效果
          tempCtx.filter = `blur(${this.data.fontSettings.backgroundBlur}px)`;
          tempCtx.drawImage(tempCanvas, 0, 0);
          
          // 将模糊后的背景绘制到主画布
          ctx.drawImage(tempCanvas, finalTagX, finalTagY);
        } else {
          if (this.data.fontSettings.borderRadius > 0) {
            // 绘制圆角矩形背景
            const radius = this.data.fontSettings.borderRadius * scaleX;
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.beginPath();
            ctx.moveTo(finalTagX + radius, finalTagY);
            ctx.lineTo(finalTagX + boxWidth - radius, finalTagY);
            ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY, finalTagX + boxWidth, finalTagY + radius);
            ctx.lineTo(finalTagX + boxWidth, finalTagY + boxHeight - radius);
            ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY + boxHeight, finalTagX + boxWidth - radius, finalTagY + boxHeight);
            ctx.lineTo(finalTagX + radius, finalTagY + boxHeight);
            ctx.quadraticCurveTo(finalTagX, finalTagY + boxHeight, finalTagX, finalTagY + boxHeight - radius);
            ctx.lineTo(finalTagX, finalTagY + radius);
            ctx.quadraticCurveTo(finalTagX, finalTagY, finalTagX + radius, finalTagY);
            ctx.closePath();
            ctx.fill();
          } else {
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.fillRect(finalTagX, finalTagY, boxWidth, boxHeight);
          }
        }
        
        // 绘制边框
        ctx.strokeStyle = this.data.fontSettings.borderColor;
        ctx.lineWidth = this.data.fontSettings.borderWidth;
        
        if (this.data.fontSettings.borderStyle === 'dashed') {
          ctx.setLineDash([10, 10]);
        } else if (this.data.fontSettings.borderStyle === 'dotted') {
          ctx.setLineDash([2, 2]);
        } else {
          ctx.setLineDash([]); // 实线
        }
        
        if (this.data.fontSettings.borderRadius > 0) {
          // 绘制圆角矩形边框
          const radius = this.data.fontSettings.borderRadius * scaleX;
          ctx.beginPath();
          ctx.moveTo(finalTagX + radius, finalTagY);
          ctx.lineTo(finalTagX + boxWidth - radius, finalTagY);
          ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY, finalTagX + boxWidth, finalTagY + radius);
          ctx.lineTo(finalTagX + boxWidth, finalTagY + boxHeight - radius);
          ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY + boxHeight, finalTagX + boxWidth - radius, finalTagY + boxHeight);
          ctx.lineTo(finalTagX + radius, finalTagY + boxHeight);
          ctx.quadraticCurveTo(finalTagX, finalTagY + boxHeight, finalTagX, finalTagY + boxHeight - radius);
          ctx.lineTo(finalTagX, finalTagY + radius);
          ctx.quadraticCurveTo(finalTagX, finalTagY, finalTagX + radius, finalTagY);
          ctx.closePath();
          ctx.stroke();
        } else {
          ctx.strokeRect(finalTagX, finalTagY, boxWidth, boxHeight);
        }
        
        // 绘制文字（在中心位置）
        ctx.fillStyle = this.data.fontSettings.color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(
          text,
          finalTagX + boxWidth / 2,
          finalTagY + boxHeight / 2
        );

        // 导出图片
        const result = await new Promise<WechatMiniprogram.CanvasToTempFilePathSuccessCallbackResult>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvas as unknown as WechatMiniprogram.Canvas,
            width: canvas.width,
            height: canvas.height,
            destWidth: canvas.width,
            destHeight: canvas.height,
            fileType: 'png',
            quality: 1,
            success: resolve,
            fail: reject
          });
        });

        // 保存到相册
        await wx.saveImageToPhotosAlbum({
          filePath: result.tempFilePath
        });

        wx.hideLoading();
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });

      } catch (err) {
        console.error('生成画师展示图失败：', err);
        wx.hideLoading();
        wx.showToast({
          title: '生成失败',
          icon: 'error'
        });
      }
    },

    // 开始拖动作品
    onWorkDragStart(e: WechatMiniprogram.TouchEvent) {
      const { artistId, workId, index } = e.currentTarget.dataset;
      const touch = e.touches[0];
      
      // 获取作品容器的位置信息
      const query = wx.createSelectorQuery().in(this);
      query.select('.works-grid').boundingClientRect(gridRect => {
        if (!gridRect) return;
        
        this.setData({
          'workDragInfo.dragging': true,
          'workDragInfo.artistId': artistId,
          'workDragInfo.workId': workId,
          'workDragInfo.sourceIndex': index,
          'workDragInfo.currentIndex': index,
          'workDragInfo.startX': touch.clientX,
          'workDragInfo.startY': touch.clientY,
          'workDragInfo.currentX': touch.clientX,
          'workDragInfo.currentY': touch.clientY
        });
      }).exec();
    },

    // 拖动作品中
    onWorkDragMove: throttle(function(this: IComponentInstance, e: WechatMiniprogram.TouchEvent) {
      if (!this.data.workDragInfo.dragging) return;
      
      const touch = e.touches[0];
      const deltaX = touch.clientX - this.data.workDragInfo.startX;
      
      // 获取当前画师的作品列表
      const artist = this.data.artistList.find(a => a.id === this.data.workDragInfo.artistId);
      if (!artist) return;
      
      // 获取所有作品预览容器的位置信息
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.work-preview-container').boundingClientRect(rects => {
        if (!rects || !rects.length) return;
        
        // 计算当前拖动位置
        const dragX = touch.clientX;
        
        // 找到最近的目标位置
        let nearestIndex = this.data.workDragInfo.sourceIndex;
        let minDistance = Infinity;
        
        rects.forEach((rect, index) => {
          if (index === this.data.workDragInfo.sourceIndex) return;
          
          const distance = Math.abs(dragX - (rect.left + rect.width / 2));
          if (distance < minDistance) {
            minDistance = distance;
            nearestIndex = index;
          }
        });
        
        // 更新当前索引和位置
        if (nearestIndex !== this.data.workDragInfo.currentIndex) {
          this.setData({
            'workDragInfo.currentIndex': nearestIndex,
            'workDragInfo.currentX': touch.clientX,
            'workDragInfo.currentY': touch.clientY
          });
        }
      }).exec();
    }, 16),

    // 结束拖动作品
    onWorkDragEnd() {
      if (!this.data.workDragInfo.dragging) return;
      
      const { artistId, sourceIndex, currentIndex } = this.data.workDragInfo;
      
      if (sourceIndex !== currentIndex) {
        // 获取当前画师的作品列表
        const artistIndex = this.data.artistList.findIndex(a => a.id === artistId);
        if (artistIndex === -1) return;
        
        const works = [...this.data.artistList[artistIndex].works];
        // 移动作品到新位置
        const [movedWork] = works.splice(sourceIndex, 1);
        works.splice(currentIndex, 0, movedWork);
        
        // 更新作品列表
        const newArtistList = [...this.data.artistList];
        newArtistList[artistIndex] = {
          ...newArtistList[artistIndex],
          works
        };
        
        this.setData({
          artistList: newArtistList
        });
      }
      
      // 重置拖动状态
      this.setData({
        'workDragInfo.dragging': false,
        'workDragInfo.artistId': '',
        'workDragInfo.workId': '',
        'workDragInfo.sourceIndex': -1,
        'workDragInfo.currentIndex': -1,
        'workDragInfo.startX': 0,
        'workDragInfo.startY': 0,
        'workDragInfo.currentX': 0,
        'workDragInfo.currentY': 0
      });
    },

    // 切换帮助弹窗
    toggleHelpPopup(e: WechatMiniprogram.TouchEvent) {
      console.log('触发 toggleHelpPopup 方法', e);
      console.log('点击的元素:', e.currentTarget.dataset.test);
      console.log('当前 showHelp 状态:', this.data.showHelp);
      this.setData({
        showHelp: !this.data.showHelp
      }, () => {
        console.log('切换后 showHelp 状态:', this.data.showHelp);
      });
    },

    // 切换字体设置面板
    toggleFontSettings() {
      this.setData({
        'fontSettings.showSettings': !this.data.fontSettings.showSettings,
        'overlaySettings.showSettings': false // 关闭另一个面板
      });
    },

    // 处理颜色变化
    onColorChange(e: WechatMiniprogram.CustomEvent) {
      const color = e.detail.color;
      this.setData({
        'fontSettings.backgroundColor': color
      });
    },

    // 调整边框宽度
    adjustBorderWidth(e: WechatMiniprogram.TouchEvent) {
      const width = parseInt(e.detail.value);
      this.setData({
        'fontSettings.borderWidth': width
      });
    },

    // 改变边框样式
    onBorderStyleChange(e: WechatMiniprogram.TouchEvent) {
      const style = e.detail.value as 'solid' | 'dashed' | 'dotted';
      this.setData({
        'fontSettings.borderStyle': style
      });
    },

    // 调整圆角大小
    adjustBorderRadius(e: WechatMiniprogram.TouchEvent) {
      const radius = parseInt(e.detail.value);
      this.setData({
        'fontSettings.borderRadius': radius
      });
    },

    // 调整背景模糊
    adjustBackgroundBlur(e: WechatMiniprogram.TouchEvent) {
      const blur = parseInt(e.detail.value);
      this.setData({
        'fontSettings.backgroundBlur': blur
      });
    },

    toggleOverlaySettings() {
      this.setData({
        'overlaySettings.showSettings': !this.data.overlaySettings.showSettings,
        'fontSettings.showSettings': false // 关闭另一个面板
      });
    },

    onOverlayColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'overlaySettings.backgroundColor': e.detail.color
      });
    },

    adjustOverlayBorderWidth(e: WechatMiniprogram.TouchEvent) {
      this.setData({
        'overlaySettings.borderWidth': e.detail.value
      });
    },

    adjustOverlayBorderRadius(e: WechatMiniprogram.TouchEvent) {
      this.setData({
        'overlaySettings.borderRadius': e.detail.value
      });
    },

    onOverlayBorderStyleChange(e: WechatMiniprogram.TouchEvent) {
      this.setData({
        'overlaySettings.borderStyle': e.detail.value
      });
    },

    onBorderColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'fontSettings.borderColor': e.detail.color
      });
    },

    onOverlayBorderColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'overlaySettings.borderColor': e.detail.color
      });
    },

    // 添加点击背景关闭面板的方法
    onTapBackground(e: WechatMiniprogram.TouchEvent) {
      // 检查点击的是否是设置面板内部
      const target = e.target as any;
      if (target && (target.id === 'fontSettings' || target.id === 'overlaySettings')) {
        return;
      }

      // 关闭所有设置面板
      this.setData({
        'fontSettings.showSettings': false,
        'overlaySettings.showSettings': false
      });
    },

    onFontColorChange(e: WechatMiniprogram.CustomEvent) {
      const color = e.detail.color || e.detail;
      this.setData({
        'fontSettings.color': color
      });
    },

    // 处理作品名称输入
    onWorkNameInput(e: WechatMiniprogram.Input) {
      const { artistId, workId } = e.currentTarget.dataset;
      const value = e.detail.value;

      // 找到对应的画师和作品
      const artistList = this.data.artistList;
      const artistIndex = artistList.findIndex(artist => artist.id === artistId);
      if (artistIndex === -1) return;

      const workIndex = artistList[artistIndex].works.findIndex(work => work.id === workId);
      if (workIndex === -1) return;

      // 更新作品名称
      this.setData({
        [`artistList[${artistIndex}].works[${workIndex}].name`]: value
      });
    },

    // 处理作品图片加载完成
    onWorkImageLoad(e: WechatMiniprogram.ImageLoad) {
      const { artistId, workId } = e.currentTarget.dataset;
      const { width, height } = e.detail;

      // 获取图片容器的实际宽度
      const query = wx.createSelectorQuery().in(this);
      query.select('.work-preview-container').boundingClientRect(rect => {
        if (!rect) return;

        // 计算实际显示尺寸
        const containerWidth = rect.width;
        const containerHeight = rect.height;
        let displayWidth = width;
        let displayHeight = height;

        // 根据容器尺寸计算实际显示尺寸
        if (width / height > containerWidth / containerHeight) {
          // 宽度适配
          displayWidth = containerWidth;
          displayHeight = (height * containerWidth) / width;
        } else {
          // 高度适配
          displayHeight = containerHeight;
          displayWidth = (width * containerHeight) / height;
        }

        // 更新作品显示尺寸
        const artistList = this.data.artistList;
        const artistIndex = artistList.findIndex(artist => artist.id === artistId);
        if (artistIndex === -1) return;

        const workIndex = artistList[artistIndex].works.findIndex(work => work.id === workId);
        if (workIndex === -1) return;

        this.setData({
          [`artistList[${artistIndex}].works[${workIndex}].displaySize`]: {
            width: displayWidth,
            height: displayHeight
          }
        });
      }).exec();
    },

    // 处理边框颜色变化
    onWorkBorderColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        workBorderColor: e.detail.color
      });
      this.updateWorkStyles();
    },

    // 处理外发光颜色变化
    onWorkGlowColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        workGlowColor: e.detail.color
      });
      this.updateWorkStyles();
    },

    // 处理外发光强度变化
    adjustWorkGlowIntensity(e: WechatMiniprogram.SliderChange) {
      this.setData({
        workGlowIntensity: e.detail.value
      });
      this.updateWorkStyles();
    },

    // 处理边框宽度变化
    adjustWorkBorderWidth(e: WechatMiniprogram.SliderChange) {
      this.setData({
        workBorderWidth: e.detail.value
      });
      this.updateWorkStyles();
    },

    // 更新作品样式
    updateWorkStyles() {
      const style = `border: ${this.data.showWorkBorder ? `${this.data.workBorderWidth}rpx solid ${this.data.workBorderColor}` : 'none'}; filter: ${this.data.showWorkGlow ? `drop-shadow(0 0 ${this.data.workGlowIntensity}rpx ${this.data.workGlowColor})` : 'none'};`;
      
      this.setData({
        workPreviewStyle: style
      });
    },

    toggleWorkBorder(e: WechatMiniprogram.SwitchChange) {
      this.setData({
        showWorkBorder: e.detail.value
      }, () => {
        this.updateWorkStyles();
      });
    },

    toggleWorkGlow(e: WechatMiniprogram.SwitchChange) {
      this.setData({
        showWorkGlow: e.detail.value
      }, () => {
        this.updateWorkStyles();
      });
    },

    // 处理作品名称字体颜色变化
    onWorkNameColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        workNameColor: e.detail.color
      });
    },

    // 处理作品名称背景颜色变化
    onWorkNameBgColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        workNameBgColor: e.detail.color
      });
    },

    // 监听滚动
    onPageScroll(e: WechatMiniprogram.ScrollViewScroll) {
      // 获取背景图设置区域的位置
      const query = wx.createSelectorQuery().in(this);
      query.select('.background-section').boundingClientRect(rect => {
        if (!rect) return;
        
        // 使用layoutUtil获取窗口高度
        const windowHeight = layoutUtil.getLayoutInfo().systemInfo.windowHeight;
        
        // 如果背景图设置区域超出可视区域，显示浮动按钮
        const isOutOfView = rect.top < (this.data.statusBarHeight + this.data.navBarHeight) || 
                           rect.bottom > (windowHeight - this.data.tabBarHeight);
        
        if (isOutOfView !== this.data.showFloatingBgBtn) {
          this.setData({ showFloatingBgBtn: isOutOfView });
        }
      }).exec();

      // 获取添加画师按钮的位置
      query.select('.add-artist-btn').boundingClientRect(rect => {
        if (!rect) return;
        
        // 使用layoutUtil获取窗口高度
        const windowHeight = layoutUtil.getLayoutInfo().systemInfo.windowHeight;
        
        // 如果按钮超出可视区域，显示浮动按钮
        const isOutOfView = rect.top < (this.data.statusBarHeight + this.data.navBarHeight) || 
                           rect.bottom > (windowHeight - this.data.tabBarHeight);
        
        if (isOutOfView !== this.data.showFloatingBtn) {
          this.setData({ showFloatingBtn: isOutOfView });
        }
      }).exec();
    },

    // 切换背景设置浮动面板
    toggleBgSettingsPopup(e: WechatMiniprogram.TouchEvent) {
      console.log('触发 toggleBgSettingsPopup 方法', e);
      console.log('点击的元素:', e.currentTarget.dataset.test);
      console.log('当前 showBgSettingsPopup 状态:', this.data.showBgSettingsPopup);
      this.setData({
        showBgSettingsPopup: !this.data.showBgSettingsPopup
      }, () => {
        console.log('切换后 showBgSettingsPopup 状态:', this.data.showBgSettingsPopup);
      });
    },

    // 添加TAB切换方法
    switchBgSettingsTab(e: WechatMiniprogram.TouchEvent) {
      const tab = e.currentTarget.dataset.tab as string;
      this.setData({
        currentBgSettingsTab: tab
      });
    },

    // 添加背景区域TAB切换方法
    switchBgTab(e: WechatMiniprogram.TouchEvent) {
      const tab = e.currentTarget.dataset.tab as string;
      this.setData({
        currentBgTab: tab
      });
    },

    // 切换画师选中状态
    toggleArtistSelection(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      
      this.setData({
        artistList: this.data.artistList.map(artist => 
          artist.id === artistId 
            ? { ...artist, isSelected: !artist.isSelected } 
            : artist
        )
      });
    },

    // 检测画师状态
    checkArtistStatus() {
      const artistList = this.data.artistList;
      const totalArtists = artistList.length;
      
      if (totalArtists === 0) {
        wx.showModal({
          title: '检测结果',
          content: '当前还未添加任何画师',
          showCancel: false
        });
        this.setData({ canDownload: false });
        return;
      }

      // 统计信息
      const noNameCount = artistList.filter(artist => !artist.name).length;
      const noWorksCount = artistList.filter(artist => !artist.works || artist.works.length === 0).length;

      // 详细信息
      const details = artistList.map((artist, index) => {
        const issues: string[] = [];
        if (!artist.name) {
          issues.push('未添加画师名');
        }
        if (!artist.works || artist.works.length === 0) {
          issues.push('未添加作品');
        }
        return {
          number: index + 1,
          id: artist.id,
          issues
        };
      }).filter(detail => detail.issues.length > 0);

      // 更新下载按钮状态
      const canDownload = noNameCount === 0 && noWorksCount === 0;

      this.setData({
        showCheckResult: true,
        canDownload,
        checkResult: {
          totalArtists,
          noNameCount,
          noWorksCount,
          details
        }
      });
    },

    // 关闭检测结果弹窗
    closeCheckResult() {
      this.setData({
        showCheckResult: false
      });
    },

    // 预览画师图鉴
    async previewArtistImage(e: WechatMiniprogram.TouchEvent) {
      const artistId = e.currentTarget.dataset.artistId;
      const artist = this.data.artistList.find(a => a.id === artistId);
      
      if (!artist || !this.data.backgroundImage) {
        wx.showToast({
          title: '无法生成预览',
          icon: 'none'
        });
        return;
      }
      
      try {
        wx.showLoading({ title: '正在生成预览...' });
        
        // 检查 canvas 是否已初始化
        if (!this._canvas || !this._ctx) {
          console.error('Canvas 未初始化');
          return;
        }

        const canvas = this._canvas;
        const ctx = this._ctx;
        
        // 设置画布尺寸为原始图片尺寸
        canvas.width = this.data.originalSize.width;
        canvas.height = this.data.originalSize.height;
        
        // 加载背景图
        const backgroundImage = canvas.createImage();
        await new Promise((resolve, reject) => {
          backgroundImage.onload = resolve;
          backgroundImage.onerror = () => reject(new Error('背景图加载失败'));
          backgroundImage.src = this.data.backgroundImage;
        });

        // 计算预览和实际图片的比例（在开始时就计算所有需要的尺寸和比例）
        const displayWidth = this.data.displaySize.width;
        const displayHeight = this.data.displaySize.height;
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const ratio = canvasWidth / displayWidth;
        const widthScale = this.data.originalSize.width / this.data.displaySize.width;
        const scaleX = canvasWidth / displayWidth;

        // 绘制背景
        ctx.drawImage(backgroundImage, 0, 0, canvasWidth, canvasHeight);
        
        // 计算展示区边距
        const topMargin = parseInt(this.data.overlayInfo.top) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const rightMargin = parseInt(this.data.overlayInfo.right) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const bottomMargin = parseInt(this.data.overlayInfo.bottom) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);
        const leftMargin = parseInt(this.data.overlayInfo.left) * widthScale / (BASE_SIZES.DESIGN_WIDTH / displayWidth);

        // 绘制半透明展示区
        ctx.fillStyle = this.data.overlaySettings.backgroundColor;
        if (this.data.overlaySettings.borderRadius > 0) {
          // 绘制圆角矩形背景
          const radius = this.data.overlaySettings.borderRadius * scaleX;
          ctx.beginPath();
          ctx.moveTo(leftMargin + radius, topMargin);
          ctx.lineTo(canvas.width - (rightMargin + radius), topMargin);
          ctx.quadraticCurveTo(canvas.width - rightMargin, topMargin, canvas.width - rightMargin, topMargin + radius);
          ctx.lineTo(canvas.width - rightMargin, canvas.height - (bottomMargin + radius));
          ctx.quadraticCurveTo(canvas.width - rightMargin, canvas.height - bottomMargin, canvas.width - (rightMargin + radius), canvas.height - bottomMargin);
          ctx.lineTo(leftMargin + radius, canvas.height - bottomMargin);
          ctx.quadraticCurveTo(leftMargin, canvas.height - bottomMargin, leftMargin, canvas.height - (bottomMargin + radius));
          ctx.lineTo(leftMargin, topMargin + radius);
          ctx.quadraticCurveTo(leftMargin, topMargin, leftMargin + radius, topMargin);
          ctx.closePath();
          ctx.fill();
        } else {
          ctx.fillRect(
            leftMargin,
            topMargin,
            canvas.width - (leftMargin + rightMargin),
            canvas.height - (topMargin + bottomMargin)
          );
        }

        // 如果设置了边框，绘制边框
        if (this.data.overlaySettings.borderWidth > 0) {
          ctx.strokeStyle = this.data.overlaySettings.borderColor;
          ctx.lineWidth = this.data.overlaySettings.borderWidth;
          
          if (this.data.overlaySettings.borderStyle === 'dashed') {
            ctx.setLineDash([10, 10]);
          } else if (this.data.overlaySettings.borderStyle === 'dotted') {
            ctx.setLineDash([2, 2]);
          } else {
            ctx.setLineDash([]);
          }

          if (this.data.overlaySettings.borderRadius > 0) {
            // 绘制圆角矩形边框
            const radius = this.data.overlaySettings.borderRadius * scaleX;
            ctx.beginPath();
            ctx.moveTo(leftMargin + radius, topMargin);
            ctx.lineTo(canvas.width - (rightMargin + radius), topMargin);
            ctx.quadraticCurveTo(canvas.width - rightMargin, topMargin, canvas.width - rightMargin, topMargin + radius);
            ctx.lineTo(canvas.width - rightMargin, canvas.height - (bottomMargin + radius));
            ctx.quadraticCurveTo(canvas.width - rightMargin, canvas.height - bottomMargin, canvas.width - (rightMargin + radius), canvas.height - bottomMargin);
            ctx.lineTo(leftMargin + radius, canvas.height - bottomMargin);
            ctx.quadraticCurveTo(leftMargin, canvas.height - bottomMargin, leftMargin, canvas.height - (bottomMargin + radius));
            ctx.lineTo(leftMargin, topMargin + radius);
            ctx.quadraticCurveTo(leftMargin, topMargin, leftMargin + radius, topMargin);
            ctx.closePath();
            ctx.stroke();
          } else {
            ctx.strokeRect(
              leftMargin,
              topMargin,
              canvas.width - (leftMargin + rightMargin),
              canvas.height - (topMargin + bottomMargin)
            );
          }
        }
        
        // 计算作品布局
        const works = artist.works;
        if (works.length > 0) {
          // 计算三列布局的尺寸
          const overlayWidth = canvas.width - (leftMargin + rightMargin);
          const overlayHeight = canvas.height - (topMargin + bottomMargin);
          
          // 设置列宽比例（左:中:右 = 1:2:1）
          const totalColumns = 4;
          const centerColumnWidth = overlayWidth / 2; // 中间列占50%
          const sideColumnWidth = overlayWidth / 4; // 两侧列各占25%
          
          // 计算作品间距和内边距
          const padding = 10 * widthScale; // 内边距
          const gap = 20 * widthScale; // 作品间距
          
          // 计算中间列可用宽度（减去内边距和间距）
          const usableWidth = centerColumnWidth - (padding * 2);
          const maxWorkWidth = usableWidth - (gap * (works.length - 1)) / works.length;
          
          // 计算每个作品的最大高度（保持等比缩放）
          const maxWorkHeight = overlayHeight - (padding * 2);

          // 加载并绘制作品
          for (let i = 0; i < works.length; i++) {
            const work = works[i];
            const workImage = canvas.createImage();
            
            await new Promise((resolve, reject) => {
              workImage.onload = resolve;
              workImage.onerror = () => reject(new Error(`作品${i + 1}加载失败`));
              workImage.src = work.url;
            });
            
            // 计算作品实际尺寸（等比缩放）
            const scale = Math.min(
              maxWorkWidth / workImage.width,
              maxWorkHeight / workImage.height
            );
            const scaledWidth = workImage.width * scale;
            const scaledHeight = workImage.height * scale;
            
            // 计算作品位置
            const totalWorksWidth = (scaledWidth * works.length) + (gap * (works.length - 1));
            const startX = leftMargin + sideColumnWidth + (centerColumnWidth - totalWorksWidth) / 2;
            const x = startX + (scaledWidth + gap) * i;
            const y = topMargin + (overlayHeight - scaledHeight) / 2;
            
            // 在画布上绘制作品
            if (this.data.showWorkGlow) {
              // 先绘制外发光效果
              ctx.shadowColor = this.data.workGlowColor;
              ctx.shadowBlur = this.data.workGlowIntensity * ratio * 4; // 增加强度倍数
              ctx.shadowOffsetX = 0;
              ctx.shadowOffsetY = 0;
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              
              // 重置阴影后再次绘制图片本身
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
            } else {
              ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
            }

            // 绘制边框
            if (this.data.showWorkBorder) {
              ctx.strokeStyle = this.data.workBorderColor;
              ctx.lineWidth = this.data.workBorderWidth * ratio;
              ctx.strokeRect(x, y, scaledWidth, scaledHeight);
            }

            // 重置阴影效果
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;

            // 绘制作品名称
            if (work.name) {
              // 设置文字样式
              const fontSize = Math.round(16 * ratio * 0.5); // 基于16rpx的字体大小
              ctx.font = `${fontSize}px sans-serif`;
              ctx.textAlign = 'center';
              ctx.textBaseline = 'bottom';

              // 绘制半透明背景
              const textPadding = 8 * ratio;
              const textMetrics = ctx.measureText(work.name);
              const textWidth = Math.min(scaledWidth, textMetrics.width + textPadding * 2);
              const textHeight = fontSize + textPadding * 2;
              const textX = x;
              const textY = y + scaledHeight - textHeight;

              // 绘制渐变背景
              const gradient = ctx.createLinearGradient(textX, textY, textX, y + scaledHeight);
              gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
              gradient.addColorStop(1, this.data.workNameBgColor);
              ctx.fillStyle = gradient;
              ctx.fillRect(textX, textY, scaledWidth, textHeight);

              // 绘制文字
              ctx.fillStyle = this.data.workNameColor;
              ctx.fillText(
                work.name,
                x + scaledWidth / 2,
                y + scaledHeight - textPadding / 2
              );
            }
          }
        }

        // 绘制画师名字标签
        // 计算标签的实际位置（使用之前计算的 ratio）
        let finalTagX = this.data.tagPosition.x * ratio;
        let finalTagY = this.data.tagPosition.y * ratio;
        
        // 计算字体大小和内边距
        const baseFontSize = 28; // 基础字体大小(rpx)
        const fontSize = Math.round(baseFontSize * ratio * 0.5); // 添加0.5的缩放因子使字体更合适
        ctx.font = `${fontSize}px sans-serif`;
        const text = artist.name || '画师名字';
        const textMetrics = ctx.measureText(text);
        
        // 计算标签框的尺寸
        const padding = {
          x: 8 * ratio, // 水平内边距
          y: 4 * ratio  // 垂直内边距
        };
        
        // 计算标签框的总宽度和高度
        const boxWidth = textMetrics.width + (padding.x * 2);
        const boxHeight = fontSize + (padding.y * 2);

        // 检查是否在预览时贴近底部
        const previewBottomThreshold = 10; // 预览时的底部阈值（像素）
        const isNearBottomInPreview = (displayHeight - (this.data.tagPosition.y + parseFloat(this.data.artistTagInfo.height))) < previewBottomThreshold;
        
        // 如果在预览时贴近底部，则在导出时也贴近底部
        if (isNearBottomInPreview) {
            finalTagY = canvasHeight - boxHeight;
        }

        // 确保标签不会超出画布边界
        finalTagX = Math.max(0, Math.min(finalTagX, canvasWidth - boxWidth));
        finalTagY = Math.max(0, Math.min(finalTagY, canvasHeight - boxHeight));

        // 绘制背景
        if (this.data.fontSettings.backgroundBlur > 0) {
          // 创建一个临时 canvas 用于模糊效果
          const tempCanvas = wx.createOffscreenCanvas({ width: boxWidth, height: boxHeight });
          const tempCtx = tempCanvas.getContext('2d');
          
          // 绘制原始背景
          tempCtx.fillStyle = this.data.fontSettings.backgroundColor;
          tempCtx.fillRect(0, 0, boxWidth, boxHeight);
          
          // 应用模糊效果
          tempCtx.filter = `blur(${this.data.fontSettings.backgroundBlur}px)`;
          tempCtx.drawImage(tempCanvas, 0, 0);
          
          // 将模糊后的背景绘制到主画布
          ctx.drawImage(tempCanvas, finalTagX, finalTagY);
        } else {
          if (this.data.fontSettings.borderRadius > 0) {
            // 绘制圆角矩形背景
            const radius = this.data.fontSettings.borderRadius * scaleX;
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.beginPath();
            ctx.moveTo(finalTagX + radius, finalTagY);
            ctx.lineTo(finalTagX + boxWidth - radius, finalTagY);
            ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY, finalTagX + boxWidth, finalTagY + radius);
            ctx.lineTo(finalTagX + boxWidth, finalTagY + boxHeight - radius);
            ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY + boxHeight, finalTagX + boxWidth - radius, finalTagY + boxHeight);
            ctx.lineTo(finalTagX + radius, finalTagY + boxHeight);
            ctx.quadraticCurveTo(finalTagX, finalTagY + boxHeight, finalTagX, finalTagY + boxHeight - radius);
            ctx.lineTo(finalTagX, finalTagY + radius);
            ctx.quadraticCurveTo(finalTagX, finalTagY, finalTagX + radius, finalTagY);
            ctx.closePath();
            ctx.fill();
          } else {
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.fillRect(finalTagX, finalTagY, boxWidth, boxHeight);
          }
        }
        
        // 绘制边框
        ctx.strokeStyle = this.data.fontSettings.borderColor;
        ctx.lineWidth = this.data.fontSettings.borderWidth;
        
        if (this.data.fontSettings.borderStyle === 'dashed') {
          ctx.setLineDash([10, 10]);
        } else if (this.data.fontSettings.borderStyle === 'dotted') {
          ctx.setLineDash([2, 2]);
        } else {
          ctx.setLineDash([]); // 实线
        }
        
        if (this.data.fontSettings.borderRadius > 0) {
          // 绘制圆角矩形边框
          const radius = this.data.fontSettings.borderRadius * scaleX;
          ctx.beginPath();
          ctx.moveTo(finalTagX + radius, finalTagY);
          ctx.lineTo(finalTagX + boxWidth - radius, finalTagY);
          ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY, finalTagX + boxWidth, finalTagY + radius);
          ctx.lineTo(finalTagX + boxWidth, finalTagY + boxHeight - radius);
          ctx.quadraticCurveTo(finalTagX + boxWidth, finalTagY + boxHeight, finalTagX + boxWidth - radius, finalTagY + boxHeight);
          ctx.lineTo(finalTagX + radius, finalTagY + boxHeight);
          ctx.quadraticCurveTo(finalTagX, finalTagY + boxHeight, finalTagX, finalTagY + boxHeight - radius);
          ctx.lineTo(finalTagX, finalTagY + radius);
          ctx.quadraticCurveTo(finalTagX, finalTagY, finalTagX + radius, finalTagY);
          ctx.closePath();
          ctx.stroke();
        } else {
          ctx.strokeRect(finalTagX, finalTagY, boxWidth, boxHeight);
        }
        
        // 绘制文字（在中心位置）
        ctx.fillStyle = this.data.fontSettings.color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(
          text,
          finalTagX + boxWidth / 2,
          finalTagY + boxHeight / 2
        );

        // 生成临时文件路径
        const result = await new Promise<WechatMiniprogram.CanvasToTempFilePathSuccessCallbackResult>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvas as unknown as WechatMiniprogram.Canvas,
            width: canvas.width,
            height: canvas.height,
            destWidth: canvas.width,
            destHeight: canvas.height,
            fileType: 'png',
            quality: 1,
            success: resolve,
            fail: reject
          });
        });

        // 显示预览
        this.setData({
          'previewPopup.show': true,
          'previewPopup.tempFilePath': result.tempFilePath,
          'previewPopup.width': canvas.width
        });

        wx.hideLoading();
      } catch (err) {
        console.error('生成预览失败：', err);
        wx.hideLoading();
        wx.showToast({
          title: '预览失败',
          icon: 'error'
        });
      }
    },

    // 关闭预览
    closePreviewPopup() {
      this.setData({
        'previewPopup.show': false
      });
    },

    // 下载预览图片
    async downloadPreviewImage() {
      if (!this.data.previewPopup.tempFilePath) {
        wx.showToast({
          title: '无可下载的图片',
          icon: 'none'
        });
        return;
      }

      try {
        await wx.saveImageToPhotosAlbum({
          filePath: this.data.previewPopup.tempFilePath
        });

        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });

        // 关闭预览
        this.closePreviewPopup();
      } catch (err) {
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        });
      }
    },

    // 处理预览图片加载完成
    onPreviewImageLoad() {
      // 图片加载完成后的处理（如果需要）
    },

    // 处理预览图片拖动
    onPreviewTouchMove(e: WechatMiniprogram.TouchEvent) {
      // 允许滚动视图的默认行为
      return;
    },

    // 显示添加画师选项
    showAddArtistOptions(e: WechatMiniprogram.TouchEvent) {
      console.log('触发 showAddArtistOptions 方法', e);
      console.log('点击的元素:', e.currentTarget.dataset.test);
      console.log('当前 showAddOptions 状态:', this.data.showAddOptions);
      this.setData({
        showAddOptions: true
      }, () => {
        console.log('设置后 showAddOptions 状态:', this.data.showAddOptions);
      });
    },

    // 关闭添加画师选项
    closeAddOptions() {
      this.setData({
        showAddOptions: false
      });
    },

    // 显示插入位置选项
    showInsertOptions() {
      this.setData({
        showAddOptions: false,
        showInsertOptions: true
      });
    },

    // 关闭插入位置选项
    closeInsertOptions() {
      this.setData({
        showInsertOptions: false
      });
    },

    // 合并完整图鉴
    async mergeCompleteAtlas() {
      // 获取已选中的画师
      const selectedArtists = this.data.artistList.filter(artist => artist.isSelected);
      
      if (selectedArtists.length === 0) {
        wx.showToast({
          title: '请先选择要合并的画师',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示进度弹窗
        this.setData({
          showProgress: true,
          progress: 0,
          progressStatus: '准备合并...'
        });

        // 检查 canvas 是否已初始化
        if (!this._canvas || !this._ctx) {
          throw new Error('Canvas 未初始化');
        }

        const canvas = this._canvas;
        const ctx = this._ctx;

        // 计算总高度（每个画师的高度相同，等于原始图片高度）
        const spacing = this.data.mergeSettings.spacing;
        const totalHeight = (this.data.originalSize.height * selectedArtists.length) + 
                           (spacing * (selectedArtists.length - 1));
        
        // 设置画布尺寸
        canvas.width = this.data.originalSize.width;
        canvas.height = totalHeight;

        // 设置背景色
        ctx.fillStyle = this.data.mergeSettings.backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 加载背景图
        const backgroundImage = canvas.createImage();
        await new Promise((resolve, reject) => {
          backgroundImage.onload = resolve;
          backgroundImage.onerror = () => reject(new Error('背景图加载失败'));
          backgroundImage.src = this.data.backgroundImage;
        });

        // 计算每个画师完成后的进度增量
        const progressIncrement = 100 / selectedArtists.length;

        // 逐个处理每个画师的图鉴
        for (let i = 0; i < selectedArtists.length; i++) {
          const artist = selectedArtists[i];
          const yOffset = i * (this.data.originalSize.height + spacing);

          this.setData({
            progressStatus: `正在处理第 ${i + 1}/${selectedArtists.length} 个画师的图鉴...`
          });

          // 绘制背景
          ctx.drawImage(
            backgroundImage,
            0,
            yOffset,
            this.data.originalSize.width,
            this.data.originalSize.height
          );

          // 计算展示区边距
          const widthScale = this.data.originalSize.width / this.data.displaySize.width;
          const topMargin = parseInt(this.data.overlayInfo.top) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const rightMargin = parseInt(this.data.overlayInfo.right) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const bottomMargin = parseInt(this.data.overlayInfo.bottom) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const leftMargin = parseInt(this.data.overlayInfo.left) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);

          // 绘制半透明展示区
          ctx.fillStyle = this.data.overlaySettings.backgroundColor;
          const overlayY = yOffset + topMargin;
          
          if (this.data.overlaySettings.borderRadius > 0) {
            const radius = this.data.overlaySettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
            this.drawRoundedRect(
              ctx,
              leftMargin,
              overlayY,
              canvas.width - (leftMargin + rightMargin),
              this.data.originalSize.height - (topMargin + bottomMargin),
              radius
            );
            ctx.fill();
          } else {
            ctx.fillRect(
              leftMargin,
              overlayY,
              canvas.width - (leftMargin + rightMargin),
              this.data.originalSize.height - (topMargin + bottomMargin)
            );
          }

          // 如果设置了边框，绘制边框
          if (this.data.overlaySettings.borderWidth > 0) {
            ctx.strokeStyle = this.data.overlaySettings.borderColor;
            ctx.lineWidth = this.data.overlaySettings.borderWidth;
            
            if (this.data.overlaySettings.borderStyle === 'dashed') {
              ctx.setLineDash([10, 10]);
            } else if (this.data.overlaySettings.borderStyle === 'dotted') {
              ctx.setLineDash([2, 2]);
            } else {
              ctx.setLineDash([]);
            }

            if (this.data.overlaySettings.borderRadius > 0) {
              const radius = this.data.overlaySettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
              this.drawRoundedRect(
                ctx,
                leftMargin,
                overlayY,
                canvas.width - (leftMargin + rightMargin),
                this.data.originalSize.height - (topMargin + bottomMargin),
                radius
              );
              ctx.stroke();
            } else {
              ctx.strokeRect(
                leftMargin,
                overlayY,
                canvas.width - (leftMargin + rightMargin),
                this.data.originalSize.height - (topMargin + bottomMargin)
              );
            }
          }

          // 绘制作品
          const works = artist.works;
          if (works.length > 0) {
            const overlayWidth = canvas.width - (leftMargin + rightMargin);
            const overlayHeight = this.data.originalSize.height - (topMargin + bottomMargin);
            
            const centerColumnWidth = overlayWidth / 2;
            const sideColumnWidth = overlayWidth / 4;
            
            const padding = 10 * widthScale;
            const gap = 20 * widthScale;
            
            const usableWidth = centerColumnWidth - (padding * 2);
            const maxWorkWidth = usableWidth - (gap * (works.length - 1)) / works.length;
            const maxWorkHeight = overlayHeight - (padding * 2);

            // 加载并绘制作品
            for (let j = 0; j < works.length; j++) {
              const work = works[j];
              const workImage = canvas.createImage();
              
              await new Promise((resolve, reject) => {
                workImage.onload = resolve;
                workImage.onerror = () => reject(new Error(`作品${j + 1}加载失败`));
                workImage.src = work.url;
              });
              
              const scale = Math.min(
                maxWorkWidth / workImage.width,
                maxWorkHeight / workImage.height
              );
              const scaledWidth = workImage.width * scale;
              const scaledHeight = workImage.height * scale;
              
              const totalWorksWidth = (scaledWidth * works.length) + (gap * (works.length - 1));
              const startX = leftMargin + sideColumnWidth + (centerColumnWidth - totalWorksWidth) / 2;
              const x = startX + (scaledWidth + gap) * j;
              const y = overlayY + (overlayHeight - scaledHeight) / 2;
              
              // 绘制作品
              if (this.data.showWorkGlow) {
                ctx.shadowColor = this.data.workGlowColor;
                ctx.shadowBlur = this.data.workGlowIntensity * widthScale * 4;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
                
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              } else {
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              }

              // 绘制边框
              if (this.data.showWorkBorder) {
                ctx.strokeStyle = this.data.workBorderColor;
                ctx.lineWidth = this.data.workBorderWidth * widthScale;
                ctx.strokeRect(x, y, scaledWidth, scaledHeight);
              }

              // 重置阴影效果
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;

              // 绘制作品名称
              if (work.name) {
                const fontSize = Math.round(16 * widthScale * 0.5);
                ctx.font = `${fontSize}px sans-serif`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';

                const textPadding = 8 * widthScale;
                const textMetrics = ctx.measureText(work.name);
                const textWidth = Math.min(scaledWidth, textMetrics.width + textPadding * 2);
                const textHeight = fontSize + textPadding * 2;
                const textX = x;
                const textY = y + scaledHeight - textHeight;

                const gradient = ctx.createLinearGradient(textX, textY, textX, y + scaledHeight);
                gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
                gradient.addColorStop(1, this.data.workNameBgColor);
                ctx.fillStyle = gradient;
                ctx.fillRect(textX, textY, scaledWidth, textHeight);

                ctx.fillStyle = this.data.workNameColor;
                ctx.fillText(
                  work.name,
                  x + scaledWidth / 2,
                  y + scaledHeight - textPadding / 2
                );
              }
            }
          }

          // 绘制画师名字标签
          const ratio = this.data.originalSize.width / this.data.displaySize.width;
          let finalTagX = this.data.tagPosition.x * ratio;
          let finalTagY = yOffset + (this.data.tagPosition.y * ratio);
          
          const fontSize = Math.round(28 * ratio * 0.5);
          ctx.font = `${fontSize}px sans-serif`;
          const text = artist.name || '画师名字';
          const textMetrics = ctx.measureText(text);
          
          const padding = {
            x: 8 * ratio,
            y: 4 * ratio
          };
          
          const boxWidth = textMetrics.width + (padding.x * 2);
          const boxHeight = fontSize + (padding.y * 2);

          // 确保标签不会超出画布边界
          finalTagX = Math.max(0, Math.min(finalTagX, canvas.width - boxWidth));
          finalTagY = Math.max(yOffset, Math.min(finalTagY, yOffset + this.data.originalSize.height - boxHeight));

          // 绘制标签背景
          if (this.data.fontSettings.borderRadius > 0) {
            const radius = this.data.fontSettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            this.drawRoundedRect(ctx, finalTagX, finalTagY, boxWidth, boxHeight, radius);
            ctx.fill();
          } else {
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.fillRect(finalTagX, finalTagY, boxWidth, boxHeight);
          }
          
          // 绘制标签边框
          if (this.data.fontSettings.borderWidth > 0) {
            ctx.strokeStyle = this.data.fontSettings.borderColor;
            ctx.lineWidth = this.data.fontSettings.borderWidth;
            
            if (this.data.fontSettings.borderStyle === 'dashed') {
              ctx.setLineDash([10, 10]);
            } else if (this.data.fontSettings.borderStyle === 'dotted') {
              ctx.setLineDash([2, 2]);
            } else {
              ctx.setLineDash([]);
            }
            
            if (this.data.fontSettings.borderRadius > 0) {
              const radius = this.data.fontSettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
              this.drawRoundedRect(ctx, finalTagX, finalTagY, boxWidth, boxHeight, radius);
              ctx.stroke();
            } else {
              ctx.strokeRect(finalTagX, finalTagY, boxWidth, boxHeight);
            }
          }
          
          // 绘制标签文字
          ctx.fillStyle = this.data.fontSettings.color;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(
            text,
            finalTagX + boxWidth / 2,
            finalTagY + boxHeight / 2
          );

          // 更新进度
          this.setData({
            progress: Math.round((i + 1) * progressIncrement)
          });
        }

        // 如果启用了整体边框，绘制边框
        if (this.data.mergeSettings.showBorder) {
          ctx.strokeStyle = this.data.mergeSettings.borderColor;
          ctx.lineWidth = this.data.mergeSettings.borderWidth;
          ctx.strokeRect(0, 0, canvas.width, canvas.height);
        }

        // 导出最终图片
        const result = await new Promise<WechatMiniprogram.CanvasToTempFilePathSuccessCallbackResult>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvas as unknown as WechatMiniprogram.Canvas,
            width: canvas.width,
            height: canvas.height,
            destWidth: canvas.width,
            destHeight: canvas.height,
            fileType: 'png',
            quality: 1,
            success: resolve,
            fail: reject
          });
        });

        // 保存到相册
        await wx.saveImageToPhotosAlbum({
          filePath: result.tempFilePath
        });

        this.setData({
          showProgress: false,
          progress: 0,
          progressStatus: ''
        });

        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });

      } catch (err) {
        console.error('合并图鉴失败：', err);
        this.setData({
          showProgress: false,
          progress: 0,
          progressStatus: ''
        });
        wx.showToast({
          title: '合并失败',
          icon: 'error'
        });
      }
    },

    // 辅助方法：绘制圆角矩形
    drawRoundedRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
    },

    // 显示合并设置弹窗
    showMergeSettings() {
      this.setData({
        showMergeSettings: true
      });
    },

    // 关闭合并设置弹窗
    closeMergeSettings() {
      this.setData({
        showMergeSettings: false
      });
    },

    // 修改合并背景色
    onMergeBgColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'mergeSettings.backgroundColor': e.detail.color
      });
    },

    // 调整图像间距
    adjustMergeSpacing(e: WechatMiniprogram.SliderChange) {
      this.setData({
        'mergeSettings.spacing': e.detail.value
      });
    },

    // 切换整体边框显示
    toggleMergeBorder(e: WechatMiniprogram.SwitchChange) {
      this.setData({
        'mergeSettings.showBorder': e.detail.value
      });
    },

    // 修改边框颜色
    onMergeBorderColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'mergeSettings.borderColor': e.detail.color
      });
    },

    // 调整边框宽度
    adjustMergeBorderWidth(e: WechatMiniprogram.SliderChange) {
      this.setData({
        'mergeSettings.borderWidth': e.detail.value
      });
    },

    // 开始合并图鉴
    startMergeAtlas() {
      this.setData({
        showMergeSettings: false
      }, () => {
        this.mergeCompleteAtlas();
      });
    },

    // 批量下载单画师图鉴
    async batchDownloadAtlas() {
      // 获取已选中的画师
      const selectedArtists = this.data.artistList.filter(artist => artist.isSelected);
      
      if (selectedArtists.length === 0) {
        wx.showToast({
          title: '请先选择要下载的画师',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示进度弹窗
        this.setData({
          showProgress: true,
          progress: 0,
          progressStatus: '准备下载...'
        });

        // 检查 canvas 是否已初始化
        if (!this._canvas || !this._ctx) {
          throw new Error('Canvas 未初始化');
        }

        const canvas = this._canvas;
        const ctx = this._ctx;

        // 计算每个画师完成后的进度增量
        const progressIncrement = 100 / selectedArtists.length;

        // 逐个处理每个画师的图鉴
        for (let i = 0; i < selectedArtists.length; i++) {
          const artist = selectedArtists[i];
          
          this.setData({
            progressStatus: `正在处理第 ${i + 1}/${selectedArtists.length} 个画师的图鉴...`
          });

          // 设置画布尺寸为原始图片尺寸
          canvas.width = this.data.originalSize.width;
          canvas.height = this.data.originalSize.height;

          // 加载背景图
          const backgroundImage = canvas.createImage();
          await new Promise((resolve, reject) => {
            backgroundImage.onload = resolve;
            backgroundImage.onerror = () => reject(new Error('背景图加载失败'));
            backgroundImage.src = this.data.backgroundImage;
          });

          // 绘制背景
          ctx.drawImage(
            backgroundImage,
            0,
            0,
            this.data.originalSize.width,
            this.data.originalSize.height
          );

          // 计算展示区边距
          const widthScale = this.data.originalSize.width / this.data.displaySize.width;
          const topMargin = parseInt(this.data.overlayInfo.top) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const rightMargin = parseInt(this.data.overlayInfo.right) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const bottomMargin = parseInt(this.data.overlayInfo.bottom) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);
          const leftMargin = parseInt(this.data.overlayInfo.left) * widthScale / (BASE_SIZES.DESIGN_WIDTH / this.data.displaySize.width);

          // 绘制半透明展示区
          ctx.fillStyle = this.data.overlaySettings.backgroundColor;
          
          if (this.data.overlaySettings.borderRadius > 0) {
            const radius = this.data.overlaySettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
            this.drawRoundedRect(
              ctx,
              leftMargin,
              topMargin,
              canvas.width - (leftMargin + rightMargin),
              this.data.originalSize.height - (topMargin + bottomMargin),
              radius
            );
            ctx.fill();
          } else {
            ctx.fillRect(
              leftMargin,
              topMargin,
              canvas.width - (leftMargin + rightMargin),
              this.data.originalSize.height - (topMargin + bottomMargin)
            );
          }

          // 如果设置了边框，绘制边框
          if (this.data.overlaySettings.borderWidth > 0) {
            ctx.strokeStyle = this.data.overlaySettings.borderColor;
            ctx.lineWidth = this.data.overlaySettings.borderWidth;
            
            if (this.data.overlaySettings.borderStyle === 'dashed') {
              ctx.setLineDash([10, 10]);
            } else if (this.data.overlaySettings.borderStyle === 'dotted') {
              ctx.setLineDash([2, 2]);
            } else {
              ctx.setLineDash([]);
            }

            if (this.data.overlaySettings.borderRadius > 0) {
              const radius = this.data.overlaySettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
              this.drawRoundedRect(
                ctx,
                leftMargin,
                topMargin,
                canvas.width - (leftMargin + rightMargin),
                this.data.originalSize.height - (topMargin + bottomMargin),
                radius
              );
              ctx.stroke();
            } else {
              ctx.strokeRect(
                leftMargin,
                topMargin,
                canvas.width - (leftMargin + rightMargin),
                this.data.originalSize.height - (topMargin + bottomMargin)
              );
            }
          }

          // 绘制作品
          const works = artist.works;
          if (works.length > 0) {
            const overlayWidth = canvas.width - (leftMargin + rightMargin);
            const overlayHeight = this.data.originalSize.height - (topMargin + bottomMargin);
            
            const centerColumnWidth = overlayWidth / 2;
            const sideColumnWidth = overlayWidth / 4;
            
            const padding = 10 * widthScale;
            const gap = 20 * widthScale;
            
            const usableWidth = centerColumnWidth - (padding * 2);
            const maxWorkWidth = usableWidth - (gap * (works.length - 1)) / works.length;
            const maxWorkHeight = overlayHeight - (padding * 2);

            // 加载并绘制作品
            for (let j = 0; j < works.length; j++) {
              const work = works[j];
              const workImage = canvas.createImage();
              
              await new Promise((resolve, reject) => {
                workImage.onload = resolve;
                workImage.onerror = () => reject(new Error(`作品${j + 1}加载失败`));
                workImage.src = work.url;
              });
              
              const scale = Math.min(
                maxWorkWidth / workImage.width,
                maxWorkHeight / workImage.height
              );
              const scaledWidth = workImage.width * scale;
              const scaledHeight = workImage.height * scale;
              
              const totalWorksWidth = (scaledWidth * works.length) + (gap * (works.length - 1));
              const startX = leftMargin + sideColumnWidth + (centerColumnWidth - totalWorksWidth) / 2;
              const x = startX + (scaledWidth + gap) * j;
              const y = topMargin + (overlayHeight - scaledHeight) / 2;
              
              // 绘制作品
              if (this.data.showWorkGlow) {
                ctx.shadowColor = this.data.workGlowColor;
                ctx.shadowBlur = this.data.workGlowIntensity * widthScale * 4;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
                
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              } else {
                ctx.drawImage(workImage, x, y, scaledWidth, scaledHeight);
              }

              // 绘制边框
              if (this.data.showWorkBorder) {
                ctx.strokeStyle = this.data.workBorderColor;
                ctx.lineWidth = this.data.workBorderWidth * widthScale;
                ctx.strokeRect(x, y, scaledWidth, scaledHeight);
              }

              // 重置阴影效果
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;

              // 绘制作品名称
              if (work.name) {
                const fontSize = Math.round(16 * widthScale * 0.5);
                ctx.font = `${fontSize}px sans-serif`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';

                const textPadding = 8 * widthScale;
                const textMetrics = ctx.measureText(work.name);
                const textWidth = Math.min(scaledWidth, textMetrics.width + textPadding * 2);
                const textHeight = fontSize + textPadding * 2;
                const textX = x;
                const textY = y + scaledHeight - textHeight;

                const gradient = ctx.createLinearGradient(textX, textY, textX, y + scaledHeight);
                gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
                gradient.addColorStop(1, this.data.workNameBgColor);
                ctx.fillStyle = gradient;
                ctx.fillRect(textX, textY, scaledWidth, textHeight);

                ctx.fillStyle = this.data.workNameColor;
                ctx.fillText(
                  work.name,
                  x + scaledWidth / 2,
                  y + scaledHeight - textPadding / 2
                );
              }
            }
          }

          // 绘制画师名字标签
          const ratio = this.data.originalSize.width / this.data.displaySize.width;
          let finalTagX = this.data.tagPosition.x * ratio;
          let finalTagY = this.data.tagPosition.y * ratio;
          
          const fontSize = Math.round(28 * ratio * 0.5);
          ctx.font = `${fontSize}px sans-serif`;
          const text = artist.name || '画师名字';
          const textMetrics = ctx.measureText(text);
          
          const padding = {
            x: 8 * ratio,
            y: 4 * ratio
          };
          
          const boxWidth = textMetrics.width + (padding.x * 2);
          const boxHeight = fontSize + (padding.y * 2);

          // 确保标签不会超出画布边界
          finalTagX = Math.max(0, Math.min(finalTagX, canvas.width - boxWidth));
          finalTagY = Math.max(0, Math.min(finalTagY, canvas.height - boxHeight));

          // 绘制标签背景
          if (this.data.fontSettings.borderRadius > 0) {
            const radius = this.data.fontSettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            this.drawRoundedRect(ctx, finalTagX, finalTagY, boxWidth, boxHeight, radius);
            ctx.fill();
          } else {
            ctx.fillStyle = this.data.fontSettings.backgroundColor;
            ctx.fillRect(finalTagX, finalTagY, boxWidth, boxHeight);
          }
          
          // 绘制标签边框
          if (this.data.fontSettings.borderWidth > 0) {
            ctx.strokeStyle = this.data.fontSettings.borderColor;
            ctx.lineWidth = this.data.fontSettings.borderWidth;
            
            if (this.data.fontSettings.borderStyle === 'dashed') {
              ctx.setLineDash([10, 10]);
            } else if (this.data.fontSettings.borderStyle === 'dotted') {
              ctx.setLineDash([2, 2]);
            } else {
              ctx.setLineDash([]);
            }
            
            if (this.data.fontSettings.borderRadius > 0) {
              const radius = this.data.fontSettings.borderRadius * (this.data.originalSize.width / this.data.displaySize.width);
              this.drawRoundedRect(ctx, finalTagX, finalTagY, boxWidth, boxHeight, radius);
              ctx.stroke();
            } else {
              ctx.strokeRect(finalTagX, finalTagY, boxWidth, boxHeight);
            }
          }
          
          // 绘制标签文字
          ctx.fillStyle = this.data.fontSettings.color;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(
            text,
            finalTagX + boxWidth / 2,
            finalTagY + boxHeight / 2
          );

          // 导出当前画师的图鉴
          const result = await new Promise<WechatMiniprogram.CanvasToTempFilePathSuccessCallbackResult>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: canvas as unknown as WechatMiniprogram.Canvas,
              width: canvas.width,
              height: canvas.height,
              destWidth: canvas.width,
              destHeight: canvas.height,
              fileType: 'png',
              quality: 1,
              success: resolve,
              fail: reject
            });
          });

          // 保存到相册
          await wx.saveImageToPhotosAlbum({
            filePath: result.tempFilePath
          });

          // 更新进度
          this.setData({
            progress: Math.round((i + 1) * progressIncrement)
          });
        }

        this.setData({
          showProgress: false,
          progress: 0,
          progressStatus: ''
        });

        wx.showToast({
          title: '已全部保存到相册',
          icon: 'success'
        });

      } catch (err) {
        console.error('批量下载图鉴失败：', err);
        this.setData({
          showProgress: false,
          progress: 0,
          progressStatus: ''
        });
        wx.showToast({
          title: '下载失败',
          icon: 'error'
        });
      }
    },
  }
});

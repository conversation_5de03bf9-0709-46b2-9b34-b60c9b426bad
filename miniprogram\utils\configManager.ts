import { WxConfig } from '../types/config';

class ConfigManager {
  private static instance: ConfigManager;
  private config: WxConfig | null;
  private observers: ((config: WxConfig) => void)[];
  private defaultConfigPath: string;
  private userConfigPath: string;
  private remoteConfigUrl: string;
  private maxRetries: number;
  private retryDelay: number;

  private constructor() {
    this.config = null;
    this.observers = [];
    this.defaultConfigPath = "wx_config/wx-config.json";
    this.userConfigPath = `${wx.env.USER_DATA_PATH}/wx-config.json`;
    this.remoteConfigUrl = "https://filewx.feifanclub.cn/wx_config/wx-config.json";
    this.maxRetries = 3;
    this.retryDelay = 1000;
  }

  // 获取单例实例
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  // 从远程获取配置
  private async fetchRemoteConfig(): Promise<string> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.remoteConfigUrl,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data) {
            resolve(JSON.stringify(res.data));
          } else {
            reject(new Error(`获取远程配置失败: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  // 加载配置
  async loadConfig(): Promise<WxConfig | null> {
    let retryCount = 0;
    let configContent: string = JSON.stringify(this.getDefaultConfig()); // 初始化为默认配置

    while (retryCount <= this.maxRetries) {
      try {

        const fileManager = wx.getFileSystemManager();
        wx.showLoading({ title: '内容加载中...' });
        // 首先尝试从远程获取配置
        try {
          configContent = await this.fetchRemoteConfig();
          console.log("成功从远程获取配置");
          
          // 保存远程配置到本地
          try {
            fileManager.writeFileSync(this.userConfigPath, configContent, "utf-8");
            console.log("远程配置已保存到本地");
          } catch (writeError) {
            console.log("保存远程配置到本地失败，继续使用远程配置");
          }
          wx.hideLoading();
          break; // 成功获取远程配置，跳出重试循环
          
        } catch (remoteError) {
          console.log("获取远程配置失败，尝试读取本地配置");
          
          // 尝试读取本地用户配置
          try {
            configContent = fileManager.readFileSync(this.userConfigPath, "utf-8") as string;
            console.log("成功加载本地用户配置");
            break; // 成功读取本地配置，跳出重试循环
          } catch (localError) {
            console.log("本地配置不存在，使用默认配置");
            configContent = JSON.stringify(this.getDefaultConfig());
            
            // 尝试保存默认配置到本地
            try {
              fileManager.writeFileSync(this.userConfigPath, configContent, "utf-8");
              console.log("默认配置已保存为本地配置");
            } catch (writeError) {
              console.log("保存默认配置失败，继续使用默认配置");
            }
            
            break; // 使用默认配置，跳出重试循环
          }
        }
      } catch (error) {
        retryCount++;
        if (retryCount <= this.maxRetries) {
          console.log(`配置加载失败，${retryCount}秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        } else {
          console.error("配置加载失败，使用默认配置");
          configContent = JSON.stringify(this.getDefaultConfig());
        }
      }
    }

    try {
      this.config = JSON.parse(configContent);
      this.notifyObservers();
      return this.config;
    } catch (parseError) {
      console.error("解析配置失败:", parseError);
      this.config = this.getDefaultConfig();
      return this.config;
    }
  }

  // 获取默认配置
  getDefaultConfig(): WxConfig {
    return {
      version: "1.0.0",
      theme: {
        primaryColor: "#07c160",
        secondaryColor: "#999999",
        backgroundColor: "#f7f7f7",
        fontSize: {
          small: "24rpx",
          normal: "28rpx",
          large: "32rpx"
        },
        spacing: {
          small: "8rpx",
          normal: "16rpx",
          large: "24rpx"
        }
      },
      navigation: {
        topBar: {
          backgroundColor: "#ffffff",
          titleColor: "#000000",
          titleSize: "32rpx",
          height: "88rpx"
        },
        tabBar: {
          backgroundColor: "#ffffff",
          borderColor: "#eeeeee",
          selectedColor: "#07c160",
          color: "#999999",
          height: "98rpx"
        }
      },
      tabList: [
        { id: 1, text: "首页", icon: "home", url: "/pages/index/index", visible: true, showInHome: true },
        { id: 2, text: "图像网格", icon: "grid", url: "/pages/tool/grid/grid", visible: true, showInHome: true },
        { id: 3, text: "图像裁剪", icon: "cut", url: "/pages/tool/img_cropper/img_cropper", visible: true, showInHome: true },
        { id: 4, text: "图像辅助", icon: "picture", url: "/pages/tool/img_help/img_help", visible: true, showInHome: true },
        { id: 5, text: "字体生成", icon: "editor-text", url: "/pages/tool/font_generation/font_generation", visible: true, showInHome: true }
      ],
      imageProcessing: {
        enabled: true,
        maxSize: 10 * 1024 * 1024, // 10MB
        supportFormats: ["jpg", "jpeg", "png", "gif"],
        cropSettings: {
          defaultAspectRatio: 1,
          maxWidth: 1024,
          maxHeight: 1024,
          quality: 0.8
        },
        gridLayout: {
          columns: 3,
          spacing: 10,
          padding: 15,
          defaultLayout: "3x3",
          maxImages: 9,
          backgroundColor: "#ffffff"
        },
        watermark: {
          enabled: false,
          text: "",
          position: "bottomRight",
          opacity: 0.7,
          fontSize: 28,
          fontColor: "#ffffff"
        },
        compression: {
          enabled: true,
          quality: 0.8,
          maxSize: 2048
        }
      },
      fontGeneration: {
        enabled: true,
        maxLength: 100,
        defaultFontSize: 32,
        supportedFonts: ["默认", "手写体", "艺术体"],
        defaultOptions: {
          fontSize: 32,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: "left",
          fontFamily: "默认"
        },
        text: {
          defaultSize: 24,
          defaultColor: "#000000",
          defaultFont: "Microsoft YaHei",
          alignment: "center"
        },
        background: {
          defaultColor: "#ffffff",
          opacity: 1,
          padding: 10
        },
        export: {
          format: "png",
          quality: 0.9
        },
        exportOptions: {
          formats: ["png", "jpg"],
          defaultFormat: "png",
          quality: 0.9
        },
        templates: []
      },
      pageConfig: {
        header: {
          height: 44,
          backgroundColor: "#ffffff",
          textColor: "#000000",
          fontSize: 16
        },
        footer: {
          height: 50,
          backgroundColor: "#ffffff",
          textColor: "#666666"
        },
        transitions: {
          duration: 300,
          type: "fade"
        }
      },
      componentStyle: {
        button: {
          primaryColor: "#07c160",
          secondaryColor: "#999999",
          borderRadius: 4,
          fontSize: 14
        },
        input: {
          height: 40,
          backgroundColor: "#f5f5f5",
          borderColor: "#e0e0e0",
          fontSize: 14
        },
        list: {
          itemHeight: 50,
          separatorColor: "#e0e0e0",
          backgroundColor: "#ffffff"
        }
      },
      utils: {
        imageProcess: {
          cacheEnabled: true,
          cacheExpiration: 86400,
          maxCacheSize: 50
        },
        request: {
          timeout: 10000,
          retryTimes: 3,
          retryDelay: 1000
        }
      },
      categoryList: [],
      ui: {
        animations: {
          pageTransition: true,
          duration: 300
        },
        loading: {
          text: "加载中...",
          icon: "loading"
        },
        toast: {
          duration: 1500,
          position: "center"
        },
        dialog: {
          confirmColor: "#07c160",
          cancelColor: "#999999"
        }
      },
      cache: {
        imageCache: {
          maxSize: 50 * 1024 * 1024,
          expires: 86400
        },
        configCache: {
          expires: 86400
        },
        fontCache: {
          maxItems: 100,
          expires: 86400
        }
      },
      errorHandling: {
        retryTimes: 3,
        retryDelay: 1000,
        errorMessages: {
          networkError: "网络连接失败，请检查网络设置",
          serverError: "服务器错误，请稍后重试",
          uploadError: "上传失败，请重试",
          downloadError: "下载失败，请重试"
        }
      },
      paperSizeInformation: [
        {
          category: "常用尺寸",
          specifications: [
            {
              paper: "A4",
              width: "210mm",
              height: "297mm",
              unit: "mm"
            },
            {
              paper: "A5",
              width: "148mm",
              height: "210mm",
              unit: "mm"
            }
          ]
        }
      ],
      artToolsIntro: {
        title: "图像工具集",
        description: "一套完整的图像处理工具",
        usageScenarios: [
          {
            title: "图片编辑",
            description: "裁剪、调整大小、添加水印等",
            tools: ["裁剪", "缩放", "水印"]
          }
        ],
        tips: [
          {
            title: "快速上手",
            content: "选择工具，上传图片即可开始编辑"
          }
        ]
      },
      features: {
        imageTools: {
          enabled: true,
          maxSize: 10 * 1024 * 1024,
          supportFormats: ["jpg", "jpeg", "png", "gif"],
          cropperOptions: {
            width: 750,
            height: 750,
            maxScale: 2.5,
            minScale: 1,
            quality: 0.8
          },
          gridOptions: {
            defaultLayout: "3x3",
            maxImages: 9,
            spacing: 4,
            backgroundColor: "#ffffff"
          },
          watermarkOptions: {
            defaultText: "我的水印",
            defaultPosition: "bottomRight",
            fontSize: 28,
            fontColor: "#ffffff",
            opacity: 0.7
          }
        },
        gridTool: {
          enabled: true,
          maxGridSize: 9,
          defaultGridSize: 3,
          spacing: 4
        },
        fontGeneration: {
          enabled: true,
          maxLength: 100,
          defaultFontSize: 32,
          supportedFonts: ["默认", "手写体", "艺术体"],
          defaultOptions: {
            fontSize: 32,
            lineHeight: 1.5,
            letterSpacing: 0,
            textAlign: "left",
            fontFamily: "默认"
          },
          text: {
            defaultSize: 24,
            defaultColor: "#000000",
            defaultFont: "Microsoft YaHei",
            alignment: "center"
          },
          background: {
            defaultColor: "#ffffff",
            opacity: 1,
            padding: 10
          },
          export: {
            format: "png",
            quality: 0.9
          },
          exportOptions: {
            formats: ["png", "jpg"],
            defaultFormat: "png",
            quality: 0.9
          },
          templates: []
        }
      }
    };
  }

  // 更新配置
  async updateConfig(newConfig: Partial<WxConfig>): Promise<void> {
    if (!this.config) {
      await this.loadConfig();
    }
    
    this.config = Object.assign({}, this.config, newConfig);

    try {
      wx.getFileSystemManager().writeFileSync(
        this.userConfigPath,
        JSON.stringify(this.config, null, 2),
        "utf-8"
      );
      this.notifyObservers();
      console.log("配置更新成功");
    } catch (error) {
      console.error("更新配置文件失败:", error);
    }
  }

  // 获取配置
  getConfig<T extends keyof WxConfig>(key?: T): T extends undefined ? WxConfig : WxConfig[T] | null {
    if (!this.config) {
      return null as any;
    }
    return key ? this.config[key] : this.config as any;
  }

  // 订阅配置变化
  subscribe(callback: (config: WxConfig) => void): () => void {
    this.observers.push(callback);
    return () => {
      this.observers = this.observers.filter(cb => cb !== callback);
    };
  }

  // 通知观察者
  private notifyObservers(): void {
    if (this.config) {
      this.observers.forEach(observer => observer(this.config!));
    }
  }

  // 热重载配置
  async hotReload(): Promise<void> {
    await this.loadConfig();
  }
}

// 导出单例实例
export const configManager = ConfigManager.getInstance();
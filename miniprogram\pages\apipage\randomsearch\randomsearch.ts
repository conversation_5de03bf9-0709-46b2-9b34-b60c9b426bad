// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api, {} from '../../../utils/api';
import eventBus from '../../../utils/eventBus';

// 常量定义
const CONSTANTS = {
  COUNTDOWN_DURATION: 15,
  DEFAULT_FAVORITES: 1,
  FAVORITES_MULTIPLIER: {
    MIN: 25,
    MAX: 85
  },
  SHARE_CONFIG: {
    title: '随机人设',
    path: '/pages/apipage/randomsearch/randomsearch'
  },
  LOADING_STEPS: {
    START: { progress: 0, text: '网络搜集人设作品中...' },
    DOWNLOAD: { progress: 25, text: '已找到，正在筛选中...\n筛选过程略慢，请耐心等待。' },
    PROCESS: { progress: 50, text: '整理评分9以上图像...' },
    CONVERT: { progress: 75, text: '整理完成，计算形体准确性...' },
    COMPLETE: { progress: 100, text: '形体计算完成，正在调整为高清图像...\n调整过程略慢，请耐心等待。' }
  }
} as const;

// 工具函数
const utils = {
  // Promise化微信API
  promisify: <T>(api: Function, options: any): Promise<T> => {
    return new Promise((resolve, reject) => {
      api({
        ...options,
        success: resolve,
        fail: reject
      });
    });
  },

  // 显示提示
  showToast: (title: string, type: 'success' | 'none' = 'none') => {
    wx.showToast({
      title,
      icon: type
    });
  },

  // 处理授权错误
  handleAuthError: (err: any) => {
    if (err.errMsg.includes('auth deny')) {
      wx.showModal({
        title: '提示',
        content: '需要您授权保存图片到相册',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting();
          }
        }
      });
    } else {
      utils.showToast('操作失败');
    }
  }
};

interface Canvas {
  createImage(): {
    onload: (value: unknown) => void;
    onerror: (err: any) => void;
    src: string;
    width: number;
    height: number;
  };
  getContext(type: string): any;
  width: number;
  height: number;
}

interface OffscreenCanvas extends Canvas {
  width: number;
  height: number;
}

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    currentDetail: null,
    loading: true,
    isEmpty: false,
    countdown: 0,
    scrollTop: 0,
    localImagePath: '',
    loadingProgress: 0,
    loadingText: '准备加载...',
    loadingStep: 1,
    loadingStepCount: 5
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      this.showApi('waifu_random_search',{'img':'sdytsyshdf'});
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },

    // 获取随机收藏数
    getRandomFavorites(originalFavorites: number): number {
      const { MIN, MAX } = CONSTANTS.FAVORITES_MULTIPLIER;
      const multiplier = Math.floor(Math.random() * (MAX - MIN + 1)) + MIN;
      return originalFavorites * multiplier;
    },

    // 过滤标签
    filterTags(tags: any[]): any[] {
      if (!Array.isArray(tags)) return [];
      return tags.filter(tag => tag.name.toLowerCase() !== 'waifu');
    },

    // 更新加载进度
    updateLoadingStatus(step: keyof typeof CONSTANTS.LOADING_STEPS) {
      const { progress, text } = CONSTANTS.LOADING_STEPS[step];
      this.setData({
        loadingProgress: progress,
        loadingText: text,
        loadingStep: Math.ceil((progress / 100) * this.data.loadingStepCount)
      });
    },

    // 处理远程图片
    async processRemoteImage(imageUrl: string): Promise<string> {
      try {
        const steps = Object.values(CONSTANTS.LOADING_STEPS);
        let currentStep = 0;
        const progressInterval = setInterval(() => {
          if (currentStep < steps.length - 1) {
            currentStep++;
            const { progress, text } = steps[currentStep];
            this.setData({
              loadingProgress: progress,
              loadingText: text,
              loadingStep: currentStep + 1
            });
          }
        }, 800);

        this.updateLoadingStatus('START');

        // 获取canvas上下文
        const canvas = await new Promise<WechatMiniprogram.Canvas>((resolve, reject) => {
          this.createSelectorQuery()
            .select('#processCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              if (res && res[0] && res[0].node) {
                resolve(res[0].node);
              } else {
                reject(new Error('获取canvas节点失败'));
              }
            });
        });

        // 创建图片对象
        const img = canvas.createImage();
        
        // 加载远程图片
        this.updateLoadingStatus('DOWNLOAD');
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = () => reject(new Error('图片加载失败'));
          img.src = imageUrl; // 直接设置远程URL
        });

        // 设置canvas尺寸为图片尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 获取绘图上下文
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('获取canvas上下文失败');
        }

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制图片到画布
        this.updateLoadingStatus('PROCESS');
        ctx.drawImage(img, 0, 0, img.width, img.height);

        // 转换为本地临时文件
        this.updateLoadingStatus('CONVERT');
        const result = await utils.promisify<WechatMiniprogram.CanvasToTempFilePathSuccessCallbackResult>(
          wx.canvasToTempFilePath,
          {
            canvas,
            quality: 1,
            fileType: 'png'
          }
        );

        this.updateLoadingStatus('COMPLETE');

        clearInterval(progressInterval);
        return result.tempFilePath;
      } catch (error) {
        console.error('处理图片失败:', error);
        throw error;
      }
    },

    // 显示api
    async showApi(e: any, params: any) {
      this.setData({ 
        loading: true,
        loadingProgress: 0,
        loadingText: CONSTANTS.LOADING_STEPS.START.text
      });
      
      try {
        // 开始API调用
        this.updateLoadingStatus('START');
        const res = await Api.api.getapi(e, params);
        // console.log('res',res);
        
        // 检查是否是NSFW内容
        if (res && res.images && res.images[0] && res.images[0].is_nsfw) {
          this.showApi(e, params);
          return;
        }

        // 处理图片数据
        if (res && res.images) {
          this.updateLoadingStatus('DOWNLOAD');
          res.images = res.images.map((img: any) => ({
            ...img,
            isLoaded: false,
            favorites: this.getRandomFavorites(parseInt(img.favorites) || CONSTANTS.DEFAULT_FAVORITES),
            tags: this.filterTags(img.tags)
          }));

          // 处理第一张图片
          if (res.images[0] && res.images[0].url) {
            try {
              const localPath = await this.processRemoteImage(res.images[0].url);
              if (localPath) {
                this.setData({ localImagePath: localPath });
              }
            } catch (error) {
              console.error('处理图片失败:', error);
              utils.showToast('图片处理失败');
            }
          }
        }

        this.setData({
          currentDetail: res,
          loading: false,
          isEmpty: !(res && res.images && res.images.length)
        });
      } catch (error) {
        console.error('获取数据失败:', error);
        utils.showToast('获取数据失败');
        this.setData({
          loading: false,
          isEmpty: true
        });
      }
    },

    // 下载图片
    async downloadImage() {
      const { localImagePath } = this.data;
      if (!localImagePath) {
        utils.showToast('图片未准备好');
        return;
      }

      console.log('准备保存图片:', localImagePath);
      wx.showLoading({ title: '正在保存...', mask: true });

      try {
        // 检查授权状态
        const auth = await utils.promisify<WechatMiniprogram.GetSettingSuccessCallbackResult>(
          wx.getSetting,
          {}
        );

        if (!auth.authSetting['scope.writePhotosAlbum']) {
          const res = await utils.promisify(wx.authorize, {
            scope: 'scope.writePhotosAlbum'
          }).catch(() => null);

          if (!res) {
            wx.hideLoading();
            wx.showModal({
              title: '提示',
              content: '需要您授权保存图片到相册',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting();
                }
              }
            });
            return;
          }
        }

        // 保存图片
        await utils.promisify<WechatMiniprogram.SaveImageToPhotosAlbumSuccessCallback>(
          wx.saveImageToPhotosAlbum,
          { filePath: localImagePath }
        );

        wx.hideLoading();
        utils.showToast('保存成功', 'success');
      } catch (error) {
        console.error('保存图片失败:', error);
        wx.hideLoading();
        
        if (error && error.errMsg && error.errMsg.includes('auth deny')) {
          utils.handleAuthError(error);
        } else {
          utils.showToast('保存失败，请重试');
        }
      }
    },

    // 开始倒计时
    startCountdown() {
      this.setData({ countdown: CONSTANTS.COUNTDOWN_DURATION });
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
      this.countdownTimer = setInterval(() => {
        const currentCount = this.data.countdown;
        if (currentCount <= 1) {
          clearInterval(this.countdownTimer);
          this.setData({ countdown: 0 });
        } else {
          this.setData({ countdown: currentCount - 1 });
        }
      }, 1000);
    },

    // 刷新图片
    refreshImage() {
      if (this.data.loading || this.data.countdown > 0) return;
      
      wx.vibrateShort({ type: 'light' });
      this.showApi('waifu_random_search', {'img':'sdytsyshdf'});
      this.startCountdown();
      this.setData({ scrollTop: 0 });
    },

    // 图片加载完成事件
    onImageLoad(e: any) {
      const { index } = e.currentTarget.dataset;
      this.setData({
        [`currentDetail.images[${index}].isLoaded`]: true
      });
    },

    // 打开链接
    openLink(e: any) {
      const { url } = e.currentTarget.dataset;
      if (!url) return;
      
      wx.setClipboardData({
        data: url,
        success: () => utils.showToast('链接已复制', 'success')
      });
    },

    // 防止滚动穿透
    preventTouchMove: () => false,

    // 分享到朋友圈
    onShareTimeline() {
      return {
        ...CONSTANTS.SHARE_CONFIG,
        query: '',
        imageUrl: this.data.localImagePath || ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        ...CONSTANTS.SHARE_CONFIG,
        imageUrl: this.data.localImagePath || ''
      };
    }
  }
});

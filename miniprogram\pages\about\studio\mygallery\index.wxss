/* 背景图片 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

/* 筛选容器样式 */
.filter-container {
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.95);
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  margin: 16rpx 16rpx 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.filter-option {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #718096;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.count-badge {
  font-size: 22rpx;
  background: #EDF2F7;
  color: #4A5568;
  border-radius: 24rpx;
  padding: 2rpx 12rpx;
  margin-top: 6rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: normal;
}

.filter-option.active {
  color: #3C7FD7;
  font-weight: 600;
}

.filter-option.active .count-badge {
  background: #3C7FD7;
  color: white;
}

.filter-option.active::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #3C7FD7;
  border-radius: 3rpx;
}

.filter-option:active {
  opacity: 0.8;
}

.content-scroll {
  flex: 1;
  padding: 16rpx;
  box-sizing: border-box;
}

/* 加载指示器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(91, 157, 243, 0.2);
  border-top: 4rpx solid #3C7FD7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #718096;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图库卡片样式 */
.gallery-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px) saturate(1.8);
  -webkit-backdrop-filter: blur(25px) saturate(1.8);
  border-radius: 0 32rpx 0 32rpx;
  margin: 32rpx 16rpx;
  padding: 20rpx 36rpx 60rpx 36rpx;
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.04),
    0 12rpx 32rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.gallery-card:active {
  transform: translateY(2rpx);
  box-shadow: 
    0 2rpx 12rpx rgba(0, 0, 0, 0.03),
    0 6rpx 16rpx rgba(0, 0, 0, 0.01);
}

/* 卡片序号 */
.card-number {
  position: absolute;
  top: 0;
  left: 0;
  width: 56rpx;
  height: 56rpx;
  background: #3C7FD7;
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 0 16rpx 0;
}

/* 卡片内容 */
.card-content {
  padding-left: 30rpx;
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 16rpx;
  padding-top: 16rpx;
}

.card-info {
  font-size: 28rpx;
  color: #4A5568;
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 8rpx;
  display: flex;
}

.label {
  color: #718096;
  min-width: 160rpx;
}

/* 卡片操作区域 */
.card-actions {
  /* display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: 16rpx;
  gap: 16rpx; */
}

.action-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  display: inline-block;
  padding: 12rpx 28rpx;
  font-size: 28rpx;
  border-radius: 32rpx 0 0 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: none;
  min-width: 200rpx;
}

.action-btn::after {
  border: none;
}

.view-btn {
  background: #3C7FD7;
  color: white;
}

.select-btn {
  background: #38A169;
  color: white;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #718096;
}

/* 状态文本样式 */
.status {
  font-weight: 500;
}

.status-completed {
  color: #38A169;
}

.status-pending {
  color: #DD6B20;
}

.status-rejected {
  color: #E53E3E;
}

/* 禁止滚动样式 */
.no-scroll {
  overflow: hidden !important;
}

{"version": 3, "sources": ["inquirer.js", "objects/separator.js", "ui/bottom-bar.js", "ui/baseUI.js", "utils/readline.js", "ui/prompt.js", "utils/utils.js", "prompts/list.js", "prompts/base.js", "objects/choices.js", "objects/choice.js", "utils/screen-manager.js", "utils/events.js", "utils/paginator.js", "prompts/input.js", "prompts/confirm.js", "prompts/rawlist.js", "prompts/expand.js", "prompts/checkbox.js", "prompts/password.js", "prompts/editor.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AMlBA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AMlBA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AMlBA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AS3BA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AS3BA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AS3BA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AENA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,ADGA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,AOrBA,ARwBA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ARwBA,AOrBA,ADGA,AJYA,ADGA,AGTA,AOrBA,ARwBA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,ADGA,AJYA,ADGA,AGTA,AOrBA,ARwBA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,ADGA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,ADGA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,ADGA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,AMlBA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,AMlBA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,AMlBA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA,ALeA;ANmBA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AJYA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,ADGA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AOrBA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AS3BA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AENA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AT2BA,AOrBA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AU9BA,ADGA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ACHA,AFMA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ADGA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ADGA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ADGA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AbuCA,ADGA,AGTA,AOrBA,ADGA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AXkCA,AS3BA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,Ad0CA,AGTA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,AXiCA,AMlBA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AFOA,ADGA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AHSA,APqBA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AHSA,AKfA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AHUA,AU9BA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA,ALeA;AOpBA,AENA,AHSA,AV8BA,AYpCA,AHSA;AELA,AENA,AHSA,AV8BA,AYpCA,AHSA;AELA,AENA,AHSA,AV8BA,AYpCA,AHSA;AELA,ADGA,AV8BA,AYpCA,AHSA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,AV8BA,AS3BA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/**\n * Inquirer.js\n * A collection of common interactive command line user interfaces.\n */\n\nvar inquirer = module.exports;\n\n/**\n * Client interfaces\n */\n\ninquirer.prompts = {};\n\ninquirer.Separator = require('./objects/separator');\n\ninquirer.ui = {\n  BottomBar: require('./ui/bottom-bar'),\n  Prompt: require('./ui/prompt')\n};\n\n/**\n * Create a new self-contained prompt module.\n */\ninquirer.createPromptModule = function (opt) {\n  var promptModule = function (questions) {\n    var ui = new inquirer.ui.Prompt(promptModule.prompts, opt);\n    var promise = ui.run(questions);\n\n    // Monkey patch the UI on the promise object so\n    // that it remains publicly accessible.\n    promise.ui = ui;\n\n    return promise;\n  };\n  promptModule.prompts = {};\n\n  /**\n   * Register a prompt type\n   * @param {String} name     Prompt type name\n   * @param {Function} prompt Prompt constructor\n   * @return {inquirer}\n   */\n\n  promptModule.registerPrompt = function (name, prompt) {\n    promptModule.prompts[name] = prompt;\n    return this;\n  };\n\n  /**\n   * Register the defaults provider prompts\n   */\n\n  promptModule.restoreDefaultPrompts = function () {\n    this.registerPrompt('list', require('./prompts/list'));\n    this.registerPrompt('input', require('./prompts/input'));\n    this.registerPrompt('confirm', require('./prompts/confirm'));\n    this.registerPrompt('rawlist', require('./prompts/rawlist'));\n    this.registerPrompt('expand', require('./prompts/expand'));\n    this.registerPrompt('checkbox', require('./prompts/checkbox'));\n    this.registerPrompt('password', require('./prompts/password'));\n    this.registerPrompt('editor', require('./prompts/editor'));\n  };\n\n  promptModule.restoreDefaultPrompts();\n\n  return promptModule;\n};\n\n/**\n * Public CLI helper interface\n * @param  {Array|Object|rx.Observable} questions - Questions settings array\n * @param  {Function} cb - Callback being passed the user answers\n * @return {inquirer.ui.Prompt}\n */\n\ninquirer.prompt = inquirer.createPromptModule();\n\n// Expose helper functions on the top level for easiest usage by common users\ninquirer.registerPrompt = function (name, prompt) {\n  inquirer.prompt.registerPrompt(name, prompt);\n};\ninquirer.restoreDefaultPrompts = function () {\n  inquirer.prompt.restoreDefaultPrompts();\n};\n", "\nvar chalk = require('chalk');\nvar figures = require('figures');\n\n/**\n * Separator object\n * Used to space/separate choices group\n * @constructor\n * @param {String} line   Separation line content (facultative)\n */\n\nvar Separator = module.exports = function (line) {\n  this.type = 'separator';\n  this.line = chalk.dim(line || new Array(15).join(figures.line));\n};\n\n/**\n * Helper function returning false if object is a separator\n * @param  {Object} obj object to test against\n * @return {Boolean}    `false` if object is a separator\n */\n\nSeparator.exclude = function (obj) {\n  return obj.type !== 'separator';\n};\n\n/**\n * Stringify separator\n * @return {String} the separator display string\n */\n\nSeparator.prototype.toString = function () {\n  return this.line;\n};\n", "/**\n * Sticky bottom bar user interface\n */\n\nvar util = require('util');\nvar through = require('through');\nvar Base = require('./baseUI');\nvar rlUtils = require('../utils/readline');\nvar _ = require('lodash');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt(opt) {\n  opt || (opt = {});\n\n  Base.apply(this, arguments);\n\n  this.log = through(this.writeLog.bind(this));\n  this.bottomBar = opt.bottomBar || '';\n  this.render();\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function () {\n  this.write(this.bottomBar);\n  return this;\n};\n\nPrompt.prototype.clean = function () {\n  rlUtils.clearLine(this.rl, this.bottomBar.split('\\n').length);\n  return this;\n};\n\n/**\n * Update the bottom bar content and rerender\n * @param  {String} bottomBar Bottom bar content\n * @return {Prompt}           self\n */\n\nPrompt.prototype.updateBottomBar = function (bottomBar) {\n  this.bottomBar = bottomBar;\n  rlUtils.clearLine(this.rl, 1);\n  this.rl.output.unmute();\n  this.clean().render();\n  this.rl.output.mute();\n  return this;\n};\n\n/**\n * Write out log data\n * @param {String} data - The log data to be output\n * @return {Prompt} self\n */\n\nPrompt.prototype.writeLog = function (data) {\n  this.rl.output.unmute();\n  this.clean();\n  this.rl.output.write(this.enforceLF(data.toString()));\n  this.render();\n  this.rl.output.mute();\n  return this;\n};\n\n/**\n * Make sure line end on a line feed\n * @param  {String} str Input string\n * @return {String}     The input string with a final line feed\n */\n\nPrompt.prototype.enforceLF = function (str) {\n  return str.match(/[\\r\\n]$/) ? str : str + '\\n';\n};\n\n/**\n * Helper for writing message in Prompt\n * @param {Prompt} prompt  - The Prompt object that extends tty\n * @param {String} message - The message to be output\n */\nPrompt.prototype.write = function (message) {\n  var msgLines = message.split(/\\n/);\n  this.height = msgLines.length;\n\n  // Write message to screen and setPrompt to control backspace\n  this.rl.setPrompt(_.last(msgLines));\n\n  if (this.rl.output.rows === 0 && this.rl.output.columns === 0) {\n    /* When it's a tty through serial port there's no terminal info and the render will malfunction,\n       so we need enforce the cursor to locate to the leftmost position for rendering. */\n    rlUtils.left(this.rl, message.length + this.rl.line.length);\n  }\n  this.rl.output.write(message);\n};\n", "\nvar _ = require('lodash');\nvar MuteStream = require('mute-stream');\nvar readline = require('readline');\n\n/**\n * Base interface class other can inherits from\n */\n\nvar UI = module.exports = function (opt) {\n  // Instantiate the Readline interface\n  // @Note: Don't reassign if already present (allow test to override the Stream)\n  if (!this.rl) {\n    this.rl = readline.createInterface(setupReadlineOptions(opt));\n  }\n  this.rl.resume();\n\n  this.onForceClose = this.onForceClose.bind(this);\n\n  // Make sure new prompt start on a newline when closing\n  this.rl.on('SIGINT', this.onForceClose);\n  process.on('exit', this.onForceClose);\n};\n\n/**\n * Handle the ^C exit\n * @return {null}\n */\n\nUI.prototype.onForceClose = function () {\n  this.close();\n  console.log('');\n};\n\n/**\n * Close the interface and cleanup listeners\n */\n\nUI.prototype.close = function () {\n  // Remove events listeners\n  this.rl.removeListener('SIGINT', this.onForceClose);\n  process.removeListener('exit', this.onForceClose);\n\n  this.rl.output.unmute();\n\n  if (this.activePrompt && typeof this.activePrompt.close === 'function') {\n    this.activePrompt.close();\n  }\n\n  // Close the readline\n  this.rl.output.end();\n  this.rl.pause();\n  this.rl.close();\n};\n\nfunction setupReadlineOptions(opt) {\n  opt = opt || {};\n\n  // Default `input` to stdin\n  var input = opt.input || process.stdin;\n\n  // Add mute capabilities to the output\n  var ms = new MuteStream();\n  ms.pipe(opt.output || process.stdout);\n  var output = ms;\n\n  return _.extend({\n    terminal: true,\n    input: input,\n    output: output\n  }, _.omit(opt, ['input', 'output']));\n}\n", "\nvar ansiEscapes = require('ansi-escapes');\n\n/**\n * Move cursor left by `x`\n * @param  {Readline} rl - Readline instance\n * @param  {Number}   x  - How far to go left (default to 1)\n */\n\nexports.left = function (rl, x) {\n  rl.output.write(ansiEscapes.cursorBackward(x));\n};\n\n/**\n * Move cursor right by `x`\n * @param  {Readline} rl - Readline instance\n * @param  {Number}   x  - How far to go left (default to 1)\n */\n\nexports.right = function (rl, x) {\n  rl.output.write(ansiEscapes.cursorForward(x));\n};\n\n/**\n * Move cursor up by `x`\n * @param  {Readline} rl - Readline instance\n * @param  {Number}   x  - How far to go up (default to 1)\n */\n\nexports.up = function (rl, x) {\n  rl.output.write(ansiEscapes.cursorUp(x));\n};\n\n/**\n * Move cursor down by `x`\n * @param  {Readline} rl - Readline instance\n * @param  {Number}   x  - How far to go down (default to 1)\n */\n\nexports.down = function (rl, x) {\n  rl.output.write(ansiEscapes.cursorDown(x));\n};\n\n/**\n * Clear current line\n * @param  {Readline} rl  - Readline instance\n * @param  {Number}   len - number of line to delete\n */\nexports.clearLine = function (rl, len) {\n  rl.output.write(ansiEscapes.eraseLines(len));\n};\n", "\nvar _ = require('lodash');\nvar rx = require('rx');\nvar util = require('util');\nvar runAsync = require('run-async');\nvar utils = require('../utils/utils');\nvar Base = require('./baseUI');\n\n/**\n * Base interface class other can inherits from\n */\n\nvar PromptUI = module.exports = function (prompts, opt) {\n  Base.call(this, opt);\n  this.prompts = prompts;\n};\nutil.inherits(PromptUI, Base);\n\nPromptUI.prototype.run = function (questions) {\n  // Keep global reference to the answers\n  this.answers = {};\n\n  // Make sure questions is an array.\n  if (_.isPlainObject(questions)) {\n    questions = [questions];\n  }\n\n  // Create an observable, unless we received one as parameter.\n  // Note: As this is a public interface, we cannot do an instanceof check as we won't\n  // be using the exact same object in memory.\n  var obs = _.isArray(questions) ? rx.Observable.from(questions) : questions;\n\n  this.process = obs\n    .concatMap(this.processQuestion.bind(this))\n    // `publish` creates a hot Observable. It prevents duplicating prompts.\n    .publish();\n\n  this.process.connect();\n\n  return this.process\n    .reduce(function (answers, answer) {\n      _.set(this.answers, answer.name, answer.answer);\n      return this.answers;\n    }.bind(this), {})\n    .toPromise(Promise)\n    .then(this.onCompletion.bind(this));\n};\n\n/**\n * Once all prompt are over\n */\n\nPromptUI.prototype.onCompletion = function (answers) {\n  this.close();\n\n  return answers;\n};\n\nPromptUI.prototype.processQuestion = function (question) {\n  question = _.clone(question);\n  return rx.Observable.defer(function () {\n    var obs = rx.Observable.of(question);\n\n    return obs\n      .concatMap(this.setDefaultType.bind(this))\n      .concatMap(this.filterIfRunnable.bind(this))\n      .concatMap(utils.fetchAsyncQuestionProperty.bind(null, question, 'message', this.answers))\n      .concatMap(utils.fetchAsyncQuestionProperty.bind(null, question, 'default', this.answers))\n      .concatMap(utils.fetchAsyncQuestionProperty.bind(null, question, 'choices', this.answers))\n      .concatMap(this.fetchAnswer.bind(this));\n  }.bind(this));\n};\n\nPromptUI.prototype.fetchAnswer = function (question) {\n  var Prompt = this.prompts[question.type];\n  this.activePrompt = new Prompt(question, this.rl, this.answers);\n  return rx.Observable.defer(function () {\n    return rx.Observable.fromPromise(this.activePrompt.run().then(function (answer) {\n      return {name: question.name, answer: answer};\n    }));\n  }.bind(this));\n};\n\nPromptUI.prototype.setDefaultType = function (question) {\n  // Default type to input\n  if (!this.prompts[question.type]) {\n    question.type = 'input';\n  }\n  return rx.Observable.defer(function () {\n    return rx.Observable.return(question);\n  });\n};\n\nPromptUI.prototype.filterIfRunnable = function (question) {\n  if (question.when === false) {\n    return rx.Observable.empty();\n  }\n\n  if (!_.isFunction(question.when)) {\n    return rx.Observable.return(question);\n  }\n\n  var answers = this.answers;\n  return rx.Observable.defer(function () {\n    return rx.Observable.fromPromise(\n      runAsync(question.when)(answers).then(function (shouldRun) {\n        if (shouldRun) {\n          return question;\n        }\n      })\n    ).filter(function (val) {\n      return val != null;\n    });\n  });\n};\n", "\nvar _ = require('lodash');\nvar rx = require('rx');\nvar runAsync = require('run-async');\n\n/**\n * Resolve a question property value if it is passed as a function.\n * This method will overwrite the property on the question object with the received value.\n * @param  {Object} question - Question object\n * @param  {String} prop     - Property to fetch name\n * @param  {Object} answers  - Answers object\n * @return {rx.Obsersable}   - Observable emitting once value is known\n */\n\nexports.fetchAsyncQuestionProperty = function (question, prop, answers) {\n  if (!_.isFunction(question[prop])) {\n    return rx.Observable.return(question);\n  }\n\n  return rx.Observable.fromPromise(runAsync(question[prop])(answers)\n    .then(function (value) {\n      question[prop] = value;\n      return question;\n    })\n  );\n};\n", "/**\n * `list` type prompt\n */\n\nvar _ = require('lodash');\nvar util = require('util');\nvar chalk = require('chalk');\nvar figures = require('figures');\nvar cliCursor = require('cli-cursor');\nvar runAsync = require('run-async');\nvar Base = require('./base');\nvar observe = require('../utils/events');\nvar Paginator = require('../utils/paginator');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  Base.apply(this, arguments);\n\n  if (!this.opt.choices) {\n    this.throwParamError('choices');\n  }\n\n  this.firstRender = true;\n  this.selected = 0;\n\n  var def = this.opt.default;\n\n  // Default being a Number\n  if (_.isNumber(def) && def >= 0 && def < this.opt.choices.realLength) {\n    this.selected = def;\n  }\n\n  // Default being a String\n  if (_.isString(def)) {\n    this.selected = this.opt.choices.pluck('value').indexOf(def);\n  }\n\n  // Make sure no default is set (so it won't be printed)\n  this.opt.default = null;\n\n  this.paginator = new Paginator();\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  var self = this;\n\n  var events = observe(this.rl);\n  events.normalizedUpKey.takeUntil(events.line).forEach(this.onUpKey.bind(this));\n  events.normalizedDownKey.takeUntil(events.line).forEach(this.onDownKey.bind(this));\n  events.numberKey.takeUntil(events.line).forEach(this.onNumberKey.bind(this));\n  events.line\n    .take(1)\n    .map(this.getCurrentValue.bind(this))\n    .flatMap(function (value) {\n      return runAsync(self.opt.filter)(value).catch(function (err) {\n        return err;\n      });\n    })\n    .forEach(this.onSubmit.bind(this));\n\n  // Init the prompt\n  cliCursor.hide();\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function () {\n  // Render question\n  var message = this.getQuestion();\n\n  if (this.firstRender) {\n    message += chalk.dim('(Use arrow keys)');\n  }\n\n  // Render choices or answer depending on the state\n  if (this.status === 'answered') {\n    message += chalk.cyan(this.opt.choices.getChoice(this.selected).short);\n  } else {\n    var choicesStr = listRender(this.opt.choices, this.selected);\n    var indexPosition = this.opt.choices.indexOf(this.opt.choices.getChoice(this.selected));\n    message += '\\n' + this.paginator.paginate(choicesStr, indexPosition, this.opt.pageSize);\n  }\n\n  this.firstRender = false;\n\n  this.screen.render(message);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.onSubmit = function (value) {\n  this.status = 'answered';\n\n  // Rerender prompt\n  this.render();\n\n  this.screen.done();\n  cliCursor.show();\n  this.done(value);\n};\n\nPrompt.prototype.getCurrentValue = function () {\n  return this.opt.choices.getChoice(this.selected).value;\n};\n\n/**\n * When user press a key\n */\nPrompt.prototype.onUpKey = function () {\n  var len = this.opt.choices.realLength;\n  this.selected = (this.selected > 0) ? this.selected - 1 : len - 1;\n  this.render();\n};\n\nPrompt.prototype.onDownKey = function () {\n  var len = this.opt.choices.realLength;\n  this.selected = (this.selected < len - 1) ? this.selected + 1 : 0;\n  this.render();\n};\n\nPrompt.prototype.onNumberKey = function (input) {\n  if (input <= this.opt.choices.realLength) {\n    this.selected = input - 1;\n  }\n  this.render();\n};\n\n/**\n * Function for rendering list choices\n * @param  {Number} pointer Position of the pointer\n * @return {String}         Rendered content\n */\nfunction listRender(choices, pointer) {\n  var output = '';\n  var separatorOffset = 0;\n\n  choices.forEach(function (choice, i) {\n    if (choice.type === 'separator') {\n      separatorOffset++;\n      output += '  ' + choice + '\\n';\n      return;\n    }\n\n    if (choice.disabled) {\n      separatorOffset++;\n      output += '  - ' + choice.name;\n      output += ' (' + (_.isString(choice.disabled) ? choice.disabled : 'Disabled') + ')';\n      output += '\\n';\n      return;\n    }\n\n    var isSelected = (i - separatorOffset === pointer);\n    var line = (isSelected ? figures.pointer + ' ' : '  ') + choice.name;\n    if (isSelected) {\n      line = chalk.cyan(line);\n    }\n    output += line + ' \\n';\n  });\n\n  return output.replace(/\\n$/, '');\n}\n", "/**\n * Base prompt implementation\n * Should be extended by prompt types.\n */\n\nvar _ = require('lodash');\nvar chalk = require('chalk');\nvar runAsync = require('run-async');\nvar Choices = require('../objects/choices');\nvar ScreenManager = require('../utils/screen-manager');\n\nvar Prompt = module.exports = function (question, rl, answers) {\n  // Setup instance defaults property\n  _.assign(this, {\n    answers: answers,\n    status: 'pending'\n  });\n\n  // Set defaults prompt options\n  this.opt = _.defaults(_.clone(question), {\n    validate: function () {\n      return true;\n    },\n    filter: function (val) {\n      return val;\n    },\n    when: function () {\n      return true;\n    }\n  });\n\n  // Check to make sure prompt requirements are there\n  if (!this.opt.message) {\n    this.throwParamError('message');\n  }\n  if (!this.opt.name) {\n    this.throwParamError('name');\n  }\n\n  // Normalize choices\n  if (Array.isArray(this.opt.choices)) {\n    this.opt.choices = new Choices(this.opt.choices, answers);\n  }\n\n  this.rl = rl;\n  this.screen = new ScreenManager(this.rl);\n};\n\n/**\n * Start the Inquiry session and manage output value filtering\n * @return {Promise}\n */\n\nPrompt.prototype.run = function () {\n  return new Promise(function (resolve) {\n    this._run(function (value) {\n      resolve(value);\n    });\n  }.bind(this));\n};\n\n// default noop (this one should be overwritten in prompts)\nPrompt.prototype._run = function (cb) {\n  cb();\n};\n\n/**\n * Throw an error telling a required parameter is missing\n * @param  {String} name Name of the missing param\n * @return {Throw Error}\n */\n\nPrompt.prototype.throwParamError = function (name) {\n  throw new Error('You must provide a `' + name + '` parameter');\n};\n\n/**\n * Called when the UI closes. Override to do any specific cleanup necessary\n */\nPrompt.prototype.close = function () {\n  this.screen.releaseCursor();\n};\n\n/**\n * Run the provided validation method each time a submit event occur.\n * @param  {Rx.Observable} submit - submit event flow\n * @return {Object}        Object containing two observables: `success` and `error`\n */\nPrompt.prototype.handleSubmitEvents = function (submit) {\n  var self = this;\n  var validate = runAsync(this.opt.validate);\n  var filter = runAsync(this.opt.filter);\n  var validation = submit.flatMap(function (value) {\n    return filter(value).then(function (filteredValue) {\n      return validate(filteredValue, self.answers).then(function (isValid) {\n        return {isValid: isValid, value: filteredValue};\n      }, function (err) {\n        return {isValid: err};\n      });\n    }, function (err) {\n      return {isValid: err};\n    });\n  }).share();\n\n  var success = validation\n    .filter(function (state) {\n      return state.isValid === true;\n    })\n    .take(1);\n\n  var error = validation\n    .filter(function (state) {\n      return state.isValid !== true;\n    })\n    .takeUntil(success);\n\n  return {\n    success: success,\n    error: error\n  };\n};\n\n/**\n * Generate the prompt question string\n * @return {String} prompt question string\n */\n\nPrompt.prototype.getQuestion = function () {\n  var message = chalk.green('?') + ' ' + chalk.bold(this.opt.message) + ' ';\n\n  // Append the default if available, and if question isn't answered\n  if (this.opt.default != null && this.status !== 'answered') {\n    message += chalk.dim('(' + this.opt.default + ') ');\n  }\n\n  return message;\n};\n", "\nvar assert = require('assert');\nvar _ = require('lodash');\nvar Separator = require('./separator');\nvar Choice = require('./choice');\n\n/**\n * Choices collection\n * Collection of multiple `choice` object\n * @constructor\n * @param {Array} choices  All `choice` to keep in the collection\n */\n\nvar Choices = module.exports = function (choices, answers) {\n  this.choices = choices.map(function (val) {\n    if (val.type === 'separator') {\n      if (!(val instanceof Separator)) {\n        val = new Separator(val.line);\n      }\n      return val;\n    }\n    return new Choice(val, answers);\n  });\n\n  this.realChoices = this.choices\n    .filter(Separator.exclude)\n    .filter(function (item) {\n      return !item.disabled;\n    });\n\n  Object.defineProperty(this, 'length', {\n    get: function () {\n      return this.choices.length;\n    },\n    set: function (val) {\n      this.choices.length = val;\n    }\n  });\n\n  Object.defineProperty(this, 'realLength', {\n    get: function () {\n      return this.realChoices.length;\n    },\n    set: function () {\n      throw new Error('Cannot set `realLength` of a Choices collection');\n    }\n  });\n};\n\n/**\n * Get a valid choice from the collection\n * @param  {Number} selector  The selected choice index\n * @return {Choice|Undefined} Return the matched choice or undefined\n */\n\nChoices.prototype.getChoice = function (selector) {\n  assert(_.isNumber(selector));\n  return this.realChoices[selector];\n};\n\n/**\n * Get a raw element from the collection\n * @param  {Number} selector  The selected index value\n * @return {Choice|Undefined} Return the matched choice or undefined\n */\n\nChoices.prototype.get = function (selector) {\n  assert(_.isNumber(selector));\n  return this.choices[selector];\n};\n\n/**\n * Match the valid choices against a where clause\n * @param  {Object} whereClause Lodash `where` clause\n * @return {Array}              Matching choices or empty array\n */\n\nChoices.prototype.where = function (whereClause) {\n  return _.filter(this.realChoices, whereClause);\n};\n\n/**\n * Pluck a particular key from the choices\n * @param  {String} propertyName Property name to select\n * @return {Array}               Selected properties\n */\n\nChoices.prototype.pluck = function (propertyName) {\n  return _.map(this.realChoices, propertyName);\n};\n\n// Expose usual Array methods\nChoices.prototype.indexOf = function () {\n  return this.choices.indexOf.apply(this.choices, arguments);\n};\nChoices.prototype.forEach = function () {\n  return this.choices.forEach.apply(this.choices, arguments);\n};\nChoices.prototype.filter = function () {\n  return this.choices.filter.apply(this.choices, arguments);\n};\nChoices.prototype.find = function (func) {\n  return _.find(this.choices, func);\n};\nChoices.prototype.push = function () {\n  var objs = _.map(arguments, function (val) {\n    return new Choice(val);\n  });\n  this.choices.push.apply(this.choices, objs);\n  this.realChoices = this.choices.filter(Separator.exclude);\n  return this.choices;\n};\n", "\nvar _ = require('lodash');\n\n/**\n * Choice object\n * Normalize input as choice object\n * @constructor\n * @param {String|Object} val  Choice value. If an object is passed, it should contains\n *                             at least one of `value` or `name` property\n */\n\nvar Choice = module.exports = function (val, answers) {\n  // Don't process Choice and Separator object\n  if (val instanceof Choice || val.type === 'separator') {\n    return val;\n  }\n\n  if (_.isString(val)) {\n    this.name = val;\n    this.value = val;\n    this.short = val;\n  } else {\n    _.extend(this, val, {\n      name: val.name || val.value,\n      value: 'value' in val ? val.value : val.name,\n      short: val.short || val.name || val.value\n    });\n  }\n\n  if (_.isFunction(val.disabled)) {\n    this.disabled = val.disabled(answers);\n  } else {\n    this.disabled = val.disabled;\n  }\n};\n", "\nvar _ = require('lodash');\nvar util = require('./readline');\nvar cliWidth = require('cli-width');\nvar stripAnsi = require('strip-ansi');\nvar stringWidth = require('string-width');\n\nfunction height(content) {\n  return content.split('\\n').length;\n}\n\nfunction lastLine(content) {\n  return _.last(content.split('\\n'));\n}\n\nvar ScreenManager = module.exports = function (rl) {\n  // These variables are keeping information to allow correct prompt re-rendering\n  this.height = 0;\n  this.extraLinesUnderPrompt = 0;\n\n  this.rl = rl;\n};\n\nScreenManager.prototype.render = function (content, bottomContent) {\n  this.rl.output.unmute();\n  this.clean(this.extraLinesUnderPrompt);\n\n  /**\n   * Write message to screen and setPrompt to control backspace\n   */\n\n  var promptLine = lastLine(content);\n  var rawPromptLine = stripAnsi(promptLine);\n\n  // Remove the rl.line from our prompt. We can't rely on the content of\n  // rl.line (mainly because of the password prompt), so just rely on it's\n  // length.\n  var prompt = promptLine;\n  if (this.rl.line.length) {\n    prompt = prompt.slice(0, -this.rl.line.length);\n  }\n  this.rl.setPrompt(prompt);\n\n  // setPrompt will change cursor position, now we can get correct value\n  var cursorPos = this.rl._getCursorPos();\n  var width = this.normalizedCliWidth();\n\n  content = forceLineReturn(content, width);\n  if (bottomContent) {\n    bottomContent = forceLineReturn(bottomContent, width);\n  }\n  // Manually insert an extra line if we're at the end of the line.\n  // This prevent the cursor from appearing at the beginning of the\n  // current line.\n  if (rawPromptLine.length % width === 0) {\n    content += '\\n';\n  }\n  var fullContent = content + (bottomContent ? '\\n' + bottomContent : '');\n  this.rl.output.write(fullContent);\n\n  /**\n   * Re-adjust the cursor at the correct position.\n   */\n\n  // We need to consider parts of the prompt under the cursor as part of the bottom\n  // content in order to correctly cleanup and re-render.\n  var promptLineUpDiff = Math.floor(rawPromptLine.length / width) - cursorPos.rows;\n  var bottomContentHeight = promptLineUpDiff + (bottomContent ? height(bottomContent) : 0);\n  if (bottomContentHeight > 0) {\n    util.up(this.rl, bottomContentHeight);\n  }\n\n  // Reset cursor at the beginning of the line\n  util.left(this.rl, stringWidth(lastLine(fullContent)));\n\n  // Adjust cursor on the right\n  util.right(this.rl, cursorPos.cols);\n\n  /**\n   * Set up state for next re-rendering\n   */\n  this.extraLinesUnderPrompt = bottomContentHeight;\n  this.height = height(fullContent);\n\n  this.rl.output.mute();\n};\n\nScreenManager.prototype.clean = function (extraLines) {\n  if (extraLines > 0) {\n    util.down(this.rl, extraLines);\n  }\n  util.clearLine(this.rl, this.height);\n};\n\nScreenManager.prototype.done = function () {\n  this.rl.setPrompt('');\n  this.rl.output.unmute();\n  this.rl.output.write('\\n');\n};\n\nScreenManager.prototype.releaseCursor = function () {\n  if (this.extraLinesUnderPrompt > 0) {\n    util.down(this.rl, this.extraLinesUnderPrompt);\n  }\n};\n\nScreenManager.prototype.normalizedCliWidth = function () {\n  var width = cliWidth({\n    defaultWidth: 80,\n    output: this.rl.output\n  });\n  if (process.platform === 'win32') {\n    return width - 1;\n  }\n  return width;\n};\n\nfunction breakLines(lines, width) {\n  // Break lines who're longuer than the cli width so we can normalize the natural line\n  // returns behavior accross terminals.\n  var regex = new RegExp(\n    '(?:(?:\\\\033[[0-9;]*m)*.?){1,' + width + '}',\n    'g'\n  );\n  return lines.map(function (line) {\n    var chunk = line.match(regex);\n    // last match is always empty\n    chunk.pop();\n    return chunk || '';\n  });\n}\n\nfunction forceLineReturn(content, width) {\n  return _.flatten(breakLines(content.split('\\n'), width)).join('\\n');\n}\n", "\nvar rx = require('rx');\n\nfunction normalizeKeypressEvents(value, key) {\n  return {value: value, key: key || {}};\n}\n\nmodule.exports = function (rl) {\n  var keypress = rx.Observable.fromEvent(rl.input, 'keypress', normalizeKeypressEvents)\n    .filter(function (e) {\n      // Ignore `enter` key. On the readline, we only care about the `line` event.\n      return e.key.name !== 'enter' && e.key.name !== 'return';\n    });\n\n  return {\n    line: rx.Observable.fromEvent(rl, 'line'),\n    keypress: keypress,\n\n    normalizedUpKey: keypress.filter(function (e) {\n      return e.key.name === 'up' || e.key.name === 'k' || (e.key.name === 'p' && e.key.ctrl);\n    }).share(),\n\n    normalizedDownKey: keypress.filter(function (e) {\n      return e.key.name === 'down' || e.key.name === 'j' || (e.key.name === 'n' && e.key.ctrl);\n    }).share(),\n\n    numberKey: keypress.filter(function (e) {\n      return e.value && '123456789'.indexOf(e.value) >= 0;\n    }).map(function (e) {\n      return Number(e.value);\n    }).share(),\n\n    spaceKey: keypress.filter(function (e) {\n      return e.key && e.key.name === 'space';\n    }).share(),\n\n    aKey: keypress.filter(function (e) {\n      return e.key && e.key.name === 'a';\n    }).share(),\n\n    iKey: keypress.filter(function (e) {\n      return e.key && e.key.name === 'i';\n    }).share()\n  };\n};\n", "\n\nvar _ = require('lodash');\nvar chalk = require('chalk');\n\n/**\n * The paginator keep trakcs of a pointer index in a list and return\n * a subset of the choices if the list is too long.\n */\n\nvar Paginator = module.exports = function () {\n  this.pointer = 0;\n  this.lastIndex = 0;\n};\n\nPaginator.prototype.paginate = function (output, active, pageSize) {\n  pageSize = pageSize || 7;\n  var middleOfList = Math.floor(pageSize / 2);\n  var lines = output.split('\\n');\n\n  // Make sure there's enough lines to paginate\n  if (lines.length <= pageSize) {\n    return output;\n  }\n\n  // Move the pointer only when the user go down and limit it to the middle of the list\n  if (this.pointer < middleOfList && this.lastIndex < active && active - this.lastIndex < pageSize) {\n    this.pointer = Math.min(middleOfList, this.pointer + active - this.lastIndex);\n  }\n  this.lastIndex = active;\n\n  // Duplicate the lines so it give an infinite list look\n  var infinite = _.flatten([lines, lines, lines]);\n  var topIndex = Math.max(0, active + lines.length - this.pointer);\n\n  var section = infinite.splice(topIndex, pageSize).join('\\n');\n  return section + '\\n' + chalk.dim('(Move up and down to reveal more choices)');\n};\n", "/**\n * `input` type prompt\n */\n\nvar util = require('util');\nvar chalk = require('chalk');\nvar Base = require('./base');\nvar observe = require('../utils/events');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  return Base.apply(this, arguments);\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  // Once user confirm (enter key)\n  var events = observe(this.rl);\n  var submit = events.line.map(this.filterInput.bind(this));\n\n  var validation = this.handleSubmitEvents(submit);\n  validation.success.forEach(this.onEnd.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n\n  events.keypress.takeUntil(validation.success).forEach(this.onKeypress.bind(this));\n\n  // Init\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error) {\n  var bottomContent = '';\n  var message = this.getQuestion();\n\n  if (this.status === 'answered') {\n    message += chalk.cyan(this.answer);\n  } else {\n    message += this.rl.line;\n  }\n\n  if (error) {\n    bottomContent = chalk.red('>> ') + error;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.filterInput = function (input) {\n  if (!input) {\n    return this.opt.default == null ? '' : this.opt.default;\n  }\n  return input;\n};\n\nPrompt.prototype.onEnd = function (state) {\n  this.answer = state.value;\n  this.status = 'answered';\n\n  // Re-render prompt\n  this.render();\n\n  this.screen.done();\n  this.done(state.value);\n};\n\nPrompt.prototype.onError = function (state) {\n  this.render(state.isValid);\n};\n\n/**\n * When user press a key\n */\n\nPrompt.prototype.onKeypress = function () {\n  this.render();\n};\n", "/**\n * `confirm` type prompt\n */\n\nvar _ = require('lodash');\nvar util = require('util');\nvar chalk = require('chalk');\nvar Base = require('./base');\nvar observe = require('../utils/events');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  Base.apply(this, arguments);\n\n  var rawDefault = true;\n\n  _.extend(this.opt, {\n    filter: function (input) {\n      var value = rawDefault;\n      if (input != null && input !== '') {\n        value = /^y(es)?/i.test(input);\n      }\n      return value;\n    }\n  });\n\n  if (_.isBoolean(this.opt.default)) {\n    rawDefault = this.opt.default;\n  }\n\n  this.opt.default = rawDefault ? 'Y/n' : 'y/N';\n\n  return this;\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb   Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  // Once user confirm (enter key)\n  var events = observe(this.rl);\n  events.keypress.takeUntil(events.line).forEach(this.onKeypress.bind(this));\n\n  events.line.take(1).forEach(this.onEnd.bind(this));\n\n  // Init\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (answer) {\n  var message = this.getQuestion();\n\n  if (typeof answer === 'boolean') {\n    message += chalk.cyan(answer ? 'Yes' : 'No');\n  } else {\n    message += this.rl.line;\n  }\n\n  this.screen.render(message);\n\n  return this;\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.onEnd = function (input) {\n  this.status = 'answered';\n\n  var output = this.opt.filter(input);\n  this.render(output);\n\n  this.screen.done();\n  this.done(output);\n};\n\n/**\n * When user press a key\n */\n\nPrompt.prototype.onKeypress = function () {\n  this.render();\n};\n", "/**\n * `rawlist` type prompt\n */\n\nvar _ = require('lodash');\nvar util = require('util');\nvar chalk = require('chalk');\nvar Base = require('./base');\nvar Separator = require('../objects/separator');\nvar observe = require('../utils/events');\nvar Paginator = require('../utils/paginator');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  Base.apply(this, arguments);\n\n  if (!this.opt.choices) {\n    this.throwParamError('choices');\n  }\n\n  this.opt.validChoices = this.opt.choices.filter(Separator.exclude);\n\n  this.selected = 0;\n  this.rawDefault = 0;\n\n  _.extend(this.opt, {\n    validate: function (val) {\n      return val != null;\n    }\n  });\n\n  var def = this.opt.default;\n  if (_.isNumber(def) && def >= 0 && def < this.opt.choices.realLength) {\n    this.selected = this.rawDefault = def;\n  }\n\n  // Make sure no default is set (so it won't be printed)\n  this.opt.default = null;\n\n  this.paginator = new Paginator();\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  // Once user confirm (enter key)\n  var events = observe(this.rl);\n  var submit = events.line.map(this.getCurrentValue.bind(this));\n\n  var validation = this.handleSubmitEvents(submit);\n  validation.success.forEach(this.onEnd.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n\n  events.keypress.takeUntil(validation.success).forEach(this.onKeypress.bind(this));\n\n  // Init the prompt\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error) {\n  // Render question\n  var message = this.getQuestion();\n  var bottomContent = '';\n\n  if (this.status === 'answered') {\n    message += chalk.cyan(this.answer);\n  } else {\n    var choicesStr = renderChoices(this.opt.choices, this.selected);\n    message += this.paginator.paginate(choicesStr, this.selected, this.opt.pageSize);\n    message += '\\n  Answer: ';\n  }\n\n  message += this.rl.line;\n\n  if (error) {\n    bottomContent = '\\n' + chalk.red('>> ') + error;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.getCurrentValue = function (index) {\n  if (index == null || index === '') {\n    index = this.rawDefault;\n  } else {\n    index -= 1;\n  }\n\n  var choice = this.opt.choices.getChoice(index);\n  return choice ? choice.value : null;\n};\n\nPrompt.prototype.onEnd = function (state) {\n  this.status = 'answered';\n  this.answer = state.value;\n\n  // Re-render prompt\n  this.render();\n\n  this.screen.done();\n  this.done(state.value);\n};\n\nPrompt.prototype.onError = function () {\n  this.render('Please enter a valid index');\n};\n\n/**\n * When user press a key\n */\n\nPrompt.prototype.onKeypress = function () {\n  var index = this.rl.line.length ? Number(this.rl.line) - 1 : 0;\n\n  if (this.opt.choices.getChoice(index)) {\n    this.selected = index;\n  } else {\n    this.selected = undefined;\n  }\n\n  this.render();\n};\n\n/**\n * Function for rendering list choices\n * @param  {Number} pointer Position of the pointer\n * @return {String}         Rendered content\n */\n\nfunction renderChoices(choices, pointer) {\n  var output = '';\n  var separatorOffset = 0;\n\n  choices.forEach(function (choice, i) {\n    output += '\\n  ';\n\n    if (choice.type === 'separator') {\n      separatorOffset++;\n      output += ' ' + choice;\n      return;\n    }\n\n    var index = i - separatorOffset;\n    var display = (index + 1) + ') ' + choice.name;\n    if (index === pointer) {\n      display = chalk.cyan(display);\n    }\n    output += display;\n  });\n\n  return output;\n}\n", "/**\n * `rawlist` type prompt\n */\n\nvar _ = require('lodash');\nvar util = require('util');\nvar chalk = require('chalk');\nvar Base = require('./base');\nvar Separator = require('../objects/separator');\nvar observe = require('../utils/events');\nvar Paginator = require('../utils/paginator');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  Base.apply(this, arguments);\n\n  if (!this.opt.choices) {\n    this.throwParamError('choices');\n  }\n\n  this.validateChoices(this.opt.choices);\n\n  // Add the default `help` (/expand) option\n  this.opt.choices.push({\n    key: 'h',\n    name: 'Help, list all options',\n    value: 'help'\n  });\n\n  this.opt.validate = function (choice) {\n    if (choice == null) {\n      return 'Please enter a valid command';\n    }\n\n    return choice !== 'help';\n  };\n\n  // Setup the default string (capitalize the default key)\n  this.opt.default = this.generateChoicesString(this.opt.choices, this.opt.default);\n\n  this.paginator = new Paginator();\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  // Save user answer and update prompt to show selected option.\n  var events = observe(this.rl);\n  var validation = this.handleSubmitEvents(\n    events.line.map(this.getCurrentValue.bind(this))\n  );\n  validation.success.forEach(this.onSubmit.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n  this.keypressObs = events.keypress.takeUntil(validation.success)\n    .forEach(this.onKeypress.bind(this));\n\n  // Init the prompt\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error, hint) {\n  var message = this.getQuestion();\n  var bottomContent = '';\n\n  if (this.status === 'answered') {\n    message += chalk.cyan(this.answer);\n  } else if (this.status === 'expanded') {\n    var choicesStr = renderChoices(this.opt.choices, this.selectedKey);\n    message += this.paginator.paginate(choicesStr, this.selectedKey, this.opt.pageSize);\n    message += '\\n  Answer: ';\n  }\n\n  message += this.rl.line;\n\n  if (error) {\n    bottomContent = chalk.red('>> ') + error;\n  }\n\n  if (hint) {\n    bottomContent = chalk.cyan('>> ') + hint;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\nPrompt.prototype.getCurrentValue = function (input) {\n  if (!input) {\n    input = this.rawDefault;\n  }\n  var selected = this.opt.choices.where({key: input.toLowerCase().trim()})[0];\n  if (!selected) {\n    return null;\n  }\n\n  return selected.value;\n};\n\n/**\n * Generate the prompt choices string\n * @return {String}  Choices string\n */\n\nPrompt.prototype.getChoices = function () {\n  var output = '';\n\n  this.opt.choices.forEach(function (choice) {\n    output += '\\n  ';\n\n    if (choice.type === 'separator') {\n      output += ' ' + choice;\n      return;\n    }\n\n    var choiceStr = choice.key + ') ' + choice.name;\n    if (this.selectedKey === choice.key) {\n      choiceStr = chalk.cyan(choiceStr);\n    }\n    output += choiceStr;\n  }.bind(this));\n\n  return output;\n};\n\nPrompt.prototype.onError = function (state) {\n  if (state.value === 'help') {\n    this.selectedKey = '';\n    this.status = 'expanded';\n    this.render();\n    return;\n  }\n  this.render(state.isValid);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.onSubmit = function (state) {\n  this.status = 'answered';\n  var choice = this.opt.choices.where({value: state.value})[0];\n  this.answer = choice.short || choice.name;\n\n  // Re-render prompt\n  this.render();\n  this.screen.done();\n  this.done(state.value);\n};\n\n/**\n * When user press a key\n */\n\nPrompt.prototype.onKeypress = function () {\n  this.selectedKey = this.rl.line.toLowerCase();\n  var selected = this.opt.choices.where({key: this.selectedKey})[0];\n  if (this.status === 'expanded') {\n    this.render();\n  } else {\n    this.render(null, selected ? selected.name : null);\n  }\n};\n\n/**\n * Validate the choices\n * @param {Array} choices\n */\n\nPrompt.prototype.validateChoices = function (choices) {\n  var formatError;\n  var errors = [];\n  var keymap = {};\n  choices.filter(Separator.exclude).forEach(function (choice) {\n    if (!choice.key || choice.key.length !== 1) {\n      formatError = true;\n    }\n    if (keymap[choice.key]) {\n      errors.push(choice.key);\n    }\n    keymap[choice.key] = true;\n    choice.key = String(choice.key).toLowerCase();\n  });\n\n  if (formatError) {\n    throw new Error('Format error: `key` param must be a single letter and is required.');\n  }\n  if (keymap.h) {\n    throw new Error('Reserved key error: `key` param cannot be `h` - this value is reserved.');\n  }\n  if (errors.length) {\n    throw new Error('Duplicate key error: `key` param must be unique. Duplicates: ' +\n        _.uniq(errors).join(', '));\n  }\n};\n\n/**\n * Generate a string out of the choices keys\n * @param  {Array}  choices\n * @param  {Number} defaultIndex - the choice index to capitalize\n * @return {String} The rendered choices key string\n */\nPrompt.prototype.generateChoicesString = function (choices, defaultIndex) {\n  var defIndex = choices.realLength - 1;\n  if (_.isNumber(defaultIndex) && this.opt.choices.getChoice(defaultIndex)) {\n    defIndex = defaultIndex;\n  }\n  var defStr = this.opt.choices.pluck('key');\n  this.rawDefault = defStr[defIndex];\n  defStr[defIndex] = String(defStr[defIndex]).toUpperCase();\n  return defStr.join('');\n};\n\n/**\n * Function for rendering checkbox choices\n * @param  {String} pointer Selected key\n * @return {String}         Rendered content\n */\n\nfunction renderChoices(choices, pointer) {\n  var output = '';\n\n  choices.forEach(function (choice) {\n    output += '\\n  ';\n\n    if (choice.type === 'separator') {\n      output += ' ' + choice;\n      return;\n    }\n\n    var choiceStr = choice.key + ') ' + choice.name;\n    if (pointer === choice.key) {\n      choiceStr = chalk.cyan(choiceStr);\n    }\n    output += choiceStr;\n  });\n\n  return output;\n}\n", "/**\n * `list` type prompt\n */\n\nvar _ = require('lodash');\nvar util = require('util');\nvar chalk = require('chalk');\nvar cliCursor = require('cli-cursor');\nvar figures = require('figures');\nvar Base = require('./base');\nvar observe = require('../utils/events');\nvar Paginator = require('../utils/paginator');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  Base.apply(this, arguments);\n\n  if (!this.opt.choices) {\n    this.throwParamError('choices');\n  }\n\n  if (_.isArray(this.opt.default)) {\n    this.opt.choices.forEach(function (choice) {\n      if (this.opt.default.indexOf(choice.value) >= 0) {\n        choice.checked = true;\n      }\n    }, this);\n  }\n\n  this.pointer = 0;\n  this.firstRender = true;\n\n  // Make sure no default is set (so it won't be printed)\n  this.opt.default = null;\n\n  this.paginator = new Paginator();\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  var events = observe(this.rl);\n\n  var validation = this.handleSubmitEvents(\n    events.line.map(this.getCurrentValue.bind(this))\n  );\n  validation.success.forEach(this.onEnd.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n\n  events.normalizedUpKey.takeUntil(validation.success).forEach(this.onUpKey.bind(this));\n  events.normalizedDownKey.takeUntil(validation.success).forEach(this.onDownKey.bind(this));\n  events.numberKey.takeUntil(validation.success).forEach(this.onNumberKey.bind(this));\n  events.spaceKey.takeUntil(validation.success).forEach(this.onSpaceKey.bind(this));\n  events.aKey.takeUntil(validation.success).forEach(this.onAllKey.bind(this));\n  events.iKey.takeUntil(validation.success).forEach(this.onInverseKey.bind(this));\n\n  // Init the prompt\n  cliCursor.hide();\n  this.render();\n  this.firstRender = false;\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error) {\n  // Render question\n  var message = this.getQuestion();\n  var bottomContent = '';\n\n  if (this.firstRender) {\n    message += '(Press ' + chalk.cyan.bold('<space>') + ' to select, ' + chalk.cyan.bold('<a>') + ' to toggle all, ' + chalk.cyan.bold('<i>') + ' to inverse selection)';\n  }\n\n  // Render choices or answer depending on the state\n  if (this.status === 'answered') {\n    message += chalk.cyan(this.selection.join(', '));\n  } else {\n    var choicesStr = renderChoices(this.opt.choices, this.pointer);\n    var indexPosition = this.opt.choices.indexOf(this.opt.choices.getChoice(this.pointer));\n    message += '\\n' + this.paginator.paginate(choicesStr, indexPosition, this.opt.pageSize);\n  }\n\n  if (error) {\n    bottomContent = chalk.red('>> ') + error;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.onEnd = function (state) {\n  this.status = 'answered';\n\n  // Rerender prompt (and clean subline error)\n  this.render();\n\n  this.screen.done();\n  cliCursor.show();\n  this.done(state.value);\n};\n\nPrompt.prototype.onError = function (state) {\n  this.render(state.isValid);\n};\n\nPrompt.prototype.getCurrentValue = function () {\n  var choices = this.opt.choices.filter(function (choice) {\n    return Boolean(choice.checked) && !choice.disabled;\n  });\n\n  this.selection = _.map(choices, 'short');\n  return _.map(choices, 'value');\n};\n\nPrompt.prototype.onUpKey = function () {\n  var len = this.opt.choices.realLength;\n  this.pointer = (this.pointer > 0) ? this.pointer - 1 : len - 1;\n  this.render();\n};\n\nPrompt.prototype.onDownKey = function () {\n  var len = this.opt.choices.realLength;\n  this.pointer = (this.pointer < len - 1) ? this.pointer + 1 : 0;\n  this.render();\n};\n\nPrompt.prototype.onNumberKey = function (input) {\n  if (input <= this.opt.choices.realLength) {\n    this.pointer = input - 1;\n    this.toggleChoice(this.pointer);\n  }\n  this.render();\n};\n\nPrompt.prototype.onSpaceKey = function () {\n  this.toggleChoice(this.pointer);\n  this.render();\n};\n\nPrompt.prototype.onAllKey = function () {\n  var shouldBeChecked = Boolean(this.opt.choices.find(function (choice) {\n    return choice.type !== 'separator' && !choice.checked;\n  }));\n\n  this.opt.choices.forEach(function (choice) {\n    if (choice.type !== 'separator') {\n      choice.checked = shouldBeChecked;\n    }\n  });\n\n  this.render();\n};\n\nPrompt.prototype.onInverseKey = function () {\n  this.opt.choices.forEach(function (choice) {\n    if (choice.type !== 'separator') {\n      choice.checked = !choice.checked;\n    }\n  });\n\n  this.render();\n};\n\nPrompt.prototype.toggleChoice = function (index) {\n  var item = this.opt.choices.getChoice(index);\n  if (item !== undefined) {\n    this.opt.choices.getChoice(index).checked = !item.checked;\n  }\n};\n\n/**\n * Function for rendering checkbox choices\n * @param  {Number} pointer Position of the pointer\n * @return {String}         Rendered content\n */\n\nfunction renderChoices(choices, pointer) {\n  var output = '';\n  var separatorOffset = 0;\n\n  choices.forEach(function (choice, i) {\n    if (choice.type === 'separator') {\n      separatorOffset++;\n      output += ' ' + choice + '\\n';\n      return;\n    }\n\n    if (choice.disabled) {\n      separatorOffset++;\n      output += ' - ' + choice.name;\n      output += ' (' + (_.isString(choice.disabled) ? choice.disabled : 'Disabled') + ')';\n    } else {\n      var isSelected = (i - separatorOffset === pointer);\n      output += isSelected ? chalk.cyan(figures.pointer) : ' ';\n      output += getCheckbox(choice.checked) + ' ' + choice.name;\n    }\n\n    output += '\\n';\n  });\n\n  return output.replace(/\\n$/, '');\n}\n\n/**\n * Get the checkbox\n * @param  {Boolean} checked - add a X or not to the checkbox\n * @return {String} Composited checkbox string\n */\n\nfunction getCheckbox(checked) {\n  return checked ? chalk.green(figures.radioOn) : figures.radioOff;\n}\n", "/**\n * `password` type prompt\n */\n\nvar util = require('util');\nvar chalk = require('chalk');\nvar Base = require('./base');\nvar observe = require('../utils/events');\n\nfunction mask(input) {\n  input = String(input);\n  if (input.length === 0) {\n    return '';\n  }\n\n  return new Array(input.length + 1).join('*');\n}\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  return Base.apply(this, arguments);\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  var events = observe(this.rl);\n\n  // Once user confirm (enter key)\n  var submit = events.line.map(this.filterInput.bind(this));\n\n  var validation = this.handleSubmitEvents(submit);\n  validation.success.forEach(this.onEnd.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n\n  events.keypress.takeUntil(validation.success).forEach(this.onKeypress.bind(this));\n\n  // Init\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error) {\n  var message = this.getQuestion();\n  var bottomContent = '';\n\n  if (this.status === 'answered') {\n    message += chalk.cyan(mask(this.answer));\n  } else {\n    message += mask(this.rl.line || '');\n  }\n\n  if (error) {\n    bottomContent = '\\n' + chalk.red('>> ') + error;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\n/**\n * When user press `enter` key\n */\n\nPrompt.prototype.filterInput = function (input) {\n  if (!input) {\n    return this.opt.default == null ? '' : this.opt.default;\n  }\n  return input;\n};\n\nPrompt.prototype.onEnd = function (state) {\n  this.status = 'answered';\n  this.answer = state.value;\n\n  // Re-render prompt\n  this.render();\n\n  this.screen.done();\n  this.done(state.value);\n};\n\nPrompt.prototype.onError = function (state) {\n  this.render(state.isValid);\n  this.rl.output.unmute();\n};\n\n/**\n * When user type\n */\n\nPrompt.prototype.onKeypress = function () {\n  this.render();\n};\n", "/**\n * `editor` type prompt\n */\n\nvar util = require('util');\nvar chalk = require('chalk');\nvar ExternalEditor = require('external-editor');\nvar Base = require('./base');\nvar observe = require('../utils/events');\nvar rx = require('rx');\n\n/**\n * Module exports\n */\n\nmodule.exports = Prompt;\n\n/**\n * Constructor\n */\n\nfunction Prompt() {\n  return Base.apply(this, arguments);\n}\nutil.inherits(Prompt, Base);\n\n/**\n * Start the Inquiry session\n * @param  {Function} cb      Callback when prompt is done\n * @return {this}\n */\n\nPrompt.prototype._run = function (cb) {\n  this.done = cb;\n\n  this.editorResult = new rx.Subject();\n\n  // Open Editor on \"line\" (Enter Key)\n  var events = observe(this.rl);\n  this.lineSubscription = events.line.forEach(this.startExternalEditor.bind(this));\n\n  // Trigger Validation when editor closes\n  var validation = this.handleSubmitEvents(this.editorResult);\n  validation.success.forEach(this.onEnd.bind(this));\n  validation.error.forEach(this.onError.bind(this));\n\n  // Prevents default from being printed on screen (can look weird with multiple lines)\n  this.currentText = this.opt.default;\n  this.opt.default = null;\n\n  // Init\n  this.render();\n\n  return this;\n};\n\n/**\n * Render the prompt to screen\n * @return {Prompt} self\n */\n\nPrompt.prototype.render = function (error) {\n  var bottomContent = '';\n  var message = this.getQuestion();\n\n  if (this.status === 'answered') {\n    message += chalk.dim('Received');\n  } else {\n    message += chalk.dim('Press <enter> to launch your preferred editor.');\n  }\n\n  if (error) {\n    bottomContent = chalk.red('>> ') + error;\n  }\n\n  this.screen.render(message, bottomContent);\n};\n\n/**\n * Launch $EDITOR on user press enter\n */\n\nPrompt.prototype.startExternalEditor = function () {\n  // Pause Readline to prevent stdin and stdout from being modified while the editor is showing\n  this.rl.pause();\n  ExternalEditor.editAsync(this.currentText, this.endExternalEditor.bind(this));\n};\n\nPrompt.prototype.endExternalEditor = function (error, result) {\n  this.rl.resume();\n  if (error) {\n    this.editorResult.onError(error);\n  } else {\n    this.editorResult.onNext(result);\n  }\n};\n\nPrompt.prototype.onEnd = function (state) {\n  this.editorResult.dispose();\n  this.lineSubscription.dispose();\n  this.answer = state.value;\n  this.status = 'answered';\n  // Re-render prompt\n  this.render();\n  this.screen.done();\n  this.done(this.answer);\n};\n\nPrompt.prototype.onError = function (state) {\n  this.render(state.isValid);\n};\n"]}
import Api from '../../utils/api';

Component({
  properties: {
    buttonText: {
      type: String,
      value: '选择纸张'
    },
    currentSize: {
      type: Object,
      value: {}
    },
    zIndex: {
      type: Number,
      value: 1000
    }
  },

  observers: {
    'selectedSize': function(size) {
      // 当选中的尺寸变化时，更新按钮文字
      if (size && size.name) {
        const displayText = `${size.name} ${size.width}×${size.height}${size.unit}`;
        this.setData({ displayButtonText: displayText });
      } else {
        this.setData({ displayButtonText: this.properties.buttonText });
      }
    }
  },

  data: {
    visible: false,
    categories: [],
    currentCategory: '',
    currentSizes: [],
    landscapeSizes: [],
    portraitSizes: [],
    selectedSize: null,
    paperSizeInfo: [],
    displayButtonText: '选择纸张',
    searchKeyword: '',
    instantSearchResults: []
  },

  lifetimes: {
    attached: function() {
      this.initPaperSizeData();
    }
  },

  methods: {
    async initPaperSizeData(options?: { forceRefresh?: boolean }) {
      try {
        const response = await Api.paper.getCategories(options);
        console.log('initPaperSizeData: API响应', response);
        
        if (!response) {
          throw new Error('纸张尺寸数据不存在');
        }

        // 尝试直接使用 response
        const paperSizeInfo = response;
        if (!paperSizeInfo.length) {
          throw new Error('纸张尺寸数据为空');
        }

        const firstCategory = paperSizeInfo[0];
        
        // 确保分类和规格都存在
        if (!firstCategory || !firstCategory.sizes || !firstCategory.sizes.length) {
          throw new Error('纸张分类或规格数据不完整');
        }

        // 处理第一个分类的横竖版分组
        const landscapeSizes = firstCategory.sizes.filter(size => size.orientation === 'landscape');
        const portraitSizes = firstCategory.sizes.filter(size => size.orientation === 'portrait');

        const firstSize = firstCategory.sizes[0];
        const currentSizeFromProperties = this.properties.currentSize;
        let newSelectedSize = null;
        let newCurrentCategory = firstCategory.name;
        let newCurrentSizes = firstCategory.sizes;
        let newLandscapeSizes = landscapeSizes;
        let newPortraitSizes = portraitSizes;

        // 尝试在最新数据中查找当前选中的尺寸（来自properties）
        if (currentSizeFromProperties && currentSizeFromProperties.name) {
          outerLoop:
          for (const category of paperSizeInfo) {
            if (category && category.sizes) {
              for (const size of category.sizes) {
                if (size && size.name === currentSizeFromProperties.name &&
                    size.width === currentSizeFromProperties.width &&
                    size.height === currentSizeFromProperties.height) {
                  newSelectedSize = size; // 找到了更新版本
                  newCurrentCategory = category.name;
                  newCurrentSizes = category.sizes;
                  newLandscapeSizes = category.sizes.filter(s => s.orientation === 'landscape');
                  newPortraitSizes = category.sizes.filter(s => s.orientation === 'portrait');
                  break outerLoop;
                }
              }
            }
          }
        }

        // 如果没有找到匹配项，或者初始没有currentSize，则默认选择第一个尺寸
        if (!newSelectedSize) {
          newSelectedSize = firstSize;
          // 确保分类数据与第一个尺寸匹配（如果它是新选择的）
          const categoryOfFirstSize = paperSizeInfo.find(cat => cat.sizes && cat.sizes.includes(firstSize));
          if (categoryOfFirstSize) {
            newCurrentCategory = categoryOfFirstSize.name;
            newCurrentSizes = categoryOfFirstSize.sizes;
            newLandscapeSizes = categoryOfFirstSize.sizes.filter(s => s.orientation === 'landscape');
            newPortraitSizes = categoryOfFirstSize.sizes.filter(s => s.orientation === 'portrait');
          }
        }

        // 设置初始数据
        const initialData = {
          categories: paperSizeInfo,
          paperSizeInfo: paperSizeInfo,
          currentCategory: newCurrentCategory,
          currentSizes: newCurrentSizes,
          landscapeSizes: newLandscapeSizes,
          portraitSizes: newPortraitSizes,
          selectedSize: newSelectedSize
        };

        console.log('initPaperSizeData: 最终设置数据', initialData);
        this.setData(initialData);
        this.setData({ searchKeyword: '', instantSearchResults: [] });

        // 触发选择事件，使用解析后的selectedSize
        if (newSelectedSize) {
          this.triggerEvent('select', { size: newSelectedSize });
        }

        // 如果是刷新操作，提示更新完成
        if (options && options.forceRefresh) {
          wx.showToast({
            title: '更新完成',
            icon: 'success',
            duration: 1500
          });
        }

      } catch (error) {
        console.error('加载纸张尺寸数据失败:', error);
        // 设置一个安全的初始状态
        this.setData({
          categories: [],
          paperSizeInfo: [],
          currentCategory: '',
          currentSizes: [],
          landscapeSizes: [],
          portraitSizes: [],
          selectedSize: null
        });
        wx.showToast({
          title: '加载纸张配置失败',
          icon: 'none'
        });
      }
    },

    setInitialCategory() {
      const currentSize = this.properties.currentSize;
      if (!currentSize || !currentSize.name || !this.data.paperSizeInfo.length) return;

      // 查找当前选中尺寸所属的分类
      const categoryInfo = this.data.paperSizeInfo.find(function(category) {
        if (!category || !category.sizes) return false;
        return category.sizes.some(function(size) {
          if (!size) return false;
          return size.name === currentSize.name &&
                 size.width === currentSize.width &&
                 size.height === currentSize.height;
        });
      });

      if (categoryInfo) {
        // 处理横竖版分组
        const landscapeSizes = categoryInfo.sizes.filter(size => size.orientation === 'landscape');
        const portraitSizes = categoryInfo.sizes.filter(size => size.orientation === 'portrait');

        this.setData({
          currentCategory: categoryInfo.name,
          currentSizes: categoryInfo.sizes,
          landscapeSizes: landscapeSizes,
          portraitSizes: portraitSizes,
          selectedSize: currentSize
        });
        this.setData({ searchKeyword: '', instantSearchResults: [] });
      }
    },

    showPicker() {
      this.setData({ visible: true });
    },

    onClose() {
      this.setData({ visible: false });
    },

    preventTouchMove() {
      // 阻止页面滚动
      return false;
    },
    
    preventBubble() {
      // 阻止事件冒泡
      return false;
    },

    onRefresh() {
      this.initPaperSizeData({ forceRefresh: true });
    },

    switchCategory(e) {
      const category = e.currentTarget.dataset.category;
      if (!category) return;
      
      const categoryInfo = this.data.paperSizeInfo.find(function(item) {
        return item && item.name === category;
      });
      
      if (categoryInfo && categoryInfo.sizes) {
        // 处理横竖版分组
        const landscapeSizes = categoryInfo.sizes.filter(size => size.orientation === 'landscape');
        const portraitSizes = categoryInfo.sizes.filter(size => size.orientation === 'portrait');
        
        this.setData({
          currentCategory: category,
          currentSizes: categoryInfo.sizes,
          landscapeSizes: landscapeSizes,
          portraitSizes: portraitSizes
        });
        this.setData({ searchKeyword: '', instantSearchResults: [] });
      }
    },

    selectSize(e) {
      const size = e.currentTarget.dataset.size;
      if (!size) return;
      
      this.setData({ selectedSize: size });
      
      // 触发选择事件
      this.triggerEvent('select', { size: size });
      
      // 关闭弹窗
      this.setData({ visible: false });
      this.setData({ searchKeyword: '', instantSearchResults: [] });
    },

    onSearchInput(e) {
      const keyword = e.detail.value.trim();
      this.setData({ searchKeyword: keyword });

      if (keyword) {
        this._generateInstantSearchResults(keyword);
      } else {
        // 当搜索框清空时，恢复显示当前分类下的所有纸张
        const currentCategoryInfo = this.data.paperSizeInfo.find(item => item && item.name === this.data.currentCategory);
        if (currentCategoryInfo && currentCategoryInfo.sizes) {
          const landscapeSizes = currentCategoryInfo.sizes.filter(size => size.orientation === 'landscape');
          const portraitSizes = currentCategoryInfo.sizes.filter(size => size.orientation === 'portrait');
          this.setData({
            landscapeSizes: landscapeSizes,
            portraitSizes: portraitSizes,
            instantSearchResults: []
          });
        } else {
          this.setData({ instantSearchResults: [] });
        }
      }
    },

    _generateInstantSearchResults(keyword) {
      const lowerCaseKeyword = keyword.toLowerCase();
      const results = [];

      this.data.paperSizeInfo.forEach(category => {
        if (category && category.sizes) {
          category.sizes.forEach(size => {
            if (!size) return;
            const nameMatch = size.name && size.name.toLowerCase().includes(lowerCaseKeyword);
            const widthMatch = size.width && String(size.width).includes(lowerCaseKeyword);
            const heightMatch = size.height && String(size.height).includes(lowerCaseKeyword);

            if (nameMatch || widthMatch || heightMatch) {
              results.push({
                size: size,
                categoryName: category.name // Include category name
              });
            }
          });
        }
      });
      this.setData({ instantSearchResults: results });
    },

    onInstantSearchResultSelect(e) {
      const selectedItem = e.currentTarget.dataset.item;
      const size = selectedItem.size;
      const categoryName = selectedItem.categoryName;

      if (!size) return;

      // Set the selected size
      this.setData({ selectedSize: size });

      // Trigger select event
      this.triggerEvent('select', { size: size });

      // Close popup and clear search state
      this.setData({
        visible: false,
        searchKeyword: '',
        instantSearchResults: []
      });

      // Also set the current category to the selected item's category for consistency
      const targetCategoryInfo = this.data.paperSizeInfo.find(item => item && item.name === categoryName);
      if (targetCategoryInfo) {
        const landscapeSizes = targetCategoryInfo.sizes.filter(s => s.orientation === 'landscape');
        const portraitSizes = targetCategoryInfo.sizes.filter(s => s.orientation === 'portrait');
        this.setData({
          currentCategory: categoryName,
          currentSizes: targetCategoryInfo.sizes,
          landscapeSizes: landscapeSizes,
          portraitSizes: portraitSizes
        });
      }
    }
  }
});
// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';

Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    currentImage: '',
    processedImage: '',
    canvasWidth: 0,
    canvasHeight: 0,
    showAdjustPanel: false,
    adjustTitle: '',
    adjustValue: 50,
    adjustMin: 0,
    adjustMax: 100,
    adjustType: '' as 'contrast' | 'grayscale' | 'tonal' | 'invert' | '',
    tonalParams: {
      threshold: 50,    // 分离阈值
      intensity: 70,    // 色调强度
      smoothness: 50,   // 过渡平滑度
      temperature: 50   // 色温偏移
    },
    isAdjusting: false,  // 添加新的状态标记
    // 新增裁剪相关数据
    showCropper: false,
    currentPaperSize: null,
    tempProcessedImage: '',
    canvasContext: null as any, // 保存canvas上下文
    originalImageData: null as any, // 保存原始图像数据
    previewCanvas: null as any, // 新增预览Canvas
    canUndo: false,    // 是否可以撤销
    canRedo: false,    // 是否可以重做
  },

  lifetimes: {
    attached() {
      this.setData({
        ...layoutUtil.getLayoutInfo(),
      });
      this._canvas = null;
      this._ctx = null;
      this._originalImageData = null;
      this._throttleTimer = null;
      this._history = [];        // 历史记录数组
      this._currentStep = -1;    // 当前步骤索引
      this.initCanvas();
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    
    detached() {
      this._canvas = null;
      this._ctx = null;
      this._originalImageData = null;
      this._history = null;
      if (this._throttleTimer) {
        clearTimeout(this._throttleTimer);
      }
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 初始化Canvas
    async initCanvas() {
      try {
        const query = this.createSelectorQuery();
        const canvas = await new Promise<WechatMiniprogram.Canvas>(resolve => {
          query.select('#processCanvas')
            .fields({ node: true, size: true })
            .exec((res) => resolve(res[0]));
        });
        
        if (!canvas || !canvas.node) return;
        
        this._canvas = canvas.node;
        this._ctx = canvas.node.getContext('2d');
      } catch (error) {
        console.error('初始化Canvas失败:', error);
      }
    },

    // 准备图像处理
    async prepareImage() {
      if (!this.data.currentImage || !this._ctx || !this._canvas) return false;

      try {
        // 如果没有临时图像数据，则加载当前显示的图像
        if (!this._originalImageData) {
          // 使用当前显示的图像，如果正在调整则使用调整前的图像
          const imageToLoad = this.data.showAdjustPanel ? this.data.currentImage : (this.data.processedImage || this.data.currentImage);
          await this.loadImageToCanvas(imageToLoad);
          this._originalImageData = this._ctx.getImageData(0, 0, this._canvas.width, this._canvas.height);
        }
        return true;
      } catch (error) {
        console.error('准备图像失败:', error);
        return false;
      }
    },

    // 加载图像到Canvas
    loadImageToCanvas(src: string): Promise<void> {
      return new Promise((resolve, reject) => {
        const img = this._canvas.createImage();
        img.onload = () => {
          this._canvas.width = img.width;
          this._canvas.height = img.height;
          this._ctx.drawImage(img, 0, 0);
          resolve();
        };
        img.onerror = reject;
        img.src = src;
      });
    },

    // 图像处理相关函数
    applyGrayscale(pixels: Uint8ClampedArray, intensity: number) {
      const factor = intensity / 100;
      for (let i = 0; i < pixels.length; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 计算灰度值
        const gray = 0.299 * r + 0.587 * g + 0.114 * b;
        
        // 根据强度混合原始颜色和灰度值
        pixels[i] = Math.round(gray * factor + r * (1 - factor));
        pixels[i + 1] = Math.round(gray * factor + g * (1 - factor));
        pixels[i + 2] = Math.round(gray * factor + b * (1 - factor));
      }
    },

    applyContrast(pixels: Uint8ClampedArray, value: number) {
      const factor = (value / 100) * 2;
      const intercept = 128 * (1 - factor);
      
      for (let i = 0; i < pixels.length; i += 4) {
        pixels[i] = Math.min(255, Math.max(0, Math.round(pixels[i] * factor + intercept)));
        pixels[i + 1] = Math.min(255, Math.max(0, Math.round(pixels[i + 1] * factor + intercept)));
        pixels[i + 2] = Math.min(255, Math.max(0, Math.round(pixels[i + 2] * factor + intercept)));
      }
    },

    applyTonalSeparation(pixels: Uint8ClampedArray, params: any) {
      const { threshold, intensity, smoothness, temperature } = params;
      const thresholdValue = threshold / 100 * 255;
      const intensityFactor = intensity / 100;
      const smoothnessFactor = smoothness / 100;
      const tempOffset = (temperature - 50) / 50 * 30;

      for (let i = 0; i < pixels.length; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 计算亮度
        const brightness = (r + g + b) / 3;
        
        // 根据亮度分配色调
        let newR, newG, newB;
        if (brightness < thresholdValue - smoothnessFactor * 50) {
          // 暗部 - 冷色调
          newR = brightness * 0.8;
          newG = brightness * 0.9;
          newB = brightness * 1.1;
        } else if (brightness > thresholdValue + smoothnessFactor * 50) {
          // 亮部 - 暖色调
          newR = brightness * 1.1;
          newG = brightness * 1.0;
          newB = brightness * 0.8;
        } else {
          // 中间调 - 中性
          newR = brightness;
          newG = brightness;
          newB = brightness;
        }

        // 应用色温偏移
        newR = Math.min(255, Math.max(0, newR + tempOffset));
        newB = Math.min(255, Math.max(0, newB - tempOffset));

        // 混合原始颜色和处理后的颜色
        pixels[i] = Math.round(newR * intensityFactor + r * (1 - intensityFactor));
        pixels[i + 1] = Math.round(newG * intensityFactor + g * (1 - intensityFactor));
        pixels[i + 2] = Math.round(newB * intensityFactor + b * (1 - intensityFactor));
      }
    },

    applyInvert(pixels: Uint8ClampedArray, intensity: number) {
      const factor = intensity / 100;
      
      for (let i = 0; i < pixels.length; i += 4) {
        pixels[i] = Math.round(pixels[i] * (1 - factor) + (255 - pixels[i]) * factor);
        pixels[i + 1] = Math.round(pixels[i + 1] * (1 - factor) + (255 - pixels[i + 1]) * factor);
        pixels[i + 2] = Math.round(pixels[i + 2] * (1 - factor) + (255 - pixels[i + 2]) * factor);
      }
    },

    // 修改图像处理函数
    async processImage() {
      if (!await this.prepareImage()) return;

      try {
        // 使用当前图像数据的副本
        const currentData = this._originalImageData.data;
        const pixels = new Uint8ClampedArray(currentData.length);
        pixels.set(currentData);
        const width = this._originalImageData.width;
        const height = this._originalImageData.height;

        // 应用效果
        switch (this.data.adjustType) {
          case 'grayscale':
            this.applyGrayscale(pixels, this.data.adjustValue);
            break;
          case 'contrast':
            this.applyContrast(pixels, this.data.adjustValue);
            break;
          case 'tonal':
            this.applyTonalSeparation(pixels, this.data.tonalParams);
            break;
          case 'invert':
            this.applyInvert(pixels, this.data.adjustValue);
            break;
        }

        // 创建新的ImageData并显示
        const newImageData = this._ctx.createImageData(width, height);
        newImageData.data.set(pixels);
        this._ctx.putImageData(newImageData, 0, 0);

        // 只在调整完成时生成临时文件
        if (!this.data.isAdjusting) {
          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: this._canvas,
              success: res => resolve(res.tempFilePath),
              fail: reject
            });
          });
          
          this.setData({ 
            tempProcessedImage: tempFilePath,
            processedImage: tempFilePath
          });
        }
      } catch (error) {
        console.error('处理图片失败:', error);
      }
    },

    // 修改滑块变化处理函数，使用防抖而不是节流
    onAdjustChange(e: WechatMiniprogram.SliderChange) {
      const value = e.detail.value;
      this.setData({ 
        adjustValue: value,
        isAdjusting: true
      });

      if (this._throttleTimer) {
        clearTimeout(this._throttleTimer);
      }
      
      this._throttleTimer = setTimeout(() => {
        this.processImage();
      }, 16); // 约60fps的更新频率
    },

    onAdjustComplete(e: WechatMiniprogram.SliderChange) {
      if (this._throttleTimer) {
        clearTimeout(this._throttleTimer);
      }

      const value = e.detail.value;
      this.setData({ 
        adjustValue: value,
        isAdjusting: false
      });
      this.processImage();
    },

    // 修改色调参数变化处理函数
    async onTonalParamChange(e: WechatMiniprogram.CustomEvent) {
      const dataset = (e.currentTarget as any).dataset;
      const param = dataset.param;
      const value = e.detail.value;
      
      this.setData({
        [`tonalParams.${param}`]: value,
        isAdjusting: true
      });

      if (this._throttleTimer) {
        clearTimeout(this._throttleTimer);
      }
      
      // 立即处理图像
      await this.processImage();

      // 生成临时预览
      if (this._canvas) {
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });

        this.setData({
          processedImage: tempFilePath
        });
      }
    },

    // 添加色调参数调整完成的处理函数
    async onTonalParamComplete(e: WechatMiniprogram.CustomEvent) {
      const dataset = (e.currentTarget as any).dataset;
      const param = dataset.param;
      const value = e.detail.value;
      
      this.setData({
        [`tonalParams.${param}`]: value,
        isAdjusting: false
      });

      await this.processImage();
    },

    // 添加新的历史记录
    async addHistory() {
      if (!this._canvas) return;
      
      try {
        const imageData = this._ctx.getImageData(0, 0, this._canvas.width, this._canvas.height);
        
        // 如果当前不是在最新步骤，需要删除当前步骤之后的记录
        if (this._currentStep < this._history.length - 1) {
          this._history = this._history.slice(0, this._currentStep + 1);
        }
        
        // 添加新的记录
        this._history.push({
          imageData: imageData,
          adjustType: this.data.adjustType,
          adjustValue: this.data.adjustValue,
          tonalParams: { ...this.data.tonalParams }
        });
        
        this._currentStep++;
        
        // 更新按钮状态
        this.setData({
          canUndo: this._currentStep > 0,
          canRedo: false
        });
      } catch (error) {
        console.error('添加历史记录失败:', error);
      }
    },

    // 撤销操作
    async undoOperation() {
      if (this._currentStep <= 0 || !this._canvas) return;
      
      try {
        this._currentStep--;
        const previousState = this._history[this._currentStep];
        
        // 恢复图像数据
        this._ctx.putImageData(previousState.imageData, 0, 0);
        
        // 恢复参数状态
        this.setData({
          adjustType: previousState.adjustType,
          adjustValue: previousState.adjustValue,
          tonalParams: previousState.tonalParams,
          canUndo: this._currentStep > 0,
          canRedo: true
        });
        
        // 生成临时文件
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });
        
        this.setData({ 
          processedImage: tempFilePath
        });
      } catch (error) {
        console.error('撤销操作失败:', error);
        wx.showToast({
          title: '撤销失败',
          icon: 'none'
        });
      }
    },

    // 重做操作
    async redoOperation() {
      if (this._currentStep >= this._history.length - 1 || !this._canvas) return;
      
      try {
        this._currentStep++;
        const nextState = this._history[this._currentStep];
        
        // 恢复图像数据
        this._ctx.putImageData(nextState.imageData, 0, 0);
        
        // 恢复参数状态
        this.setData({
          adjustType: nextState.adjustType,
          adjustValue: nextState.adjustValue,
          tonalParams: nextState.tonalParams,
          canUndo: true,
          canRedo: this._currentStep < this._history.length - 1
        });
        
        // 生成临时文件
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });
        
        this.setData({ 
          processedImage: tempFilePath
        });
      } catch (error) {
        console.error('重做操作失败:', error);
        wx.showToast({
          title: '重做失败',
          icon: 'none'
        });
      }
    },

    // 修改确认调整方法
    async confirmAdjust() {
      if (!this._canvas) return;
      
      try {
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });

        // 添加到历史记录
        await this.addHistory();

        this.setData({
          processedImage: tempFilePath,
          showAdjustPanel: false,
          adjustType: ''
        });

        // 清除临时图像数据，这样下次处理时会重新加载当前显示的图像
        this._originalImageData = null;

        wx.showToast({
          title: '处理成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('确认调整失败:', error);
        wx.showToast({
          title: '处理失败',
          icon: 'none'
        });
      }
    },

    closeAdjustPanel() {
      // 恢复调整前的图像
      this.setData({
        showAdjustPanel: false,
        adjustType: '',
        processedImage: this.data.currentImage,
        tempProcessedImage: ''
      });

      // 清除临时图像数据
      this._originalImageData = null;
    },

    // 分享给朋友
    onShareAppMessage: function () {
      return {
        title: '图片处理工具 - 一键美化您的图片',
        path: '/pages/tool/img_help/img_help',
        imageUrl: '/assets/share-image.png',
        success: function(res) {
          wx.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: function(res) {
          wx.showToast({
            title: '分享失败',
            icon: 'none',
            duration: 2000
          });
        }
      };
    },

    // 分享到朋友圈
    onShareTimeline: function () {
      return {
        title: '图片处理工具 - 专业的图片编辑与美化',
        query: '',
        imageUrl: '/assets/share-image.png'
      };
    },

    // 修改图片选择方法
    async chooseImage() {
      try {
        const res = await wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera']
        });
        
        if (res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          // 重置历史记录
          this._history = [];
          this._currentStep = -1;
          
          this.setData({
            currentImage: tempFilePath,
            showCropper: true,
            processedImage: '',
            showAdjustPanel: false,
            canUndo: false,
            canRedo: false
          });
        }
      } catch (error) {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    },

    previewImage() {
      if (!this.data.currentImage) return;
      
      wx.previewImage({
        urls: [this.data.processedImage || this.data.currentImage]
      });
    },

    convertToGrayscale() {
      this.setData({
        showAdjustPanel: true,
        adjustTitle: '灰度转换',
        adjustValue: 50,
        adjustMin: 0,
        adjustMax: 100,
        adjustType: 'grayscale'
      });
      this.processImage();
    },

    adjustContrast() {
      this.setData({
        showAdjustPanel: true,
        adjustTitle: '对比度调节',
        adjustValue: 50,
        adjustMin: 0,
        adjustMax: 200,
        adjustType: 'contrast'
      });
      this.processImage();
    },

    separateTones() {
      this.setData({
        showAdjustPanel: true,
        adjustTitle: '色调分离',
        adjustType: 'tonal',
        'tonalParams.threshold': 50,
        'tonalParams.intensity': 70,
        'tonalParams.smoothness': 50,
        'tonalParams.temperature': 50
      });
      this.processImage();
    },

    invertColors() {
      // 直接设置反色效果，不显示调节面板
      this.setData({
        showAdjustPanel: false,
        adjustTitle: '反色效果',
        adjustValue: 100,  // 直接使用最大反色效果
        adjustMin: 0,
        adjustMax: 100,
        adjustType: 'invert',
        isAdjusting: false
      });

      // 立即处理图像并添加到历史记录
      this.processImage().then(async () => {
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });

        // 更新图像显示
        this.setData({
          processedImage: tempFilePath
        });

        // 添加到历史记录
        await this.addHistory();
      });
    },

    // 新增裁剪相关方法
    onPaperSizeSelect(e: any) {
      const { size } = e.detail;
      this.setData({ currentPaperSize: size });
    },

    onImageCropped(e: any) {
      const { path, width, height } = e.detail;
      this.setData({
        currentImage: path,
        processedImage: path,
        showCropper: false,
        canvasWidth: width,
        canvasHeight: height
      });

      // 重置历史记录
      this._originalImageData = null;  // 强制重新加载图像数据
      this.prepareImage().then(() => {
        // 添加初始状态到历史记录
        this._history = [{
          imageData: this._originalImageData,
          adjustType: '',
          adjustValue: 50,
          tonalParams: {
            threshold: 50,
            intensity: 70,
            smoothness: 50,
            temperature: 50
          }
        }];
        this._currentStep = 0;
        this.setData({
          canUndo: false,
          canRedo: false
        });
      });
    },

    onCropperClose() {
      this.setData({
        showCropper: false
      });
    },

    // 重置到原图
    async resetToOriginal() {
      if (!this._canvas || !this.data.currentImage) return;
      
      try {
        // 如果历史记录中有原始状态，直接使用第一个状态
        if (this._history.length > 0) {
          const originalState = this._history[0];
          this._ctx.putImageData(originalState.imageData, 0, 0);
          
          // 生成临时文件
          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: this._canvas,
              success: res => resolve(res.tempFilePath),
              fail: reject
            });
          });

          // 更新状态
          this.setData({
            processedImage: tempFilePath,
            showAdjustPanel: false,
            adjustType: originalState.adjustType,
            adjustValue: originalState.adjustValue,
            tonalParams: { ...originalState.tonalParams },
            canUndo: false,  // 在原图状态时不能撤销
            canRedo: this._history.length > 1  // 如果有后续操作则可以重做
          });

          // 设置当前步骤为0（原图状态）
          this._currentStep = 0;

          wx.showToast({
            title: '已恢复原图',
            icon: 'success'
          });
        } else {
          // 如果没有历史记录，重新加载原始图片
          await this.loadImageToCanvas(this.data.currentImage);
          this._originalImageData = this._ctx.getImageData(0, 0, this._canvas.width, this._canvas.height);
          
          const tempFilePath = await new Promise<string>((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: this._canvas,
              success: res => resolve(res.tempFilePath),
              fail: reject
            });
          });

          // 重置所有状态
          this.setData({
            processedImage: tempFilePath,
            showAdjustPanel: false,
            adjustType: '',
            adjustValue: 50,
            adjustMin: 0,
            adjustMax: 100,
            tonalParams: {
              threshold: 50,
              intensity: 70,
              smoothness: 50,
              temperature: 50
            },
            canUndo: false,
            canRedo: false
          });

          // 创建新的历史记录
          this._history = [{
            imageData: this._originalImageData,
            adjustType: '',
            adjustValue: 50,
            tonalParams: {
              threshold: 50,
              intensity: 70,
              smoothness: 50,
              temperature: 50
            }
          }];
          this._currentStep = 0;
        }
      } catch (error) {
        console.error('恢复原图失败:', error);
        wx.showToast({
          title: '恢复失败',
          icon: 'none'
        });
      }
    },

    // 保存图片
    async saveImage() {
      if (!this._canvas || !this.data.processedImage) {
        wx.showToast({
          title: '没有可保存的图片',
          icon: 'none'
        });
        return;
      }

      try {
        // 获取最高质量的PNG图片
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: this._canvas,
            fileType: 'png',
            quality: 1,
            success: res => resolve(res.tempFilePath),
            fail: reject
          });
        });

        // 保存图片到相册
        await wx.saveImageToPhotosAlbum({
          filePath: tempFilePath
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存图片失败:', error);
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '保存失败',
            content: '请授权保存图片到相册',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    },
  }
});

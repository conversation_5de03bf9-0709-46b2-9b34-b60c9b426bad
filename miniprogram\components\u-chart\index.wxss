.u-chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.u-chart-canvas {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.u-chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.u-chart-loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: loading 1s linear infinite;
}

.u-chart-loading-text {
  font-size: 26rpx;
  color: #999;
  margin-top: 10rpx;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 
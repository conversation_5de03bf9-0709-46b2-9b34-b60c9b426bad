<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="图像辅助" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll" enhanced="{{true}}" bounces="{{true}}">
      <!-- 图片处理区域 -->
      <view class="image-section">
        <view class="image-container {{!currentImage ? 'empty' : ''}}">
          <image wx:if="{{currentImage}}" 
                 src="{{processedImage || currentImage}}" 
                 mode="widthFix" 
                 bindtap="previewImage"/>
          <view wx:else class="upload-hint" bindtap="chooseImage">
            <text class="upload-icon">📷</text>
            <text class="upload-text">点击选择图片</text>
          </view>
        </view>
        
        <!-- 添加重新上传按钮 -->
        <view class="operation-btns" wx:if="{{currentImage}}">
          <view class="op-btn undo-btn {{canUndo ? '' : 'disabled'}}" bindtap="{{canUndo ? 'undoOperation' : ''}}">
            <image class="op-icon" src="{{constants.STATIC_URL.ICON}}undo.svg" mode="aspectFit"></image>
            <text class="op-text">撤销</text>
          </view>
          
          <view class="op-btn original-btn" bindtap="resetToOriginal">
            <image class="op-icon" src="{{constants.STATIC_URL.ICON}}refresh.svg" mode="aspectFit"></image>
            <text class="op-text">原图</text>
          </view>

          <view class="reupload-btn" bindtap="chooseImage">
            <image class="reupload-icon" src="{{constants.STATIC_URL.ICON}}picture.svg" mode="aspectFit"></image> 
            <text class="reupload-text">重新上传</text>
          </view>

          <view class="op-btn redo-btn {{canRedo ? '' : 'disabled'}}" bindtap="{{canRedo ? 'redoOperation' : ''}}">
            <image class="op-icon" src="{{constants.STATIC_URL.ICON}}redo.svg" mode="aspectFit"></image>
            <text class="op-text">重做</text>
          </view>
        </view>

        <!-- 保存按钮 -->
        <view class="save-btn-wrapper" wx:if="{{currentImage}}">
          <button class="save-btn" bindtap="saveImage">
            <image class="save-icon" src="{{constants.STATIC_URL.ICON}}save.svg" mode="aspectFit"></image>
            <text class="save-text">保存图片</text>
          </button>
        </view>
        
        <!-- 隐藏的Canvas用于图片处理 -->
        <canvas type="2d" 
                id="processCanvas"
                class="process-canvas"
                style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;">
        </canvas>
        
        <!-- 图片处理工具栏 -->
        <view class="tool-bar">
          <view class="tool-item {{currentImage ? '' : 'disabled'}}" 
                bindtap="{{currentImage ? 'convertToGrayscale' : ''}}">
            <text class="tool-icon">⚫</text>
            <text class="tool-name">灰度</text>
          </view>
          <view class="tool-item {{currentImage ? '' : 'disabled'}}" 
                bindtap="{{currentImage ? 'adjustContrast' : ''}}">
            <text class="tool-icon">◐</text>
            <text class="tool-name">对比度</text>
          </view>
          <view class="tool-item {{currentImage ? '' : 'disabled'}}" 
                bindtap="{{currentImage ? 'separateTones' : ''}}">
            <text class="tool-icon">◑</text>
            <text class="tool-name">色调分离</text>
          </view>
          <view class="tool-item {{currentImage ? '' : 'disabled'}}" 
                bindtap="{{currentImage ? 'invertColors' : ''}}">
            <text class="tool-icon">⬤</text>
            <text class="tool-name">反色</text>
          </view>
        </view>
      </view>

      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}randompicture.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">图像辅助工具</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">多种效果</text>
                    <text class="feature-desc">提供灰度、对比度、色调分离等多种图像处理效果</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">实时预览</text>
                    <text class="feature-desc">即时查看处理效果，支持撤销和重做操作</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">参数调节</text>
                    <text class="feature-desc">可以精细调整处理参数，获得理想效果</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">快速保存</text>
                    <text class="feature-desc">一键保存处理后的图片到本地相册</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">上传图片</text>
                    <text class="step-desc">选择需要处理的图片文件</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">选择效果</text>
                    <text class="step-desc">点击工具栏选择需要的处理效果</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">调整参数</text>
                    <text class="step-desc">根据需要调整处理效果的具体参数</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">保存图片</text>
                    <text class="step-desc">确认效果后保存处理完成的图片</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">效果叠加</text>
                  <text class="tip-text">可以组合使用多种效果获得独特的处理效果</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">参数微调</text>
                  <text class="tip-text">细微调整参数可以获得更精确的处理效果</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">对比原图</text>
                  <text class="tip-text">随时可以查看原图对比处理效果的差异</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">及时保存</text>
                  <text class="tip-text">满意的处理效果要及时保存，避免丢失</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->

      <!-- 裁剪组件 -->
      <wxmy-cropper
          wx:if="{{showCropper}}"
          imageSrc="{{currentImage}}"
          bindcropped="onImageCropped"
          bindclose="onCropperClose"
        ></wxmy-cropper>

      <!-- <wxmy-cropper 
        wx:if="{{showCropper}}"
        imageSrc="{{currentImage}}"
        bindcropped="onImageCropped"
        bindclose="onCropperClose">
        <view slot="cropper-title">
          <paper-size-picker
            id="paper-size-picker"
            button-text="{{currentPaperSize ? currentPaperSize.paper : '选择尺寸'}}"
            current-size="{{currentPaperSize}}"
            bindselect="onPaperSizeSelect"
            tab-bar-height="{{50}}"
          />
        </view>
      </wxmy-cropper> -->
    </scroll-view>
  </view>

  <!-- 添加遮罩层和调整调节面板位置 -->
  <view class="mask {{showAdjustPanel ? 'show' : ''}}" bindtap="closeAdjustPanel"></view>

  <!-- 调节面板移到最外层 -->
  <view class="adjust-panel {{showAdjustPanel ? 'show' : ''}}">
    <view class="adjust-header">
      <text class="text">{{adjustTitle}}</text>
      <text class="close-btn" bindtap="closeAdjustPanel">×</text>
    </view>
    <view class="adjust-content">
      <block wx:if="{{adjustType === 'tonal'}}">
        <view class="tones-legend">
          <view class="tone-item">
            <view class="tone-color light"></view>
            <text class="text">亮部 - 暖色调</text>
          </view>
          <view class="tone-item">
            <view class="tone-color mid"></view>
            <text class="text">中间调 - 中性色调</text>
          </view>
          <view class="tone-item">
            <view class="tone-color dark"></view>
            <text class="text">暗部 - 冷色调</text>
          </view>
        </view>
        <view class="tonal-controls">
          <view class="control-item">
            <text class="text">分离阈值</text>
            <slider value="{{tonalParams.threshold}}" 
                    min="0" max="100" step="1"
                    show-value
                    data-param="threshold"
                    bindchanging="onTonalParamChange"
                    bindchange="onTonalParamComplete"/>
          </view>
          <view class="control-item">
            <text class="text">色调强度</text>
            <slider value="{{tonalParams.intensity}}" 
                    min="0" max="100" step="1"
                    show-value
                    data-param="intensity"
                    bindchanging="onTonalParamChange"
                    bindchange="onTonalParamComplete"/>
          </view>
          <view class="control-item">
            <text class="text">过渡平滑</text>
            <slider value="{{tonalParams.smoothness}}" 
                    min="0" max="100" step="1"
                    show-value
                    data-param="smoothness"
                    bindchanging="onTonalParamChange"
                    bindchange="onTonalParamComplete"/>
          </view>
          <view class="control-item">
            <text class="text">色温偏移</text>
            <slider value="{{tonalParams.temperature}}" 
                    min="0" max="100" step="1"
                    show-value
                    data-param="temperature"
                    bindchanging="onTonalParamChange"
                    bindchange="onTonalParamComplete"/>
          </view>
        </view>
      </block>
      <block wx:else>
        <slider value="{{adjustValue}}" 
                min="{{adjustMin}}" 
                max="{{adjustMax}}" 
                step="1"
                show-value
                bindchanging="onAdjustChange"
                bindchange="onAdjustComplete"/>
      </block>
    </view>
    <view class="adjust-actions">
      <button class="action-btn cancel" bindtap="closeAdjustPanel">取消</button>
      <button class="action-btn confirm" bindtap="confirmAdjust">确定</button>
    </view>
  </view>

  <tab-bar height="{{tabBarHeight}}" currentTab="3"><!-- 底部导航 --></tab-bar>
</view>


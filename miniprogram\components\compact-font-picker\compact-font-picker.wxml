<view class="cfp-container {{customClass}}" style="{{styleVars}}">
  <!-- 触发按钮 -->
  <view class="cfp-trigger" bindtap="showPicker">
    <view class="cfp-selected" wx:if="{{selectedFont}}">
      <text style="font-family: {{selectedFont.value}}Font;" class="cfp-preview">{{previewText || '字体'}}</text>
      <view class="cfp-info">
        <text class="cfp-name">{{selectedFont.name}}</text>
        <view class="cfp-arrow"></view>
      </view>
    </view>
    <view class="cfp-placeholder" wx:else>
      <text class="text">选择字体</text>
      <view class="cfp-arrow"></view>
    </view>
  </view>

  <!-- 弹出层 -->
  <view class="cfp-mask {{visible ? 'cfp-show' : ''}}" bindtap="hidePicker"></view>
  <view class="cfp-popup-wrapper {{visible ? 'cfp-show' : ''}}">
    <view class="cfp-popup" catch:tap="preventBubble" style="padding-bottom: calc({{tabBarHeight}}px + {{safeAreaBottom}}px);">
      <view class="cfp-header">
        <text class="cfp-title">选择字体</text>
        <view class="cfp-close" bindtap="hidePicker">×</view>
      </view>

      <!-- 分类切换 -->
      <scroll-view scroll-x class="cfp-tabs" enhanced="{{true}}" show-scrollbar="{{false}}">
        <view class="cfp-tabs-inner">
          <view 
            wx:for="{{fontCategories}}" 
            wx:key="type"
            class="cfp-tab {{currentCategory === item.type ? 'cfp-active' : ''}}"
            data-category="{{item.type}}"
            bindtap="onCategoryChange"
          >
            {{item.name}}
          </view>
        </view>
      </scroll-view>

      <!-- 字体列表 -->
      <scroll-view scroll-y class="cfp-fonts" enhanced="{{true}}">
        <view class="cfp-fonts-grid">
          <block wx:for="{{fontCategories}}" wx:key="type" wx:for-item="category">
            <block wx:if="{{category.type === currentCategory}}">
              <view 
                class="cfp-font-item {{selectedFont.value === font.value ? 'cfp-active' : ''}}" 
                wx:for="{{category.fonts}}" 
                wx:key="id"
                wx:for-item="font"
                data-font="{{font}}"
                bindtap="onSelectFont"
              >
                <text style="font-family: {{font.value}}Font;" class="cfp-font-preview">{{previewText || '字体'}}</text>
                <text class="cfp-font-name">{{font.name}}</text>
                <view class="cfp-font-tags" wx:if="{{font.tags && font.tags.length > 0}}">
                  <text class="cfp-font-tag" wx:for="{{font.tags}}" wx:key="*this">{{item}}</text>
                </view>
              </view>
            </block>
          </block>
        </view>
      </scroll-view>

      <!-- 加载中遮罩 -->
      <view class="cfp-loading" wx:if="{{isLoading}}">
        <view class="cfp-loading-spinner"></view>
        <text class="text">加载中...</text>
      </view>
    </view>
  </view>
</view>
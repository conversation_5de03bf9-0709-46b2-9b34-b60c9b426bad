<wxs src="../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <!-- 自定义导航栏 -->
  <nav-bar title="{{artToolsIntro.title}}" showBack="{{false}}" showMore=""></nav-bar>
  <!-- 内容区域 -->
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
<!-- <view >{{layoutStyle}}</view> -->
<!--  <view style="margin: 20px;">
  <button class="" bindtap="testjump" data-k="0">空值测试跳转</button>
  <button class="" bindtap="testjump" data-k="1">加密值测试跳转</button>
  <button class="" bindtap="testjump" data-k="2">自定义值测试跳转</button>
</view>
<view style="margin: 20px;">
  <button class="subscribe-btn" bindtap="testAllSubscribe">
    {{currentBatchIndex === 0 ? '订阅绘画提醒' : '继续订阅提醒'}}
  </button>
  <view class="subscribe-tip">提示：将分批订阅7个绘画相关提醒，每次最多3个</view>
</view>
<button open-type="contact">联系客服</button>
-->

    <snapshot id="page-snapshot" mode="view" width="500" height="400" style="width: 500px; height: 400px;">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 导航菜单区域 -->
      <block wx:for="{{navMenus}}" wx:key="id">
        <view class="category-section animate-item">
          <view class="category-header" bindtap="toggleCategory" data-category-id="{{item.id}}">
            <view class="category-title">
              <view class="tool-icon-wrapper">
                <image class="tool-icon" src="{{constants.STATIC_URL.ICON}}{{item.icon}}.svg" mode="aspectFit"></image>
              </view>
              <text class="text">{{item.text}}</text>
              <view class="category-arrow {{item.expanded ? 'expanded' : ''}}"></view>
            </view>
          </view>
          <view class="tools-container {{item.expanded ? 'expanded' : ''}}">
            <block wx:for="{{item.submenus}}" wx:key="id" wx:for-item="submenu">
              <view class="tool-card animate-item" bindtap="navigateToTool" data-url="{{submenu.url}}">
                <view class="tool-icon-wrapper">
                  <image class="tool-icon" src="{{constants.STATIC_URL.ICON}}{{submenu.icon}}.svg" mode="aspectFit"></image>
                </view>
                <view class="tool-info">
                  <text class="tool-name">{{submenu.text}}</text>
                  <text class="tool-desc" wx:if="{{submenu.description}}">{{submenu.description}}</text>
                  <view class="feature-list" wx:if="{{submenu.features && submenu.features.length > 0}}">
                    <text class="feature-item" wx:for="{{submenu.features}}" wx:for-item="feature" wx:key="*this">{{feature}}</text>
                  </view>
                </view>
                <mp-icon type="field" icon="arrow" color="#999999" size="{{16}}"></mp-icon>
              </view>
            </block>
          </view>
        </view>
      </block>
      
      <!-- 工具说明区域 -->
      <view class="tool-intro animate-item">
        <view class="intro-header">
          <view class="tool-icon-wrapper" style="width: 64rpx; height: 64rpx;">
            <mp-icon type="field" icon="like" color="white" size="{{24}}"></mp-icon>
          </view>
          <text class="intro-title">{{artToolsIntro.title}}</text>
        </view>
        <view class="intro-content">
          <view class="intro-section">
            <text class="section-title">关于工具</text>
            <text class="section-text">{{artToolsIntro.description}}</text>
          </view>
          
          <!-- 使用场景 -->
          <view class="intro-section">
            <text class="section-title">使用场景</text>
            <view class="guide-list">
              <view class="guide-item" wx:for="{{usageScenarios}}" wx:key="title">
                <text class="guide-name">{{item.title}}</text>
                <text class="guide-desc">{{item.description}}</text>
                <view class="tools-used">
                  <text class="tool-tag" wx:for="{{item.tools}}" wx:for-item="tool" wx:key="*this">{{tool}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 使用建议 -->
          <view class="intro-section">
            <text class="section-title">使用建议</text>
            <view class="tip-list">
              <view class="tip-item" wx:for="{{tips}}" wx:key="title">
                <mp-icon type="field" icon="done" color="#2575fc" size="{{18}}"></mp-icon>
                <view class="tip-content">
                  <text class="tip-title">{{item.title}}</text>
                  <text class="tip-text">{{item.content}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    </snapshot>
  </view>

  <!-- 底部导航栏 -->
  <tab-bar currentTab="0"></tab-bar>

  <!-- 登录组件 -->
  <login-modal id="login-modal" bind:loginsuccess="onLoginSuccess"></login-modal>

  <!-- 添加订阅选择弹窗 -->
  <view class="subscribe-modal {{showSubscribeModal ? 'show' : ''}}" catchtouchmove="preventDefault">
    <view class="subscribe-mask" bindtap="closeSubscribeModal"></view>
    <view class="subscribe-content">
      <view class="subscribe-title">订阅提醒</view>
      <view class="subscribe-desc">请选择需要订阅的提醒内容</view>
      <view class="template-list">
        <view class="template-item" wx:for="{{subscribeTemplates}}" wx:key="id">
          <view class="template-info">
            <view class="template-title">{{item.title}}</view>
            <view class="template-desc">{{item.desc}}</view>
            <view class="time-picker" wx:if="{{item.checked}}">
              <view class="time-label">发送时间：</view>
              <picker 
                mode="date" 
                value="{{item.sendTime}}" 
                start="{{currentDate}}" 
                bindchange="handleTimeChange" 
                data-index="{{index}}"
              >
                <view class="picker-value">
                  {{item.sendTime || '点击选择发送时间'}}
                </view>
              </picker>
            </view>
          </view>
          <view class="template-checkbox" bindtap="handleTemplateChange" data-index="{{index}}">
            <view class="checkbox {{item.checked ? 'checked' : ''}}"></view>
          </view>
        </view>
      </view>
      <view class="subscribe-actions">
        <button class="cancel-btn" bindtap="closeSubscribeModal">取消</button>
        <button class="confirm-btn" bindtap="confirmSubscribe">确认订阅</button>
      </view>
    </view>
  </view>
</view>


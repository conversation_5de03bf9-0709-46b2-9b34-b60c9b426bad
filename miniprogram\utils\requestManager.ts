/**
 * 通用请求管理器
 * 
 * 主要功能：
 * 1. 自动缓存管理：支持内存缓存和持久化存储
 * 2. 请求合并：相同请求自动合并，避免重复请求
 * 3. 自动重试：支持失败自动重试
 * 4. 统一错误处理：标准化的错误处理流程
 * 5. 请求/响应拦截：支持全局拦截器
 * 6. 并发控制：支持请求队列和并发限制
 * 7. 请求超时：支持超时配置和自动取消
 * 
 * 使用示例：
 * 
 * 1. 基础GET请求：
 * ```ts
 * // 简单的GET请求
 * const data = await request<UserInfo>('/user/info');
 * 
 * // 带查询参数的GET请求
 * const data = await request<SearchResult>('/search', { keyword: '测试' });
 * ```
 * 
 * 2. POST请求：
 * ```ts
 * // 发送POST请求
 * const result = await request<LoginResult>('/login', 
 *   { username: 'test', password: '123456' },
 *   { method: 'POST' }
 * );
 * ```
 * 
 * 3. 缓存控制：
 * ```ts
 * // 使用24小时缓存
 * const data = await request<CategoryList>('/categories', null, {
 *   cache: {
 *     strategy: CacheStrategy.BOTH,
 *     expireTime: 24 * 60 * 60 * 1000
 *   }
 * });
 * 
 * // 强制刷新缓存
 * const data = await request<CategoryList>('/categories', null, {
 *   cache: { forceRefresh: true }
 * });
 * ```
 * 
 * 4. 错误处理：
 * ```ts
 * try {
 *   const data = await request('/api/data');
 * } catch (error) {
 *   if (error instanceof ApiError) {
 *     console.error('业务错误:', error.code, error.message);
 *   } else {
 *     console.error('网络错误:', error);
 *   }
 * }
 * ```
 * 
 * 5. 高级功能：
 * ```ts
 * // 添加请求拦截器
 * requestManager.interceptors.request.use(config => {
 *   config.header = {
 *     ...config.header,
 *     'Authorization': `Bearer ${getToken()}`
 *   };
 *   return config;
 * });
 * 
 * // 添加响应拦截器
 * requestManager.interceptors.response.use(
 *   response => {
 *     // 处理响应数据
 *     return response;
 *   },
 *   error => {
 *     if (error.code === 401) {
 *       // 处理token过期
 *       return refreshToken().then(() => {
 *         // 重试请求
 *         return requestManager.retryRequest(error.config);
 *       });
 *     }
 *     return Promise.reject(error);
 *   }
 * );
 * 
 * // 设置请求超时
 * const data = await request('/api/data', null, {
 *   timeout: 5000  // 5秒超时
 * });
 * 
 * // 取消请求
 * const controller = new AbortController();
 * const data = await request('/api/data', null, {
 *   signal: controller.signal
 * });
 * // 取消请求
 * controller.abort();
 * ```
 */

import { BASE_URL, CACHE_NONE, CACHE_MEMORY, CACHE_STORAGE, CACHE_BOTH } from './constants';

function ApiError(code: number, message: string) {
  this.code = code;
  this.message = message;
  this.name = 'ApiError';
}

// 请求选项接口
export interface RequestOptions {
  method?: RequestMethod;
  header?: { [key: string]: string };
  cache?: {
    strategy: string;
    expireTime?: number;
    forceRefresh?: boolean;
  };
  autoRetry?: boolean;
  showError?: boolean;
}

// 请求方法类型
export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'OPTIONS' | 'HEAD' | 'TRACE' | 'CONNECT';

// 标准响应接口
export interface StandardResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

interface IRequestManager {
  memoryCache: { [key: string]: any };
  getFullUrl(url: string): string;
  request(url: string, data?: any, options?: RequestOptions): Promise<any>;
}

interface IRequestManagerConstructor {
  new(): IRequestManager;
  instance?: IRequestManager;
}

var RequestManager = (function() {
  var instance: IRequestManager | null = null;

  function RequestManagerConstructor(this: IRequestManager) {
    if (instance) {
      return instance;
    }
    instance = this;
    this.memoryCache = {};
  }

  RequestManagerConstructor.prototype.getFullUrl = function(url: string): string {
    if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
      return url;
    }
    return BASE_URL + (url.charAt(0) === '/' ? url.substring(1) : url);
  };

  RequestManagerConstructor.prototype.request = function(url: string, data?: any, options?: RequestOptions): Promise<any> {
    // 彻底禁止 pollinations/feed 缓存：不读取本地缓存，不写入本地缓存
    var self = this;
    if (url.indexOf('pollinations/feed') !== -1) {
      // 跳过本地缓存读取逻辑，直接发起网络请求
      return new Promise((resolve, reject) => {
        wx.request({
          url: self.getFullUrl(url),
          data: data,
          method: (options && options.method) || 'GET',
          success: (res) => resolve(res.data),
          fail: (err) => reject(err)
        });
      });
    }

    // 检查是否存在缓存配置且未强制刷新
    if (options && options.cache && !options.cache.forceRefresh) {
      const cacheKey = self.getFullUrl(url) + JSON.stringify(data);
      const now = Date.now();

      // 处理内存缓存
      if (options.cache.strategy === CACHE_MEMORY || options.cache.strategy === CACHE_BOTH) {
        const memoryCache = self.memoryCache[cacheKey];
        if (memoryCache && (!options.cache.expireTime || (now - memoryCache.timestamp < options.cache.expireTime))) {
          console.log(`缓存命中 (内存): ${url}`);
          return Promise.resolve(memoryCache.data);
        }
      }

      // 处理本地存储缓存
      if (options.cache.strategy === CACHE_STORAGE || options.cache.strategy === CACHE_BOTH) {
        try {
          const storageCache = wx.getStorageSync(cacheKey);
          if (storageCache && (!options.cache.expireTime || (now - storageCache.timestamp < options.cache.expireTime))) {
            console.log(`缓存命中 (本地存储): ${url}`);
            return Promise.resolve(storageCache.data);
          }
        } catch (e) {
          console.error('读取本地存储缓存失败:', e);
        }
      }
    }

    return new Promise(function(resolve, reject) {
      // 从本地存储获取token
      const token = wx.getStorageSync('token');
      
      // 准备header
      const headers = (options && options.header) || {
        'content-type': 'application/json'
      };
      
      // 如果有token，添加到header中
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        // 兼容其他认证方式
        headers['token'] = token;
      }

      wx.request({
        url: self.getFullUrl(url),
        method: (options && options.method) || 'GET',
        data: data,
        header: headers,
        success: function(res: WechatMiniprogram.RequestSuccessCallbackResult) {
          var result = res.data as any;
          if (!result) {
            reject(new ApiError(-1, '接口返回为空'));
            return;
          }
          if (result.code !== undefined && result.code !== 1) {
            // 处理401未授权错误，可能是token过期
            if (result.code === 401) {
              console.error('授权已过期，请重新登录');
              // 可以在这里触发重新登录
              reject(new ApiError(result.code, '请登录后操作'));
              return;
            }
            reject(new ApiError(result.code, result.msg || '请求失败'));
            return;
          }

          // 添加缓存写入逻辑
          if (options && options.cache) {
            const cacheKey = self.getFullUrl(url) + JSON.stringify(data);
            const now = Date.now();
            const cacheData = { data: result.data || result, timestamp: now };

            // 写入内存缓存
            if (options.cache.strategy === CACHE_MEMORY || options.cache.strategy === CACHE_BOTH) {
              self.memoryCache[cacheKey] = cacheData;
              console.log(`缓存写入成功 (内存): ${url}`);
            }

            // 写入本地存储缓存
            if (options.cache.strategy === CACHE_STORAGE || options.cache.strategy === CACHE_BOTH) {
              try {
                wx.setStorageSync(cacheKey, cacheData);
                console.log(`缓存写入成功 (本地存储): ${url}`);
              } catch (e) {
                console.error('写入本地存储缓存失败:', e);
              }
            }
          }

          resolve(result.data || result);
        },
        fail: function(err: WechatMiniprogram.GeneralCallbackResult) {
          reject(new ApiError(-1, err.errMsg || '请求失败'));
        }
      });
    });
  };

  return RequestManagerConstructor as unknown as IRequestManagerConstructor;
})();

var manager = new RequestManager();

function request<T = any>(url: string, data?: any, options?: RequestOptions): Promise<T> {
  return manager.request(url, data, options);
}

// 导出所有需要的内容
export {
  request,
  RequestManager,
  ApiError,
  CACHE_NONE,
  CACHE_MEMORY,
  CACHE_STORAGE,
  CACHE_BOTH
}; 
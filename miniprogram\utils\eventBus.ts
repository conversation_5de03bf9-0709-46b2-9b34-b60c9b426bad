// eventBus.ts
export class EventBus {
  private events: Map<string, Function[]>;

  constructor() {
    this.events = new Map();
  }

  // 注册事件监听
  on(eventName: string, callback: Function) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, []);
    }
    const callbacks = this.events.get(eventName);
    if (callbacks) {
      callbacks.push(callback);
    }
  }

  // 注册一次性事件监听
  once(eventName: string, callback: Function) {
    const wrappedCallback = (data?: any) => {
      callback(data);
      this.off(eventName, wrappedCallback);
    };
    this.on(eventName, wrappedCallback);
  }

  // 移除事件监听
  off(eventName: string, callback: Function) {
    if (!this.events.has(eventName)) return;
    const callbacks = this.events.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  emit(eventName: string, data?: any) {
    if (!this.events.has(eventName)) return;
    const callbacks = this.events.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }
}

// 创建全局事件总线实例
const eventBusInstance = new EventBus();

// 将事件总线挂载到全局对象
(wx as any).event = eventBusInstance;

// 添加一个状态标志表示登录弹窗当前是否正在显示
let loginModalShowing = false;

// 添加获取和设置登录弹窗状态的方法
const getLoginModalStatus = () => loginModalShowing;
const setLoginModalStatus = (status: boolean) => {
  loginModalShowing = status;
};

// 正确绑定方法到事件总线实例
const on = eventBusInstance.on.bind(eventBusInstance);
const off = eventBusInstance.off.bind(eventBusInstance);
const emit = eventBusInstance.emit.bind(eventBusInstance);
const once = eventBusInstance.once.bind(eventBusInstance);

// 在导出对象中添加这两个方法
export default {
  // 原有的事件总线方法，正确绑定this
  on,
  off,
  emit,
  once,
  // 新增的登录弹窗状态管理方法
  getLoginModalStatus,
  setLoginModalStatus
}; 
.weui-half-screen-dialog__hd__main:focus {
  outline: none;
}
.weui-dialog__root {
  position: absolute;
  z-index: 1000;
}
.weui-animate-fade-in {
  animation: weuiFadeIn ease 0.3s forwards;
}
@keyframes weuiFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.weui-animate-fade-out {
  animation: weuiFadeOut ease 0.3s forwards;
}
@keyframes weuiFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.weui-animate-slide-up {
  animation: weuiSlideUp ease 0.3s forwards;
}
@keyframes weuiSlideUp {
  from {
    transform: translate3d(0, 100%, 0);
  }
  to {
    transform: translateZ(0);
  }
}
.weui-animate-slide-down {
  animation: weuiSlideDown ease 0.3s forwards;
}
@keyframes weuiSlideDown {
  from {
    transform: translateZ(0);
  }
  to {
    transform: translate3d(0, 100%, 0);
  }
}
.weui-icon-close-thin {
  background-color: var(--weui-FG-0);
  /* skyline 不支持 currentColor */
}



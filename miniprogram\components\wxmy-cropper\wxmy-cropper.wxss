/* pages/cropper/cropper.wxss */
Page {
  /* 主题色 */
  --primary-color: #07c160;
  /* 点的颜色 */
  --point-color: rgb(255, 255, 255);
  /* 边框颜色 */
  --border-color: rgba(255, 255, 255, 0.8);
  /* 虚线颜色 */
  --dashed-color: rgba(255, 255, 255, 0.6);
  /* 裁剪区域背景色 */
  --box-bg: transparent;
  /* 裁剪所有的背景色 */
  --cropper-bg: rgba(0, 0, 0, 0.8);
  /* 装饰线条颜色 */
  --decoration-color: #2196f3;
  /* 缩放手柄颜色 */
  --handle-color: #2196f3;
}
.wx-content-info {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cropper-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
.cropper-content {
  position: relative;
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.wx-corpper {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wx-corpper-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.wx-corpper-content-bg {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wx-corpper-content-bg image {
  position: absolute;
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}
.wx-corpper-crop-box {
  position: absolute;
  background: var(--box-bg);
  z-index: 2;
  outline: 9999px solid var(--cropper-bg);
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}
.cropper-container{
  position: relative;
}
.paper-size-selector {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  padding: 16rpx 40rpx;
  /* border-radius: 40rpx; */
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.paper-size-selector .popup-content {
  position: fixed;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 1001;
}

.paper-size-selector .popup-mask {
  position: fixed;
  top: var(--nav-height, 0);
  left: 0;
  right: 0;
  bottom: calc(var(--tab-bar-height, 0) + var(--safe-bottom, 0));
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.paper-size-selector paper-size-picker {
  display: inline-block;
  margin: 0 auto;
}

.cropper-config {
  position: relative;
  padding: 0 20rpx;
  margin-bottom: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
}
.wx-corpper-content-bg .mask{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}
.wxmy-cropper-view-box {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  overflow: visible;
  outline: 2rpx solid var(--border-color);
}
.wxmy-cropper-view-box-img {
  position: absolute;
  overflow: hidden;
}
.wxmy-cropper-view-box .wxmy-cropper-dashed-h {
  position: absolute;
  top: 33.33%;
  left: 0;
  width: 100%;
  height: 33.33%;
  border-top: 2rpx dashed var(--dashed-color);
  border-bottom: 2rpx dashed var(--dashed-color);
}
.wxmy-cropper-view-box .wxmy-cropper-dashed-v {
  position: absolute;
  left: 33.33%;
  top: 0;
  width: 33.33%;
  height: 100%;
  border-left: 2rpx dashed var(--dashed-color);
  border-right: 2rpx dashed var(--dashed-color);
}
.wxmy-cropper-view-box .wxmy-cropper-line-t {
  position: absolute;
  display: block;
  width: 100%;
  background-color: var(--primary-color);
  top: 0;
  left: 0;
  height: 1px;
  opacity: 0.1;
  cursor: n-resize;
}
.wxmy-cropper-view-box .wxmy-cropper-line-t::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 0rpx;
  width: 100%;
  -webkit-transform: translate3d(0, -50%, 0);
  transform: translate3d(0, -50%, 0);
  bottom: 0;
  height: 41rpx;
  background: transparent;
  z-index: 11;
}
.wxmy-cropper-view-box .wxmy-cropper-line-r {
  position: absolute;
  display: block;
  background-color: var(--primary-color);
  top: 0;
  right: 0px;
  width: 1px;
  opacity: 0.1;
  height: 100%;
  cursor: e-resize;
}
.wxmy-cropper-view-box .wxmy-cropper-line-r::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 41rpx;
  -webkit-transform: translate3d(-50%, 0, 0);
  transform: translate3d(-50%, 0, 0);
  bottom: 0;
  height: 100%;
  background: transparent;
  z-index: 11;
}
.wxmy-cropper-view-box .wxmy-cropper-line-b {
  position: absolute;
  display: block;
  width: 100%;
  background-color: var(--primary-color);
  bottom: 0;
  left: 0;
  height: 1px;
  opacity: 0.1;
  cursor: s-resize;
}
.wxmy-cropper-view-box .wxmy-cropper-line-b::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 0rpx;
  width: 100%;
  -webkit-transform: translate3d(0, -50%, 0);
  transform: translate3d(0, -50%, 0);
  bottom: 0;
  height: 41rpx;
  background: transparent;
  z-index: 11;
}
.wxmy-cropper-view-box .wxmy-cropper-line-l {
  position: absolute;
  display: block;
  background-color: var(--primary-color);
  top: 0;
  left: 0;
  width: 1px;
  opacity: 0.1;
  height: 100%;
  cursor: w-resize;
}
.wxmy-cropper-view-box .wxmy-cropper-line-l::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 41rpx;
  -webkit-transform: translate3d(-50%, 0, 0);
  transform: translate3d(-50%, 0, 0);
  bottom: 0;
  height: 100%;
  background: transparent;
  z-index: 11;
}
.wxmy-cropper-view-box .wxmy-cropper-point {
  width: 24rpx;
  height: 24rpx;
  /* background-color: var(--primary-color); */
  opacity: .75;
  position: absolute;
  z-index: 3;
}
.wxmy-cropper-view-box .point-t {
  top: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}
.wxmy-cropper-view-box .point-r {
  top: 50%;
  left: 100%;
  margin-left: -3px;
  margin-top: -3px;
  cursor: n-resize;
}
.wxmy-cropper-view-box .point-tr,
.wxmy-cropper-view-box .point-rb,
.wxmy-cropper-view-box .point-bl,
.wxmy-cropper-view-box .point-lt {
  cursor: n-resize;
  width: 24rpx;
  height: 24rpx;
  position: absolute;
  z-index: 1112;
  opacity: 1;
}
.wxmy-cropper-view-box .point-rb {
  right: 0;
  bottom: 0;
  -webkit-transform: translate3d(50%, 50%, 0);
}
.wxmy-cropper-view-box .point-tr {
  right: 0;
  top: 0;
  -webkit-transform: translate3d(50%, -50%, 0);
}
.wxmy-cropper-view-box .point-bl {
  left: 0;
  bottom: 0;
  -webkit-transform: translate3d(-50%, 50%, 0);
}
.wxmy-cropper-view-box .point-lt {
  top: 0;
  left: 0;
  -webkit-transform: translate3d(-50%, -50%, 0);
}
.wxmy-cropper-view-box .point-rb::before,
.wxmy-cropper-view-box .point-rb::after,
.wxmy-cropper-view-box .point-tr::before,
.wxmy-cropper-view-box .point-tr::after,
.wxmy-cropper-view-box .point-bl::before,
.wxmy-cropper-view-box .point-bl::after,
.wxmy-cropper-view-box .point-lt::before,
.wxmy-cropper-view-box .point-lt::after {
  content: '';
  position: absolute;
  background-color: var(--primary-color);
}
.wxmy-cropper-view-box .point-rb::before {
  width: 6rpx;
  height: 30rpx;
  right: calc(44%);
  bottom: calc(44%);
}
.wxmy-cropper-view-box .point-rb::after {
  height: 6rpx;
  width: 30rpx;
  right: calc(44%);
  bottom: calc(44%);
}
.wxmy-cropper-view-box .point-tr::before {
  width: 6rpx;
  height: 30rpx;
  right: calc(44%);
  top: calc(44%);
}
.wxmy-cropper-view-box .point-tr::after {
  height: 6rpx;
  width: 30rpx;
  right: calc(44%);
  top: calc(44%);
}
.wxmy-cropper-view-box .point-bl::before {
  width: 6rpx;
  height: 30rpx;
  left: calc(44%);
  bottom: calc(44%);
}
.wxmy-cropper-view-box .point-bl::after {
  height: 6rpx;
  width: 30rpx;
  left: calc(44%);
  bottom: calc(44%);
}
.wxmy-cropper-view-box .point-lt::before {
  width: 6rpx;
  height: 30rpx;
  left: calc(44%);
  top: calc(44%);
}
.wxmy-cropper-view-box .point-lt::after {
  height: 6rpx;
  width: 30rpx;
  left: calc(44%);
  top: calc(44%);
}
.wxmy-cropper-view-box .point-b {
  left: 50%;
  top: 100%;
  margin-left: -3px;
  margin-top: -3px;
  cursor: n-resize;
}
.wxmy-cropper-view-box .point-l {
  left: 0%;
  top: 50%;
  margin-left: -3px;
  margin-top: -3px;
  cursor: n-resize;
}
.cropper-config .cropper-cancle,
.cropper-config .cropper-save {
  color: #fff;
  font-size: 26rpx;
  padding: 15rpx 25px;
  display: block;
}
/* 裁剪框预览内容 */
.wxmy-cropper-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.wxmy-cropper-viewer image {
  position: absolute;
  z-index: 2;
}
.wxmy-cropper-scale-handle {
  position: absolute;
  right: -24rpx;
  bottom: -24rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: var(--handle-color);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  touch-action: none;
}

.scale-handle-icon {
  width: 24rpx;
  height: 24rpx;
  border-right: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
}

/* 添加网格样式 */
.image-grid-container {
  position: absolute;
  pointer-events: none;
  z-index: 3;
}

.grid-line-horizontal {
  position: absolute;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
}

.grid-line-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
}

.cropper-btn {
  padding: 16rpx 40rpx;
  /* border-radius: 40rpx; */
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.cropper-btn-cancel {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
}

.cropper-btn-select {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 20rpx;
}

.cropper-btn-confirm {
  color: #fff;
  background: var(--handle-color);
}

.cropper-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 添加按钮图标 */
.cropper-btn-cancel::before,
.cropper-btn-select::before,
.cropper-btn-confirm::before {
  font-family: "Material Icons";
  margin-right: 8rpx;
  font-size: 32rpx;
  vertical-align: -4rpx;
}

.cropper-btn-cancel::before {
  content: "✕";
}

.cropper-btn-select::before {
  content: "📷";
}

.cropper-btn-confirm::before {
  content: "✓";
}

/* 四角装饰 */
.corner-decoration {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-color: var(--decoration-color);
  border-style: solid;
  border-width: 0;
}

.corner-lt {
  top: -2rpx;
  left: -2rpx;
  border-top-width: 4rpx;
  border-left-width: 4rpx;
}

.corner-rt {
  top: -2rpx;
  right: -2rpx;
  border-top-width: 4rpx;
  border-right-width: 4rpx;
}

.corner-lb {
  bottom: -2rpx;
  left: -2rpx;
  border-bottom-width: 4rpx;
  border-left-width: 4rpx;
}

.corner-rb {
  bottom: -2rpx;
  right: -2rpx;
  border-bottom-width: 4rpx;
  border-right-width: 4rpx;
}

/* 覆盖纸张选择器弹出层样式 */
.wx-content-info .paper-size-picker--popup-content {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 600rpx !important;
  max-height: 800rpx !important;
  background: #ffffff !important;
  border-radius: 20rpx !important;
  z-index: 2000 !important;
}

.wx-content-info .paper-size-picker--popup-mask {
  position: fixed !important;
  top: var(--nav-height) !important;
  left: 0 !important;
  right: 0 !important;
  bottom: calc(var(--tab-bar-height) + var(--safe-bottom)) !important;
  background: rgba(0, 0, 0, 0.6) !important;
  z-index: 1999 !important;
}

.wx-content-info .paper-list {
  max-height: 600rpx !important;
}

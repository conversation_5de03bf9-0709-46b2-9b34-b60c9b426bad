<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="中国传统色" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <canvas type="2d" id="shareCanvas" style="position:fixed; left:-9999px;"></canvas>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item {{currentCategory === '' ? 'active' : ''}}" bindtap="switchCategory" data-category="">
        <text>全部</text>
      </view>
      <scroll-view 
        scroll-x 
        class="category-scroll" 
        enhanced="{{true}}"
        show-scrollbar="{{false}}"
        fast-deceleration="{{true}}"
        bounces="{{false}}"
        enable-flex="{{true}}"
        scroll-anchoring="{{true}}">
        <view class="category-list">
          <view class="filter-item {{currentCategory === item ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="index"
                bindtap="switchCategory"
                data-category="{{item}}">
            <text>{{item}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 左侧颜色条 -->
      <scroll-view scroll-y class="color-strip-scroll" 
        enhanced="{{true}}" 
        show-scrollbar="{{false}}" 
        fast-deceleration="{{true}}"
        bounces="{{false}}"
        enable-flex="{{true}}"
        scroll-anchoring="{{true}}"
        style="{{zheight}}">
        <view class="color-strip">
          <view class="color-block {{selectedColor && selectedColor.id === item.id ? 'active' : ''}}"
                wx:for="{{displayColorList}}" 
                wx:key="id"
                style="background-color: {{item.hex_value}};opacity:0.9"
                bindtap="onColorSelect"
                data-color="{{item}}">
                <text class="p_num">{{index+1}}</text>
                <view class="p_text" style="background-color:rgba({{cmyk}},1)">
                  <text class="text" wx:for="{{item.color_name}}" wx:key="index">{{item}}</text>
                </view>
                
          </view>
        </view>
      </scroll-view>

      <!-- 右侧颜色展示区 -->
      <view class="color-display" style="background-color: {{selectedColor.hex_value || '#FFFFFF'}}" bindtap="showColorDetail">
        <!-- 山水动画背景 -->
        <view class="ink-animation">
          <image class="ink-bg" src="{{constants.STATIC_URL.ICON}}chinacolorbg.gif" mode="aspectFill"></image>
          <view class="ink-overlay"></view>
        </view>
        <block wx:if="{{selectedColor}}">
          <!-- 左侧竖排颜色描述 -->
          <view class="vertical-description">
            <view class="vertical-text-container">
              <text class="vertical-text">{{selectedColor.psychological_effect}}</text>
            </view>
          </view>
          <!-- 竖排颜色名称和拼音 -->
          <view class="vertical-color-name">
            <view class="vertical-text-container">
              <text wx:for="{{selectedColor.color_name}}" wx:key="index" class="vertical-text">{{item}}</text>
            </view>
            <view class="vertical-pinyin">{{selectedColor.color_pinyin}}</view>
          </view>
          <!-- 颜色别名独立显示 -->
          <block wx:if="{{selectedColor.color_alias}}">
            <view class="vertical-alias-container">
              <text wx:for="{{selectedColor.color_alias}}" wx:key="index" class="vertical-alias-text">{{item}}</text>
            </view>
          </block>
          <view class="color-info-overlay">
            <view class="color-details">
              <view class="detail-row">
                <text class="label">RGB</text>
                <text class="value">{{selectedColor.rgb}}</text>
              </view>
              <view class="detail-row">
                <text class="label">CMYK</text>
                <text class="value">{{selectedColor.cmyk}}</text>
              </view>
              <view class="detail-row">
                <text class="label">HEX</text>
                <text class="value">{{selectedColor.hex_value}}</text>
              </view>
            </view>
            
            <block wx:if="{{selectedColor.modern_mix_formula.basic_colors_formula}}">
              <view class="formula-section">
                <view class="formula-block">
                  <view class="formula-title">基础色配比</view>
                  <view class="formula-desc">{{selectedColor.modern_mix_formula.basic_colors_formula.description}}</view>
                  <view class="percentage-list">
                    <view class="percentage-item" wx:for="{{selectedColor.modern_mix_formula.basic_colors_formula.percentages}}" wx:key="index">
                      <text class="color-name">{{index}}</text>
                      <text class="percentage">{{item}}%</text>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            
            <view class="view-more-btn" bindtap="showColorDetail">
              <text>查看更多详情</text>
              <text class="arrow">></text>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 颜色详情弹出层 -->
<view class="color-detail-popup {{showColorDetail ? 'show' : ''}}" bindtap="hideColorDetail" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
  <view class="popup-content" catchtap="stopPropagation">
    <view class="popup-close" bindtap="hideColorDetail">×</view>
    
    <scroll-view scroll-y class="popup-scroll" 
      enhanced="{{true}}"
      show-scrollbar="{{false}}"
      fast-deceleration="{{true}}"
      bounces="{{false}}"
      enable-flex="{{true}}"
      scroll-anchoring="{{true}}"
      style="bottom:{{isTabBarCollapsed?tabBarHeight:0}}px">
      <!-- 颜色预览区 -->
      <view class="detail-section color-preview-section" style="background-color: {{selectedColor.hex_value}}">
        <view class="color-name"><text wx:for="{{selectedColor.color_name}}" wx:key="index">{{item}}</text></view>
        <view class="color-pinyin">{{selectedColor.color_pinyin}}</view>
        <view class="color-name-en">{{selectedColor.color_name_en}}</view>
      </view>
      <!-- 心理感知 -->
      <block wx:if="{{selectedColor.psychological_effect}}">
        <view class="detail-section">
          <view class="section-content">
            <text wx:for="{{selectedColor.color_description}}" wx:key="index" class="vertical-text">{{item}}</text>
          </view>
        </view>
      </block>

      <!-- 配方信息部分 -->
      <block wx:if="{{selectedColor.modern_mix_formula}}">
        <view class="detail-section">
          <view class="section-title">调色配方</view>
          <!-- 基础色配比 -->
          <block wx:if="{{selectedColor.modern_mix_formula.basic_colors_formula}}">
            <view class="formula-block">
              <view class="formula-title">基础色配比</view>
              <view class="formula-desc">{{selectedColor.modern_mix_formula.basic_colors_formula.description}}</view>
              <view class="percentage-list">
                <view class="percentage-item" wx:for="{{selectedColor.modern_mix_formula.basic_colors_formula.percentages}}" wx:key="index">
                  <text class="color-name">{{index}}</text>
                  <text class="percentage">{{item}}%</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 矿物颜料配比 -->
          <block wx:if="{{selectedColor.modern_mix_formula.mineral_formula}}">
            <view class="formula-block">
              <view class="formula-title">矿物颜料配比</view>
              <view class="mineral-list">
                <view class="mineral-item" wx:for="{{selectedColor.modern_mix_formula.mineral_formula.materials}}" wx:key="index">
                  <view class="mineral-info">
                    <text class="mineral-name">{{index}}</text>
                    <text class="mineral-origin" wx:if="{{selectedColor.modern_mix_formula.mineral_formula.origins[index]}}">
                      ({{selectedColor.modern_mix_formula.mineral_formula.origins[index]}})
                    </text>
                  </view>
                  <text class="mineral-percentage">{{item}}%</text>
                </view>
              </view>
              <view class="craft-method">
                <text class="method-label">工艺方法：</text>
                <text class="method-value">{{selectedColor.modern_mix_formula.mineral_formula.craft_method}}</text>
              </view>
              <view class="process-steps">
                <view class="step-item" wx:for="{{selectedColor.modern_mix_formula.mineral_formula.processing_steps}}" wx:key="index">
                  <text class="step-number">{{index + 1}}</text>
                  <text class="step-desc">{{item}}</text>
                </view>
              </view>
            </view>
          </block>
        </view>
      </block>

      

      <!-- 基本信息 -->
      <block wx:if="{{selectedColor.color_alias || selectedColor.color_category || selectedColor.color_subcategory}}">
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="section-content info-grid">
            <block wx:if="{{selectedColor.color_alias}}">
              <view class="info-item">
                <text class="info-label">颜色别名：</text>
                <text class="info-value">{{selectedColor.color_alias}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.color_category}}">
              <view class="info-item">
                <text class="info-label">颜色系列：</text>
                <text class="info-value">{{selectedColor.color_category}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.color_subcategory}}">
              <view class="info-item">
                <text class="info-label">颜色分类：</text>
                <text class="info-value">{{selectedColor.color_subcategory}}</text>
              </view>
            </block>
          </view>
        </view>
      </block>

      <!-- 颜色描述 -->
      <!-- <block wx:if="{{selectedColor.color_description}}">
        <view class="detail-section">
          <view class="section-title">颜色描述</view>
          <view class="section-content">
            <text>{{selectedColor.color_description}}</text>
          </view>
        </view>
      </block> -->

      

      <!-- 相关颜色 -->
      <block wx:if="{{(selectedColor.similar_colors && selectedColor.similar_colors.length > 0) || (selectedColor.complementary_colors && selectedColor.complementary_colors.length > 0)}}">
        <view class="detail-section">
          <view class="section-title">相关颜色</view>
          <view class="section-content tags-container">
            <block wx:if="{{selectedColor.similar_colors && selectedColor.similar_colors.length > 0}}">
              <view class="tag-group">
                <view class="tag-title">相似色：</view>
                <view class="tags">
                  <text class="tag" wx:for="{{selectedColor.similar_colors}}" wx:key="index">{{item}}</text>
                </view>
              </view>
            </block>
            <block wx:if="{{selectedColor.complementary_colors && selectedColor.complementary_colors.length > 0}}">
              <view class="tag-group">
                <view class="tag-title">互补色：</view>
                <view class="tags">
                  <text class="tag" wx:for="{{selectedColor.complementary_colors}}" wx:key="index">{{item}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>

      <!-- 染色工艺 -->
      <block wx:if="{{selectedColor.historical_material || selectedColor.auxiliary_material || selectedColor.craft_method || selectedColor.origin_place}}">
        <view class="detail-section">
          <view class="section-title">染色工艺</view>
          <view class="section-content info-grid">
            <block wx:if="{{selectedColor.historical_material}}">
              <view class="info-item">
                <text class="info-label">染料来源：</text>
                <text class="info-value">{{selectedColor.historical_material}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.auxiliary_material}}">
              <view class="info-item">
                <text class="info-label">媒染剂：</text>
                <text class="info-value">{{selectedColor.auxiliary_material}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.craft_method}}">
              <view class="info-item">
                <text class="info-label">染色方法：</text>
                <text class="info-value">{{selectedColor.craft_method}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.origin_place}}">
              <view class="info-item">
                <text class="info-label">产地：</text>
                <text class="info-value">{{selectedColor.origin_place}}</text>
              </view>
            </block>
          </view>
        </view>
      </block>

      

      <!-- 历史渊源 -->
      <block wx:if="{{selectedColor.dynasty_first_seen || selectedColor.historical_reference || selectedColor.metadata.traditional_usage}}">
        <view class="detail-section">
          <view class="section-title">历史渊源</view>
          <view class="section-content">
            <block wx:if="{{selectedColor.dynasty_first_seen}}">
              <view class="history-item">
                <text class="history-label">首次出现：</text>
                <text class="history-value">{{selectedColor.dynasty_first_seen}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.historical_reference}}">
              <view class="history-item">
                <text class="history-label">历史出处：</text>
                <text class="history-value">{{selectedColor.historical_reference}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.metadata.traditional_usage}}">
              <view class="history-item">
                <text class="history-label">传统用途：</text>
                <text class="history-value">{{selectedColor.metadata.traditional_usage}}</text>
              </view>
            </block>
          </view>
        </view>
      </block>

      <!-- 文化内涵 -->
      <block wx:if="{{selectedColor.cultural_meaning}}">
        <view class="detail-section">
          <view class="section-title">文化内涵</view>
          <view class="section-content">
            <text>{{selectedColor.cultural_meaning}}</text>
          </view>
        </view>
      </block>

      <!-- 使用场景 -->
      <block wx:if="{{selectedColor.usage_scenarios || selectedColor.seasonal_usage || selectedColor.taboo_scenarios}}">
        <view class="detail-section">
          <view class="section-title">使用场景</view>
          <view class="section-content">
            <block wx:if="{{selectedColor.usage_scenarios}}">
              <view class="usage-item">
                <text class="usage-label">适用场景：</text>
                <text class="usage-value">{{selectedColor.usage_scenarios}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.taboo_scenarios}}">
              <view class="usage-item">
                <text class="usage-label">禁忌场景：</text>
                <text class="usage-value">{{selectedColor.taboo_scenarios}}</text>
              </view>
            </block>
            <block wx:if="{{selectedColor.seasonal_usage}}">
              <view class="seasonal-usage">
                <view class="season-title">季节适用：</view>
                <view class="season-grid">
                  <view class="season-item {{selectedColor.seasonal_usage['春'] == '适宜' ? 'suitable' : (selectedColor.seasonal_usage['春'] == '特别适宜' ? 'very-suitable' : 'unsuitable')}}">
                    <text class="season-name">春季</text>
                    <text class="season-status">{{selectedColor.seasonal_usage['春']}}</text>
                  </view>
                  <view class="season-item {{selectedColor.seasonal_usage['夏'] == '适宜' ? 'suitable' : (selectedColor.seasonal_usage['夏'] == '特别适宜' ? 'very-suitable' : 'unsuitable')}}">
                    <text class="season-name">夏季</text>
                    <text class="season-status">{{selectedColor.seasonal_usage['夏']}}</text>
                  </view>
                  <view class="season-item {{selectedColor.seasonal_usage['秋'] == '适宜' ? 'suitable' : (selectedColor.seasonal_usage['秋'] == '特别适宜' ? 'very-suitable' : 'unsuitable')}}">
                    <text class="season-name">秋季</text>
                    <text class="season-status">{{selectedColor.seasonal_usage['秋']}}</text>
                  </view>
                  <view class="season-item {{selectedColor.seasonal_usage['冬'] == '适宜' ? 'suitable' : (selectedColor.seasonal_usage['冬'] == '特别适宜' ? 'very-suitable' : 'unsuitable')}}">
                    <text class="season-name">冬季</text>
                    <text class="season-status">{{selectedColor.seasonal_usage['冬']}}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</view>



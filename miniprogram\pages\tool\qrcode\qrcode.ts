// index.ts
import { layoutUtil } from '../../../utils/layout';
import Api from '../../../utils/api';
import { API_ENDPOINTS } from '../../../utils/constants';

// 导入 IAppOption 类型
interface IAppOption {
  globalData: {
    sceneParams?: {
      scene: string;
      query: Record<string, string>;
    };
  };
  getSceneParams(): {
    scene: string;
    query: Record<string, string>;
  } | undefined;
}

interface IComponentData {
  layoutInfo: any;
  layoutStyle: string;
  pages: Array<{
    name: string;
    path: string;
  }>;
  envVersions: Array<{
    name: string;
    value: string;
  }>;
  pageIndex: number;
  scene: string;
  width: number;
  check_path: boolean;
  env_version: 'develop' | 'trial' | 'release';
  currentVersionName: string;
  auto_color: boolean;
  line_color: {
    r: number;
    g: number;
    b: number;
  };
  is_hyaline: boolean;
  generating: boolean;
  qrcodePath: string;
  currentScene: string;
  scanResult: string;
  scanResult_all: string;
  isCurrentPageExpanded: boolean;
  currentPageArray: Array<{key: string, value: string}>;
}

// 添加API响应类型定义
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

interface QRCodeParams {
  scene: string;
  page: string;
  check_path: boolean;
  env_version: 'develop' | 'trial' | 'release';
  width: number;
  auto_color: boolean;
  is_hyaline: boolean;
  line_color?: {
    r: number;
    g: number;
    b: number;
  };
}

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    layoutInfo: layoutUtil.getLayoutInfo(),
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    pages: [
      { name: '跳转页', path: 'pages/redirect/index' },
      { name: '首页', path: 'pages/index/index' },
      { name: '图像网格', path: 'pages/tool/grid/grid' },
      { name: '图像裁剪', path: 'pages/tool/img_cropper/img_cropper' },
      { name: '图像辅助', path: 'pages/tool/img_help/img_help' },
      { name: '字体生成', path: 'pages/tool/font_generation/font_generation' },
      { name: '小程序码', path: 'pages/tool/qrcode/qrcode' }
    ],
    envVersions: [
      { name: '开发版', value: 'develop' },
      { name: '体验版', value: 'trial' },
      { name: '正式版', value: 'release' }
    ],
    pageIndex: 5,
    scene: '{ "我" : [ {"key": "好好学习"}, {"key1": "天天向上"} ] }',
    width: 430,
    check_path: false,
    env_version: 'develop' as const,
    currentVersionName: '开发版',
    auto_color: true,
    line_color: {
      r: 0,
      g: 0,
      b: 0
    },
    is_hyaline: false,
    generating: false,
    qrcodePath: '',
    currentScene: '',
    scanResult: '',
    isCurrentPageExpanded: false,
    currentPageArray: [] as Array<{key: string, value: string}>,
  } as IComponentData,

  lifetimes: {
    attached() {

      try {
        // 获取当前页面实例
        const pages = getCurrentPages();
        if (pages) {
          if (pages.length > 0) {
            const currentPage = pages[pages.length - 1];
            if (currentPage) {
              // 将对象转换为数组形式以便展示
              const currentPageArray = Object.keys(currentPage).map(key => ({
                key,
                value: typeof currentPage[key] === 'object' 
                  ? JSON.stringify(currentPage[key]) 
                  : String(currentPage[key])
              }));
              
              this.setData({
                currentPage: currentPage,
                currentPageOptions: currentPage.options,
                currentPageArray: currentPageArray
              });
              // 获取页面参数
              this.handlePageQuery(currentPage.options);
            }
          }
        }
      } catch (error) {
        console.error('获取页面参数出错:', error);
      }

      // 尝试从缓存中获取二维码
      const cachedQRCode = wx.getStorageSync('cached_qrcode');
      if (cachedQRCode) {
        this.setData({
          qrcodePath: cachedQRCode
        });
      }
    }
  },

  methods: {
    

    // 处理页面参数
    handlePageQuery(query) {
      
      try {
        // 获取全局扫码参数
        const app = getApp<IAppOption>();
        const sceneParams = app.getSceneParams();
        
        if (sceneParams) {
          // 如果有全局扫码参数，优先使用
          const queryString = Object.keys(sceneParams.query)
            .map(key => `${key}=${sceneParams.query[key]}`)
            .join(', ');
            
          this.setData({
            scene: sceneParams.scene,
            scanResult: sceneParams.scene,
            scanResult_all: queryString
          });
          return;
        }

        // 如果没有全局扫码参数，则处理页面参数
        if (!query) {
          return;
        }

        // 处理scene参数
        if (query.scene) {
          const scene = decodeURIComponent(query.scene);
          this.setData({
            scene: scene,
            scanResult: scene
          });
          console.log('页面扫码参数:', scene);
        }

        // 处理其他查询参数
        const keys = Object.keys(query);
        if (keys.length > 0) {
          if (!query.scene) {  // 如果不是通过扫码进入
            const queryParams = keys.map(function(key) {
              return key + '=' + query[key];
            }).join(', ');
            this.setData({
              scanResult_all: queryParams
            });
            console.log('其他页面参数:', queryParams);
          }
        }
      } catch (error) {
        console.error('处理页面参数出错:', error);
      }
    },

    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 页面选择改变
    onPageChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        pageIndex: e.detail.value
      });
    },

    // 场景值输入
    onSceneInput(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        scene: e.detail.value
      });
    },

    // 二维码尺寸改变
    onWidthChange(e: WechatMiniprogram.CustomEvent) {
      const width = parseInt(e.detail.value);
      if (width >= 280 && width <= 1280) {
        this.setData({
          width: width
        });
      } else {
        wx.showToast({
          title: '宽度必须在280-1280之间',
          icon: 'none'
        });
      }
    },

    // 检查页面路径开关
    onCheckPathChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        check_path: e.detail.value
      });
    },

    // 环境版本改变
    onEnvVersionChange(e: WechatMiniprogram.CustomEvent) {
      const selectedVersion = this.data.envVersions[e.detail.value];
      this.setData({
        env_version: selectedVersion.value as 'develop' | 'trial' | 'release',
        currentVersionName: selectedVersion.name
      });
    },

    // 自动配色开关
    onAutoColorChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        auto_color: e.detail.value
      });
    },

    // 线条颜色改变
    onLineColorChange(e: WechatMiniprogram.CustomEvent) {
      const { channel } = e.currentTarget.dataset;
      const value = parseInt(e.detail.value);
      if (value >= 0 && value <= 255) {
        this.setData({
          [`line_color.${channel}`]: value
        });
      } else {
        wx.showToast({
          title: '颜色值必须在0-255之间',
          icon: 'none'
        });
      }
    },

    // 透明底色开关
    onHyalineChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        is_hyaline: e.detail.value
      });
    },
/**
     * 加密场景值
     * @param scene 原始场景值
     * @returns 加密后的场景值
     */
async _encryptScene(scene: any): Promise<string> {
  console.log("对话框scene",scene);
  
  try {
    const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: API_ENDPOINTS.ENCRYPT,
        method: 'POST',
        data: {
          data: scene
        },
        success: resolve,
        fail: reject
      });
    });
    
    if (res.statusCode === 200) {
      const result = res.data as {
        code: number;
        msg: string;
        data: any;
      };
      
      if (result.code === 1) {
        // 确保返回的是字符串类型
        const encryptedData = typeof result.data === 'string' ? result.data : JSON.stringify(result.data);
        return encryptedData;
      } else {
        throw new Error(result.msg || '加密失败');
      }
    } else {
      throw new Error('请求失败');
    }
  } catch (error) {
    console.error('场景值加密失败:', error);
    throw error;
  }
},

    // 生成小程序码
    async generateQRCode() {
      if (!this.data.scene) {
        wx.showToast({
          title: '请输入场景参数',
          icon: 'none'
        });
        return;
      }

      this.setData({ 
        generating: true,
        currentScene: this.data.scene
      });
      wx.showLoading({ title: '生成中...' });

      try {
        // 先加密场景值
        const encryptedScene = await this._encryptScene(this.data.scene);
        console.log("加密后的场景值:",encryptedScene);
        
        // 构建请求参数
        const params: QRCodeParams = {
          scene: encryptedScene, // 使用加密后的场景值
          page: this.data.pages[this.data.pageIndex].path,
          check_path: this.data.check_path,
          env_version: this.data.env_version,
          width: this.data.width,
          auto_color: this.data.auto_color,
          is_hyaline: this.data.is_hyaline
        };

        if (!this.data.auto_color) {
          params.line_color = this.data.line_color;
        }

        console.log('请求参数:', params);
        const result = await Api.common.generateWXACode(params) as ApiResponse<string>
        console.log('接口响应:', result);

        if (result) {
          if (result.code === 1) {
            if (result.data) {
              // 更新页面显示的二维码
              this.setData({
                qrcodePath: result.data,
                generating: false
              });

              // 保存到本地缓存
              wx.setStorageSync('cached_qrcode', result.data);

              wx.hideLoading();
              wx.showToast({
                title: '生成成功',
                icon: 'success'
              });
            } else {
              throw new Error('生成小程序码失败：返回数据为空');
            }
          } else {
            let errorMsg = '生成小程序码失败';
            if (result.msg) {
              errorMsg = result.msg;
            }
            throw new Error(errorMsg);
          }
        } else {
          throw new Error('生成小程序码失败：返回结果为空');
        }
      } catch (error) {
        console.error('生成小程序码失败:', error);
        wx.hideLoading();
        let errorMessage = '生成失败，请重试';
        if (error) {
          if (error.message) {
            errorMessage = error.message;
          }
        }
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
        this.setData({ generating: false });
      }
    },

    // 预览小程序码
    previewQRCode() {
      if (this.data.qrcodePath) {
        wx.previewImage({
          urls: [this.data.qrcodePath]
        });
      }
    },

    // 保存小程序码到相册
    saveQRCode() {
      if (!this.data.qrcodePath) return;

      wx.showLoading({ title: '保存中...' });

      wx.downloadFile({
        url: this.data.qrcodePath,
        success: (res) => {
          if (res.statusCode === 200) {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: () => {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: () => {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        },
        complete: () => {
          wx.hideLoading();
        }
      });
    },

    // 切换当前页面信息的展开状态
    toggleCurrentPage() {
      this.setData({
        isCurrentPageExpanded: !this.data.isCurrentPageExpanded
      });
    }
  }
});

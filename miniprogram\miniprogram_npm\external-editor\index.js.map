{"version": 3, "sources": ["index.js", "errors/CreateFileError.js", "errors/ReadFileError.js", "errors/RemoveFileError.js", "errors/LaunchEditorError.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;AC<PERSON>,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;AIXA,AFMA,ACHA,AHSA;AIXA,AFMA,ACHA,AHSA;AIXA,AFMA,ACHA,AHSA;AIXA,ADGA,AHSA;AIXA,ADGA,AHSA;AIXA,ADGA,AHSA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n\n/*\n  ExternalEditor\n  <PERSON> <<EMAIL>>\n  MIT\n */\n\n(function() {\n  var ChatDet, CreateFileError, ExternalEditor, FS, IConvLite, LaunchEditorError, ReadFileError, RemoveFileError, Spawn, SpawnSync, Temp,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; };\n\n  FS = require('fs');\n\n  Temp = require('tmp');\n\n  SpawnSync = require('child_process').spawnSync;\n\n  Spawn = require('child_process').spawn;\n\n  IConvLite = require('iconv-lite');\n\n  ChatDet = require('chardet');\n\n  CreateFileError = require('./errors/CreateFileError');\n\n  ReadFileError = require('./errors/ReadFileError');\n\n  RemoveFileError = require('./errors/RemoveFileError');\n\n  LaunchEditorError = require('./errors/LaunchEditorError');\n\n  ExternalEditor = (function() {\n    ExternalEditor.edit = function(text) {\n      var editor;\n      if (text == null) {\n        text = '';\n      }\n      editor = new ExternalEditor(text);\n      editor.run();\n      editor.cleanup();\n      return editor.text;\n    };\n\n    ExternalEditor.editAsync = function(text, callback) {\n      var editor;\n      if (text == null) {\n        text = '';\n      }\n      editor = new ExternalEditor(text);\n      return editor.runAsync(function(error_run, text) {\n        var error_cleanup;\n        if (!error_run) {\n          try {\n            editor.cleanup();\n            if (typeof callback === 'function') {\n              return setImmediate(callback, null, text);\n            }\n          } catch (error) {\n            error_cleanup = error;\n            if (typeof callback === 'function') {\n              return setImmediate(callback, error_cleanup, null);\n            }\n          }\n        } else {\n          if (typeof callback === 'function') {\n            return setImmediate(callback, error_run, null);\n          }\n        }\n      });\n    };\n\n    ExternalEditor.CreateFileError = CreateFileError;\n\n    ExternalEditor.ReadFileError = ReadFileError;\n\n    ExternalEditor.RemoveFileError = RemoveFileError;\n\n    ExternalEditor.LaunchEditorError = LaunchEditorError;\n\n    ExternalEditor.prototype.text = '';\n\n    ExternalEditor.prototype.temp_file = void 0;\n\n    ExternalEditor.prototype.editor = {\n      bin: void 0,\n      args: []\n    };\n\n    ExternalEditor.prototype.last_exit_status = void 0;\n\n    function ExternalEditor(text1) {\n      this.text = text1 != null ? text1 : '';\n      this.launchEditorAsync = bind(this.launchEditorAsync, this);\n      this.launchEditor = bind(this.launchEditor, this);\n      this.removeTemporaryFile = bind(this.removeTemporaryFile, this);\n      this.readTemporaryFile = bind(this.readTemporaryFile, this);\n      this.createTemporaryFile = bind(this.createTemporaryFile, this);\n      this.determineEditor = bind(this.determineEditor, this);\n      this.cleanup = bind(this.cleanup, this);\n      this.runAsync = bind(this.runAsync, this);\n      this.run = bind(this.run, this);\n      this.determineEditor();\n      this.createTemporaryFile();\n    }\n\n    ExternalEditor.prototype.run = function() {\n      this.launchEditor();\n      return this.readTemporaryFile();\n    };\n\n    ExternalEditor.prototype.runAsync = function(callback) {\n      var error_launch;\n      try {\n        return this.launchEditorAsync((function(_this) {\n          return function() {\n            var error_read;\n            try {\n              _this.readTemporaryFile();\n              if (typeof callback === 'function') {\n                return setImmediate(callback, null, _this.text);\n              }\n            } catch (error) {\n              error_read = error;\n              if (typeof callback === 'function') {\n                return setImmediate(callback, error_read, null);\n              }\n            }\n          };\n        })(this));\n      } catch (error) {\n        error_launch = error;\n        if (typeof callback === 'function') {\n          return setImmediate(callback, error_launch, null);\n        }\n      }\n    };\n\n    ExternalEditor.prototype.cleanup = function() {\n      return this.removeTemporaryFile();\n    };\n\n    ExternalEditor.prototype.determineEditor = function() {\n      var args, ed, editor;\n      ed = /^win/.test(process.platform) ? 'notepad' : 'vim';\n      editor = process.env.VISUAL || process.env.EDITOR || ed;\n      args = editor.split(/\\s+/);\n      this.editor.bin = args.shift();\n      return this.editor.args = args;\n    };\n\n    ExternalEditor.prototype.createTemporaryFile = function() {\n      var e;\n      try {\n        this.temp_file = Temp.tmpNameSync({});\n        return FS.writeFileSync(this.temp_file, this.text, {\n          encoding: 'utf8'\n        });\n      } catch (error) {\n        e = error;\n        throw new CreateFileError(e);\n      }\n    };\n\n    ExternalEditor.prototype.readTemporaryFile = function() {\n      var buffer, e, encoding;\n      try {\n        buffer = FS.readFileSync(this.temp_file);\n        if (!buffer.length) {\n          return this.text = '';\n        }\n        encoding = ChatDet.detect(buffer);\n        return this.text = IConvLite.decode(buffer, encoding);\n      } catch (error) {\n        e = error;\n        throw new ReadFileError(e);\n      }\n    };\n\n    ExternalEditor.prototype.removeTemporaryFile = function() {\n      var e;\n      try {\n        return FS.unlinkSync(this.temp_file);\n      } catch (error) {\n        e = error;\n        throw new RemoveFileError(e);\n      }\n    };\n\n    ExternalEditor.prototype.launchEditor = function() {\n      var e, run;\n      try {\n        run = SpawnSync(this.editor.bin, this.editor.args.concat([this.temp_file]), {\n          stdio: 'inherit'\n        });\n        return this.last_exit_status = run.status;\n      } catch (error) {\n        e = error;\n        throw new LaunchEditorError(e);\n      }\n    };\n\n    ExternalEditor.prototype.launchEditorAsync = function(callback) {\n      var child_process, e;\n      try {\n        child_process = Spawn(this.editor.bin, this.editor.args.concat([this.temp_file]), {\n          stdio: 'inherit'\n        });\n        return child_process.on('exit', (function(_this) {\n          return function(code) {\n            _this.last_exit_status = code;\n            if (typeof callback === 'function') {\n              return callback();\n            }\n          };\n        })(this));\n      } catch (error) {\n        e = error;\n        throw new LaunchEditorError(e);\n      }\n    };\n\n    return ExternalEditor;\n\n  })();\n\n  module.exports = ExternalEditor;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n\n/*\n  ExternalEditor\n  <PERSON> <<EMAIL>>\n  MIT\n */\n\n(function() {\n  var CreateFileError,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  CreateFileError = (function(superClass) {\n    extend(CreateFileError, superClass);\n\n    CreateFileError.prototype.message = 'Failed to create temporary file for editor';\n\n    function CreateFileError(original_error) {\n      this.original_error = original_error;\n    }\n\n    return CreateFileError;\n\n  })(Error);\n\n  module.exports = CreateFileError;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n\n/*\n  ExternalEditor\n  <PERSON> <<EMAIL>>\n  MIT\n */\n\n(function() {\n  var ReadFileError,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ReadFileError = (function(superClass) {\n    extend(ReadFileError, superClass);\n\n    ReadFileError.prototype.message = 'Failed to read temporary file';\n\n    function ReadFileError(original_error) {\n      this.original_error = original_error;\n    }\n\n    return ReadFileError;\n\n  })(Error);\n\n  module.exports = ReadFileError;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n\n/*\n  ExternalEditor\n  <PERSON> <<EMAIL>>\n  MIT\n */\n\n(function() {\n  var RemoveFileError,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  RemoveFileError = (function(superClass) {\n    extend(RemoveFileError, superClass);\n\n    RemoveFileError.prototype.message = 'Failed to cleanup temporary file';\n\n    function RemoveFileError(original_error) {\n      this.original_error = original_error;\n    }\n\n    return RemoveFileError;\n\n  })(Error);\n\n  module.exports = RemoveFileError;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n\n/*\n  ExternalEditor\n  <PERSON> <<EMAIL>>\n  MIT\n */\n\n(function() {\n  var LaunchEditorError,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  LaunchEditorError = (function(superClass) {\n    extend(LaunchEditorError, superClass);\n\n    LaunchEditorError.prototype.message = 'Failed launch editor';\n\n    function LaunchEditorError(original_error) {\n      this.original_error = original_error;\n    }\n\n    return LaunchEditorError;\n\n  })(Error);\n\n  module.exports = LaunchEditorError;\n\n}).call(this);\n"]}
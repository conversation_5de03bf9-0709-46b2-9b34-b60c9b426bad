/* 筛选栏 */
.filter-bar {
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid rgba(139, 69, 19, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.category-scroll {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.category-list {
  display: inline-flex;
  padding: 0 10rpx;
}

.filter-item {
  padding: 12rpx 36rpx;
  margin: 0 12rpx;
  background: rgba(255, 255, 255, 0.6);
  font-size: 28rpx;
  color: #4A3B2C;
  position: relative;
  border: 1rpx solid rgba(139, 69, 19, 0.3);
  outline: 1rpx solid rgba(139, 69, 19, 0.3);
  outline-offset: -3px;
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
  will-change: transform, background-color;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-item::before,
.filter-item::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.3);
  opacity: 0;
  transition: all 0.3s ease;
}

.filter-item::before {
  top: 6rpx;
  left: 6rpx;
}

.filter-item::after {
  bottom: 6rpx;
  right: 6rpx;
}

.filter-item.active {
  background: rgba(139, 69, 19, 0.15);
  color: #8B4513;
  font-weight: 500;
  border-color: rgba(139, 69, 19, 0.3);
}

.filter-item.active::before,
.filter-item.active::after {
  opacity: 1;
}

.filter-item:first-child {
  margin-left: 0;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item:active {
  transform: scale(0.95);
  background: rgba(139, 69, 19, 0.2);
}

/* 排序栏 */
.sort-bar {
  background: rgba(255, 255, 255, 0.95);
  padding: 16rpx 20rpx;
  display: flex;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.sort-item {
  padding: 8rpx 20rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sort-item.active {
  color: #333;
  font-weight: bold;
  position: relative;
}

.sort-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #333;
  border-radius: 2rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  min-height: 400rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #666;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  min-height: 400rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.retry-button {
  padding: 16rpx 40rpx;
  background: #666;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
}

.retry-button:active {
  opacity: 0.8;
}

/* 颜色网格布局 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

/* 颜色项样式 */
.color-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.color-item:active {
  transform: scale(0.98);
}

/* 颜色预览区域 */
.color-preview {
  height: 200rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
}

.color-name {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.color-pinyin {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 颜色信息区域 */
.color-info {
  padding: 16rpx;
  background: #fff;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 24rpx;
}

.value {
  color: #333;
  font-size: 24rpx;
}

/* 颜色详情弹窗 */
.color-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

/* 详情头部 */
.detail-header {
  padding: 40rpx;
  text-align: center;
}

.detail-name {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.detail-pinyin {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-top: 12rpx;
  display: block;
}

/* 详情内容 */
.detail-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: #666;
  border-radius: 3rpx;
}

/* 详情网格 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.detail-item {
  background: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.item-label {
  color: #666;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  display: block;
}

.item-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 描述文本 */
.detail-desc,
.detail-culture,
.usage-text {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

/* 历史信息 */
.history-info {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

.history-item {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
  display: block;
}

/* 关闭按钮 */
.modal-close {
  text-align: center;
  padding: 20rpx;
  color: #666;
  font-size: 28rpx;
  border-top: 1rpx solid #eee;
}

.modal-close:active {
  background: #f5f5f5;
}

/* 主体内容区样式 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧颜色条样式 */
.color-strip-scroll {
  width: 150rpx;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.color-strip {
  display: flex;
  flex-direction: column;
}

.color-block {
  width: 140rpx;
  height: 50px;
  position: relative;
  cursor: pointer;
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 0 100%, 0 calc(100% - 20px));
  outline-offset: -1px;
  outline: 1rpx solid rgba(255, 255, 255, 0.9);
  will-change: transform, opacity;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.color-block:active {
  transform: scale(0.95) translateZ(0);
  opacity: 0.8;
}

.color-block.active {
  width: 150rpx;
  height: 50px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  outline: 0;
  clip-path: none;
  transform: scale(1.05) translateZ(0);
}
.color-block.active .p_text{
  display: none;
}
.color-block .p_text{
  position: absolute;
  right: 10rpx;
  bottom: 5rpx;
  font-size: 24rpx;
}

.color-block .p_num{
  font-size: 18rpx;
  left:0;
  top:0;
  background-color: rgba(255, 255, 255, 0.5);
  color: #000;
  padding: 0 5px;
  text-align: center;
  
}

.color-block .p_num,
.color-block .p_text .text{
  color: #fff;
  
  text-shadow: 
    -1px -1px 0 rgb(51, 51, 51),  
    1px -1px 0 rgb(51, 51, 51),
    -1px 1px 0 rgb(51, 51, 51),
    1px 1px 0 rgb(51, 51, 51);
}

/* 右侧颜色展示区样式 */
.color-display {
  flex: 1;
  position: relative;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* border-left: 3px solid #ccc; */
  overflow: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 山水动画背景 */
.ink-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.ink-bg {
  width: 100%;
  height: 100%;
  opacity: 0.1;
  /* filter: contrast(1.2) brightness(1.5); */
  /* mix-blend-mode: overlay; */
}

.ink-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  mix-blend-mode: overlay;
}

/* 确保其他内容在动画上层 */
.vertical-color-name {
  position: absolute;
  right: 30px;
  top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.vertical-color-name .vertical-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.vertical-color-name .vertical-text {
  font-family: CustomFont, sans-serif;
  font-size: 160rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  display: block;
  margin: 20rpx 0;
  line-height: 1;
  text-align: center;
  width: 120rpx;
  height: 120rpx;
  writing-mode: vertical-rl;
  -webkit-writing-mode: vertical-rl;
  text-orientation: upright;
  -webkit-text-orientation: upright;
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.vertical-color-name .vertical-pinyin {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);
  margin: 30rpx 20rpx 0 0;
  letter-spacing: 4rpx;
}

/* 确保信息面板在动画上层 */
.color-info-overlay {
  position: absolute;
  bottom: 100rpx;
  left: 120rpx;
  right: -30rpx;
  background: rgba(255, 255, 255, 0.7);
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2;
  border: 2rpx solid rgba(139, 69, 19, 0.3);
  outline: 2rpx solid rgba(139, 69, 19, 0.3);
  outline-offset: -4px;
  clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
  will-change: transform;
}

/* 查看更多按钮 */
.view-more-btn {
  margin-top: 20rpx;
  padding: 16rpx;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  will-change: transform, background-color;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-more-btn text {
  font-size: 26rpx;
  color: #8B4513;
}

.view-more-btn .arrow {
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

.view-more-btn:active {
  transform: scale(0.95);
  background: rgba(139, 69, 19, 0.2);
}

.view-more-btn:active .arrow {
  transform: translateX(4rpx);
}

/* 调整弹出层中的配方样式 */
.detail-section .formula-block {
  margin: 20rpx 0;
  background: #fff;
  border: 1px solid rgba(139, 69, 19, 0.1);
}

.detail-section .section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #8B4513;
}

.color-title {
  margin-bottom: 30rpx;
}

.color-name {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
}

.color-pinyin {
  font-size: 24rpx;
  color: #fff;
  margin-top: 10rpx;
  display: block;
}

.color-details {
  display: grid;
  grid-template-columns: repeat(4,1fr);
  gap: 20rpx;
}

.detail-row {
  display: flex;
  flex-direction: column;
}

.detail-row .label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.detail-row .value {
  font-size: 28rpx;
  color: #333;
}


/* 左侧竖排颜色描述 */


.vertical-description .vertical-text-container {
  position: absolute;
  left: 10rpx;
  top: 50rpx;
  writing-mode: vertical-rl;
  text-orientation: upright;
  color: rgba(255, 255, 255, 0.5);
  font-size: 50rpx;
  line-height: 1.8;
  text-shadow: 0 2rpx 2rpx rgba(0, 0, 0, 0.3);
  max-height: 80vh;
  overflow: hidden;
}

@font-face {
  font-family: 'dnmbxyt';
  src: url('https://tool.feifan919.com/wx/font/dnmbxyt.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
}

.vertical-description .vertical-text {
  margin: 4rpx 0;
  display: inline-block;
  font-family: 'dnmbxyt', cursive;

}

/* 颜色详情弹出层 */
.color-detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, visibility;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.color-detail-popup.show {
  opacity: 1;
  visibility: visible;
}

.color-detail-popup .popup-content {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 85%;
  background: #fff;
  padding: 0;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: -4rpx 0 30rpx rgba(0, 0, 0, 0.1);
}

.color-detail-popup.show .popup-content {
  transform: translateX(0);
}

.popup-close {
  position: absolute;
  top: 40rpx;
  left: -90rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  will-change: transform, background-color;
}

.popup-close:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.7);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 80rpx;
}

.popup-scroll {
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24rpx;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.05);
}

.color-preview-section {
  height: 300rpx;
  /* margin: -30rpx -30rpx 30rpx -30rpx; */
  border-radius: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.color-preview-section::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
  pointer-events: none;
}

.color-preview-section .color-name {
  font-size: 72rpx;
  z-index: 1;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  margin-bottom: 16rpx;
  text-align: center;
}

.color-preview-section .color-pinyin {
  font-size: 32rpx;
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  text-align: center;
}

.section-title {
  font-size: 30rpx;
  margin-bottom: 16rpx;
}

.section-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
}

.color-values {
  gap: 12rpx;
}

.color-value-item {
  margin-top: 10rpx;

}

/* 主界面中央颜色名称 */
.center-color-name {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
}

.main-color-name {
  font-size: 120rpx;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  display: block;
  margin-bottom: 20rpx;
  font-family: CustomFont, sans-serif;
}

.main-color-pinyin {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  display: block;
}

/* 历史信息样式 */
.history-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.history-value {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  line-height: 1.6;
}

/* 颜色预览区新增样式 */
.color-name-en {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  text-align: center;
  z-index: 1;
}

/* 信息网格布局 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  background: #f8fafd;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  line-height: 1.6;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-direction: column;
  /* gap: 20rpx; */
}

.tag-group {
  /* display: flex; */
  flex-direction: column;
  /* gap: 12rpx; */
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.tag-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 50rpx;
}

/* .tag {
  padding: 8rpx 20rpx;
  background: #f0f5ff;
  color: #4a90e2;
  border-radius: 100rpx;
  font-size: 24rpx;
} */

/* 季节适用性 */
.seasonal-usage {
  margin-top: 24rpx;
  background: #f8fafd;
  padding: 20rpx;
  border-radius: 12rpx;
}

.season-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.season-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.season-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: 8rpx;
  background: #fff;
  border: 1rpx solid #eee;
}

.season-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.season-status {
  font-size: 22rpx;
}

.season-item.unsuitable {
  background: #fff5f5;
  border-color: #ffebeb;
}

.season-item.unsuitable .season-status {
  color: #ff4d4f;
}

.season-item.suitable {
  background: #f6ffed;
  border-color: #e8f7d2;
}

.season-item.suitable .season-status {
  color: #52c41a;
}

.season-item.very-suitable {
  background: #e6f7ff;
  border-color: #bae7ff;
}

.season-item.very-suitable .season-status {
  color: #1890ff;
}

/* 使用场景样式 */
.usage-item {
  margin-bottom: 16rpx;
  background: #f8fafd;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
}

.usage-item:last-child {
  margin-bottom: 20rpx;
}

.usage-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 16rpx;
}

.usage-value {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.vertical-alias-container {
  position: absolute;
  right: 20rpx;
  top:250rpx;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.vertical-alias-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  font-size: 32rpx;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-family: "CustomFont", sans-serif;
  margin: 4rpx 0;
}

/* 配方部分样式 */
.formula-section {
  margin-top: 30rpx;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20rpx;
}

.formula-block {
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.formula-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  padding-left: 16rpx;
  border-left: 4rpx solid #8B4513;
}

.formula-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.percentage-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.percentage-item {
  display: flex;
  align-items: center;
  background: rgba(139, 69, 19, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.percentage-item .color-name {
  font-size: 24rpx;
  color: #333;
  margin-right: 8rpx;
}

.percentage-item .percentage {
  font-size: 24rpx;
  color: #8B4513;
  font-weight: 500;
}

.mineral-list {
  margin-bottom: 20rpx;
}

.mineral-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.mineral-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.mineral-name {
  font-size: 26rpx;
  color: #333;
}

.mineral-origin {
  font-size: 22rpx;
  color: #666;
}

.mineral-percentage {
  font-size: 26rpx;
  color: #8B4513;
  font-weight: 500;
}

.craft-method {
  margin: 20rpx 0;
  padding: 12rpx;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 6rpx;
}

.method-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.method-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.process-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  border: 1px solid rgba(139, 69, 19, 0.2);
}

.step-number {
  width: 32rpx;
  height: 32rpx;
  background: #8B4513;
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.step-desc {
  font-size: 24rpx;
  color: #333;
}

.share-canvas {
  position: fixed;
  left: -9999px;
  visibility: hidden;
}

import { layoutUtil } from '../../utils/layout';

interface IPageData {
  layoutStyle: string;
  showLoginModal: boolean;
}

Page({
  data: {
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    showLoginModal: true
  } as IPageData,

  onLoad() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 登录成功回调
  onLoginSuccess(e: any) {
    const { userInfo } = e.detail;
    console.log('登录成功login.ts:', userInfo);
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 登录弹窗关闭回调
  onLoginModalClose() {
    // 如果未登录，保持弹窗显示
    const userInfo = wx.getStorageSync('userInfo');
    console.log('登录弹窗关闭login.ts:', userInfo);
    
    if (!userInfo) {
      this.setData({
        showLoginModal: true
      });
    }
  }
}); 
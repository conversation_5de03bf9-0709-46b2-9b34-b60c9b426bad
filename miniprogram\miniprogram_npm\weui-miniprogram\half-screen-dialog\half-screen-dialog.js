var globalThis=this,self=this;module.exports=require("../_commons/0.js")([{ids:[12],modules:{129:function(t,e){Component({options:{multipleSlots:!0},properties:{closabled:{type:Boolean,value:!0},title:{type:String,value:""},subTitle:{type:String,value:""},extClass:{type:String,value:""},desc:{type:String,value:""},tips:{type:String,value:""},maskClosable:{type:Boolean,value:!0},mask:{type:Boolean,value:!0},show:{type:<PERSON><PERSON><PERSON>,value:!1,observer:"_showChange"},buttons:{type:Array,value:[]},rootPortal:{type:Boolean,value:!1}},data:{wrapperShow:!1,innerShow:!1},lifetimes:{ready:function(){this._showChange(this.data.show)}},methods:{_showChange:function(t){var e=this;t?this.setData({wrapperShow:!0,innerShow:!0}):(this.setData({innerShow:!1}),setTimeout((function(){e.setData({wrapperShow:!1})}),300))},close:function(t){var e=t.currentTarget.dataset.type;(this.data.maskClosable||"close"===e)&&(this.setData({show:!1}),this.triggerEvent("close"))},buttonTap:function(t){var e=t.currentTarget.dataset.index;this.triggerEvent("buttontap",{index:e,item:this.data.buttons[e]},{})},onMaskMouseMove:function(){}}})},13:function(t,e,a){t.exports=a(129)}},entries:[[13,0]]}]);
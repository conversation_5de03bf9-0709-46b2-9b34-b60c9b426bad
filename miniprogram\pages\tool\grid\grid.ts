import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
interface PaperSpecification {
  name: string;
  width: number;
  height: number;
  unit?: string;
  paper?: string;
}

Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    currentTab: 0,
    showFloatLayer: false,
    showCropper: false,
    tempFilePath: '',
    croppedImage: '' as string,
    showZoomImage: false,
    showActionPopup: false,
    zoomedImage: '',
    zoomScale: 1,
    showGridSettings: false,
    showMainSettings: false,
    tab_current: 'grid',
    currentCell: {
      row: -1,
      col: -1,
      coordinate: ''
    },
    annotatedCell: {
      row: -1,
      col: -1
    },
    isAnnotated: false,
    gridSettings: {
      rows: 3,
      columns: 3,
      color: '#ff0000',
      opacity: 50
    },
    zoomGridSettings: {
      show: true,
      rows: 3,
      columns: 3,
      color: '#ff0000',
      opacity: 60
    },
    coordinateSettings: {
      fontSize: 24,
      color: '#000000',
      opacity: 60,
      horizontalPosition: 0,
      verticalPosition: 0,
    },
    annotationSettings: {
      color: '#00ffff',
      opacity: 20
    },
    coordinates: [] as string[],
    verticalCoordinates: [] as string[],
    canvasWidth: 300,
    canvasHeight: 300,
    dragX: 0,
    dragY: 0,
    lastX: 0,
    lastY: 0,
    isDragging: false,
    showExportLoading: false,
    annotations: {
      rows: [] as number[],
      columns: [] as number[]
    },
    showAnnotationSettings: false,
    positionOptions: {
      horizontal: ['上', '下', '无'],
      vertical: ['左', '右', '无']
    },
    showSizeCalculator: false,
    selectedPaperSizeIndex: 0,
    calculatedResults: {
      cellWidth: '0',
      cellHeight: '0',
      totalWidth: '0',
      totalHeight: '0',
      horizontalMargin: '0',
      verticalMargin: '0'
    },
    statusBarHeight: 0,
    navBarHeight: 44,
    gridConfig: {
      maxGridSize: 9,
      defaultGridSize: 3,
      spacing: 4
    },
    imageConfig: {
      maxSize: 2048,
      quality: 0.8
    },
    selectedImage: '',
    gridSize: 3,
    showGrid: true,
    isLoading: false,
    cropperConfig: {
      width: 750,
      height: 750,
      maxScale: 2.5,
      minScale: 1,
      quality: 0.8
    },
    currentPaperSize: {} as PaperSpecification
  },
  lifetimes: {
    attached() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        this.setData({ 
          statusBarHeight: systemInfo.statusBarHeight || 20 
        });
      } catch (error) {
        console.error('获取系统信息失败:', error);
        this.setData({ statusBarHeight: 20 });
      }
      this.generateCoordinates(this.data.gridSettings.columns)
      this.generateVerticalCoordinates(this.data.gridSettings.rows)
      this.setData({
        contentStyle: layoutUtil.getContentStyle(),
        contentStyle_top: layoutUtil.navigationHeight
      });
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },
  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    // 分享给朋友
    onShareAppMessage: async function () {
      let shareImagePath = '';
      try {
        const res = await wx.createSelectorQuery().select('.content-wrapper').node().exec();
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 设置画布尺寸
        canvas.width = 500;
        canvas.height = 400;
        
        // 获取页面截图
        const screenshotRes = await wx.createSelectorQuery()
          .select('.content-wrapper')
          .fields({
            node: true,
            size: true,
          })
          .exec();
          
        if (screenshotRes[0]) {
          const { width, height } = screenshotRes[0];
          const pixelRatio = wx.getSystemInfoSync().pixelRatio;
          canvas.width = width * pixelRatio;
          canvas.height = height * pixelRatio;
          ctx.scale(pixelRatio, pixelRatio);
          
          // 将页面内容绘制到画布
          const canvasImage = await wx.canvasToTempFilePath({
            canvas,
            width: width,
            height: height,
            destWidth: width * pixelRatio,
            destHeight: height * pixelRatio
          });
          
          shareImagePath = canvasImage.tempFilePath;
        }
      } catch (error) {
        console.error('生成分享图片失败:', error);
      }

      return {
        title: '网格工具 - 轻松制作临摹网格',
        path: '/pages/tool/grid/grid',
        imageUrl: shareImagePath || '',
        success: function(res) {
          wx.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: function(res) {
          wx.showToast({
            title: '分享失败',
            icon: 'none',
            duration: 2000
          });
        }
      };
    },

    // 分享到朋友圈
    onShareTimeline: async function () {
      let shareImagePath = '';
      try {
        const res = await wx.createSelectorQuery().select('.content-wrapper').node().exec();
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 设置画布尺寸
        canvas.width = 500;
        canvas.height = 400;
        
        // 获取页面截图
        const screenshotRes = await wx.createSelectorQuery()
          .select('.content-wrapper')
          .fields({
            node: true,
            size: true,
          })
          .exec();
          
        if (screenshotRes[0]) {
          const { width, height } = screenshotRes[0];
          const pixelRatio = wx.getSystemInfoSync().pixelRatio;
          canvas.width = width * pixelRatio;
          canvas.height = height * pixelRatio;
          ctx.scale(pixelRatio, pixelRatio);
          
          // 将页面内容绘制到画布
          const canvasImage = await wx.canvasToTempFilePath({
            canvas,
            width: width,
            height: height,
            destWidth: width * pixelRatio,
            destHeight: height * pixelRatio
          });
          
          shareImagePath = canvasImage.tempFilePath;
        }
      } catch (error) {
        console.error('生成分享图片失败:', error);
      }

      return {
        title: '网格工具 - 支持多种纸张尺寸的临摹网格工具',
        query: '',
        imageUrl: shareImagePath || ''
      };
    },


    async chooseImage() {
      if (this.data.isLoading) return;
      
      try {
        this.setData({ isLoading: true });
        const res = await wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera'],
          sizeType: ['compressed']
        });

        if (res.tempFiles.length > 0) {
          const image = res.tempFiles[0];
          
          const maxSize = this.data.imageConfig.maxSize * 1024;
          if (image.size > maxSize) {
            wx.showToast({
              title: `图片大小不能超过${this.data.imageConfig.maxSize}MB`,
              icon: 'none'
            });
            return;
          }

          this.setData({
            selectedImage: image.tempFilePath,
            showCropper: true
          });
        }
      } catch (error) {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      } finally {
        this.setData({ isLoading: false });
      }
    },

    adjustGridSize(e: WechatMiniprogram.TouchEvent) {
      const { type } = e.currentTarget.dataset;
      let { gridSize } = this.data;
      
      if (type === 'increase' && gridSize < this.data.gridConfig.maxGridSize) {
        gridSize++;
      } else if (type === 'decrease' && gridSize > 1) {
        gridSize--;
      }
      
      this.setData({ gridSize });
    },

    toggleGrid() {
      this.setData({
        showGrid: !this.data.showGrid
      });
    },

    onCropperComplete(e: any) {
      const { url } = e.detail;
      this.setData({
        selectedImage: url,
        showCropper: false
      });
    },

    onCropperCancel() {
      this.setData({
        showCropper: false,
        selectedImage: ''
      });
    },

    async saveImage() {
      if (!this.data.selectedImage || this.data.isLoading) return;
      
      try {
        this.setData({ isLoading: true });
        await wx.saveImageToPhotosAlbum({
          filePath: this.data.selectedImage
        });
        
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存图片失败:', error);
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      } finally {
        this.setData({ isLoading: false });
      }
    },

    preventTouchMove() {
      return false;
    },

    onGridRowsDecrease() {
      const rows = Math.max(1, this.data.gridSettings.rows - 1)
      this.setData({
        'gridSettings.rows': rows
      })
      this.generateVerticalCoordinates(rows)
      this.calculateSizes()
    },
    onGridRowsIncrease() {
      const rows = Math.min(20, this.data.gridSettings.rows + 1)
      this.setData({
        'gridSettings.rows': rows
      })
      this.generateVerticalCoordinates(rows)
      this.calculateSizes()
    },
    generateCoordinates(columns: number) {
      const coordinates: string[] = []
      for (let i = 0; i < columns; i++) {
        coordinates.push(String.fromCharCode(65 + (i % 26)))
      }
      this.setData({ coordinates })
    },
    generateVerticalCoordinates(rows: number) {
      const verticalCoordinates: string[] = []
      for (let i = 0; i < rows; i++) {
        verticalCoordinates.push((i + 1).toString())
      }
      this.setData({ verticalCoordinates })
    },
    onGridColumnsChange(e: any) {
      const columns = Math.max(1, Math.min(20, parseInt(e.detail.value) || 4))
      this.setData({
        'gridSettings.columns': columns
      })
      this.generateCoordinates(columns)
      this.calculateSizes()
    },
    onGridColumnsIncrease() {
      const columns = Math.min(20, this.data.gridSettings.columns + 1)
      this.setData({
        'gridSettings.columns': columns
      })
      this.generateCoordinates(columns)
      this.calculateSizes()
    },
    onGridColumnsDecrease() {
      const columns = Math.max(1, this.data.gridSettings.columns - 1)
      this.setData({
        'gridSettings.columns': columns
      })
      this.generateCoordinates(columns)
      this.calculateSizes()
    },

    onColorConfirm(e: WechatMiniprogram.CustomEvent) {
      const { color } = e.detail;
      this.setData({
        'gridSettings.color': color,
        'gridSettings.opacity': 100
      });
    },
    onGridOpacityChange(e: any) {
      this.setData({
        'gridSettings.opacity': e.detail.value
      })
    },
    handleUpload() {
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          console.log(res);
          
          const tempFilePath = res.tempFilePaths[0]
          this.setData({
            tempFilePath,
            showCropper: true
          })
        }
      })
    },
    handleCropperClose(e: any) {
      if (e.detail && e.detail.path) {
        this.setData({
          croppedImage: e.detail.path
        })
      }
      
      this.setData({
        showCropper: false
      })
    },
    onCoordinateFontSizeDecrease() {
      const fontSize = Math.max(12, this.data.coordinateSettings.fontSize - 2)
      this.setData({
        'coordinateSettings.fontSize': fontSize
      })
    },
    onCoordinateFontSizeIncrease() {
      const fontSize = Math.min(40, this.data.coordinateSettings.fontSize + 2)
      this.setData({
        'coordinateSettings.fontSize': fontSize
      })
    },
    onCoordinateFontSizeChange(e: any) {
      const fontSize = Math.max(12, Math.min(40, parseInt(e.detail.value) || 24))
      this.setData({
        'coordinateSettings.fontSize': fontSize
      })
    },

    onCoordinateColorConfirm(e: WechatMiniprogram.CustomEvent) {
      const { color } = e.detail;
      this.setData({
        'coordinateSettings.color': color,
        'coordinateSettings.opacity': 100
      });
    },
    onCoordinateOpacityChange(e: any) {
      this.setData({
        'coordinateSettings.opacity': e.detail.value
      })
    },
    dragStart(e: any) {
      const touch = e.touches[0]
      this.setData({
        lastX: touch.clientX,
        lastY: touch.clientY,
        isDragging: true
      })
    },
    
    dragMove(e: any) {
      if (!this.data.isDragging) return
      
      const touch = e.touches[0]
      const deltaX = touch.clientX - this.data.lastX
      const deltaY = touch.clientY - this.data.lastY
      
      const newX = this.data.dragX + deltaX
      const newY = this.data.dragY + deltaY
      
      this.setData({
        dragX: newX,
        dragY: newY,
        lastX: touch.clientX,
        lastY: touch.clientY
      })
    },
    
    dragEnd() {
      this.setData({
        isDragging: false
      })
    },
    
    toggleGridSettings() {
      this.setData({
        showGridSettings: !this.data.showGridSettings
      })
    },

    hideZoomImage() {
      this.setData({
        showZoomImage: false,
        showGridSettings: false,
        zoomScale: 1,
        dragX: 0,
        dragY: 0,
        isDragging: false,
        annotations: {
          rows: [],
          columns: []
        }
      })
    },

    onZoomScaleChange(e: any) {
      const scale = e.detail.value / 100
      this.setData({
        zoomScale: scale
      })
    },
    
    preventBubble() {
      return
    },
    onGridCellTap(e: any) {
      const { row, col } = e.currentTarget.dataset
      const coordinate = `${this.data.coordinates[col]}${this.data.verticalCoordinates[row]}`
      this.setData({
        showActionPopup: true,
        currentCell: {
          row: Number(row),
          col: Number(col),
          coordinate
        }
      })
    },

    hideActionPopup() {
      this.setData({
        showActionPopup: false
      })
    },

    handleAnnotation() {
      const isSameCell = this.data.currentCell.row === this.data.annotatedCell.row && 
                        this.data.currentCell.col === this.data.annotatedCell.col;
      
      if (this.data.isAnnotated && isSameCell) {
        this.setData({
          showActionPopup: false,
          isAnnotated: false,
          annotatedCell: {
            row: -1,
            col: -1
          }
        })
      } else {
        this.setData({
          showActionPopup: false,
          isAnnotated: true,
          annotatedCell: {
            row: this.data.currentCell.row,
            col: this.data.currentCell.col
          }
        })
      }
    },

    toggleArrayItem(array: number[], item: number): number[] {
      const index = array.indexOf(item)
      if (index === -1) {
        return [...array, item]
      } else {
        return array.filter(i => i !== item)
      }
    },

    handleZoom() {
      const { row, col } = this.data.currentCell;
      
      wx.getImageInfo({
        src: this.data.croppedImage,
        success: (res) => {
          const imageWidth = res.width;
          const imageHeight = res.height;
          
          const cellWidth = imageWidth / this.data.gridSettings.columns;
          const cellHeight = imageHeight / this.data.gridSettings.rows;
          const x = col * cellWidth;
          const y = row * cellHeight;

          const canvasId = 'cropCanvas';
          const canvas = wx.createCanvasContext(canvasId, this);
          
          canvas.drawImage(this.data.croppedImage, x, y, cellWidth, cellHeight, 0, 0, cellWidth, cellHeight);
          canvas.draw(false, () => {
            wx.canvasToTempFilePath({
              canvasId: canvasId,
              x: 0,
              y: 0,
              width: cellWidth,
              height: cellHeight,
              destWidth: cellWidth * 4,
              destHeight: cellHeight * 4,
              fileType: 'png',
              quality: 1,
              success: (res) => {
                this.setData({
                  showActionPopup: false,
                  showZoomImage: true,
                  zoomedImage: res.tempFilePath,
                  zoomScale: 2,
                  'zoomGridSettings.show': true
                });
              },
              fail: (error) => {
                console.error('导出图片失败:', error);
                wx.showToast({
                  title: '处理图片失败',
                  icon: 'error'
                });
              }
            }, this);
          });
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error);
          wx.showToast({
            title: '获取图片失败',
            icon: 'error'
          });
        }
      })
    },

    onZoomGridShowChange(e: any) {
      this.setData({
        'zoomGridSettings.show': e.detail.value
      })
    },

    onZoomGridRowsChange(e: any) {
      const rows = Math.max(1, Math.min(20, parseInt(e.detail.value) || 2))
      this.setData({
        'zoomGridSettings.rows': rows
      })
    },

    onZoomGridRowsDecrease() {
      const rows = Math.max(1, this.data.zoomGridSettings.rows - 1)
      this.setData({
        'zoomGridSettings.rows': rows
      })
    },

    onZoomGridRowsIncrease() {
      const rows = Math.min(20, this.data.zoomGridSettings.rows + 1)
      this.setData({
        'zoomGridSettings.rows': rows
      })
    },

    onZoomGridColumnsChange(e: any) {
      const columns = Math.max(1, Math.min(20, parseInt(e.detail.value) || 2))
      this.setData({
        'zoomGridSettings.columns': columns
      })
    },

    onZoomGridColumnsDecrease() {
      const columns = Math.max(1, this.data.zoomGridSettings.columns - 1)
      this.setData({
        'zoomGridSettings.columns': columns
      })
    },

    onZoomGridColumnsIncrease() {
      const columns = Math.min(20, this.data.zoomGridSettings.columns + 1)
      this.setData({
        'zoomGridSettings.columns': columns
      })
    },

    onZoomGridColorConfirm(e: WechatMiniprogram.CustomEvent) {
      const { color } = e.detail;
      this.setData({
        'zoomGridSettings.color': color,
        'zoomGridSettings.opacity': 100
      });
    },

    onZoomGridOpacityChange(e: any) {
      this.setData({
        'zoomGridSettings.opacity': e.detail.value
      })
    },

    downloadZoomedImage() {
      this.setData({ showExportLoading: true })
      const scale = this.data.zoomScale
      const { coordinate } = this.data.currentCell
      
      wx.getImageInfo({
        src: this.data.zoomedImage,
        success: (res) => {
          const imageWidth = res.width * scale
          const imageHeight = res.height * scale

          const canvasId = 'cropCanvas'
          const canvas = wx.createCanvasContext(canvasId, this)
          
          this.setData({
            canvasWidth: imageWidth,
            canvasHeight: imageHeight
          })
          
          canvas.drawImage(this.data.zoomedImage, 0, 0, imageWidth, imageHeight)

          if (this.data.zoomGridSettings.show) {
            this.drawZoomGrid(canvas, imageWidth, imageHeight)
          }
          
          canvas.draw(false, () => {
            wx.canvasToTempFilePath({
              canvasId: canvasId,
              x: 0,
              y: 0,
              width: imageWidth,
              height: imageHeight,
              success: (res) => {
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    this.setData({ showExportLoading: false })
                    wx.showToast({
                      title: `已保存格子${coordinate}`,
                      icon: 'success'
                    })
                  },
                  fail: () => {
                    this.setData({ showExportLoading: false })
                    wx.showToast({
                      title: '保存失败',
                      icon: 'error'
                    })
                  }
                })
              },
              fail: (error) => {
                console.error('导出图片失败:', error)
                this.setData({ showExportLoading: false })
                wx.showToast({
                  title: '导出失败',
                  icon: 'error'
                })
              }
            }, this)
          })
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error)
          this.setData({ showExportLoading: false })
          wx.showToast({
            title: '获取图片失败',
            icon: 'error'
          })
        }
      })
    },

    drawZoomGrid(canvas: any, width: number, height: number) {
      const { rows, columns, color, opacity } = this.data.zoomGridSettings
      
      canvas.setStrokeStyle(color)
      canvas.setGlobalAlpha(opacity / 100)
      canvas.setLineWidth(1)

      for (let i = 1; i < rows; i++) {
        const y = (i * height) / rows
        canvas.beginPath()
        canvas.moveTo(0, y)
        canvas.lineTo(width, y)
        canvas.stroke()
      }

      for (let i = 1; i < columns; i++) {
        const x = (i * width) / columns
        canvas.beginPath()
        canvas.moveTo(x, 0)
        canvas.lineTo(x, height)
        canvas.stroke()
      }

      canvas.setGlobalAlpha(1)
    },

    downloadGridOnly() {
      if (!this.data.croppedImage) {
        wx.showToast({
          title: '请先上传图片',
          icon: 'none'
        })
        return
      }

      this.setData({ showExportLoading: true })
      
      wx.getImageInfo({
        src: this.data.croppedImage,
        success: (res) => {
          const imageWidth = res.width
          const imageHeight = res.height

          const canvasId = 'cropCanvas'
          const canvas = wx.createCanvasContext(canvasId, this)
          
          this.setData({
            canvasWidth: imageWidth,
            canvasHeight: imageHeight
          })
          
          canvas.clearRect(0, 0, imageWidth, imageHeight)
          
          this.drawGridAndCoordinates(canvas, imageWidth, imageHeight)
          
          canvas.draw(false, () => {
            setTimeout(() => {
              wx.canvasToTempFilePath({
                canvasId: canvasId,
                x: 0,
                y: 0,
                width: imageWidth,
                height: imageHeight,
                destWidth: imageWidth * 2,
                destHeight: imageHeight * 2,
                fileType: 'png',
                quality: 1,
                success: (res) => {
                  wx.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath,
                    success: () => {
                      this.setData({ showExportLoading: false })
                      wx.showToast({
                        title: '网格图已保存',
                        icon: 'success'
                      })
                    },
                    fail: (error) => {
                      console.error('保存图片失败:', error)
                      this.setData({ showExportLoading: false })
                      wx.showToast({
                        title: '保存失败',
                        icon: 'error'
                      })
                    }
                  })
                },
                fail: (error) => {
                  console.error('导出图片失败:', error)
                  this.setData({ showExportLoading: false })
                  wx.showToast({
                    title: '导出失败',
                    icon: 'error'
                  })
                }
              }, this)
            }, 100)
          })
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error)
          this.setData({ showExportLoading: false })
          wx.showToast({
            title: '获取图片失败',
            icon: 'error'
          })
        }
      })
    },

    downloadCombinedImage() {
      this.setData({ showExportLoading: true })
      wx.getImageInfo({
        src: this.data.croppedImage,
        success: (res) => {
          const imageWidth = res.width
          const imageHeight = res.height

          const canvasId = 'cropCanvas'
          const canvas = wx.createCanvasContext(canvasId, this)
          
          this.setData({
            canvasWidth: imageWidth,
            canvasHeight: imageHeight
          })
          
          canvas.drawImage(this.data.croppedImage, 0, 0, imageWidth, imageHeight)
          
          this.drawGridAndCoordinates(canvas, imageWidth, imageHeight)
          
          canvas.draw(false, () => {
            wx.canvasToTempFilePath({
              canvasId: canvasId,
              x: 0,
              y: 0,
              width: imageWidth,
              height: imageHeight,
              destWidth: imageWidth * 2,
              destHeight: imageHeight * 2,
              fileType: 'png',
              quality: 1,
              success: (res) => {
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    this.setData({ showExportLoading: false })
                    wx.showToast({
                      title: '图片已保存',
                      icon: 'success'
                    })
                  },
                  fail: () => {
                    this.setData({ showExportLoading: false })
                    wx.showToast({
                      title: '保存失败',
                      icon: 'error'
                    })
                  }
                })
              },
              fail: (error) => {
                console.error('导出图片失败:', error)
                this.setData({ showExportLoading: false })
                wx.showToast({
                  title: '导出失败',
                  icon: 'error'
                })
              }
            }, this)
          })
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error)
          this.setData({ showExportLoading: false })
          wx.showToast({
            title: '获取图片失败',
            icon: 'error'
          })
        }
      })
    },

    drawGridAndCoordinates(canvas: any, width: number, height: number) {
      const { rows, columns, color, opacity } = this.data.gridSettings;
      const { fontSize, color: coordColor, opacity: coordOpacity, horizontalPosition, verticalPosition } = this.data.coordinateSettings;
      
      canvas.setStrokeStyle(color);
      canvas.setGlobalAlpha(opacity / 100);
      canvas.setLineWidth(1);

      for (let i = 1; i < rows; i++) {
        const y = (i * height) / rows;
        canvas.beginPath();
        canvas.moveTo(0, y);
        canvas.lineTo(width, y);
        canvas.stroke();
      }

      for (let i = 1; i < columns; i++) {
        const x = (i * width) / columns;
        canvas.beginPath();
        canvas.moveTo(x, 0);
        canvas.lineTo(x, height);
        canvas.stroke();
      }

      canvas.setFontSize(fontSize);
      canvas.setFillStyle(coordColor);
      canvas.setGlobalAlpha(coordOpacity / 100);

      const cellWidth = width / columns;
      const cellHeight = height / rows;

      if (horizontalPosition !== 2) {
        canvas.setTextAlign('center');
        canvas.setTextBaseline(horizontalPosition === 0 ? 'top' : 'bottom');
        const y = horizontalPosition === 0 ? fontSize / 2 : height - fontSize / 2;
        
        for (let i = 0; i < columns; i++) {
          const x = (i + 0.5) * cellWidth;
          const text = String.fromCharCode(65 + (i % 26));
          canvas.fillText(text, x, y);
        }
      }

      if (verticalPosition !== 2) {
        canvas.setTextAlign(verticalPosition === 0 ? 'left' : 'right');
        canvas.setTextBaseline('middle');
        const x = verticalPosition === 0 ? fontSize / 2 : width - fontSize / 2;
        
        for (let i = 0; i < rows; i++) {
          const y = (i + 0.5) * cellHeight;
          canvas.fillText((i + 1).toString(), x, y);
        }
      }

      canvas.setGlobalAlpha(1);
    },

    onAnnotationColorConfirm(e: WechatMiniprogram.CustomEvent) {
      const { color } = e.detail;
      this.setData({
        'annotationSettings.color': color,
        'annotationSettings.opacity': 100
      });
    },

    onAnnotationOpacityChange(e: WechatMiniprogram.CustomEvent) {
      this.setData({
        'annotationSettings.opacity': e.detail.value
      });
    },

    toggleMainSettings() {
      this.setData({
        showMainSettings: !this.data.showMainSettings
      })
    },

    clearAnnotation() {
      this.setData({
        isAnnotated: false,
        annotatedCell: {
          row: -1,
          col: -1
        }
      })
    },

    switchTab(e: any) {
      const tab = e.currentTarget.dataset.tab
      this.setData({
        tab_current: tab
      })
    },

    onHorizontalPositionChange(e: any) {
      this.setData({
        'coordinateSettings.horizontalPosition': parseInt(e.detail.value)
      });
    },

    onVerticalPositionChange(e: any) {
      this.setData({
        'coordinateSettings.verticalPosition': parseInt(e.detail.value)
      });
    },

    onPaperSizeSelect(e: any) {
      const { size } = e.detail;
      this.setData({ currentPaperSize: size });
      this.calculateSizes();
    },

    calculateSizes() {
      if (!this.data.currentPaperSize || !this.data.croppedImage) return;

      wx.getImageInfo({
        src: this.data.croppedImage,
        success: (imageInfo) => {
          const paperWidth = Number(this.data.currentPaperSize.width);
          const paperHeight = Number(this.data.currentPaperSize.height);
          
          const imageWidth = Number(imageInfo.width);
          const imageHeight = Number(imageInfo.height);
          
          const imageRatio = imageWidth / imageHeight;
          const paperRatio = paperWidth / paperHeight;
          
          let finalWidth: number, finalHeight: number;
          let horizontalMargin: string, verticalMargin: string;
          
          if (imageRatio > paperRatio) {
            finalWidth = Number(paperWidth);
            finalHeight = Number(finalWidth / imageRatio);
            horizontalMargin = '0.00';
            verticalMargin = ((paperHeight - finalHeight) / 2).toFixed(2);
          } else {
            finalHeight = Number(paperHeight);
            finalWidth = Number(finalHeight * imageRatio);
            verticalMargin = '0.00';
            horizontalMargin = ((paperWidth - finalWidth) / 2).toFixed(2);
          }

          const cellWidth = (finalWidth / this.data.gridSettings.columns).toFixed(2);
          const cellHeight = (finalHeight / this.data.gridSettings.rows).toFixed(2);

          this.setData({
            calculatedResults: {
              cellWidth,
              cellHeight,
              totalWidth: finalWidth.toFixed(2),
              totalHeight: finalHeight.toFixed(2),
              horizontalMargin,
              verticalMargin
            }
          });

          console.log('尺寸计算结果:', {
            原始图片尺寸: `${imageWidth}x${imageHeight}px`,
            目标纸张尺寸: `${paperWidth}x${paperHeight}cm`,
            最终图片尺寸: `${finalWidth.toFixed(2)}x${finalHeight.toFixed(2)}cm`,
            网格尺寸: `${cellWidth}x${cellHeight}cm`,
            留白: `水平${horizontalMargin}cm, 垂直${verticalMargin}cm`
          });
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error);
          wx.showToast({
            title: '计算失败',
            icon: 'none'
          });
        }
      });
    },

    showSizeCalculator() {
      if (!this.data.croppedImage) {
        wx.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
        return;
      }
      
      this.setData({
        showSizeCalculator: true
      });
      
      if (this.data.currentPaperSize) {
        this.calculateSizes();
      }
    },

    hideSizeCalculator() {
      this.setData({
        showSizeCalculator: false
      })
    },

    onPaperSizeChange(e: any) {
      this.setData({
        selectedPaperSizeIndex: parseInt(e.detail.value)
      })
      this.calculateSizes()
    },
  }
  
})

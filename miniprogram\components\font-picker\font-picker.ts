/**
 * 字体选择器组件
 * 
 * 功能：
 * - 支持字体分类展示
 * - 实时预览字体效果
 * - 弹出层交互
 * - 字体缓存管理
 * - 支持刷新字体列表
 * - 支持主题定制
 * - 支持样式变量配置
 * - 支持自定义层级
 * 
 * 使用方法：
 * 1. 在页面的 json 配置文件中引入组件：
 * ```json
 * {
 *   "usingComponents": {
 *     "font-picker": "/components/font-picker/font-picker"
 *   }
 * }
 * ```
 * 
 * 2. 在页面的 wxml 中使用组件：
 * ```html
 * <font-picker
 *   tabBarHeight="{{tabBarHeight}}"
 *   default-font="{{selectedFont}}"
 *   primary-color="#1890ff"
 *   base-z-index="{{1000}}"
 *   popup-max-height="70vh"
 *   preview-text="{{previewText}}"
 *   custom-class="custom-font-picker"
 *   bindconfirm="onFontSelected"
 *   bindcancel="onFontPickerCancel"
 *   bindpreviewChange="onPreviewChange"
 * />
 * ```
 * 
 * 3. 在页面的 ts/js 中处理回调：
 * ```typescript
 * Component({
 *   data: {
 *     selectedFont: null,
 *     previewText: '测试文字',
 *     tabBarHeight: 0
 *   },
 * 
 *   lifetimes: {
 *     attached() {
 *       // 获取底部导航栏高度
 *       const systemInfo = wx.getSystemInfoSync();
 *       this.setData({
 *         tabBarHeight: systemInfo.screenHeight - systemInfo.safeArea.bottom
 *       });
 *     }
 *   },
 * 
 *   methods: {
 *     // 字体选择确认回调
 *     onFontSelected(e: WechatMiniprogram.CustomEvent) {
 *       const { font, fontData } = e.detail;
 *       console.log('选中的字体:', font);
 *       console.log('字体数据:', fontData);
 * 
 *       this.setData({ 
 *         selectedFont: font 
 *       });
 * 
 *       // 可以在这里处理字体数据，比如上传到服务器或保存到本地
 *       wx.setStorageSync('selected_font', {
 *         font: font,
 *         fontData: fontData
 *       });
 * 
 *       wx.showToast({
 *         title: `已选择: ${font.name}`,
 *         icon: 'success'
 *       });
 *     },
 * 
 *     // 取消选择回调
 *     onFontPickerCancel() {
 *       console.log('取消选择字体');
 *       wx.showToast({
 *         title: '已取消选择',
 *         icon: 'none'
 *       });
 *     },
 * 
 *     // 预览文字变更回调
 *     onPreviewChange(e: WechatMiniprogram.CustomEvent) {
 *       const { text, font } = e.detail;
 *       console.log('预览文字:', text);
 *       console.log('当前字体:', font);
 * 
 *       this.setData({
 *         previewText: text
 *       });
 *     }
 *   }
 * });
 * ```
 * 
 * 4. 在页面中设置主题变量（可选）：
 * ```css
 * .custom-font-picker {
 *   --primary-color: #ff4400;
 *   --text-color: #222;
 *   --font-size-large: 40rpx;
 *   --spacing-medium: 24rpx;
 * }
 * ```
 * 
 * 组件属性说明：
 * @property {string} previewText - 预览文字，默认为"创意"
 * @property {object} defaultFont - 默认选中的字体对象
 * @property {number} tabBarHeight - 底部导航栏高度，用于调整弹出层位置
 * @property {string} primaryColor - 主题色，默认为 #1890ff
 * @property {string} primaryLightColor - 主题色浅色版
 * @property {string} primaryDarkColor - 主题色深色版
 * @property {string} textColor - 主要文字颜色
 * @property {string} textColorSecondary - 次要文字颜色
 * @property {string} textColorPlaceholder - 占位符文字颜色
 * @property {string} borderColor - 边框颜色
 * @property {string} backgroundColor - 背景色
 * @property {string} backgroundColorSecondary - 背景色次要
 * @property {string} maskBackground - 遮罩层背景色
 * @property {number} baseZIndex - 弹出层z-index基础值，默认为 1000
 * @property {string} popupMaxHeight - 弹出层最大高度，默认为 70vh
 * @property {object} fontSizes - 字体大小配置对象
 * @property {string} fontSizes.large - 大号字体大小，默认 36rpx
 * @property {string} fontSizes.medium - 中号字体大小，默认 28rpx
 * @property {string} fontSizes.small - 小号字体大小，默认 24rpx
 * @property {string} fontSizes.mini - 迷你号字体大小，默认 22rpx
 * @property {object} spacing - 间距配置对象
 * @property {string} spacing.large - 大间距，默认 30rpx
 * @property {string} spacing.medium - 中等间距，默认 20rpx
 * @property {string} spacing.small - 小间距，默认 16rpx
 * @property {string} spacing.mini - 迷你间距，默认 8rpx
 * @property {string} customClass - 自定义样式类
 * 
 * CSS变量说明：
 * --primary-color: 主题色
 * --primary-color-light: 主题色浅色版
 * --text-color: 主要文字颜色
 * --text-color-secondary: 次要文字颜色
 * --text-color-placeholder: 占位符文字颜色
 * --border-color: 边框颜色
 * --background-color: 背景色
 * --mask-background: 遮罩层背景色
 * --font-size-large: 大号字体大小
 * --font-size-medium: 中号字体大小
 * --font-size-small: 小号字体大小
 * --font-size-mini: 迷你号字体大小
 * --spacing-large: 大间距
 * --spacing-medium: 中等间距
 * --spacing-small: 小间距
 * --spacing-mini: 迷你间距
 * --border-radius-large: 大圆角
 * --border-radius-medium: 中等圆角
 * --border-radius-small: 小圆角
 * 
 * 组件事件说明：
 * @event confirm - 字体选择确认事件
 * @param {object} font - 选中的字体信息
 * @param {string} fontData - 字体的 base64 数据
 * 
 * @event cancel - 取消选择事件
 * 
 * @event previewChange - 预览文字变更事件
 * @param {string} text - 变更后的预览文字
 * @param {object} font - 当前选中的字体
 * 
 * 字体对象结构：
 * ```typescript
 * interface FontItem {
 *   name: string;      // 字体名称
 *   value: string;     // 字体值（用于font-family）
 *   format: string;    // 字体格式（如ttf、woff等）
 *   path: string;      // 字体文件路径
 * }
 * ```
 * 
 * 返回数据结构：
 * ```typescript
 * interface FontPickerResult {
 *   font: FontItem;    // 选中的字体信息
 *   fontData: string;  // 字体的base64数据
 * }
 * ```
 */

import Api from '../../utils/api';
import eventBus from '../../utils/eventBus';// 底部自定义导航栏收缩状态
import { layoutUtil} from '../../utils/layout';

type FontItem = {
  id: number;
  name: string;
  path: string;
  tags: string;
  weigh: number;
}

type FontCategory = {
  id: number;
  type: string;
  name: string;
  icon: string;
  description: string;
  weigh: number;
  status: string;
  fonts: FontItem[];
}

type AppConfig = {
  api: {
    font: {
      baseUrl: string;
      preview: string;
      fontsListUrl: string;
      timeout: number;
      retry: number;
    }
  }
}

type FontResponse = {
  categories: FontCategory[];
  settings?: {
    defaultCategory?: string;
  }
}

Component({
  properties: {
    // 预览文字
    previewText: {
      type: String,
      value: '创意'
    },
    // 默认选中的字体
    defaultFont: {
      type: Object,
      value: null
    },
    // 底部导航栏高度（包含安全区域和横线位置）
    // 如果不传值，会自动计算安全区域和导航栏基础高度
    tabBarHeight: {
      type: Number,
      value: 0
    },
    // 主题色
    primaryColor: {
      type: String,
      value: '#1890ff'
    },
    // 主题色-浅色
    primaryLightColor: {
      type: String,
      value: '#e6f7ff'
    },
    // 主题色-深色
    primaryDarkColor: {
      type: String,
      value: '#0050c9'
    },
    // 文字颜色-主要
    textColor: {
      type: String,
      value: '#333333'
    },
    // 文字颜色-次要
    textColorSecondary: {
      type: String,
      value: '#666666'
    },
    // 文字颜色-占位符
    textColorPlaceholder: {
      type: String,
      value: '#999999'
    },
    // 边框颜色
    borderColor: {
      type: String,
      value: '#f0f0f0'
    },
    // 背景色-主要
    backgroundColor: {
      type: String,
      value: '#ffffff'
    },
    // 背景色-次要
    backgroundColorSecondary: {
      type: String,
      value: '#f5f5f5'
    },
    // 遮罩层背景色
    maskBackground: {
      type: String,
      value: 'rgba(0, 0, 0, 0.5)'
    },
    // 弹出层z-index基础值
    baseZIndex: {
      type: Number,
      value: 1000
    },
    // 弹出层最大高度
    popupMaxHeight: {
      type: String,
      value: '70vh'
    },
    // 字体大小配置
    fontSizes: {
      type: Object,
      value: {
        large: '36rpx',    // 大号字体
        medium: '28rpx',   // 中号字体
        small: '24rpx',    // 小号字体
        mini: '22rpx'      // 迷你号字体
      }
    },
    // 间距配置
    spacing: {
      type: Object,
      value: {
        large: '30rpx',    // 大间距
        medium: '20rpx',   // 中等间距
        small: '16rpx',    // 小间距
        mini: '8rpx'       // 迷你间距
      }
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed:true,// 底部自定义导航栏收缩状态
    visible: false,
    currentCategory: '',
    selectedFont: null,
    tempSelectedFont: null,
    isLoading: false,
    fontCategories: [] as FontCategory[],
    config: null,
    retryCount: 0,
    cacheKey: 'font_picker_cache',
    previewCacheKey: 'font_preview_cache',
    safeAreaBottom: 0,
    customTabBarHeight: 0,  // 实际计算后的底部高度
    // 计算后的z-index值
    maskZIndex: 1000,
    contentZIndex: 1001,
    loadingZIndex: 100,
    styleVars: {}
  },

  lifetimes: {
    attached() {
      console.log('字体选择器组件初始化');
      this.init();

      // 注册事件监听 底部自定义导航栏收缩状态
      this.handleTabBarChange = this.handleTabBarChange.bind(this);
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange);
      
      // 初始化时获取当前状态并更新布局 底部自定义导航栏收缩状态
      const isCollapsed = wx.getStorageSync('tab-bar-show');
      this.setData({ isTabBarCollapsed: isCollapsed });
    }
  },

  methods: {
    async init() {
      try {
        console.log('初始化字体选择器...');
        await this.loadFontsList();
        
        // 设置默认字体
        if (this.properties.defaultFont) {
          this.setData({
            selectedFont: this.properties.defaultFont,
            tempSelectedFont: this.properties.defaultFont
          });
          console.log('设置默认字体:', this.properties.defaultFont);
        }
      } catch (error) {
        console.error('初始化失败:', error);
        wx.showToast({
          title: '加载字体失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    async loadFontsList() {
      try {
        console.log('正在加载字体列表...');
        this.setData({ isLoading: true });
        
        const response = await Api.font.getCategories();
        console.log('API响应:', response);

        if (Array.isArray(response)) {
          this.setData({
            fontCategories: response,
            isLoading: false
          });
          console.log('字体加载成功:', this.data.fontCategories);

          // 设置默认分类
          if (response.length > 0) {
            this.setData({
              currentCategory: response[0].type
            });
            console.log('设置默认分类:', response[0].type);
          }
        } else {
          console.error('无效的API响应格式，期望数组但收到:', typeof response);
          throw new Error('无效的数据格式');
        }
      } catch (error) {
        console.error('加载字体列表失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载字体失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 获取预览图像缓存
    async getPreviewCache(fontValue: string): Promise<string | null> {
      try {
        const cacheKey = `${this.data.previewCacheKey}_${fontValue}`;
        const cache = wx.getStorageSync(cacheKey);
        if (cache) {
          const { data, timestamp } = cache;
          const now = Date.now();
          const expires = 7 * 24 * 60 * 60; // 缓存7天

          if (now - timestamp < expires * 1000) {
            return data;
          }
        }
        return null;
      } catch (error) {
        console.error('获取预览缓存失败:', error);
        return null;
      }
    },

    // 设置预览图像缓存
    setPreviewCache(fontValue: string, base64Data: string) {
      try {
        const cacheKey = `${this.data.previewCacheKey}_${fontValue}`;
        wx.setStorageSync(cacheKey, {
          data: base64Data,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('设置预览缓存失败:', error);
      }
    },

    // 预加载所有字体
    async preloadFonts(categories: FontCategory[]) {
      this.setData({ isLoading: true });
      try {
        const { config } = this.data;
        if (!config) throw new Error('配置未加载');

        const hexText = this.textToHex('创意'); // 使用固定的预览文字
        
        // 限制并发加载数量
        const concurrentLimit = 3;
        const fonts = categories.flatMap(category => category.fonts);
        const chunks = [];
        
        // 将字体分组，每组concurrentLimit个
        for (let i = 0; i < fonts.length; i += concurrentLimit) {
          chunks.push(fonts.slice(i, i + concurrentLimit));
        }

        // 按组加载字体
        for (const chunk of chunks) {
          const promises = chunk.map(async font => {
            try {
              // 先检查缓存
              const cachedPreview = await this.getPreviewCache(font.value);
              if (cachedPreview) {
                // 使用缓存的预览图像
                await this.loadSingleFont(font.value, cachedPreview);
                return;
              }

              // 如果没有缓存，则重新生成
              const fontUrl = `${config.api && config.api.font && config.api.font.baseUrl}${config.api && config.api.font && config.api.font.preview}?format=${font && font.format || ''}&hex=1&font_file=${font && font.path || ''}&t=ttf&words=${hexText}`;
              const fontData = await this.getFontData(fontUrl);
              if (fontData) {
                // 保存到缓存
                this.setPreviewCache(font.value, fontData);
                // 加载字体
                await this.loadSingleFont(font.value, fontData);
              }
            } catch (error) {
              console.error(`预加载字体失败: ${font.name}`, error);
            }
          });

          await Promise.all(promises);
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error('预加载字体失败:', error);
      } finally {
        this.setData({ isLoading: false });
      }
    },

    // 获取字体数据
    async getFontData(url: string): Promise<string | null> {
      try {
        const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
          wx.request({
            url,
            method: 'GET',
            responseType: 'arraybuffer',
            header: {
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Origin': 'https://font.chinaz.com',
              'Referer': 'https://font.chinaz.com/'
            },
            success: resolve,
            fail: reject
          });
        });

        if (res.statusCode === 200 && res.data) {
          const buffer = res.data as ArrayBuffer;
          const base64 = wx.arrayBufferToBase64(buffer);
          return base64;
        }
        return null;
      } catch (error) {
        console.error('获取字体数据失败:', error);
        return null;
      }
    },

    // 加载单个字体
    async loadSingleFont(fontValue: string, base64Data: string): Promise<void> {
      return new Promise((resolve, reject) => {
        wx.loadFontFace({
          family: `${fontValue}Font`,
          source: `data:font/ttf;base64,${base64Data}`,
          success: () => {
            resolve();
          },
          fail: (err) => {
            console.error(`字体加载失败: ${fontValue}`, err);
            reject(err);
          },
          complete: () => {
            // 强制更新视图
            this.setData({
              _forceUpdate: Date.now()
            });
          }
        });
      });
    },

    // 错误处理
    handleError(message: string) {
      const maxRetries = 3;
      const retryDelay = 1000;

      if (this.data.retryCount < maxRetries) {
        setTimeout(() => {
          this.setData({ retryCount: this.data.retryCount + 1 });
          this.loadFontsList();
        }, retryDelay);
      } else {
        wx.showToast({
          title: message,
          icon: 'none'
        });
      }
    },

    // 显示选择器
    showPicker() {
      console.log('显示字体选择器');
      this.setData({
        visible: true,
        tempSelectedFont: this.data.selectedFont
      });
    },

    // 隐藏选择器
    hidePicker() {
      console.log('隐藏字体选择器');
      this.setData({
        visible: false,
        tempSelectedFont: this.data.selectedFont
      });
    },

    // 点击遮罩层
    onMaskClick() {
      this.hidePicker();
    },

    // 切换分类
    onCategoryChange(e: WechatMiniprogram.TouchEvent) {
      const category = e.currentTarget.dataset.category;
      console.log('切换分类:', category);
      this.setData({
        currentCategory: category
      });
    },

    // 处理预览文字输入
    async onPreviewInput(e: WechatMiniprogram.Input) {
      const value = e.detail.value;
      this.setData({ previewText: value });
      
      // 如果已经选择了字体，需要重新加载字体以包含新输入的文字
      if (this.data.selectedFont) {
        this.setData({ isLoading: true });
        try {
          const { config } = this.data;
          if (!config) throw new Error('配置未加载');

          const font = this.data.selectedFont;
          const hexText = this.textToHex(value);
          const fontUrl = `${config.api && config.api.font && config.api.font.baseUrl}${config.api && config.api.font && config.api.font.preview}?format=${font && font.format || ''}&hex=1&font_file=${font && font.path || ''}&t=ttf&words=${hexText}`;
          
          const fontData = await this.getFontData(fontUrl);
          if (fontData && font) {
            await this.loadSingleFont(font.value, fontData);
          }
        } catch (error) {
          console.error('更新预览文字失败:', error);
        } finally {
          this.setData({ isLoading: false });
        }
      }
      
      // 触发预览文字变更事件
      const selectedFont = this.data.selectedFont;
      const path = selectedFont && selectedFont.path || '';
      console.log('预览文字变更:', path);
      
      this.triggerEvent('previewChange', {
        text: value,
        font: this.data.selectedFont
      });
    },

    // 选择字体
    async onSelectFont(e: WechatMiniprogram.TouchEvent) {
      const font = e.currentTarget.dataset.font;
      console.log('选择字体:', font);
      
      try {
        // 更新选中状态
        this.setData({
          selectedFont: font
        });

        // 触发选择事件
        this.triggerEvent('confirm', {
          font: font
        });

        // 选择完成后自动关闭弹窗
        this.hidePicker();

        wx.showToast({
          title: '已选择字体',
          icon: 'success',
          duration: 1500
        });
      } catch (error) {
        console.error('选择字体失败:', error);
        wx.showToast({
          title: '选择失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 将文字转换为hex格式
    textToHex(text: string) {
      return text.split('').map(char => char.charCodeAt(0).toString(16)).join(',');
    },

    // 取消选择
    onCancel() {
      this.setData({
        tempSelectedFont: this.data.selectedFont // 恢复之前的选择
      });
      this.hidePicker();
      this.triggerEvent('cancel');
    },

    // 确认选择
    onConfirm() {
      if (this.data.tempSelectedFont) {
        this.setData({
          selectedFont: this.data.tempSelectedFont
        });
        this.hidePicker();
        this.triggerEvent('confirm', {
          font: this.data.selectedFont
        });
      }
    },

    // 刷新字体列表
    async refreshFontsList() {
      console.log('刷新字体列表...');
      await this.loadFontsList();
    },
    //  底部自定义导航栏收缩状态
    handleTabBarChange: function(data: { isCollapsed: boolean }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed
      });
    },
  }
}); 
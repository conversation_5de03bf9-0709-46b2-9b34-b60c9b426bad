/**index.wxss**/

/* 浮动按钮样式 */
.float-btn {
  margin: 20rpx;
  background-color: #1989fa;
  color: #fff;
  border-radius: 10rpx;
}

/* 下载按钮自定义样式 */
.download-btn {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: #333;
}

.download-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

/* 当遮罩显示时禁止页面滚动 */
/* 内容区域浮动层样式 */
.content-float-layer {
  position:absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;  /* 默认不接收事件 */
}

.content-float-layer.show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;  /* 显示时接收事件 */
}

.float-layer-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.float-btn-wrapper {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 1001;  /* 确保按钮在遮罩层之上 */
}

/* 导航高度信息样式 */
.nav-info {
  margin: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  font-size: 28rpx;
  color: #333;
  line-height: 2;
  padding: 0 20rpx;
  font-weight: bold;
}

.info-sub-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  padding: 0 40rpx;
}

.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.page {
  padding-bottom: 40rpx;
}

.weui-panel {
  background-color: rgba(255, 255, 255, 0.9);
  margin: 20rpx;
  border-radius: 12rpx;
}

.weui-cell__title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.weui-cell__desc {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 半屏弹窗动画 */
.weui-mask {
  transition: opacity 0.3s;
  opacity: 0;
}

.weui-mask_show {
  opacity: 1;
}

.weui-half-screen-dialog {
  transition: transform 0.3s;
  transform: translateY(100%);
}

.weui-half-screen-dialog_show {
  transform: translateY(0);
}

/* 主题色覆盖 */
.weui-btn_primary {
  background-color: #07c160;
}

.weui-btn_primary:not(.weui-btn_disabled):active {
  background-color: #06ad56;
}

.button-sp-area {
  padding: 15px;
  background-color: #fff;
}

.weui-btn {
  margin: 10px;
}

.weui-cells {
  margin: 20rpx 0;
}

.searchbar-result {
  margin-top: 0;
  font-size: 14px;
}

/* 容器样式 */
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 内容区域样式 */


.content-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

/* 浮层样式 */
.float-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.float-layer.show {
  opacity: 1;
  visibility: visible;
}

.float-content {
  width: 80%;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.float-header {
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.float-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.float-body {
  padding: 24rpx;
}

/* 底部导航样式 */
.nav-footer {
  width: 100%;
  background: linear-gradient(90deg, 
    rgba(46, 93, 248, 0.98) 0%,
    rgba(75, 116, 247, 0.98) 25%,
    rgba(46, 93, 248, 0.98) 50%,
    rgba(75, 116, 247, 0.98) 75%,
    rgba(46, 93, 248, 0.98) 100%);
  background-size: 300% 300%;
  box-shadow: 0 -4rpx 30rpx rgba(46, 93, 248, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-shrink: 0;
  padding: 12rpx 0;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  position: relative;
  overflow: hidden;
  border-radius: 32rpx 32rpx 0 0;
  z-index: 100;
}

/* 添加底部导航的光效果 */
.nav-footer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: lightShine 6s ease-in-out infinite;
  pointer-events: none;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  position: relative;
  transition: all 0.3s ease;
}

/* 底部指示条样式 */
.tab-item::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 28rpx;
  height: 3rpx;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 4rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active::after {
  opacity: 1;
}

/* 图标容器 */
.tab-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon {
  transform: scale(1.1);
}

.tab-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.75);
  margin-top: 6rpx;
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: #ffffff;
  font-weight: 500;
}

.tab-item:active {
  transform: scale(0.92);
  opacity: 0.8;
}

/* 添加涟漪效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.tab-item::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.tab-item:active::before {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 内容区域弹窗相关样式 */
.content-scroll.no-scroll {
  overflow: hidden;
}

.dialog-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.dialog-container.show {
  pointer-events: auto;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dialog-container.show .dialog-mask {
  opacity: 1;
}

/* 对话框样式 */
.dialog-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  visibility: hidden;
  width: 85%;
  max-width: 600rpx;
}

.dialog-wrapper.show {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
}

.custom-dialog {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.dialog-header {
  padding: 24rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.dialog-content {
  padding: 32rpx 24rpx;
  min-height: 80rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.dialog-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.dialog-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  text-align: center;
  color: #666;
  background: #fff;
  border: none;
  border-radius: 0;
}

.dialog-btn::after {
  display: none;
}

.dialog-btn.primary {
  color: #07c160;
  font-weight: 500;
}

/* 半屏弹窗样式 */
.half-screen-wrapper {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  max-height: 90%; /* 最大高度为内容区域的90% */
  transform: translateY(100%);
  transition: transform 0.3s ease;
  visibility: hidden;
}

.half-screen-wrapper.show {
  transform: translateY(0);
  visibility: visible;
}

.custom-half-screen {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  max-height: calc(90vh - var(--top-nav-height) - var(--bottom-nav-height)); /* 限制最大高度 */
  display: flex;
  flex-direction: column;
}

.half-screen-header {
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.header-main {
  flex: 1;
  padding-right: 24rpx;
}

.half-screen-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.half-screen-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.close-btn {
  padding: 12rpx;
}

.half-screen-content {
  padding: 32rpx 24rpx;
  overflow-y: auto; /* 内容过多时可滚动 */
  -webkit-overflow-scrolling: touch;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.tips-text {
  font-size: 26rpx;
  color: #999;
  margin-top: 16rpx;
  display: block;
}

.half-screen-footer {
  padding: 24rpx;
  display: flex;
  gap: 20rpx;
}

.half-screen-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  color: #333;
  border: none;
}

.half-screen-btn.primary {
  background: #07c160;
  color: #fff;
}

/* 操作菜单样式 */
.action-sheet-wrapper {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  visibility: hidden;
}

.action-sheet-wrapper.show {
  transform: translateY(0);
  visibility: visible;
}

.custom-action-sheet {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.action-sheet-header {
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
  border-bottom: 1rpx solid #eee;
}

.action-sheet-content {
  max-height: 60vh;
  overflow-y: auto;
}

.action-item {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.action-item .warn {
  color: #fa5151;
}

.action-sheet-cancel {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  background: #f7f7f7;
}

/* 自定义顶部提示样式 */
.custom-toptips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  background: #07c160;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  z-index: 1001;
}

.custom-toptips.show {
  transform: translateY(0);
}

.toptips-text {
  color: #fff;
  font-size: 28rpx;
  margin-left: 8rpx;
}

.cropper {
  width: 100%;
  height: 100%;
}

.cropper-buttons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  padding: 30rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}

.cropper-buttons .weui-btn {
  flex: 1;
  margin: 0;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cropper-buttons .weui-btn::after {
  display: none;
}

.cropper-buttons .weui-btn[type="default"] {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.cropper-buttons .weui-btn[type="default"]:active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(2rpx);
}

.cropper-buttons .weui-btn[type="primary"] {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  color: #fff;
  box-shadow: 0 8rpx 20rpx rgba(0, 134, 255, 0.3);
}

.cropper-buttons .weui-btn[type="primary"]:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(0, 134, 255, 0.2);
}

.mini-btn {
  margin-right: 5px;
}

.crop-result {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  background: #fff;
}

.result-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 8rpx;
}

/* 上传组件样式优化 */
.weui-uploader__input-box {
  margin-bottom: 0;
  width: 200rpx;
  height: 200rpx;
}

.weui-uploader__file {
  width: 200rpx;
  height: 200rpx;
}

.weui-uploader__img {
  width: 100%;
  height: 100%;
}

/* 裁剪相关样式 */
.cropper-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 100;
  display: none;
}

.cropper-mask.show {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.cropper-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  box-sizing: border-box;
}

.cropper-title {
  padding: 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 101;
}

.cropper-title paper-size-picker {
  width: auto;
  min-width: 300rpx;
  display: inline-block;
}

.size-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.size-picker .arrow {
  font-size: 24rpx;
  color: #fff;
  margin-left: 8rpx;
}

.cropper-area {
  width: 100%;
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.crop-image {
  position: absolute;
  transform: none;
  z-index: 1;
}

.crop-frame {
  position: absolute;
  border: 2rpx solid #fff;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  cursor: move;
  touch-action: none;
  box-sizing: border-box;
  z-index: 10;
}

.cropper-canvas {
  position: absolute;
  left: -9999px;
  visibility: hidden;
}

/* 四个角的样式 */
.corner {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  border-color: #fff;
  border-style: solid;
  z-index: 11;
}

/* 添加缩放控制点样式 */
.scale-control {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  right: -20rpx;
  bottom: -20rpx;
  background-color: #0086FF;
  border-radius: 8rpx;
  z-index: 12;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.top-left {
  top: -4rpx;
  left: -4rpx;
  border-width: 4rpx 0 0 4rpx;
}

.top-right {
  top: -4rpx;
  right: -4rpx;
  border-width: 4rpx 4rpx 0 0;
}

.bottom-left {
  bottom: -4rpx;
  left: -4rpx;
  border-width: 0 0 4rpx 4rpx;
}

.bottom-right {
  bottom: -4rpx;
  right: -4rpx;
  border-width: 0 4rpx 4rpx 0;
}

/* 上传和裁剪相关样式 */
.upload-section {
  margin: 20rpx;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.upload-btn {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  color: #fff;
  padding: 24rpx 48rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(0, 195, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.upload-btn::before {
  content: "📷";
  font-size: 36rpx;
  margin-right: 8rpx;
}

.preview-section {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  margin: 20rpx 20rpx;
}

.preview-container {
  position: relative;
  width: 100%;
}

.preview-image {
  width: 100%;
  display: block;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

/* 功能按钮组样式 */
.config-button-group {
  padding: 20rpx;
}

.button-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.feature-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.feature-btn.primary {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
}

.feature-btn.primary .feature-text {
  color: #fff;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #333;
}

.scale-picker {
  padding: 20rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scale-picker .arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.dialog-content picker {
  width: 100%;
}

/* 工具说明样式 */
.tool-intro {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 134, 255, 0.2);
}

.intro-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-content {
  margin-bottom: 40rpx;
}

.feature-section {
  margin-bottom: 36rpx;
}

.feature-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: flex-start;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-icon .text {
  color: #fff;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.usage-section {
  margin-top: 40rpx;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-section {
  margin-top: 40rpx;
}

.tip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
}

.tip-icon {
  color: #0086FF;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.feature-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.feature-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.feature-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.4s both; }

.step-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.step-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.step-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.step-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.tip-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.6s both; }

/* 确保弹出层能显示在最上层 */
:root {
  --popup-z-index: 1000;
}

.paper-size-picker-popup {
  z-index: var(--popup-z-index) !important;
}

/* 工具介绍 */
.tool-intro {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.features {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.feature-item {
  background: #f0f9ff;
  color: #0066cc;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
}

.usage, .tips {
  margin-top: 20px;
}

.usage-title, .tips-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.usage-step, .tip-item {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 8px;
}

/* 上传区域 */
.upload-section {
  padding: 32px 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-btn {
  width: 200px;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background: #f0f0f0;
  border-color: #ccc;
}

.upload-icon {
  font-size: 48px;
  color: #999;
  margin-bottom: 8px;
}

.upload-text {
  color: #666;
  font-size: 16px;
}

/* 预览区域 */
.preview-section {
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.upload-new {
  background: #f0f0f0;
  color: #666;
}

.action-btn.download {
  background: #0066cc;
  color: #fff;
}

/* 放大倍数选择弹窗 */
.scale-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.scale-dialog.show {
  opacity: 1;
  visibility: visible;
}

.scale-content {
  width: 80%;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
}

.scale-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.scale-options {
  margin-bottom: 20px;
}

.scale-option {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.scale-option:last-child {
  border-bottom: none;
}

.scale-option radio {
  margin-right: 8px;
}

.scale-buttons {
  display: flex;
  gap: 12px;
}

.scale-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
}

.scale-btn.cancel {
  background: #f0f0f0;
  color: #666;
}

.scale-btn.confirm {
  background: #0066cc;
  color: #fff;
}

/* 顶部提示 */
.top-tips {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  padding: 8px 16px;
  border-radius: 0 0 8px 8px;
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
  z-index: 1000;
}

.top-tips.show {
  transform: translateX(-50%) translateY(0);
}

.top-tips.success {
  background: #07c160;
}

.top-tips.error {
  background: #fa5151;
}

.top-tips.info {
  background: #1485ee;
}

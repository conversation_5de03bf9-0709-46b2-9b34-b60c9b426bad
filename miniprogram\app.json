{"pages": ["pages/ai/texttoimage/texttoimage", "pages/about/ai/mygenerateImage/mygenerateImage", "pages/index/index", "pages/about/about/index", "pages/about/artistProfile/index", "pages/about/studio/mygallery/index", "pages/about/studio/series/index", "pages/about/studio/mygallery/detail/index", "pages/redirect/index", "pages/series/gallery/index", "pages/apipage/chinacolor/chinacolor", "pages/apipage/randomsearch/randomsearch", "pages/apipage/facialandbody/facialandbody", "pages/ai/artworkanalysis/artworkanalysis", "pages/tool/grid/grid", "pages/series/recruitment/index", "pages/series/recruitment_detail/index", "pages/login/login", "pages/tool/art_atlas/art_atlas", "pages/tool/user/user", "pages/tool/qrcode/qrcode", "pages/tool/font_generation/font_generation", "pages/tool/img_cropper/img_cropper", "pages/tool/com_page/com_page", "pages/tool/img_help/img_help", "pages/uchart-demo/index", "pages/tool/test/test", "pages/test/test"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "创意工具库", "navigationBarTextStyle": "black", "navigationStyle": "custom"}, "useExtendedLib": {"weui": true}, "usingComponents": {"nav-bar": "/components/nav-bar/nav-bar", "tab-bar": "/components/tab-bar/tab-bar"}, "style": "v2", "componentFramework": "glass-easel", "lazyCodeLoading": "requiredComponents", "sitemapLocation": "sitemap.json", "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation"]}
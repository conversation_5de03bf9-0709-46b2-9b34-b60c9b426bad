.font-generator {
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.input-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.text-input {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.font-selector {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.font-list {
  white-space: nowrap;
}

.font-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
  min-width: 120rpx;
}

.font-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.font-item .text {
  font-size: 32rpx;
}

.font-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


.preview-container {
  margin-top: 20rpx;
  /* background-color: #f5f5f5; */
  border-radius: 12rpx;
}

.preview-text {
  width: 100%;
  min-height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #fff; */
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
  border: 1px solid rgb(255, 160, 52);
}

.preview-text .text {
  text-align: center;
  /* line-height: 1.5; */
  
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  color: #666;
  font-size: 28rpx;
}

.progress-bar {
  width: 80%;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  margin: 0 auto 20rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.loading-text {
  font-size: 28rpx;
  color: #1890ff;
}

.test-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16rpx 32rpx;
  padding: 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.3s;
  cursor: pointer;
}

.test-button:active {
  background-color: #e0e0e0;
}

.test-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.test-button .text {
  color: #333;
  font-size: 28rpx;
}

.action-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.save-button {
  background: #1890ff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0;
}

.save-button:active {
  opacity: 0.8;
}

.preview-button {
  margin-top: 20rpx;
  background: #1890ff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  width: 100%;
}

.preview-button:active {
  opacity: 0.8;
}

.settings-panel {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.settings-section {
  margin-bottom: 30rpx;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}

.setting-item .text {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.setting-item slider {
  flex: 1;
}


.setting-item radio-group {
  flex: 1;
  display: flex;
  gap: 30rpx;
}

.setting-item radio {
  font-size: 28rpx;
  color: #333;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10rpx;
  padding: 10rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.color-block {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s;
  position: relative;
}

.color-block.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30rpx;
  height: 30rpx;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23fff"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>') no-repeat center;
  background-size: contain;
}

/* 白色背景下的对钩样式 */
.color-block[data-color="#FFFFFF"].active::after {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>') no-repeat center;
  background-size: contain;
}

.color-block.transparent {
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
    linear-gradient(-45deg, #ccc 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ccc 75%),
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 10rpx 10rpx;
  background-position: 0 0, 0 5rpx, 5rpx -5rpx, -5rpx 0rpx;
  border: 2rpx solid #e8e8e8;
}

.color-block.transparent.active::after {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>') no-repeat center;
}

.font-size-control {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.size-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
  transition: all 0.3s;
}

.size-btn:active {
  background: #e0e0e0;
}

.font-size-control slider {
  flex: 1;
}

/* 工具说明样式 */
.tool-intro {
  margin: 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 134, 255, 0.2);
}

.intro-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-content {
  margin-bottom: 40rpx;
}

.feature-section {
  margin-bottom: 36rpx;
}

.feature-section:last-child {
  margin-bottom: 0;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: flex-start;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-icon .text {
  color: #fff;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.usage-section {
  margin-top: 40rpx;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-section {
  margin-top: 40rpx;
}

.tip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
}

.tip-icon {
  color: #0086FF;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.feature-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.feature-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }

.step-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.step-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.step-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.step-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.action-buttons {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.2);
  transition: all 0.3s ease;
}

.export-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(46, 125, 50, 0.1);
}

.export-button image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  filter: brightness(0) invert(1);
}

.content-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.content-scroll {
  height: 100%;
  width: 100%;
}

.category-tabs {
  display: flex;
  margin-bottom: 20rpx;
  background: #f5f5f5;
  padding: 8rpx;
  border-radius: 12rpx;
  gap: 12rpx;
}

.category-tab {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.category-tab.active {
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);
}

.font-list {
  margin-top: 20rpx;
  padding: 8rpx;
}

.refresh-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  background: #e0e0e0;
  transform: rotate(180deg);
}

.refresh-btn image {
  width: 24rpx;
  height: 24rpx;
}

.button-group {
  margin: 30rpx 0;
  padding: 0 20rpx;
}

.export-btn {
  position: relative;
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
  overflow: hidden;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(24, 144, 255, 0.15);
}

.export-btn::after {
  display: none;
}

.export-btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1890ff, #0050c9);
  transition: all 0.3s ease;
  z-index: 1;
}

.export-btn .text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  margin-left: 12rpx;
  position: relative;
  z-index: 2;
}

.export-btn .export-icon {
  width: 36rpx;
  height: 36rpx;
  position: relative;
  z-index: 2;
  filter: brightness(0) invert(1);
}

.export-btn.loading .export-btn-bg {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
}

.export-btn.loading {
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.export-btn:active .export-btn-bg {
  transform: scale(0.98);
  background: linear-gradient(135deg, #096dd9, #0050c9);
}

.preview-char {
  width: 80rpx;
  height: 40rpx;
  display: block;
  margin: 0 auto 10rpx;
  object-fit: contain;
}
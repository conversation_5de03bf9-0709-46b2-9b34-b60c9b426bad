<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="单图裁剪" showBack="{{true}}" showMore="">顶部导航</nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 中间内容区域 -->
      <view class="content-inner">
        <!-- 图片上传区域 -->
        <view class="upload-section" wx:if="{{!showCropper && !croppedImage}}">
          <button class="upload-btn" bindtap="handleUpload">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传图片</text>
          </button>
        </view>

        <!-- 裁剪结果预览 -->
        <view class="preview-section" wx:if="{{croppedImage}}">
          <image class="preview-image" src="{{croppedImage}}" mode="aspectFit"></image>
          <view class="action-buttons">
            <button class="action-btn upload-new" bindtap="handleUpload">重新上传</button>
            <button class="action-btn download" bindtap="handleDownload">下载结果</button>
          </view>
        </view>

        <!-- 说明开始 -->
        <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}cut.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">图像裁剪</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <view class="section-title">功能特点</view>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">1</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">自由裁剪</text>
                    <text class="feature-desc">支持任意比例的自由裁剪，满足各种尺寸需求</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">2</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">固定比例</text>
                    <text class="feature-desc">提供常用纸张尺寸的固定比例裁剪选项</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">3</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">高清导出</text>
                    <text class="feature-desc">支持高质量图片导出，保持原图清晰度</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <view class="section-title">使用步骤</view>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">选择图片</text>
                    <text class="step-desc">点击"选择图片"按钮，从相册选择或拍摄新照片</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">调整裁剪框</text>
                    <text class="step-desc">拖动裁剪框边角调整大小，拖动中心区域移动位置</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">选择尺寸</text>
                    <text class="step-desc">点击顶部尺寸选择器，选择需要的纸张尺寸比例</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">确认裁剪</text>
                    <text class="step-desc">调整完成后点击"完成"按钮，即可获得裁剪后的图片</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <view class="section-title">使用提示</view>
              <view class="tip-list">
                <view class="tip-item">
                  <text class="tip-icon">💡</text>
                  <text class="tip-title">图片质量</text>
                  <text class="tip-text">建议使用清晰度较高的原图进行裁剪，以获得最佳效果</text>
                </view>
                <view class="tip-item">
                  <text class="tip-icon">📏</text>
                  <text class="tip-title">比例选择</text>
                  <text class="tip-text">根据实际需求选择合适的纸张尺寸，避免后期重复调整</text>
                </view>
                <view class="tip-item">
                  <text class="tip-icon">🔍</text>
                  <text class="tip-title">细节调整</text>
                  <text class="tip-text">可以通过拖动和缩放来精确调整裁剪区域的位置和大小</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
      </view>

      <!-- 裁剪组件 -->
      <wxmy-cropper
        wx:if="{{showCropper}}"
        imageSrc="{{tempFilePath}}"
        bindcropped="handleCropperComplete"
        bindclose="handleCropperCancel"
      ></wxmy-cropper>

      <!-- 放大倍数选择弹窗 -->
      <view class="scale-dialog {{showScaleDialog ? 'show' : ''}}" catchtouchmove="preventTouchMove">
        <view class="scale-content">
          <view class="scale-title">选择导出大小</view>
          <radio-group class="scale-options" bindchange="handleScaleSelect">
            <label class="scale-option" wx:for="{{scaleOptions}}" wx:key="value">
              <radio value="{{index}}" checked="{{selectedScale === item.value}}"/>
              <text class="text">{{item.text}}</text>
            </label>
          </radio-group>
          <view class="scale-buttons">
            <button class="scale-btn cancel" bindtap="cancelScaleDialog">取消</button>
            <button class="scale-btn confirm" bindtap="confirmScaleAndDownload">确定</button>
          </view>
        </view>
      </view>

      <!-- 顶部提示 -->
      <view class="top-tips {{showTopTips ? 'show' : ''}} {{topTipsType}}">
        {{topTipsMsg}}
      </view>

      <!-- 导出用Canvas -->
      <canvas type="2d" id="exportCanvas" style="position: absolute; left: -9999px; width: 1px; height: 1px;"></canvas>
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="2"><!-- 底部导航 --></tab-bar>
</view>

<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="{{pagetitle}}"><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view wx:for="{{filterTabs}}" 
              wx:key="*this" 
              class="filter-tab {{currentTab === index ? 'active' : ''}}"
              bindtap="switchTab"
              data-index="{{index}}">
          {{item}}
        </view>
      </view>

      <!-- 招募列表 -->
      <view class="recruitment-list">
        <!-- 添加加载指示器 -->
        <view class="loading-container" wx:if="{{isLoading}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <view wx:for="{{filteredRecruitmentList}}" 
              wx:key="id" 
              class="recruitment-card {{item.recruitStatus === 'ended' ? 'expired' : ''}} {{item.recruitStatus === 'notStarted' ? 'notStarted' : ''}} {{item.isApplied ? 'applied' : ''}} {{!item.isApplied && item.recruitStatus != 'ended' ? 'status-border-blue' :item.isApplied && item.recruitStatus == 'ended' ? 'status-border-gray' :!item.isApplied && item.recruitStatus == 'ended' ? 'status-border-gray' :item.isApplied && item.recruitStatus != 'ended' ? 'status-border-green' : ''}}">
          <!-- 序号 -->
          <view class="card-number {{item.recruitStatus}} {{item.isApplied ? 'applied' : ''}}">{{index + 1}}</view>
          
          <view class="card-content">
            <!-- 标题 -->
            <view class="card-title">{{item.title}}</view>
            
            <!-- 基本信息 -->
            <view class="card-info">
              <view class="info-item">
                <text class="label">系列名称：</text>
                <text>{{item.series_name}}</text>
              </view>
              <view class="info-item">
                <text class="label">招募类型：</text>
                <text>{{item.openrecruitment}}</text>
              </view>
              <view class="info-item">
                <text class="label">价格区间：</text>
                <text>¥{{item.price_min}} - {{item.price_max}}</text>
              </view>
              <view class="info-item">
                <text class="label">卡片数量：</text>
                <text>{{item.min_cards}} - {{item.max_cards}}张</text>
              </view>
              <view class="info-item">
                <text class="label">招募时间：</text>
                <text>{{item.recruitment_start_date}} 至 {{item.recruitment_end_date}}</text>
              </view>
              <!-- <view class="info-item">
                <text class="label">已申请：</text>
                <text>{{item.joined_count.total}}人 (已通过{{item.joined_count.approved}}人)</text>
              </view> -->
              <!-- <view class="info-item">
                <text>{{
                  !item.isApplied && item.recruitStatus != 'ended' ? '可申请' :
                  item.isApplied && item.recruitStatus == 'ended' ? '已申请，已结束' :
                  !item.isApplied && item.recruitStatus == 'ended' ? '未申请，已结束' :
                  item.isApplied && item.recruitStatus != 'ended' ? '已申请，招募中' : ''
                }}</text>
              </view> -->
            </view>

            <!-- 艺术风格标签 -->
            <view class="art-styles">
              <block wx:if="{{item.artStyleTagArray && item.artStyleTagArray.length > 0}}">
                <text wx:for="{{item.artStyleTagArray}}" 
                    wx:key="*this" 
                    wx:for-item="style" 
                    wx:if="{{style}}"
                    class="tag">{{style}}</text>
              </block>
              <block wx:elif="{{item.art_styles}}">
                <text wx:for="{{safe.safeSplit(item.art_styles)}}" 
                    wx:key="*this" 
                    wx:for-item="style" 
                    class="tag">{{style}}</text>
              </block>
            </view>
            <!-- 详情按钮 -->
            <view class="card-action">
              <button class="detail-btn {{item.isApplied && item.recruitStatus!= 'ended' ? 'applied' : ''}} {{item.recruitStatus}}" 
                      bindtap="{{!item.isApplied && item.recruitStatus != 'ended' ? 'showDetail' :item.isApplied && item.recruitStatus != 'ended' ? 'showDetail' : ''}}" 
                      data-id="{{item.id}}">
                {{item.buttonText || '详情'}}
              </button>
            </view>
          </view>
        </view>

        <!-- 空状态展示 -->
        <view class="empty-state" wx:if="{{filteredRecruitmentList.length === 0}}">
          <image class="empty-image" src="/assets/empty.png" mode="aspectFit"></image>
          <text class="empty-text">{{showInvitationTab && currentTab === 0 ? '没有找到当前邀请信息' : '暂无招募信息'}}</text>
        </view>
      </view>

    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 详情浮窗 -->
<view class="detail-popup {{showDetailPopup ? 'show' : ''}}" wx:if="{{currentRecruitment}}" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}};">
  <view class="popup-content" style="{{zheight3}};">
    <!-- 关闭按钮 -->
    <view class="popup-close" bindtap="closeDetailPopup">×</view>
    
    <!-- 标题 -->
    <view class="popup-title">{{currentRecruitment.title}}</view>
    
    <!-- 内容区域 -->
    <scroll-view class="popup-scroll" scroll-y>
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-title">基本信息</view>
        <view class="info-grid">
          <view class="info-item">
            <text class="label">系列名称</text>
            <text>{{currentRecruitment.series_name}}</text>
          </view>
          <view class="info-item">
            <text class="label">招募类型</text>
            <text>{{currentRecruitment.openrecruitment}}</text>
          </view>
          <view class="info-item">
            <text class="label">价格区间</text>
            <text>¥{{currentRecruitment.price_min}} - {{currentRecruitment.price_max}}</text>
          </view>
          <view class="info-item">
            <text class="label">卡片数量</text>
            <text>{{currentRecruitment.min_cards}} - {{currentRecruitment.max_cards}}张</text>
          </view>
          <view class="info-item">
            <text class="label">招募开始时间</text>
            <text>{{currentRecruitment.recruitment_start_date}}</text>
          </view>
          <view class="info-item">
            <text class="label">招募结束时间</text>
            <text>{{currentRecruitment.recruitment_end_date}}</text>
          </view>
        </view>
      </view>
      
      <!-- 申请情况 -->
      <view class="detail-section">
        <view class="section-title">申请情况</view>
        <view class="section-content">
          <view class="info-item">
            <text class="label">申请人数：</text>
            <text>{{currentRecruitment.joined_count.total}}人</text>
          </view>
          <view class="info-item">
            <text class="label">已通过人数：</text>
            <text>{{currentRecruitment.joined_count.approved}}人</text>
          </view>
        </view>
      </view>
      
      <!-- 艺术风格标签 -->
      <view class="detail-section">
        <view class="section-title">艺术风格</view>
        <view class="section-content">
          <block wx:if="{{artStyleTags && artStyleTags.length > 0}}">
            <text wx:for="{{artStyleTags}}" 
                 wx:key="*this" 
                 wx:for-item="style"
                 wx:if="{{style}}"
                 class="tag">{{style}}</text>
          </block>
          <block wx:elif="{{currentRecruitment && currentRecruitment.art_styles}}">
            <text wx:for="{{safe.safeSplit(currentRecruitment.art_styles)}}" 
                 wx:key="*this" 
                 wx:for-item="style"
                 class="tag">{{style}}</text>
          </block>
          <text wx:if="{{!currentRecruitment || !currentRecruitment.art_styles && (!artStyleTags || artStyleTags.length === 0)}}" class="no-styles">暂无艺术风格信息</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮区域 -->
    <view class="popup-footer" style="padding-bottom: 20px;">
      <button class="apply-btn {{currentRecruitment.recruitStatus === 'ended' ? 'disabled' : ''}} {{currentRecruitment.recruitStatus === 'notStarted' ? 'not-started' : ''}} {{currentRecruitment.isApplied ? 'applied' : ''}}" 
              bindtap="{{currentRecruitment.isApplied && currentRecruitment.recruitStatus == 'ended' ? '' :currentRecruitment.isApplied && currentRecruitment.recruitStatus != 'ended' ? '' : 'applyForSeries'}}" 
              disabled="{{currentRecruitment.recruitStatus === 'ended' || currentRecruitment.recruitStatus === 'notStarted'}}">
        {{currentRecruitment.applyButtonText || '申请系列'}}
      </button>
    </view>
  </view>
</view>


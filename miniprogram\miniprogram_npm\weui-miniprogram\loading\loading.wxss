.wx_loading_view {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.wx_loading_view__hide {
  height: 0 !important;
  display: none;
}
.wx_loading_view__animated.wx_loading_view__hide {
  display: flex;
}
.loading {
  color: rgba(255, 255, 255, 0.9);
  font-size: 17px;
  text-align: center;
}
.loading_view_translation {
  transition: height 0.2s 0.3s ease;
}
.weui-loading-animation {
  height: 15.047px;
  width: 15.047px;
  animation: loading 1s linear infinite;
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.wx_dot_loading,
.wx_dot_loading::before,
.wx_dot_loading::after {
  display: inline-block;
  vertical-align: middle;
  width: 6px;
  height: 6px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  font-size: 0;
  animation: dot2 1.6s step-start infinite;
}
.wx_dot_loading {
  position: relative;
}
.wx_dot_loading::before {
  content: "";
  position: absolute;
  left: -12px;
  background-color: rgba(0, 0, 0, 0.1);
  animation: dot1 1.6s step-start infinite;
}
.wx_dot_loading::after {
  content: "";
  position: absolute;
  right: -12px;
  background-color: rgba(0, 0, 0, 0.5);
  animation: dot3 1.6s step-start infinite;
}
@keyframes dot1 {
  0%,
  100% {
    background-color: rgba(0, 0, 0, 0.1);
  }
  30% {
    background-color: rgba(0, 0, 0, 0.5);
  }
  60% {
    background-color: rgba(0, 0, 0, 0.3);
  }
}
@keyframes dot2 {
  0%,
  100% {
    background-color: rgba(0, 0, 0, 0.3);
  }
  30% {
    background-color: rgba(0, 0, 0, 0.1);
  }
  60% {
    background-color: rgba(0, 0, 0, 0.5);
  }
}
@keyframes dot3 {
  0%,
  100% {
    background-color: rgba(0, 0, 0, 0.5);
  }
  30% {
    background-color: rgba(0, 0, 0, 0.3);
  }
  60% {
    background-color: rgba(0, 0, 0, 0.1);
  }
}
.wx_dot_loading_white {
  background-color: rgba(255, 255, 255, 0.3);
  animation: dotw2 1.6s step-start infinite;
}
.wx_dot_loading_white::before {
  background-color: rgba(255, 255, 255, 0.5);
  animation: dotw1 1.6s step-start infinite;
}
.wx_dot_loading_white::after {
  background-color: rgba(255, 255, 255, 0.1);
  animation: dotw3 1.6s step-start infinite;
}
@keyframes dotw1 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.5);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.1);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
@keyframes dotw2 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.3);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.5);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
@keyframes dotw3 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.1);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.3);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.5);
  }
}
.wx_dot_loading_white {
  background-color: rgba(255, 255, 255, 0.3);
  animation: dotw2 1.6s step-start infinite;
}
.wx_dot_loading_white::before {
  background-color: rgba(255, 255, 255, 0.5);
  animation: dotw1 1.6s step-start infinite;
}
.wx_dot_loading_white::after {
  background-color: rgba(255, 255, 255, 0.1);
  animation: dotw3 1.6s step-start infinite;
}
@keyframes dotw1 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.5);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.1);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
@keyframes dotw2 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.3);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.5);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
@keyframes dotw3 {
  0%,
  100% {
    background-color: rgba(255, 255, 255, 0.1);
  }
  30% {
    background-color: rgba(255, 255, 255, 0.3);
  }
  60% {
    background-color: rgba(255, 255, 255, 0.5);
  }
}
.weui-loadmore {
  display: flex;
  /* skyline 不支持 inline */
  justify-content: center;
  align-items: center;
}
.weui-loadmore .weui-loading-animation {
  margin-right: 4.518px;
  /* skyline 不支持 em */
}



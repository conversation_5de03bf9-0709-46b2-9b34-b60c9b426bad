.cfp-container {
  --primary-color: var(--primary-color, #1890ff);
  --text-color: var(--text-color, #333);
  --text-secondary: var(--text-secondary, #666);
  --border-color: var(--border-color, #eee);
  --bg-color: var(--bg-color, #fff);
  --bg-light: var(--bg-light, #f5f7fa);
  --radius: var(--radius, 8rpx);
  position: relative;
  width: 100%;
}

/* 触发按钮 */
.cfp-trigger {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  background: var(--bg-light);
  border-radius: var(--radius);
  border: 1rpx solid var(--border-color);
  transition: all 0.2s;
}

.cfp-trigger:active {
  opacity: 0.8;
}

.cfp-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.cfp-preview {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.cfp-info {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.cfp-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-right: 8rpx;
  max-width: 160rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cfp-arrow {
  width: 12rpx;
  height: 12rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  transition: transform 0.2s;
}

.cfp-placeholder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #999;
  font-size: 26rpx;
}

/* 弹出层 */
.cfp-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s;
}

.cfp-mask.cfp-show {
  opacity: 1;
  visibility: visible;
}

.cfp-popup-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s, visibility 0.25s;
}

.cfp-popup-wrapper.cfp-show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.cfp-popup {
  background: #fff;
  width: 85%;
  max-width: 600rpx;
  max-height: 80vh;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.25s;
  display: flex;
  flex-direction: column;
}

.cfp-popup-wrapper.cfp-show .cfp-popup {
  transform: scale(1);
}

.cfp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  background: rgba(255, 255, 255, 0.98);
}

.cfp-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.cfp-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
  transition: all 0.2s ease;
  margin: -12rpx;
}

/* 分类标签 */
.cfp-tabs {
  white-space: nowrap;
  padding: 10rpx 0;
  border-bottom: 1rpx solid var(--border-color);
  background: rgba(255, 255, 255, 0.98);
}

.cfp-tabs-inner {
  display: inline-flex;
  padding: 0 20rpx;
}

.cfp-tab {
  display: inline-block;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #666;
  background: #f0f2f5;
  border-radius: 100rpx;
  margin-right: 16rpx;
  transition: all 0.2s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.cfp-tab.cfp-active {
  background: var(--primary-color);
  color: #fff;
  box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.3);
}

/* 字体列表 */
.cfp-fonts {
  flex: 1;
  padding: 12rpx 0;
}

.cfp-fonts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
  padding: 0 16rpx;
}

.cfp-font-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 6rpx;
  background: #f5f7fa;
  border-radius: var(--radius);
  border: 1rpx solid transparent;
  transition: all 0.2s;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.cfp-font-item:active {
  opacity: 0.8;
}

.cfp-font-item.cfp-active {
  background: rgba(24, 144, 255, 0.1);
  border-color: var(--primary-color);
  box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.2);
}

.cfp-font-preview {
  font-size: 30rpx;
  line-height: 1.3;
  margin-bottom: 6rpx;
}

.cfp-font-name {
  font-size: 20rpx;
  color: var(--text-secondary);
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 加载中 */
.cfp-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.cfp-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(24, 144, 255, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: cfp-spin 0.8s linear infinite;
  margin-bottom: 12rpx;
}

@keyframes cfp-spin {
  to { transform: rotate(360deg); }
}

.cfp-font-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4rpx;
  margin-top: 4rpx;
}

.cfp-font-tag {
  font-size: 16rpx;
  color: var(--primary-color);
  background: rgba(24, 144, 255, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  line-height: 1.2;
}
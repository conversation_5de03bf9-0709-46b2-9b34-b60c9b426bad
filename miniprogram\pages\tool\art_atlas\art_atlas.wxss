.background-section {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.section-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
}

.upload-area {
  display: flex;
  justify-content: center;
  align-items: center;
  /* overflow: hidden; */
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.upload-icon {
  font-size: 60rpx;
  line-height: 1;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 28rpx;
}

.preview-container {
  position: relative;
  /* margin: 0 auto; */
}

.preview-image {
  object-fit: contain;
  display: block;
}

.preview-overlay {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  border: 0 solid #333;
  transition: all 0.3s ease;
}

.resize-handle {
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #333;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  touch-action: none;
}

.resize-handle.top {
  top: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.resize-handle.right {
  right: -16rpx;
  top: 50%;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.resize-handle.bottom {
  bottom: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.resize-handle.left {
  left: -16rpx;
  top: 50%;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.resize-arrow {
  font-size: 18rpx;
  color: #333;
  line-height: 1;
  pointer-events: none;
}

.resize-handle:active {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.overlay-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.artist-name-tag {
  position: absolute;
  z-index: 2;
  touch-action: none;
  user-select: none;
  cursor: move;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.3s ease;
}

.artist-name-tag.dragging {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.artist-name {
  font-size: inherit;
  color: inherit;
  font-weight: normal;
  white-space: nowrap;
  text-align: center;
  position: relative;
  z-index: 2;
  padding: 8rpx 16rpx;
  transition: all 0.2s ease;
  box-sizing: border-box;
  display: inline-block;
  border-radius: inherit;
  background-color: inherit;
}

.artist-name:active {
  opacity: 0.8;
}

.image-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(245, 245, 245, 0.9);
  border-radius: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  position: relative;
}

.info-label {
  color: #666;
  min-width: 140rpx;
}

.info-value {
  flex: 1;
}

.info-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 10rpx 0;
}

.fold-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.fold-btn.folded {
  transform: rotate(-90deg);
}

.fold-icon {
  font-size: 24rpx;
  color: #666;
}

.info-formula {
  margin: 4rpx 0 12rpx 30rpx;
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 500rpx;
}

.info-formula.hidden {
  max-height: 0;
  margin: 0;
  opacity: 0;
}

.formula-text {
  display: block;
  line-height: 1.6;
  color: #0066cc;
  font-family: 'Courier New', Courier, monospace;
}

/* 字体设置样式 */
.font-settings {
  margin: 0;
  padding: 0;
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 800rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.font-settings.hidden {
  max-height: 0;
  margin: 0;
  padding: 0;
  opacity: 0;
  border: none;
  pointer-events: none;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 12rpx 0;
}

.slider-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.font-size-slider slider {
  flex: 1;
}

/* 画师区域样式 */
.artist-section {
  margin: 20rpx 20rpx 0;
  padding: 0 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.artist-tag-num {
  position: absolute;
  top: -20rpx;
  left: -10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #007AFF, #0056b3);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s ease;
}

.artist-tag-num.selected {
  transform: scale(1.1);
}

.artist-tag-num .checkmark {
  position: absolute;
  left: 70%;
  bottom: -40rpx;
  transform: translateX(-50%) scale(0.9);
  font-size: 60rpx;
  color: #4CD964;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  text-shadow: 
    0 1px 2px rgba(0, 0, 0, 0.2),
    0 0 4px rgba(0, 0, 0, 0.1);
}

.artist-tag-num.selected .checkmark {
  transform: translateX(-50%) scale(1.1);
}

.add-artist-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  margin: 20rpx 0;
  background: linear-gradient(135deg, #6c5ce7, #5d4aef);
  border: none;
  border-radius: 8rpx;
  color: white;
  font-size: 28rpx;
}

.add-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.artist-list {
  margin-top: 20rpx;
}

.artist-item {
  margin-bottom: 30rpx;
  padding: 20rpx 0;
  /* background: rgba(245, 245, 245, 0.9); */
  border-radius: 8rpx;
}

.artist-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  gap: 10rpx;
}

.artist-name-input {
  flex: 1;
  min-width: 0;
  height: 60rpx;
  padding: 0 20rpx;
  border: none;
  font-size: 28rpx;
  text-align: left;
  background: transparent;
}

.artist-buttons {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.add-work-btn {
  display: inline-flex;
  align-items: center;
  height: 60rpx;
  padding: 0 16rpx;
  color: #666;
  border: 1px solid rgb(214, 214, 214);
  font-size: 24rpx;
  background: transparent !important;
  border-radius: 6rpx;
  min-width: unset !important;
  width: auto !important;
}

.add-work-btn::after {
  border: none;
}

.add-work-text {
  font-size: 24rpx;
  white-space: nowrap;
}

.download-btn {
  display: inline-flex;
  align-items: center;
  height: 60rpx;
  padding: 0 16rpx;
  color: #666;
  border: 1px solid rgb(214, 214, 214);
  font-size: 24rpx;
  background: transparent !important;
  border-radius: 6rpx;
  min-width: unset !important;
  width: auto !important;
}

.download-btn::after {
  border: none;
}

.download-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

.download-text {
  font-size: 24rpx;
  white-space: nowrap;
}

.delete-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  width: 60rpx !important;
  min-width: unset !important;
  padding: 0;
  color: #ff4d4f;
  border: 1px solid #ff4d4f;
  font-size: 24rpx;
  background: transparent !important;
  border-radius: 6rpx;
}

.delete-btn::after {
  border: none;
}

.delete-btn:active {
  opacity: 0.8;
}

.delete-icon {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.artist-preview {
  position: relative;
  margin: 20rpx 0;
  border-radius: 8rpx;
  overflow: visible;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.artist-preview .preview-image {
  object-fit: contain;
  display: block;
}

.artist-preview .preview-overlay {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  overflow: hidden;
}

.artist-preview .works-grid {
  position: absolute;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  z-index: 2;
  width: calc(100% - 40rpx);
  height: calc(100% - 40rpx);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: visible;
}

.artist-preview .work-preview-container {
  position: relative;
  flex: 0 0 auto;
  width: calc((100% - 60rpx) / 4);
  max-width: 160rpx;
  height: 100%;
  transition: all 0.3s ease;
  touch-action: pan-x;
  z-index: 1;
  will-change: transform;
  display: flex;
  align-items: center;
  justify-content: center;
}

.artist-preview .work-preview-container.dragging {
  z-index: 10;
  opacity: 0.8;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: none;
  cursor: move;
}

.artist-preview .work-preview-container.target {
  transform: scale(0.95);
  opacity: 0.6;
}

.artist-preview .work-preview-container.dragging .work-preview {
  transform: scale(1.05);
  border: 2rpx solid #4CAF50;
}

.artist-preview .work-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  border: var(--work-border-width, 2rpx) solid var(--work-border-color, #f00);
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  background-color: transparent;
  filter: drop-shadow(0 0 var(--work-glow-intensity, 8rpx) var(--work-glow-color, rgba(255, 0, 0, 0.8)));
}

.artist-preview .work-preview-container:not(.dragging) {
  transform: translate(0, 0);
}

.artist-preview .preview-work-delete {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 0, 0, 0.8);
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  z-index: 3;
}

/* 根据作品数量调整宽度 */
.artist-preview .works-grid[data-count="1"] .work-preview {
  width: 160rpx;
}

.artist-preview .works-grid[data-count="2"] .work-preview {
  width: 140rpx;
}

.artist-preview .works-grid[data-count="3"] .work-preview {
  width: 120rpx;
}

.artist-preview .works-grid[data-count="4"] .work-preview {
  width: 100rpx;
}

.artist-preview .artist-name-tag {
  position: absolute;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: 0 0;
}

.artist-preview .artist-name {
  font-size: inherit;  /* 继承父元素设置的字体大小 */
  color: #000;
  font-weight: normal;
  white-space: nowrap;
  text-align: center;
  position: relative;
  z-index: 2;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  background-color: rgba(173, 216, 230, 0.6);
  border: 1px dashed #333;
}

.reupload-btn-wrapper {
  display: none;
}

.reupload-btn {
  display: inline-flex;
  align-items: center;
  height: 60rpx;
  padding: 0 16rpx;
  color: #666;
  border: 1px solid rgb(214, 214, 214);
  font-size: 24rpx;
  background: transparent !important;
  border-radius: 6rpx;
  min-width: unset !important;
  width: auto !important;
}

.reupload-btn::after {
  border: none;
}

.reupload-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.reupload-text {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
}

/* 作品浮窗样式 */
.work-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.work-popup-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.work-popup-image {
  width: 100%;
  height: 600rpx;
  object-fit: contain;
  border-radius: 8rpx;
}

.work-popup-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  gap: 20rpx;
}

.work-popup-delete {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ee5253);
  border: none;
  border-radius: 8rpx;
  color: white;
  font-size: 28rpx;
}

.work-popup-close {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6c757d, #495057);
  border: none;
  border-radius: 8rpx;
  color: white;
  font-size: 28rpx;
}

.menu-wrapper {
  position: relative;
  margin-left: 10rpx;
}

.menu-btn {
  width: 60rpx !important;
  height: 60rpx !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent !important;
  border: 1px solid rgb(214, 214, 214);
  border-radius: 6rpx;
}

.menu-btn::after {
  border: none;
}

.menu-icon {
  font-size: 36rpx;
  color: #666;
  line-height: 1;
}

.menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  display: none;
}

.menu-mask.show {
  display: block;
}

.menu-popup {
  position: absolute;
  top: 70rpx;
  right: 0;
  min-width: 300rpx;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
  opacity: 0;
  transform: translateY(-10rpx);
  visibility: hidden;
  transition: all 0.2s ease;
}

.menu-popup.show {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.menu-popup::before {
  content: '';
  position: absolute;
  top: -8rpx;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  background: white;
  transform: rotate(45deg);
  box-shadow: -2rpx -2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  width: 100% !important;
  height: 80rpx !important;
  padding: 0 20rpx !important;
  margin: 0 !important;
  display: flex;
  align-items: center;
  background: transparent !important;
  border-radius: 0 !important;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eee;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item::after {
  border: none;
}

.menu-item.delete {
  color: #ff6b6b;
}

.menu-item-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.menu-item.delete .menu-item-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.menu-item-text {
  flex: 1;
  text-align: left;
}

/* 帮助按钮样式 */
.help-btn {
  position: fixed;
  right: 30rpx;
  bottom: calc(var(--bottom-nav-height) + var(--safe-area-inset-bottom) + 30rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 900;
  transition: all 0.3s ease;
}

.help-btn:active {
  transform: scale(0.95) rotate(12deg);
}

.help-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

/* 帮助弹窗样式 */
.help-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.help-popup.show {
  opacity: 1;
  visibility: visible;
}

.help-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.help-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 600rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.help-popup.show .help-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.help-scroll {
  max-height: 70vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 30rpx;
}

.help-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  background: white;
  position: sticky;
  top: 0;
  z-index: 1;
}

.help-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.help-title::before {
  content: "💡";
  font-size: 36rpx;
}

.help-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.help-close:active {
  transform: scale(0.9);
  background: #eeeeee;
}

.help-section {
  margin-bottom: 48rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.help-section:nth-child(1) .help-section-title::before {
  content: "🖼️";
  margin-right: 12rpx;
}

.help-section:nth-child(2) .help-section-title::before {
  content: "👨‍🎨";
  margin-right: 12rpx;
}

.help-section:nth-child(3) .help-section-title::before {
  content: "💡";
  margin-right: 12rpx;
}

.help-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.help-item {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.help-step {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
}

.help-detail {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding-left: 20rpx;
}

.help-detail .text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.help-detail text::before {
  content: "•";
  color: #6c5ce7;
  margin-right: 8rpx;
  font-size: 32rpx;
  line-height: 1;
}

/* 动画效果 */
.help-item {
  transition: transform 0.3s ease;
}

.help-item:active {
  transform: scale(0.98);
}

/* 新增样式 */
.border-width-slider,
.border-radius-slider,
.blur-slider {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 10rpx 0;
  margin-top: 20rpx;
}

.border-style-selector {
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.border-style-group {
  display: flex;
  gap: 30rpx;
  margin-top: 10rpx;
}

.border-style-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.border-style-item radio {
  transform: scale(0.8);
}

.border-settings-row {
  display: flex;
  gap: 20rpx;
  /* margin-top: 20rpx; */
}

.border-settings-row .border-width-slider,
.border-settings-row .border-radius-slider {
  flex: 1;
  min-width: 0;
}

.border-settings-row .slider-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.border-settings-row slider {
  width: 100%;
}

/* 设置面板样式 */
.settings-panel {
  margin: 0;
  padding: 0;
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 800rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.settings-panel.hidden {
  max-height: 0;
  margin: 0;
  padding: 0;
  opacity: 0;
  border: none;
  pointer-events: none;
}

.settings-header {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-content {
  padding: 16rpx;
}

.settings-row {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  gap: 20rpx;
}

.settings-row:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.settings-row slider {
  flex: 1;
  margin: 0;
}

.border-style {
  flex-direction: column;
  align-items: flex-start;
  gap: 12rpx;
}

.border-style-group {
  display: flex;
  gap: 30rpx;
  padding-top: 8rpx;
}

.border-style-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 24rpx;
  color: #666;
}

.border-style-item radio {
  transform: scale(0.7);
  margin-right: 2rpx;
}

/* 修改作品预览容器样式 */
.work-preview-container {
  position: relative;
  flex: 0 0 auto;
  width: calc((100% - 60rpx) / 4);
  max-width: 160rpx;
  height: 100%;
  transition: all 0.3s ease;
  touch-action: pan-x;
  z-index: 1;
  will-change: transform;
  display: flex;
  align-items: center;
  justify-content: center;
}

.work-preview {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  border: var(--work-border-width, 2rpx) solid var(--work-border-color, #f00);
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  background-color: transparent;
  filter: drop-shadow(0 0 var(--work-glow-intensity, 8rpx) var(--work-glow-color, rgba(255, 0, 0, 0.8)));
}

/* 更新作品名称浮动层样式 */
.work-name-overlay {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  pointer-events: none;
  overflow: hidden;
}

.work-name-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.7) 100%);
}

.work-name-text {
  position: relative;
  color: white;
  font-size: 16rpx;
  line-height: 1.2;
  text-align: center;
  padding: 8rpx;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  box-sizing: border-box;
}

/* 作品预览容器悬停效果 */
.work-preview-container:active .work-name-overlay {
  opacity: 1;
}

/* 添加弹出层输入框样式 */
.popup-input {
  width: 100%;
  height: 80rpx;
  margin: 20rpx 0;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background: white;
  box-sizing: border-box;
}

.popup-input:focus {
  border-color: #6c5ce7;
}

.work-border-settings {
  margin: 20rpx 30rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
  overflow: hidden;
  background: #ffffff;
}

.work-border-settings .settings-header {
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.work-border-settings .settings-content {
  padding: 0;
}

.work-border-settings .settings-group {
  padding: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.work-border-settings .settings-group:last-child {
  border-bottom: none;
}

.work-border-settings .settings-group-title {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: rgba(108, 92, 231, 0.05);
}

.work-border-settings .settings-row {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  gap: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.work-border-settings .settings-row:last-child {
  border-bottom: none;
}

.work-border-settings .settings-row.switch-row {
  padding: 12rpx 24rpx;
}

.work-border-settings .settings-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.work-border-settings slider {
  flex: 1;
  margin: 0;
}

.work-border-settings switch {
  transform: scale(0.9);
  margin-left: auto;
}

/* 浮动按钮基础样式 */
.floating-add-btn,
.floating-bg-settings-btn {
  position: fixed;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 900;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.8) translateY(20rpx);
  pointer-events: none;
  visibility: hidden;
}

.floating-add-btn.show,
.floating-bg-settings-btn.show {
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
  visibility: visible;
}

.floating-add-btn {
  background: linear-gradient(135deg, #6c5ce7, #5d4aef);
}

.floating-bg-settings-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.help-btn {
  position: fixed;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 900;
  background: linear-gradient(135deg, #6c5ce7, #5d4aef);
}

.floating-add-btn:active,
.floating-bg-settings-btn:active,
.help-btn:active {
  transform: scale(0.95);
}

.floating-add-icon,
.floating-bg-settings-icon,
.help-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

/* 背景设置浮动按钮样式 */
.floating-bg-settings-btn {
  position: fixed;
  right: 30rpx;
  bottom: calc(var(--bottom-nav-height) + var(--safe-area-inset-bottom) + 220rpx);
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 900;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.8) translateY(20rpx);
  pointer-events: none;
  visibility: hidden;
}

.floating-bg-settings-btn.show {
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
  visibility: visible;
}

.floating-bg-settings-btn:active {
  transform: scale(0.95);
}

.floating-bg-settings-icon {
  font-size: 40rpx;
}

/* 背景设置浮动面板样式 */
.bg-settings-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.bg-settings-popup.show {
  opacity: 1;
  visibility: visible;
}

.bg-settings-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.bg-settings-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 600rpx;
  background: #f8f9fa;
  width: 92%;
  max-width: 650rpx;
  max-height: 85%;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.bg-settings-popup.show .bg-settings-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.bg-settings-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  background: white;
  position: sticky;
  top: 0;
  z-index: 1;
}

.bg-settings-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.bg-settings-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.bg-settings-close:active {
  transform: translateY(-50%) scale(0.9);
  background: #eeeeee;
}

.bg-settings-scroll {
  max-height: calc(85vh - 180rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: #ffffff;
}

.popup-section {
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* TAB样式 */
.bg-settings-tabs {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #6c5ce7;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 4rpx;
  background: #6c5ce7;
  border-radius: 4rpx;
}

.tab-text {
  position: relative;
  z-index: 1;
}

.tab-content {
  display: block;
  opacity: 1;
  transition: all 0.3s ease;
}

.tab-content.hidden {
  display: none;
  opacity: 0;
}

/* 背景区域TAB样式 */
.bg-tabs {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin: 20rpx 0;
}

.bg-tabs .tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.bg-tabs .tab-item.active {
  color: #6c5ce7;
  font-weight: 500;
}

.bg-tabs .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 4rpx;
  background: #6c5ce7;
  border-radius: 4rpx;
}

.bg-tabs .tab-text {
  position: relative;
  z-index: 1;
}

.background-section .tab-content {
  display: block;
  opacity: 1;
  transition: all 0.3s ease;
}

.background-section .tab-content.hidden {
  display: none;
  opacity: 0;
}

.empty-settings {
  padding: 60rpx 30rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.atlas-download-section {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.atlas-download-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.atlas-download-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.atlas-download-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.atlas-download-buttons {
  display: flex;
  gap: 20rpx;
}

.atlas-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.atlas-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.atlas-btn-detect {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.atlas-btn-batch {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.atlas-btn-merge {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.atlas-btn.disabled {
  opacity: 0.5;
  background: #ccc;
  cursor: not-allowed;
  pointer-events: none;
  box-shadow: none;
}

.atlas-btn.disabled:active {
  transform: none;
  box-shadow: none;
}

.atlas-btn-batch.disabled {
  background: linear-gradient(135deg, #ccc, #999);
}

.atlas-btn-merge.disabled {
  background: linear-gradient(135deg, #ccc, #999);
}

/* 检测结果弹窗样式 */
.check-result-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 2000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.check-result-popup.show {
  visibility: visible;
  opacity: 1;
}

.check-result-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
}

.check-result-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 92%;
  max-width: 650rpx;
  max-height: 85vh;
  background: white;
  border-radius: 24rpx;
  opacity: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  z-index: 2001;
}

.check-result-popup.show .check-result-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.check-result-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  background: white;
  position: sticky;
  top: 0;
  z-index: 1;
}

.check-result-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.check-result-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.check-result-close:active {
  transform: scale(0.9);
  background: #eeeeee;
}

.check-result-scroll {
  max-height: calc(85vh - var(--top-nav-height) - var(--bottom-nav-height) - var(--safe-area-inset-bottom) - 100rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 统计信息区域 */
.check-result-stats {
  padding: 32rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  background: #f8f9fa;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-item.warning {
  background: #fff3e0;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.stat-item.warning .stat-value {
  color: #f57c00;
}

/* 分割线 */
.check-result-divider {
  height: 20rpx;
  background: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* 详细信息区域 */
.check-result-details {
  padding: 32rpx;
}

.details-header {
  margin-bottom: 24rpx;
}

.details-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.detail-item:active {
  transform: scale(0.98);
}

.detail-number {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6c5ce7;
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 50%;
}

.detail-issues {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.issue-tag {
  padding: 6rpx 16rpx;
  background: #ff9800;
  color: white;
  font-size: 24rpx;
  border-radius: 100rpx;
}

/* 预览弹窗样式 */
.preview-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.preview-popup.show {
  opacity: 1;
  visibility: visible;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
}

.preview-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.preview-scroll {
  flex: 1;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  display: flex;
  align-items: center;
}

.preview-scroll-inner {
  /* min-width: 100%; */
  height: 100%;
  display: flex;
  justify-content: center;
  /* align-items: center; */
}

.preview-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  
  top: 0;
  z-index: 1000;
}

.preview-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #fff;
}

.preview-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.preview-close:active {
  transform: scale(0.9);
  background: #eeeeee;
}

.preview-image {
  height: 100%;
  width: auto;
  display: block;
  touch-action: pan-x pan-y;
  user-select: none;
  -webkit-user-drag: none;
}

/* 添加画师选项弹窗 */
.add-options-popup {
  position: fixed;
  top: var(--top-nav-height);
  left: 0;
  width: 100%;
  height: calc(100vh - var(--top-nav-height) - var(--bottom-nav-height) - var(--safe-area-inset-bottom));
  z-index: 800;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.add-options-popup.show {
  visibility: visible;
  opacity: 1;
}

.add-options-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 800;
}

.add-options-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 92%;
  max-width: 650rpx;
  background: white;
  border-radius: 24rpx;
  opacity: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 801;
}

.add-options-popup.show .add-options-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.add-options-body {
  padding: 32rpx;
  background: white;
}

.add-options-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.add-options-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.add-options-close:active {
  transform: scale(0.9);
  background: #eeeeee;
}

.add-option-btn {
  width: 100% !important;
  height: auto !important;
  padding: 24rpx !important;
  margin: 0 0 20rpx 0 !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #f8f9fa !important;
  border: 1px solid #eee !important;
  border-radius: 16rpx !important;
  box-shadow: none !important;
  transition: all 0.3s ease;
}

.add-option-btn:last-child {
  margin-bottom: 0 !important;
}

.add-option-btn:active {
  transform: scale(0.98);
  background: #f0f0f0 !important;
}

.add-option-icon {
  font-size: 40rpx;
  color: #6c5ce7;
  margin-bottom: 8rpx;
}

.add-option-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.add-option-desc {
  font-size: 24rpx;
  color: #666;
}

/* 插入位置选择弹窗 */
.insert-options-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 800;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.insert-options-popup.show {
  visibility: visible;
  opacity: 1;
}

.insert-options-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 800;
}

.insert-options-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 92%;
  max-width: 650rpx;
  background: white;
  border-radius: 24rpx;
  opacity: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 801;
  display: flex;
  flex-direction: column;
}

.insert-options-popup.show .insert-options-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.insert-options-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  background: white;
}

.insert-options-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.insert-options-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.insert-options-close:active {
  transform: scale(0.9);
  background: #eeeeee;
}

.insert-options-body {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 0;
  background: white;
}

.insert-options-list {
  padding: 0 32rpx;
}

.insert-option-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border: 1px solid #eee;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.insert-option-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.insert-option-num {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6c5ce7;
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 50%;
  margin-right: 16rpx;
}

.insert-option-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.insert-option-hint {
  font-size: 24rpx;
  color: #666;
  margin-left: 16rpx;
}

/* 进度提示弹窗样式 */
.progress-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.progress-popup.show {
  opacity: 1;
  visibility: visible;
}

.progress-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.progress-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 80%;
  max-width: 500rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.progress-popup.show .progress-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.progress-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.progress-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.progress-body {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.progress-bar-wrapper {
  width: 100%;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6c5ce7, #5d4aef);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 40rpx;
  font-weight: 600;
  color: #6c5ce7;
}

.progress-status {
  font-size: 28rpx;
  color: #666;
}

/* 合并设置弹窗样式 */
.merge-settings-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.merge-settings-popup.show {
  opacity: 1;
  visibility: visible;
}

.merge-settings-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.merge-settings-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.merge-settings-popup.show .merge-settings-content {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.merge-settings-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
  position: relative;
}

.merge-settings-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.merge-settings-close {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  background: #f5f5f5;
}

.merge-settings-close:active {
  transform: translateY(-50%) scale(0.9);
  background: #eeeeee;
}

.merge-settings-body {
  padding: 32rpx;
}

.merge-settings-body .settings-group {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 32rpx;
}

.merge-settings-body .settings-row {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  gap: 20rpx;
}

.merge-settings-body .settings-row:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.merge-settings-body .settings-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.merge-settings-body .settings-row slider {
  flex: 1;
}

.merge-settings-body .switch-row {
  justify-content: space-between;
}

.start-merge-btn {
  width: 100% !important;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6c5ce7, #5d4aef) !important;
  border-radius: 12rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin-top: 20rpx;
  transition: all 0.3s ease;
}

.start-merge-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

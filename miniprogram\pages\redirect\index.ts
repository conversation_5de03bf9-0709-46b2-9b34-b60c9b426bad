import { layoutUtil } from '../../utils/layout';
import Api from '../../utils/api';
interface IPageOptions {
  url?: string;
  [key: string]: any;
}

interface IDecryptedData {
  url: string;
  sid: string;
  other?: {
    recruitment?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface INewParams extends Omit<IDecryptedData, 'other'> {
  other?: string;
}

Page({
  data: {
    loading: true,
    isDebug: false,
    debugParams: [],
    decryptedParams: [],
    hasDecrypted: false,
    decryptError: ''
  },

  async onLoad(options: IPageOptions) {
    let debugFlag = false;
    
    try {
      const debug = await Api.common.debug('jumpdebug');
      debugFlag = debug && (debug.jumpdebug === 1 || debug.jumpdebug === "1");
      
      if (debugFlag) {
        
        // 将对象转换为更易于显示的格式
        const debugParamArray = this.convertParamsToArray(options);
        
        this.setData({
          loading: false,
          isDebug: true,
          debugParams: debugParamArray,
          // 如果没有p参数，直接标记解密完成但结果为空
          hasDecrypted: true,
          decryptedParams: []
        });
        
        // 检查是否有加密参数 (p 或 scene)
        const encryptedParam = options.p || options.scene;
        if (encryptedParam) {
          try {
            let decryptedData;
            
            // 调试模式下也尝试判断是否是JSON字符串格式的p参数
            if (typeof encryptedParam === 'string' && encryptedParam.startsWith('{') && encryptedParam.endsWith('}')) {
              try {
                // 尝试将其解析为JSON对象
                decryptedData = JSON.parse(encryptedParam);
                console.log('调试模式 - 检测到JSON格式的p参数，直接解析:', decryptedData);
              } catch (jsonError) {
                console.error('调试模式 - JSON解析失败，尝试常规解密:', jsonError);
                // JSON解析失败，继续尝试常规解密
                decryptedData = await this.decrypt(encryptedParam);
              }
            } else {
              // 常规解密流程
              decryptedData = await this.decrypt(encryptedParam);
            }
            
            // 将解密后的数据转换为更易于显示的格式
            const decryptedParamArray = this.convertParamsToArray(decryptedData || {});
            
            // 在页面上显示原始参数和解密后的参数
            this.setData({
              decryptedParams: decryptedParamArray,
              hasDecrypted: true
            });
          } catch (error) {
            console.error('调试模式下解密出错:', error);
            this.setData({
              decryptError: '解密失败: ' + (error.message || '未知错误'),
              hasDecrypted: true
            });
          }
        } else {
          this.setData({
            decryptError: '没有找到可解密的参数',
            hasDecrypted: true
          });
        }
        
        // 提前返回，中断后续执行
        return;
      }
    } catch (error) {
      console.error('获取调试标志出错:', error);
      // 出错时继续常规流程
    }
    
    // 检查是否直接使用url参数进行跳转
    if (options.url) {
      console.log('检测到直接url参数跳转:', options.url);
      
      // 确保URL以/开头
      let targetUrl = options.url;
      if (!targetUrl.startsWith('/')) {
        targetUrl = '/' + targetUrl;
      }
      
      // 构建新的参数对象，排除url字段
      const newParams = { ...options };
      delete newParams.url;  // 删除url字段，因为它已经作为目标地址使用了
      
      // 构建完整的跳转 URL
      let finalUrl = targetUrl;
      const queryParams = Object.entries(newParams)
        .filter(([key, value]) => value !== undefined && value !== null)
        .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
        .join('&');
      if (queryParams) {
        finalUrl += (targetUrl.includes('?') ? '&' : '?') + queryParams;
      }
      console.log('finalUrl', finalUrl);
      
      // 执行跳转
      this.performNavigation(finalUrl);
      return;
    }
    
    // 修改正常流程也支持 scene 参数
    const encryptedParam = options.p || options.scene;
    if (!debugFlag && encryptedParam) {
      try {
        let decryptedData;
        
        // 尝试判断是否是JSON字符串格式的p参数
        if (typeof encryptedParam === 'string' && encryptedParam.startsWith('{') && encryptedParam.endsWith('}')) {
          try {
            // 尝试将其解析为JSON对象
            decryptedData = JSON.parse(encryptedParam);
            console.log('检测到JSON格式的p参数，直接解析:', decryptedData);
          } catch (jsonError) {
            console.error('JSON解析失败，尝试常规解密:', jsonError);
            // JSON解析失败，继续尝试常规解密
            decryptedData = await this.decrypt(encryptedParam);
          }
        } else {
          // 常规解密流程
          decryptedData = await this.decrypt(encryptedParam);
        }
        
        console.log('解密后的数据', decryptedData);
        
        // 再次检查调试模式，以防解密过程中状态变化
        try {
          const debugCheck = await Api.common.debug('jumpdebug');
          if (debugCheck && (debugCheck.jumpdebug === 1 || debugCheck.jumpdebug === "1")) {
            
            // 将原始参数和解密后的参数都转换为更易于显示的格式
            const originalParamArray = this.convertParamsToArray(options);
            const decryptedParamArray = this.convertParamsToArray(decryptedData || {});
            
            this.setData({
              loading: false,
              isDebug: true,
              debugParams: originalParamArray,
              decryptedParams: decryptedParamArray,
              hasDecrypted: true
            });
            return;
          }
        } catch (error) {
          console.error('二次检查调试模式出错:', error);
        }
        
        if (!decryptedData || !decryptedData.url) {
          console.error('解密数据无效或缺少URL');
          throw new Error('解密数据无效');
        }
        
        // 添加判断：如果包含 user_id，则唤起登录功能
        if (decryptedData.user_id) {
          console.log('检测到 user_id，唤起登录功能');
          
          // 使用全局 app 实例来调用登录方法
          const app = getApp();
          app.showLoginModal({
            user_id: decryptedData.user_id,
            source: decryptedData._category || 'redirect'
          });
          
          // 如果希望直接返回，不继续处理跳转，可以在这里添加 return 语句
          // return;
        }
        
        // 修复URL格式 - 确保URL以/开头
        let targetUrl = decryptedData.url;
        if (!targetUrl.startsWith('/')) {
          targetUrl = '/' + targetUrl;
        }
        
        
        // 构建新的参数对象，排除url字段
        const newParams = { ...decryptedData };
        delete newParams.url;  // 删除url字段，因为它已经作为目标地址使用了

        
        // 如果有嵌套对象，直接展开到参数中
        if (newParams.other && typeof newParams.other === 'object') {
          Object.assign(newParams, newParams.other);
          delete newParams.other;  // 删除原来的other对象
        }
        // 构建完整的跳转 URL
        let finalUrl = targetUrl;
        const queryParams = Object.entries(newParams)
          .filter(([key, value]) => value !== undefined && value !== null)
          .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
          .join('&');
        if (queryParams) {
          finalUrl += (targetUrl.includes('?') ? '&' : '?') + queryParams;
        }
        console.log('finalUrl', finalUrl);
        
        
        // 执行跳转
        this.performNavigation(finalUrl);
      } catch (error) {
        console.error('跳转流程出错:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
        // 2秒后返回
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    } else {
      if (!options || (!options.p && !options.url)) {
        console.error('未提供有效的p参数或url参数');
      }
      wx.showToast({
        title: '跳转失败',
        icon: 'error'
      });
      // 2秒后返回
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },
  
  // 添加一个共用的跳转方法
  performNavigation(finalUrl: string) {
    wx.navigateTo({
      url: finalUrl,
      success: () => {
        console.log('navigateTo 成功');
      },
      fail: (err) => {
        console.log('navigateTo 失败，尝试 redirectTo', err);
        // 如果 navigateTo 失败，尝试 redirectTo
        wx.redirectTo({
          url: finalUrl,
          success: () => {
            console.log('redirectTo 成功');
          },
          fail: (redirectErr) => {
            console.log('redirectTo 失败，尝试 switchTab', redirectErr);
            // 如果 redirectTo 也失败，尝试 switchTab
            wx.switchTab({
              url: finalUrl,
              success: () => {
                console.log('switchTab 成功');
              },
              fail: (switchErr) => {
                console.error('所有跳转方式都失败:', {
                  navigateToError: err,
                  redirectToError: redirectErr,
                  switchTabError: switchErr
                });
                wx.showToast({
                  title: '跳转失败',
                  icon: 'error'
                });
              }
            });
          }
        });
      }
    });
  },
  
  //解密
  async decrypt(encrypted: string) {
    try {
      const res = await Api.common.decrypt(encrypted);
      return res;
    } catch (error) {
      console.error('解密出错:', error);
      wx.showToast({
        title: '解密失败',
        icon: 'error'
      });
      throw error; // 抛出错误以便上层捕获
    }
  },
  
  // 添加一个新方法来处理参数转换
  convertParamsToArray(params: object): Array<{key: string, value: string}> {
    const result: Array<{key: string, value: string}> = [];
    
    if (!params) return result;
    
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        let value = params[key];
        
        // 如果值是对象，将其转换为JSON字符串
        if (typeof value === 'object' && value !== null) {
          try {
            value = JSON.stringify(value, null, 2);
          } catch (e) {
            value = '[复杂对象]';
          }
        }
        
        result.push({
          key: key,
          value: String(value)
        });
      }
    }
    
    return result;
  },
  
}); 
<!-- 引入顶部导航栏 -->
<nav-bar title="uCharts图表示例" showBack="{{true}}" showHome="{{true}}"></nav-bar>

<view class="container">
  <view class="chart-container">
    <view class="chart-title">图表展示</view>
    
    <!-- 图表类型选择器 -->
    <view class="chart-type-selector">
      <view class="selector-label">选择图表类型：</view>
      <view class="selector-buttons">
        <!-- 常规图表 -->
        <view class="type-btn {{currentChart === 'column' ? 'active' : ''}}" bindtap="switchChartType" data-type="column">柱状图</view>
        <view class="type-btn {{currentChart === 'line' ? 'active' : ''}}" bindtap="switchChartType" data-type="line">折线图</view>
        <view class="type-btn {{currentChart === 'area' ? 'active' : ''}}" bindtap="switchChartType" data-type="area">区域图</view>
        <view class="type-btn {{currentChart === 'bar' ? 'active' : ''}}" bindtap="switchChartType" data-type="bar">条状图</view>
        <view class="type-btn {{currentChart === 'mount' ? 'active' : ''}}" bindtap="switchChartType" data-type="mount">山峰图</view>
        
        <!-- 饼图类 -->
        <view class="type-btn {{currentChart === 'pie' ? 'active' : ''}}" bindtap="switchChartType" data-type="pie">饼图</view>
        <view class="type-btn {{currentChart === 'ring' ? 'active' : ''}}" bindtap="switchChartType" data-type="ring">圆环图</view>
        <view class="type-btn {{currentChart === 'rose' ? 'active' : ''}}" bindtap="switchChartType" data-type="rose">玫瑰图</view>
        
        <!-- 特殊图表 -->
        <view class="type-btn {{currentChart === 'radar' ? 'active' : ''}}" bindtap="switchChartType" data-type="radar">雷达图</view>
        <view class="type-btn {{currentChart === 'gauge' ? 'active' : ''}}" bindtap="switchChartType" data-type="gauge">仪表盘</view>
        <view class="type-btn {{currentChart === 'funnel' ? 'active' : ''}}" bindtap="switchChartType" data-type="funnel">漏斗图</view>
        <view class="type-btn {{currentChart === 'word' ? 'active' : ''}}" bindtap="switchChartType" data-type="word">词云图</view>
        <view class="type-btn {{currentChart === 'map' ? 'active' : ''}}" bindtap="switchChartType" data-type="map">地图</view>
        <view class="type-btn {{currentChart === 'arcbar' ? 'active' : ''}}" bindtap="switchChartType" data-type="arcbar">圆弧进度条</view>
        
        <!-- 坐标散点图 -->
        <view class="type-btn {{currentChart === 'scatter' ? 'active' : ''}}" bindtap="switchChartType" data-type="scatter">散点图</view>
        <view class="type-btn {{currentChart === 'bubble' ? 'active' : ''}}" bindtap="switchChartType" data-type="bubble">气泡图</view>
        
        <!-- 高级图表 -->
        <view class="type-btn {{currentChart === 'candle' ? 'active' : ''}}" bindtap="switchChartType" data-type="candle">K线图</view>
        <view class="type-btn {{currentChart === 'mix' ? 'active' : ''}}" bindtap="switchChartType" data-type="mix">混合图</view>
        <view class="type-btn {{currentChart === 'tline' ? 'active' : ''}}" bindtap="switchChartType" data-type="tline">时间轴折线</view>
        <view class="type-btn {{currentChart === 'tarea' ? 'active' : ''}}" bindtap="switchChartType" data-type="tarea">时间轴区域</view>
      </view>
    </view>
    
    <!-- 添加qiun-wx-ucharts组件 -->
    <view class="chart-wrapper {{showLoading ? 'loading' : ''}}" style="{{layoutStyle}}">
      <qiun-wx-ucharts 
        type="{{currentChart}}"
        canvas2d="{{true}}"
        canvasId="ucharts-demo"
        chartData="{{chartData}}"
        opts="{{opts}}"
        loadingType="1"
        errorShow="{{true}}"
        bind:getIndex="_getIndex" 
        bind:error="_error" 
        bind:complete="_complete" />
      <view class="loading-box" wx:if="{{showLoading}}">
        <view class="loading-icon"></view>
        <view class="loading-text">图表加载中...</view>
      </view>
    </view>
    
    <view class="action-btn-container">
      <button class="update-btn" bindtap="updateChart">更新数据</button>
    </view>
    
    <view class="chart-tips">
      <view class="tips-title">图表说明</view>
      <view class="tips-item">• 该示例展示了多种图表类型的使用</view>
      <view class="tips-item">• 可通过点击上方按钮切换不同图表类型</view>
      <view class="tips-item">• 可通过更新数据按钮动态刷新图表</view>
      <view class="tips-item">• 支持点击图表数据查看详情</view>
      <view class="tips-item">• 点击图例可以切换数据系列的显示/隐藏</view>
    </view>
  </view>
</view>

<!-- 引入底部菜单栏 -->
<tab-bar currentTab="0"></tab-bar> 
.content-scroll {
  box-sizing: border-box;
  padding: 20rpx;
}

.no-scroll {
  overflow: hidden;
}

/* 个人信息头部 */
.profile-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  overflow: hidden;
}

.avatar-btn {
  padding: 0;
  margin: 0;
  width: 100% !important;
  max-width: 140rpx !important;
  height: 140rpx;
  border-radius: 70rpx;
  background-color: transparent;
  line-height: normal;
  position: relative;
  overflow: hidden;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

.avatar-btn:active {
  opacity: 0.8;
}

.avatar-btn::after {
  border: none !important;
  width: 100% !important;
  height: 100% !important;
  left: 0 !important;
  right: 0 !important;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  border: 4rpx solid #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}

.avatar-edit-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  border-bottom-left-radius: 70rpx;
  border-bottom-right-radius: 70rpx;
  line-height: 1;
  padding-top: 0;
  padding-bottom: 8rpx;
}

.user-info {
  flex: 1;
}

.nickname-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.nickname-input {
  flex: 1;
  font-size: 36rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #e0e0e0;
  padding: 6rpx 0;
  margin-right: 10rpx;
}

/* 签名标签 */
.signature-tags {
  margin-bottom: 20rpx;
}

.signature-tag {
  background-color: #f0f8ff;
  color: #4a90e2;
  margin-bottom: 10rpx;
}

.add-signature-tag {
  background-color: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  margin-bottom: 10rpx;
  display: inline-flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border: 1rpx dashed #4a90e2;
}

.add-signature-tag text {
  margin-left: 4rpx;
  font-size: 24rpx;
}

.add-signature-tag mp-icon {
  margin-right: -2rpx;
}

.signature-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  margin-bottom: 10rpx;
  padding-left: 6rpx;
}

.signature-input-wrapper {
  position: relative;
  margin-bottom: 20rpx;
}

.signature-input {
  font-size: 28rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 80rpx;
  vertical-align: middle;
}

.signature-input::placeholder {
  line-height: 80rpx;
  color: #999;
  vertical-align: middle;
}

.signature-placeholder {
  line-height: 80rpx !important;
  color: #999 !important;
  vertical-align: middle !important;
  font-size: 28rpx !important;
}

.signature-input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.signature-input-confirm, .signature-input-cancel {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.signature-input-confirm {
  background-color: #4a90e2;
  color: white;
}

.signature-input-cancel {
  background-color: #f0f0f0;
  color: #666;
}

.user-type {
  display: flex;
  align-items: center;
}

.type-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  display: inline-block;
}

.type-tag.artist {
  background-color: #ffeceb;
  color: #ff6b61;
}

.type-tag.studio {
  background-color: #e6f7ff;
  color: #1890ff;
}

.type-tag.user {
  background-color: #f0f0f0;
  color: #666;
}

.level-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #fff4e0;
  color: #ffab2b;
  display: flex;
  align-items: center;
}

/* 卡片样式 */
.card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
  font-weight: normal;
}

.edit-btn {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-content {
  padding: 20rpx 30rpx;
}

/* 表单项样式 */
.form-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: center;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  height: 70rpx;
  border-bottom: 1rpx solid #e0e0e0;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
}

/* 展开/收起按钮 */
.expand-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0;
  padding: 12rpx 0;
  color: #666;
  font-size: 26rpx;
  border-top: 1rpx dashed #e0e0e0;
  border-bottom: 1rpx dashed #e0e0e0;
}

.expand-btn text {
  margin-right: 8rpx;
}

.arrow-up {
  transform: rotate(-90deg);
}

.arrow-down {
  transform: rotate(90deg);
}

.more-info {
  margin-top: 10rpx;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 选择器样式 */
.picker-view {
  flex: 1;
  height: 70rpx;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
  font-size: 28rpx;
  color: #333;
}

/* 标签编辑 */
.tags-edit-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.tag-delete {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #999;
}

.empty-tag {
  font-size: 24rpx;
  color: #999;
}

.add-tag {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.add-tag text {
  margin-left: 8rpx;
}

/* 合同状态 */
.contract-status {
  font-size: 28rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.contract-status.pending {
  background-color: #fff4e0;
  color: #ffab2b;
}

.contract-status.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.contract-status.expired {
  background-color: #f5f5f5;
  color: #999;
}

.contract-status.terminated {
  background-color: #ffeceb;
  color: #ff6b61;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  margin-bottom: 40rpx;
  padding-bottom: 60rpx;
}

.save-button {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  background-color: #4CAF50;
  color: white;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.cancel-button {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  background-color: #f0f0f0;
  color: #666;
  font-size: 30rpx;
}

/* 弹窗样式 */
.picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: relative;
  width: 80%;
  max-height: 80%;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.picker-body {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.checkbox-item text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

.picker-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.picker-confirm {
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #ff6b61;
  color: white;
  font-size: 30rpx;
}

// app.ts
/**
 * 小程序布局高度说明
 * 
 * 1. 导航区域高度
 * +-----------------+
 * | 状态栏          | statusBarHeight (系统状态栏，通常20px，异形屏会更高)
 * +-----------------+
 * | 导航内容        | navigationHeight (整个导航区域，含状态栏，通常88px)
 * +-----------------+ 
 * 计算公式：navigationHeight = statusBarHeight + (胶囊顶部 - 状态栏高度) * 2 + 胶囊高度
 * 
 * 2. 胶囊按钮信息
 * - menuButtonHeight: 32px (胶囊按钮高度)
 * - menuButtonTop: 24px (胶囊按钮顶部距离)
 * - menuButtonWidth: 87px (胶囊按钮宽度)
 * - menuButtonRight: 10px (胶囊按钮右侧距离)
 * 
 * 3. 内容区域高度
 * - contentHeight: 屏幕高度 - navigationHeight - tabBarTotalHeight
 * - viewportHeight: windowHeight - navigationHeight - tabBarTotalHeight
 * 
 * 4. 底部导航高度
 * +------------------+
 * | TabBar内容       | tabBarContentHeight (底部导航内容区域，默认98px)
 * +------------------+
 * | 安全区域         | safeAreaBottom (底部安全区域，最小34px)
 * +------------------+
 * 计算公式：tabBarTotalHeight = tabBarContentHeight + safeAreaBottom
 * 
 * 5. 安全区域
 * - safeAreaTop: 0px (顶部安全区域，通常等于状态栏高度)
 * - safeAreaBottom: 34px+ (底部安全区域，异形屏更高)
 * - safeAreaLeft: 0px (左侧安全区域)
 * - safeAreaRight: 0px (右侧安全区域)
 * 
 * 6. 默认值配置
 * - DEFAULT_TAB_BAR_HEIGHT: 98px (底部导航内容高度)
 * - MIN_SAFE_AREA_BOTTOM: 34px (最小底部安全区域)
 * - DEFAULT_STATUS_BAR_HEIGHT: 20px (默认状态栏高度)
 * - DEFAULT_NAV_BAR_HEIGHT: 88px (默认导航栏总高度)
 * - DEFAULT_MENU_BUTTON_HEIGHT: 32px (默认胶囊按钮高度)
 * 
 * 注意事项：
 * 1. 异形屏适配：需要考虑刘海屏、挖孔屏等特殊情况
 * 2. 横屏适配：部分高度值在横屏时会有变化
 * 3. 机型兼容：不同机型可能有细微差异，建议使用 layoutUtil 动态计算
 */

import { layoutUtil } from './utils/layout'

interface GlobalData {
  layoutUtil: typeof layoutUtil;
  systemInfo: WechatMiniprogram.GetSystemInfoSyncResult;
  launchOptions?: WechatMiniprogram.LaunchOptionsApp;
  sceneParams?: {
    scene: string;
    query: Record<string, string>;
  };
}

interface IAppOption {
  globalData: GlobalData;
  getLayoutUtil(): typeof layoutUtil;
  getSystemInfo(): WechatMiniprogram.GetSystemInfoSyncResult;
  getSceneParams(): GlobalData['sceneParams'];
}

interface AppInstance extends IAppOption {
}

App<AppInstance>({
  globalData: {
    layoutUtil,
    systemInfo: {} as WechatMiniprogram.GetSystemInfoSyncResult,
    launchOptions: undefined,
    sceneParams: undefined
  },

  onLaunch(options) {
    try {
      // 初始化系统信息
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      
      // 保存启动参数
      this.globalData.launchOptions = options;
      
      // 处理场景值和参数
      // this._handleSceneParams(options);
      
    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  },

  onShow(options) {
  },


  /**
   * 获取布局工具实例
   */
  getLayoutUtil() {
    return this.globalData.layoutUtil;
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    return this.globalData.systemInfo;
  },

  /**
   * 获取场景参数
   */
  getSceneParams() {
    return this.globalData.sceneParams;
  },

  // 全局登录方法
  showLoginModal(options: { user_id?: number, source?: string } = {}) {
    // 存储登录数据
    if (options.user_id) {
      wx.setStorageSync('redirectUserIdData', {
        user_id: options.user_id,
        source: options.source || ''
      });
    }
    
    // 找到一个在前台的页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.selectComponent) {
      // 尝试直接在当前页面找到登录组件并显示
      const loginModal = currentPage.selectComponent('#login-modal');
      if (loginModal) {
        loginModal.setData({
          show: true,
          bindUserId: options.user_id,
          bindSource: options.source,
          modalTitle: options.user_id ? '请重新登录绑定账号' : '当前内容需要登录后才能使用'
        });
        return;
      }
    }
    
    // 如果找不到组件，尝试跳转到登录页面
    wx.navigateTo({
      url: '/pages/login/index',
      fail: (err) => {
        console.error('导航到登录页面失败:', err);
        wx.showToast({
          title: '登录功能暂时不可用',
          icon: 'none'
        });
      }
    });
  }
});
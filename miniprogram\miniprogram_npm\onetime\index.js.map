{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nconst mimicFn = require('mimic-fn');\n\nmodule.exports = (fn, opts) => {\n\t// TODO: Remove this in v3\n\tif (opts === true) {\n\t\tthrow new TypeError('The second argument is now an options object');\n\t}\n\n\tif (typeof fn !== 'function') {\n\t\tthrow new TypeError('Expected a function');\n\t}\n\n\topts = opts || {};\n\n\tlet ret;\n\tlet called = false;\n\tconst fnName = fn.displayName || fn.name || '<anonymous>';\n\n\tconst onetime = function () {\n\t\tif (called) {\n\t\t\tif (opts.throw === true) {\n\t\t\t\tthrow new Error(`Function \\`${fnName}\\` can only be called once`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tcalled = true;\n\t\tret = fn.apply(this, arguments);\n\t\tfn = null;\n\n\t\treturn ret;\n\t};\n\n\tmimicFn(onetime, fn);\n\n\treturn onetime;\n};\n"]}
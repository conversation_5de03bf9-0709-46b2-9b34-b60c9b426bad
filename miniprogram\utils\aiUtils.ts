// aiUtils.ts
/**
 * 提取错误信息的函数
 * @param error 错误对象
 * @returns 格式化的错误消息
 */
export function extractErrorMessage(error: any): string {
  let errorMessage = '未知错误';
  
  if (error) {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error.errMsg) {
      errorMessage = error.errMsg;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.error && error.error.message) {
      errorMessage = error.error.message;
    } else if (error.data && error.data.error && error.data.error.message) {
      errorMessage = error.data.error.message;
    }
  }
  
  return errorMessage;
}

/**
 * 显示错误信息对话框
 * @param error 错误对象
 * @param title 对话框标题
 * @returns 格式化的错误消息
 */
export function showErrorMessage(error: any, title: string = '操作失败'): string {
  const errorMessage = extractErrorMessage(error);
  
  wx.showModal({
    title: title,
    content: errorMessage,
    showCancel: false
  });
  
  return errorMessage;
}

/**
 * 更新组件进度条状态
 * @param component 组件实例
 * @param percent 进度百分比
 * @param text 进度文本
 */
export function updateProgress(component: any, percent: number, text: string) {
  wx.hideLoading();
  // 只允许递增
  const lastProgress = component.data.progress || 0;
  const newProgress = Math.max(lastProgress, percent);
  component.setData({
    progress: newProgress,
    progressPercent: newProgress,
    progressText: text
  });
  wx.showLoading({ title: text })
}

/**
 * TabBar 状态处理函数
 * @param component 组件实例
 * @param data TabBar数据
 */
export function handleTabBarChange(component: any, data: { 
  isCollapsed: boolean,
  expandedHeight: number,
  collapsedHeight: number,
  currentHeight: number 
}) {
  component.setData({
    isTabBarCollapsed: data.isCollapsed,
    tabBarHeight: data.currentHeight
  });
}

/**
 * 图片转 base64 函数
 * @param imagePath 图片路径
 * @returns Promise 包含base64编码的图片
 */
export function imageToBase64(imagePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (imagePath.startsWith('data:image')) {
        resolve(imagePath); // 已经是 base64 了
        return;
      }
      
      const fileContent = wx.getFileSystemManager().readFileSync(imagePath, 'base64') as string;
      const base64Url = `data:image/jpeg;base64,${fileContent}`;
      resolve(base64Url);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 保存图片到相册
 * @param imageUrl 图片URL
 * @returns Promise
 */
export function saveImageToAlbum(imageUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '保存中...',
    });

    // 下载图片
    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
              resolve();
            },
            fail: (error) => {
              wx.hideLoading();
              // 如果是用户拒绝授权
              if (error.errMsg.indexOf('auth deny') !== -1 || error.errMsg.indexOf('auth denied') !== -1) {
                wx.showModal({
                  title: '提示',
                  content: '请授权保存图片到相册',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
              reject(error);
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载图片失败',
            icon: 'none'
          });
          reject(new Error('图片下载状态码错误: ' + res.statusCode));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  });
} 


/**
 * 通用按钮冷却倒计时工具
 * @param page 页面实例（this）
 * @param key 按钮key（如'expand'）
 * @param seconds 倒计时秒数
 */
export function startButtonCooldown(page: any, key: string, seconds: number) {
  if (!page || !key) return;
  // 初始化data结构
  if (!page.data.buttonCooldowns) {
    page.setData({ buttonCooldowns: { [key]: { cooldown: 0, disabled: false } } });
  } else if (!page.data.buttonCooldowns[key]) {
    const bcs = { ...page.data.buttonCooldowns };
    bcs[key] = { cooldown: 0, disabled: false };
    page.setData({ buttonCooldowns: bcs });
  }
  // 清理旧timer
  if (!page._buttonCooldownTimers) page._buttonCooldownTimers = {};
  if (page._buttonCooldownTimers[key]) clearInterval(page._buttonCooldownTimers[key]);
  // 设置初始状态
  const bcs = { ...page.data.buttonCooldowns };
  bcs[key] = { cooldown: seconds, disabled: true };
  page.setData({ buttonCooldowns: bcs });
  // 启动倒计时
  page._buttonCooldownTimers[key] = setInterval(() => {
    const bcs2 = { ...page.data.buttonCooldowns };
    bcs2[key].cooldown--;
    if (bcs2[key].cooldown <= 0) {
      clearInterval(page._buttonCooldownTimers[key]);
      bcs2[key].disabled = false;
      bcs2[key].cooldown = 0;
      page.setData({ buttonCooldowns: bcs2 });
      return;
    }
    page.setData({ buttonCooldowns: bcs2 });
  }, 1000);
}
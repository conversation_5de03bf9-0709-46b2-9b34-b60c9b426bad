import { ThemeConfig } from '../types/config';
import { configManager } from './configManager';

class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: ThemeConfig | null = null;

  private constructor() {
    // 订阅配置更新
    configManager.subscribe((config) => {
      this.currentTheme = config.theme;
      this.applyTheme();
    });
  }

  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  async initialize(): Promise<void> {
    const config = configManager.getConfig();
    if (config) {
      this.currentTheme = config.theme;
      this.applyTheme();
    }
  }

  private applyTheme(): void {
    if (!this.currentTheme) return;

    // 更新CSS变量
    const root = document.documentElement;
    root.style.setProperty('--primary-color', this.currentTheme.primaryColor);
    root.style.setProperty('--secondary-color', this.currentTheme.secondaryColor);
    root.style.setProperty('--background-color', this.currentTheme.backgroundColor);
    
    // 字体大小
    root.style.setProperty('--font-size-small', this.currentTheme.fontSize.small);
    root.style.setProperty('--font-size-normal', this.currentTheme.fontSize.normal);
    root.style.setProperty('--font-size-large', this.currentTheme.fontSize.large);
    
    // 间距
    root.style.setProperty('--spacing-small', this.currentTheme.spacing.small);
    root.style.setProperty('--spacing-normal', this.currentTheme.spacing.normal);
    root.style.setProperty('--spacing-large', this.currentTheme.spacing.large);
  }

  getThemeValue<K extends keyof ThemeConfig>(key: K): ThemeConfig[K] | null {
    if (!this.currentTheme) return null;
    return this.currentTheme[key];
  }

  getFontSize(size: 'small' | 'normal' | 'large'): string {
    if (!this.currentTheme) return '';
    return this.currentTheme.fontSize[size];
  }

  getSpacing(size: 'small' | 'normal' | 'large'): string {
    if (!this.currentTheme) return '';
    return this.currentTheme.spacing[size];
  }

  async updateTheme(newTheme: Partial<ThemeConfig>): Promise<void> {
    const config = configManager.getConfig();
    if (config) {
      await configManager.updateConfig({
        theme: {
          ...config.theme,
          ...newTheme
        }
      });
    }
  }
}

export const themeManager = ThemeManager.getInstance(); 
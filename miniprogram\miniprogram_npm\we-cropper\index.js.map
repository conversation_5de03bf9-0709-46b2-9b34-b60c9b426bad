{"version": 3, "sources": ["we-cropper.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/**\n * we-cropper v1.4.0\n * (c) 2021 dlhandsome\n * @license MIT\n */\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.WeCropper = factory());\n}(this, (function () { \n\nvar device = void 0;\nvar TOUCH_STATE = ['touchstarted', 'touchmoved', 'touchended'];\nvar adaptAPI = {\n  strokeStyle: 'setStrokeStyle',\n  fillStyle: 'setFillStyle',\n  lineWidth: 'setLineWidth'\n};\n\nfunction firstLetterUpper (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\nfunction setTouchState (instance) {\n  var arg = [], len = arguments.length - 1;\n  while ( len-- > 0 ) arg[ len ] = arguments[ len + 1 ];\n\n  TOUCH_STATE.forEach(function (key, i) {\n    if (arg[i] !== undefined) {\n      instance[key] = arg[i];\n    }\n  });\n}\n\nfunction validator (instance, o) {\n  Object.defineProperties(instance, o);\n}\n\nfunction\tgetDevice () {\n  if (!device) {\n    device = wx.getSystemInfoSync();\n  }\n  return device\n}\n\nfunction adapt2d (context, handle, value) {\n  if (context.type === '2d') {\n    context.ctx[handle] = value;\n  } else {\n    context.ctx[adaptAPI[handle]](value);\n  }\n}\n\nvar tmp = {};\n\nvar ref = getDevice();\nvar pixelRatio = ref.pixelRatio;\n\nvar DEFAULT = {\n  id: {\n    default: 'cropper',\n    get: function get () {\n      return tmp.id\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'string') {\n        console.error((\"id：\" + value + \" is invalid\"));\n      }\n      tmp.id = value;\n    }\n  },\n  width: {\n    default: 750,\n    get: function get () {\n      return tmp.width\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'number') {\n        console.error((\"width：\" + value + \" is invalid\"));\n      }\n      tmp.width = value;\n    }\n  },\n  height: {\n    default: 750,\n    get: function get () {\n      return tmp.height\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'number') {\n        console.error((\"height：\" + value + \" is invalid\"));\n      }\n      tmp.height = value;\n    }\n  },\n  pixelRatio: {\n    default: pixelRatio,\n    get: function get () {\n      return tmp.pixelRatio\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'number') {\n        console.error((\"pixelRatio：\" + value + \" is invalid\"));\n      }\n      tmp.pixelRatio = value;\n    }\n  },\n  scale: {\n    default: 2.5,\n    get: function get () {\n      return tmp.scale\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'number') {\n        console.error((\"scale：\" + value + \" is invalid\"));\n      }\n      tmp.scale = value;\n    }\n  },\n  zoom: {\n    default: 5,\n    get: function get () {\n      return tmp.zoom\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'number') {\n        console.error((\"zoom：\" + value + \" is invalid\"));\n      } else if (value < 0 || value > 10) {\n        console.error(\"zoom should be ranged in 0 ~ 10\");\n      }\n      tmp.zoom = value;\n    }\n  },\n  src: {\n    default: '',\n    get: function get () {\n      return tmp.src\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'string') {\n        console.error((\"src：\" + value + \" is invalid\"));\n      }\n      tmp.src = value;\n    }\n  },\n  cut: {\n    default: {},\n    get: function get () {\n      return tmp.cut\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'object') {\n        console.error((\"cut：\" + value + \" is invalid\"));\n      }\n      tmp.cut = value;\n    }\n  },\n  boundStyle: {\n    default: {},\n    get: function get () {\n      return tmp.boundStyle\n    },\n    set: function set (value) {\n      if (typeof (value) !== 'object') {\n        console.error((\"boundStyle：\" + value + \" is invalid\"));\n      }\n      tmp.boundStyle = value;\n    }\n  },\n  onReady: {\n    default: null,\n    get: function get () {\n      return tmp.ready\n    },\n    set: function set (value) {\n      tmp.ready = value;\n    }\n  },\n  onBeforeImageLoad: {\n    default: null,\n    get: function get () {\n      return tmp.beforeImageLoad\n    },\n    set: function set (value) {\n      tmp.beforeImageLoad = value;\n    }\n  },\n  onImageLoad: {\n    default: null,\n    get: function get () {\n      return tmp.imageLoad\n    },\n    set: function set (value) {\n      tmp.imageLoad = value;\n    }\n  },\n  onBeforeDraw: {\n    default: null,\n    get: function get () {\n      return tmp.beforeDraw\n    },\n    set: function set (value) {\n      tmp.beforeDraw = value;\n    }\n  }\n};\n\nvar ref$1 = getDevice();\nvar windowWidth = ref$1.windowWidth;\n\nfunction prepare () {\n  var self = this;\n\n  // v1.4.0 版本中将不再自动绑定we-cropper实例\n  self.attachPage = function () {\n    var pages = getCurrentPages();\n    // 获取到当前page上下文\n    var pageContext = pages[pages.length - 1];\n    // 把this依附在Page上下文的wecropper属性上，便于在page钩子函数中访问\n    Object.defineProperty(pageContext, 'wecropper', {\n      get: function get () {\n        console.warn(\n          'Instance will not be automatically bound to the page after v1.4.0\\n\\n' +\n          'Please use a custom instance name instead\\n\\n' +\n          'Example: \\n' +\n          'this.mycropper = new WeCropper(options)\\n\\n' +\n          '// ...\\n' +\n          'this.mycropper.getCropperImage()'\n        );\n        return self\n      },\n      configurable: true\n    });\n  };\n\n  self.createCtx = function () {\n    var id = self.id;\n    var targetId = self.targetId;\n\n    if (id) {\n      self.ctx = self.ctx || wx.createCanvasContext(id);\n      self.targetCtx = self.targetCtx || wx.createCanvasContext(targetId);\n\n      // 2d 没有这个方法\n      if (typeof self.ctx.setStrokeStyle !== 'function') {\n        self.type = '2d';\n      }\n    } else {\n      console.error(\"constructor: create canvas context failed, 'id' must be valuable\");\n    }\n  };\n\n  self.deviceRadio = windowWidth / 750;\n}\n\n/**\n * String type check\n */\n\n/**\n * Number type check\n */\n\n/**\n * Array type check\n */\n\n/**\n * undefined type check\n */\n\n\n\n\n\n/**\n * Function type check\n */\nvar isFunc = function (v) { return typeof v === 'function'; };\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\n\n\n\n\n\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/)\n */\n\n\n/**\n * Check if val is a valid array index.\n */\n\nvar EVENT_TYPE = ['ready', 'beforeImageLoad', 'beforeDraw', 'imageLoad'];\n\nfunction observer () {\n  var self = this;\n\n  self.on = function (event, fn) {\n    if (EVENT_TYPE.indexOf(event) > -1) {\n      if (isFunc(fn)) {\n        event === 'ready'\n          ? fn(self)\n          : self[(\"on\" + (firstLetterUpper(event)))] = fn;\n      }\n    } else {\n      console.error((\"event: \" + event + \" is invalid\"));\n    }\n    return self\n  };\n}\n\nfunction wxPromise (fn) {\n  return function (obj) {\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n\n    if ( obj === void 0 ) obj = {};\n    return new Promise(function (resolve, reject) {\n      obj.success = function (res) {\n        resolve(res);\n      };\n      obj.fail = function (err) {\n        reject(err);\n      };\n      fn.apply(void 0, [ obj ].concat( args ));\n    })\n  }\n}\n\nfunction draw (ctx, reserve) {\n  if ( reserve === void 0 ) reserve = false;\n\n  return new Promise(function (resolve) {\n    ctx.draw && ctx.draw(reserve, resolve);\n  })\n}\n\nvar getImageInfo = wxPromise(wx.getImageInfo);\n\nvar canvasToTempFilePath = wxPromise(wx.canvasToTempFilePath);\n\nvar loadCanvasImage = function (context, src) {\n  return new Promise(function (resolve, reject) {\n    if (context.type === '2d') {\n      var img = context.canvas.createImage();\n      img.onload = function () {\n        resolve(img);\n      };\n      img.onerror = function (e) {\n        reject(e);\n      };\n      img.src = src;\n    } else {\n      resolve(src);\n    }\n  })\n};\n\nvar commonjsGlobal = typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\n\n\n\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar base64 = createCommonjsModule(function (module, exports) {\n/*! http://mths.be/base64 v0.1.0 by @mathias | MIT license */\n(function(root) {\n\n\t// Detect free variables `exports`.\n\tvar freeExports = 'object' == 'object' && exports;\n\n\t// Detect free variable `module`.\n\tvar freeModule = 'object' == 'object' && module &&\n\t\tmodule.exports == freeExports && module;\n\n\t// Detect free variable `global`, from Node.js or Browserified code, and use\n\t// it as `root`.\n\tvar freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal;\n\tif (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {\n\t\troot = freeGlobal;\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tvar InvalidCharacterError = function(message) {\n\t\tthis.message = message;\n\t};\n\tInvalidCharacterError.prototype = new Error;\n\tInvalidCharacterError.prototype.name = 'InvalidCharacterError';\n\n\tvar error = function(message) {\n\t\t// Note: the error messages used throughout this file match those used by\n\t\t// the native `atob`/`btoa` implementation in Chromium.\n\t\tthrow new InvalidCharacterError(message);\n\t};\n\n\tvar TABLE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\t// http://whatwg.org/html/common-microsyntaxes.html#space-character\n\tvar REGEX_SPACE_CHARACTERS = /[\\t\\n\\f\\r ]/g;\n\n\t// `decode` is designed to be fully compatible with `atob` as described in the\n\t// HTML Standard. http://whatwg.org/html/webappapis.html#dom-windowbase64-atob\n\t// The optimized base64-decoding algorithm used is based on @atk’s excellent\n\t// implementation. https://gist.github.com/atk/1020396\n\tvar decode = function(input) {\n\t\tinput = String(input)\n\t\t\t.replace(REGEX_SPACE_CHARACTERS, '');\n\t\tvar length = input.length;\n\t\tif (length % 4 == 0) {\n\t\t\tinput = input.replace(/==?$/, '');\n\t\t\tlength = input.length;\n\t\t}\n\t\tif (\n\t\t\tlength % 4 == 1 ||\n\t\t\t// http://whatwg.org/C#alphanumeric-ascii-characters\n\t\t\t/[^+a-zA-Z0-9/]/.test(input)\n\t\t) {\n\t\t\terror(\n\t\t\t\t'Invalid character: the string to be decoded is not correctly encoded.'\n\t\t\t);\n\t\t}\n\t\tvar bitCounter = 0;\n\t\tvar bitStorage;\n\t\tvar buffer;\n\t\tvar output = '';\n\t\tvar position = -1;\n\t\twhile (++position < length) {\n\t\t\tbuffer = TABLE.indexOf(input.charAt(position));\n\t\t\tbitStorage = bitCounter % 4 ? bitStorage * 64 + buffer : buffer;\n\t\t\t// Unless this is the first of a group of 4 characters…\n\t\t\tif (bitCounter++ % 4) {\n\t\t\t\t// …convert the first 8 bits to a single ASCII character.\n\t\t\t\toutput += String.fromCharCode(\n\t\t\t\t\t0xFF & bitStorage >> (-2 * bitCounter & 6)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t};\n\n\t// `encode` is designed to be fully compatible with `btoa` as described in the\n\t// HTML Standard: http://whatwg.org/html/webappapis.html#dom-windowbase64-btoa\n\tvar encode = function(input) {\n\t\tinput = String(input);\n\t\tif (/[^\\0-\\xFF]/.test(input)) {\n\t\t\t// Note: no need to special-case astral symbols here, as surrogates are\n\t\t\t// matched, and the input is supposed to only contain ASCII anyway.\n\t\t\terror(\n\t\t\t\t'The string to be encoded contains characters outside of the ' +\n\t\t\t\t'Latin1 range.'\n\t\t\t);\n\t\t}\n\t\tvar padding = input.length % 3;\n\t\tvar output = '';\n\t\tvar position = -1;\n\t\tvar a;\n\t\tvar b;\n\t\tvar c;\n\t\tvar buffer;\n\t\t// Make sure any padding is handled outside of the loop.\n\t\tvar length = input.length - padding;\n\n\t\twhile (++position < length) {\n\t\t\t// Read three bytes, i.e. 24 bits.\n\t\t\ta = input.charCodeAt(position) << 16;\n\t\t\tb = input.charCodeAt(++position) << 8;\n\t\t\tc = input.charCodeAt(++position);\n\t\t\tbuffer = a + b + c;\n\t\t\t// Turn the 24 bits into four chunks of 6 bits each, and append the\n\t\t\t// matching character for each of them to the output.\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 18 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer >> 12 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer >> 6 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer & 0x3F)\n\t\t\t);\n\t\t}\n\n\t\tif (padding == 2) {\n\t\t\ta = input.charCodeAt(position) << 8;\n\t\t\tb = input.charCodeAt(++position);\n\t\t\tbuffer = a + b;\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 10) +\n\t\t\t\tTABLE.charAt((buffer >> 4) & 0x3F) +\n\t\t\t\tTABLE.charAt((buffer << 2) & 0x3F) +\n\t\t\t\t'='\n\t\t\t);\n\t\t} else if (padding == 1) {\n\t\t\tbuffer = input.charCodeAt(position);\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 2) +\n\t\t\t\tTABLE.charAt((buffer << 4) & 0x3F) +\n\t\t\t\t'=='\n\t\t\t);\n\t\t}\n\n\t\treturn output;\n\t};\n\n\tvar base64 = {\n\t\t'encode': encode,\n\t\t'decode': decode,\n\t\t'version': '0.1.0'\n\t};\n\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof undefined == 'function' &&\n\t\ttypeof undefined.amd == 'object' &&\n\t\tundefined.amd\n\t) {\n\t\tundefined(function() {\n\t\t\treturn base64;\n\t\t});\n\t}\telse if (freeExports && !freeExports.nodeType) {\n\t\tif (freeModule) { // in Node.js or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = base64;\n\t\t} else { // in Narwhal or RingoJS v0.7.0-\n\t\t\tfor (var key in base64) {\n\t\t\t\tbase64.hasOwnProperty(key) && (freeExports[key] = base64[key]);\n\t\t\t}\n\t\t}\n\t} else { // in Rhino or a web browser\n\t\troot.base64 = base64;\n\t}\n\n}(commonjsGlobal));\n});\n\nfunction makeURI (strData, type) {\n  return 'data:' + type + ';base64,' + strData\n}\n\nfunction fixType (type) {\n  type = type.toLowerCase().replace(/jpg/i, 'jpeg');\n  var r = type.match(/png|jpeg|bmp|gif/)[0];\n  return 'image/' + r\n}\n\nfunction encodeData (data) {\n  var str = '';\n  if (typeof data === 'string') {\n    str = data;\n  } else {\n    for (var i = 0; i < data.length; i++) {\n      str += String.fromCharCode(data[i]);\n    }\n  }\n  return base64.encode(str)\n}\n\n/**\n * 获取图像区域隐含的像素数据\n * @param canvasId canvas标识\n * @param x 将要被提取的图像数据矩形区域的左上角 x 坐标\n * @param y 将要被提取的图像数据矩形区域的左上角 y 坐标\n * @param width 将要被提取的图像数据矩形区域的宽度\n * @param height 将要被提取的图像数据矩形区域的高度\n * @param done 完成回调\n */\nfunction getImageData (canvasId, x, y, width, height, done) {\n  wx.canvasGetImageData({\n    canvasId: canvasId,\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    success: function success (res) {\n      done(res, null);\n    },\n    fail: function fail (res) {\n      done(null, res);\n    }\n  });\n}\n\n/**\n * 生成bmp格式图片\n * 按照规则生成图片响应头和响应体\n * @param oData 用来描述 canvas 区域隐含的像素数据 { data, width, height } = oData\n * @returns {*} base64字符串\n */\nfunction genBitmapImage (oData) {\n  //\n  // BITMAPFILEHEADER: http://msdn.microsoft.com/en-us/library/windows/desktop/dd183374(v=vs.85).aspx\n  // BITMAPINFOHEADER: http://msdn.microsoft.com/en-us/library/dd183376.aspx\n  //\n  var biWidth = oData.width;\n  var biHeight\t= oData.height;\n  var biSizeImage = biWidth * biHeight * 3;\n  var bfSize = biSizeImage + 54; // total header size = 54 bytes\n\n  //\n  //  typedef struct tagBITMAPFILEHEADER {\n  //  \tWORD bfType;\n  //  \tDWORD bfSize;\n  //  \tWORD bfReserved1;\n  //  \tWORD bfReserved2;\n  //  \tDWORD bfOffBits;\n  //  } BITMAPFILEHEADER;\n  //\n  var BITMAPFILEHEADER = [\n    // WORD bfType -- The file type signature; must be \"BM\"\n    0x42, 0x4D,\n    // DWORD bfSize -- The size, in bytes, of the bitmap file\n    bfSize & 0xff, bfSize >> 8 & 0xff, bfSize >> 16 & 0xff, bfSize >> 24 & 0xff,\n    // WORD bfReserved1 -- Reserved; must be zero\n    0, 0,\n    // WORD bfReserved2 -- Reserved; must be zero\n    0, 0,\n    // DWORD bfOffBits -- The offset, in bytes, from the beginning of the BITMAPFILEHEADER structure to the bitmap bits.\n    54, 0, 0, 0\n  ];\n\n  //\n  //  typedef struct tagBITMAPINFOHEADER {\n  //  \tDWORD biSize;\n  //  \tLONG  biWidth;\n  //  \tLONG  biHeight;\n  //  \tWORD  biPlanes;\n  //  \tWORD  biBitCount;\n  //  \tDWORD biCompression;\n  //  \tDWORD biSizeImage;\n  //  \tLONG  biXPelsPerMeter;\n  //  \tLONG  biYPelsPerMeter;\n  //  \tDWORD biClrUsed;\n  //  \tDWORD biClrImportant;\n  //  } BITMAPINFOHEADER, *PBITMAPINFOHEADER;\n  //\n  var BITMAPINFOHEADER = [\n    // DWORD biSize -- The number of bytes required by the structure\n    40, 0, 0, 0,\n    // LONG biWidth -- The width of the bitmap, in pixels\n    biWidth & 0xff, biWidth >> 8 & 0xff, biWidth >> 16 & 0xff, biWidth >> 24 & 0xff,\n    // LONG biHeight -- The height of the bitmap, in pixels\n    biHeight & 0xff, biHeight >> 8 & 0xff, biHeight >> 16 & 0xff, biHeight >> 24 & 0xff,\n    // WORD biPlanes -- The number of planes for the target device. This value must be set to 1\n    1, 0,\n    // WORD biBitCount -- The number of bits-per-pixel, 24 bits-per-pixel -- the bitmap\n    // has a maximum of 2^24 colors (16777216, Truecolor)\n    24, 0,\n    // DWORD biCompression -- The type of compression, BI_RGB (code 0) -- uncompressed\n    0, 0, 0, 0,\n    // DWORD biSizeImage -- The size, in bytes, of the image. This may be set to zero for BI_RGB bitmaps\n    biSizeImage & 0xff, biSizeImage >> 8 & 0xff, biSizeImage >> 16 & 0xff, biSizeImage >> 24 & 0xff,\n    // LONG biXPelsPerMeter, unused\n    0, 0, 0, 0,\n    // LONG biYPelsPerMeter, unused\n    0, 0, 0, 0,\n    // DWORD biClrUsed, the number of color indexes of palette, unused\n    0, 0, 0, 0,\n    // DWORD biClrImportant, unused\n    0, 0, 0, 0\n  ];\n\n  var iPadding = (4 - ((biWidth * 3) % 4)) % 4;\n\n  var aImgData = oData.data;\n\n  var strPixelData = '';\n  var biWidth4 = biWidth << 2;\n  var y = biHeight;\n  var fromCharCode = String.fromCharCode;\n\n  do {\n    var iOffsetY = biWidth4 * (y - 1);\n    var strPixelRow = '';\n    for (var x = 0; x < biWidth; x++) {\n      var iOffsetX = x << 2;\n      strPixelRow += fromCharCode(aImgData[iOffsetY + iOffsetX + 2]) +\n        fromCharCode(aImgData[iOffsetY + iOffsetX + 1]) +\n        fromCharCode(aImgData[iOffsetY + iOffsetX]);\n    }\n\n    for (var c = 0; c < iPadding; c++) {\n      strPixelRow += String.fromCharCode(0);\n    }\n\n    strPixelData += strPixelRow;\n  } while (--y)\n\n  var strEncoded = encodeData(BITMAPFILEHEADER.concat(BITMAPINFOHEADER)) + encodeData(strPixelData);\n\n  return strEncoded\n}\n\n/**\n * 转换为图片base64\n * @param canvasId canvas标识\n * @param x 将要被提取的图像数据矩形区域的左上角 x 坐标\n * @param y 将要被提取的图像数据矩形区域的左上角 y 坐标\n * @param width 将要被提取的图像数据矩形区域的宽度\n * @param height 将要被提取的图像数据矩形区域的高度\n * @param type 转换图片类型\n * @param done 完成回调\n */\nfunction convertToImage (canvasId, x, y, width, height, type, done) {\n  if ( done === void 0 ) done = function () {};\n\n  if (type === undefined) { type = 'png'; }\n  type = fixType(type);\n  if (/bmp/.test(type)) {\n    getImageData(canvasId, x, y, width, height, function (data, err) {\n      var strData = genBitmapImage(data);\n      isFunc(done) && done(makeURI(strData, 'image/' + type), err);\n    });\n  } else {\n    console.error('暂不支持生成\\'' + type + '\\'类型的base64图片');\n  }\n}\n\nvar CanvasToBase64 = {\n  convertToImage: convertToImage,\n  // convertToPNG: function (width, height, done) {\n  //   return convertToImage(width, height, 'png', done)\n  // },\n  // convertToJPEG: function (width, height, done) {\n  //   return convertToImage(width, height, 'jpeg', done)\n  // },\n  // convertToGIF: function (width, height, done) {\n  //   return convertToImage(width, height, 'gif', done)\n  // },\n  convertToBMP: function (ref, done) {\n    if ( ref === void 0 ) ref = {};\n    var canvasId = ref.canvasId;\n    var x = ref.x;\n    var y = ref.y;\n    var width = ref.width;\n    var height = ref.height;\n    if ( done === void 0 ) done = function () {};\n\n    return convertToImage(canvasId, x, y, width, height, 'bmp', done)\n  }\n};\n\nfunction methods () {\n  var self = this;\n\n  var boundWidth = self.width; // 裁剪框默认宽度，即整个画布宽度\n  var boundHeight = self.height; // 裁剪框默认高度，即整个画布高度\n\n  var id = self.id;\n  var targetId = self.targetId;\n  var pixelRatio = self.pixelRatio;\n\n  var ref = self.cut;\n  var x = ref.x; if ( x === void 0 ) x = 0;\n  var y = ref.y; if ( y === void 0 ) y = 0;\n  var width = ref.width; if ( width === void 0 ) width = boundWidth;\n  var height = ref.height; if ( height === void 0 ) height = boundHeight;\n\n  self.updateCanvas = function (done) {\n    if (self.croperTarget) {\n      //  画布绘制图片\n      self.ctx.drawImage(\n        self.croperTarget,\n        self.imgLeft,\n        self.imgTop,\n        self.scaleWidth,\n        self.scaleHeight\n      );\n    }\n    isFunc(self.onBeforeDraw) && self.onBeforeDraw(self.ctx, self);\n\n    self.setBoundStyle(self.boundStyle); //\t设置边界样式\n\n    if (self.type !== '2d') {\n      self.ctx.draw(false, done);\n    }\n\n    done && done();\n    return self\n  };\n\n  self.pushOrigin = self.pushOrign = function (src) {\n    self.src = src;\n\n    isFunc(self.onBeforeImageLoad) && self.onBeforeImageLoad(self.ctx, self);\n\n    return loadCanvasImage(self, src).then(function (img) {\n      self.croperTarget = img;\n\n      return getImageInfo({ src: src })\n        .then(function (res) {\n          var innerAspectRadio = res.width / res.height;\n          var customAspectRadio = width / height;\n\n          if (innerAspectRadio < customAspectRadio) {\n            self.rectX = x;\n            self.baseWidth = width;\n            self.baseHeight = width / innerAspectRadio;\n            self.rectY = y - Math.abs((height - self.baseHeight) / 2);\n          } else {\n            self.rectY = y;\n            self.baseWidth = height * innerAspectRadio;\n            self.baseHeight = height;\n            self.rectX = x - Math.abs((width - self.baseWidth) / 2);\n          }\n\n          self.imgLeft = self.rectX;\n          self.imgTop = self.rectY;\n          self.scaleWidth = self.baseWidth;\n          self.scaleHeight = self.baseHeight;\n\n          self.update();\n\n          return new Promise(function (resolve) {\n            self.updateCanvas(resolve);\n          })\n        })\n        .then(function () {\n          isFunc(self.onImageLoad) && self.onImageLoad(self.ctx, self);\n        })\n    })\n  };\n\n  self.removeImage = function () {\n    self.src = '';\n    self.croperTarget = '';\n\n    if (self.type === '2d') {\n      return self.ctx.clearRect(0, 0, self.canvas.width, self.canvas.height)\n    } else {\n      return draw(self.ctx)\n    }\n  };\n\n  self.getCropperBase64 = function (done) {\n    if ( done === void 0 ) done = function () {};\n\n    CanvasToBase64.convertToBMP({\n      canvasId: id,\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }, done);\n  };\n\n  self.getCropperImage = function (opt, fn) {\n    var customOptions = Object.assign({fileType: 'jpg'}, opt);\n    var callback = isFunc(opt) ? opt : isFunc(fn) ? fn : null;\n\n    var canvasOptions = {\n      canvasId: id,\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    };\n\n    if (self.type === '2d') {\n      canvasOptions.canvas = self.canvas;\n    }\n\n    var task = function () { return Promise.resolve(); };\n\n    if (customOptions.original) {\n      // original mode\n      task = function () {\n        self.targetCtx.drawImage(\n          self.croperTarget,\n          self.imgLeft * pixelRatio,\n          self.imgTop * pixelRatio,\n          self.scaleWidth * pixelRatio,\n          self.scaleHeight * pixelRatio\n        );\n\n        canvasOptions = {\n          canvasId: targetId,\n          x: x * pixelRatio,\n          y: y * pixelRatio,\n          width: width * pixelRatio,\n          height: height * pixelRatio\n        };\n\n        return draw(self.targetCtx)\n      };\n    }\n\n    return task()\n      .then(function () {\n        Object.assign(canvasOptions, customOptions);\n        var arg = canvasOptions.componentContext\n          ? [canvasOptions, canvasOptions.componentContext]\n          : [canvasOptions];\n\n        return canvasToTempFilePath.apply(null, arg)\n      })\n      .then(function (res) {\n        var tempFilePath = res.tempFilePath;\n        return callback\n          ? callback.call(self, tempFilePath, null)\n          : tempFilePath\n      })\n      .catch(function (err) {\n        if (callback) {\n          callback.call(self, null, err);\n        } else {\n          throw err\n        }\n      })\n  };\n}\n\n/**\n * 获取最新缩放值\n * @param oldScale 上一次触摸结束后的缩放值\n * @param oldDistance 上一次触摸结束后的双指距离\n * @param zoom 缩放系数\n * @param touch0 第一指touch对象\n * @param touch1 第二指touch对象\n * @returns {*}\n */\nvar getNewScale = function (oldScale, oldDistance, zoom, touch0, touch1) {\n  var xMove, yMove, newDistance;\n  // 计算二指最新距离\n  xMove = Math.round(touch1.x - touch0.x);\n  yMove = Math.round(touch1.y - touch0.y);\n  newDistance = Math.round(Math.sqrt(xMove * xMove + yMove * yMove));\n\n  return oldScale + 0.001 * zoom * (newDistance - oldDistance)\n};\n\nfunction update () {\n  var self = this;\n\n  if (!self.src) { return }\n\n  self.__oneTouchStart = function (touch) {\n    self.touchX0 = Math.round(touch.x);\n    self.touchY0 = Math.round(touch.y);\n  };\n\n  self.__oneTouchMove = function (touch) {\n    var xMove, yMove;\n    // 计算单指移动的距离\n    if (self.touchended) {\n      return self.updateCanvas()\n    }\n    xMove = Math.round(touch.x - self.touchX0);\n    yMove = Math.round(touch.y - self.touchY0);\n\n    var imgLeft = Math.round(self.rectX + xMove);\n    var imgTop = Math.round(self.rectY + yMove);\n\n    self.outsideBound(imgLeft, imgTop);\n\n    self.updateCanvas();\n  };\n\n  self.__twoTouchStart = function (touch0, touch1) {\n    var xMove, yMove, oldDistance;\n\n    self.touchX1 = Math.round(self.rectX + self.scaleWidth / 2);\n    self.touchY1 = Math.round(self.rectY + self.scaleHeight / 2);\n\n    // 计算两指距离\n    xMove = Math.round(touch1.x - touch0.x);\n    yMove = Math.round(touch1.y - touch0.y);\n    oldDistance = Math.round(Math.sqrt(xMove * xMove + yMove * yMove));\n\n    self.oldDistance = oldDistance;\n  };\n\n  self.__twoTouchMove = function (touch0, touch1) {\n    var oldScale = self.oldScale;\n    var oldDistance = self.oldDistance;\n    var scale = self.scale;\n    var zoom = self.zoom;\n\n    self.newScale = getNewScale(oldScale, oldDistance, zoom, touch0, touch1);\n\n    //  设定缩放范围\n    self.newScale <= 1 && (self.newScale = 1);\n    self.newScale >= scale && (self.newScale = scale);\n\n    self.scaleWidth = Math.round(self.newScale * self.baseWidth);\n    self.scaleHeight = Math.round(self.newScale * self.baseHeight);\n    var imgLeft = Math.round(self.touchX1 - self.scaleWidth / 2);\n    var imgTop = Math.round(self.touchY1 - self.scaleHeight / 2);\n\n    self.outsideBound(imgLeft, imgTop);\n\n    self.updateCanvas();\n  };\n\n  self.__xtouchEnd = function () {\n    self.oldScale = self.newScale;\n    self.rectX = self.imgLeft;\n    self.rectY = self.imgTop;\n  };\n}\n\nvar handle = {\n  //  图片手势初始监测\n  touchStart: function touchStart (e) {\n    var self = this;\n    var ref = e.touches;\n    var touch0 = ref[0];\n    var touch1 = ref[1];\n\n    if (!self.src) { return }\n\n    setTouchState(self, true, null, null);\n\n    // 计算第一个触摸点的位置，并参照改点进行缩放\n    self.__oneTouchStart(touch0);\n\n    // 两指手势触发\n    if (e.touches.length >= 2) {\n      self.__twoTouchStart(touch0, touch1);\n    }\n  },\n\n  //  图片手势动态缩放\n  touchMove: function touchMove (e) {\n    var self = this;\n    var ref = e.touches;\n    var touch0 = ref[0];\n    var touch1 = ref[1];\n\n    if (!self.src) { return }\n\n    setTouchState(self, null, true);\n\n    // 单指手势时触发\n    if (e.touches.length === 1) {\n      self.__oneTouchMove(touch0);\n    }\n    // 两指手势触发\n    if (e.touches.length >= 2) {\n      self.__twoTouchMove(touch0, touch1);\n    }\n  },\n\n  touchEnd: function touchEnd (e) {\n    var self = this;\n\n    if (!self.src) { return }\n\n    setTouchState(self, false, false, true);\n    self.__xtouchEnd();\n  }\n};\n\nfunction cut () {\n  var self = this;\n  var boundWidth = self.width; // 裁剪框默认宽度，即整个画布宽度\n  var boundHeight = self.height;\n  // 裁剪框默认高度，即整个画布高度\n  var ref = self.cut;\n  var x = ref.x; if ( x === void 0 ) x = 0;\n  var y = ref.y; if ( y === void 0 ) y = 0;\n  var width = ref.width; if ( width === void 0 ) width = boundWidth;\n  var height = ref.height; if ( height === void 0 ) height = boundHeight;\n\n  /**\n   * 设置边界\n   * @param imgLeft 图片左上角横坐标值\n   * @param imgTop 图片左上角纵坐标值\n   */\n  self.outsideBound = function (imgLeft, imgTop) {\n    self.imgLeft = imgLeft >= x\n      ? x\n      : self.scaleWidth + imgLeft - x <= width\n        ? x + width - self.scaleWidth\n        :\timgLeft;\n\n    self.imgTop = imgTop >= y\n      ? y\n      : self.scaleHeight + imgTop - y <= height\n        ? y + height - self.scaleHeight\n        : imgTop;\n  };\n\n  /**\n   * 设置边界样式\n   * @param color\t边界颜色\n   */\n  self.setBoundStyle = function (ref) {\n    if ( ref === void 0 ) ref = {};\n    var color = ref.color; if ( color === void 0 ) color = '#04b00f';\n    var mask = ref.mask; if ( mask === void 0 ) mask = 'rgba(0, 0, 0, 0.3)';\n    var lineWidth = ref.lineWidth; if ( lineWidth === void 0 ) lineWidth = 1;\n\n    var half = lineWidth / 2;\n    var boundOption = [\n      {\n        start: { x: x - half, y: y + 10 - half },\n        step1: { x: x - half, y: y - half },\n        step2: { x: x + 10 - half, y: y - half }\n      },\n      {\n        start: { x: x - half, y: y + height - 10 + half },\n        step1: { x: x - half, y: y + height + half },\n        step2: { x: x + 10 - half, y: y + height + half }\n      },\n      {\n        start: { x: x + width - 10 + half, y: y - half },\n        step1: { x: x + width + half, y: y - half },\n        step2: { x: x + width + half, y: y + 10 - half }\n      },\n      {\n        start: { x: x + width + half, y: y + height - 10 + half },\n        step1: { x: x + width + half, y: y + height + half },\n        step2: { x: x + width - 10 + half, y: y + height + half }\n      }\n    ];\n\n    // 绘制半透明层\n    self.ctx.beginPath();\n    adapt2d(self, 'fillStyle', mask);\n    self.ctx.fillRect(0, 0, x, boundHeight);\n    self.ctx.fillRect(x, 0, width, y);\n    self.ctx.fillRect(x, y + height, width, boundHeight - y - height);\n    self.ctx.fillRect(x + width, 0, boundWidth - x - width, boundHeight);\n    self.ctx.fill();\n\n    boundOption.forEach(function (op) {\n      self.ctx.beginPath();\n      adapt2d(self, 'strokeStyle', color);\n      adapt2d(self, 'lineWidth', lineWidth);\n      self.ctx.moveTo(op.start.x, op.start.y);\n      self.ctx.lineTo(op.step1.x, op.step1.y);\n      self.ctx.lineTo(op.step2.x, op.step2.y);\n      self.ctx.stroke();\n    });\n  };\n}\n\nvar version = \"1.4.0\";\n\nvar WeCropper = function WeCropper (params) {\n  var self = this;\n  var _default = {};\n\n  validator(self, DEFAULT);\n\n  Object.keys(DEFAULT).forEach(function (key) {\n    _default[key] = DEFAULT[key].default;\n  });\n  Object.assign(self, _default, params);\n\n  self.prepare();\n  self.attachPage();\n  self.createCtx();\n  self.observer();\n  self.cutt();\n  self.methods();\n  self.init();\n  self.update();\n\n  return self\n};\n\nWeCropper.prototype.init = function init () {\n  var self = this;\n  var src = self.src;\n\n  self.version = version;\n\n  typeof self.onReady === 'function' && self.onReady(self.ctx, self);\n\n  if (src) {\n    self.pushOrign(src);\n  } else {\n    self.updateCanvas();\n  }\n  setTouchState(self, false, false, false);\n\n  self.oldScale = 1;\n  self.newScale = 1;\n\n  return self\n};\n\nObject.assign(WeCropper.prototype, handle);\n\nWeCropper.prototype.prepare = prepare;\nWeCropper.prototype.observer = observer;\nWeCropper.prototype.methods = methods;\nWeCropper.prototype.cutt = cut;\nWeCropper.prototype.update = update;\n\nreturn WeCropper;\n\n})));\n"]}
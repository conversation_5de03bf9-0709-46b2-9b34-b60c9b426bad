// appSettings.ts
// 创意工具库应用配置管理工具

import { DOMAIN } from './constants';

// 配置API基础URL
const APP_SETTINGS_BASE_URL = `${DOMAIN}/api/app_settings`;

/**
 * 通用配置接口
 */
export interface AppSetting<T> {
  key: string;
  value: T;
  group: string;
  description?: string;
  require_login: number;
  created_at: string;
  updated_at: string;
}

/**
 * 获取单个配置项
 * @param key 配置项的键名
 * @returns Promise<T> 配置项的值
 */
export async function getSetting<T>(key: string): Promise<T> {
  try {
    const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: `${APP_SETTINGS_BASE_URL}/get`,
        method: 'GET',
        data: { key },
        success: resolve,
        fail: reject
      });
    });
    
    if (res.statusCode === 200 && res.data) {
      const data = res.data as { code: number; msg: string; data: AppSetting<T> };
      if (data.code === 1 && data.data) {
        return data.data.value;
      }
      throw new Error(data.msg || '获取配置失败');
    }
    throw new Error(`请求失败: ${res.statusCode}`);
  } catch (error) {
    console.error(`获取配置[${key}]失败:`, error);
    throw error;
  }
}

/**
 * 获取分组配置项
 * @param group 分组名称
 * @returns Promise<Record<string, any>> 分组下的所有配置项
 */
export async function getGroupSettings(group: string): Promise<Record<string, any>> {
  try {
    const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: `${APP_SETTINGS_BASE_URL}/getGroup`,
        method: 'GET',
        data: { group },
        success: resolve,
        fail: reject
      });
    });
    
    if (res.statusCode === 200 && res.data) {
      const data = res.data as { code: number; msg: string; data: AppSetting<any>[] | any };
      if (data.code === 1 && data.data) {
        // 检查 data.data 是否为数组
        if (Array.isArray(data.data)) {
          // 将分组配置转换为键值对对象
          const settings: Record<string, any> = {};
          data.data.forEach(setting => {
            settings[setting.key] = setting.value;
          });
          return settings;
        } else {
          // 如果不是数组，检查是否是对象
          if (typeof data.data === 'object' && data.data !== null) {
            // 尝试直接使用对象
            console.warn(`分组配置[${group}]不是数组格式，尝试直接使用对象`);
            return data.data;
          }
          console.error(`分组配置[${group}]格式不正确: 不是数组或对象`, data.data);
          return {}; // 返回空对象
        }
      }
      throw new Error(data.msg || '获取分组配置失败');
    }
    throw new Error(`请求失败: ${res.statusCode}`);
  } catch (error) {
    console.error(`获取分组配置[${group}]失败:`, error);
    throw error;
  }
}

/**
 * 获取所有配置项
 * @param token 可选的身份验证令牌
 * @returns Promise<Record<string, any>> 所有配置项
 */
export async function getAllSettings(token?: string): Promise<Record<string, any>> {
  try {
    const header: Record<string, string> = {};
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
      header['token'] = token;
    }
    
    const res = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: `${APP_SETTINGS_BASE_URL}/getAll`,
        method: 'GET',
        header,
        success: resolve,
        fail: reject
      });
    });
    
    if (res.statusCode === 200 && res.data) {
      const data = res.data as { code: number; msg: string; data: AppSetting<any>[] | any };
      if (data.code === 1 && data.data) {
        // 检查 data.data 是否为数组
        if (Array.isArray(data.data)) {
          // 将所有配置转换为键值对对象
          const settings: Record<string, any> = {};
          data.data.forEach(setting => {
            settings[setting.key] = setting.value;
          });
          return settings;
        } else {
          // 如果不是数组，检查是否是对象
          if (typeof data.data === 'object' && data.data !== null) {
            // 尝试直接使用对象
            console.warn('配置数据不是数组格式，尝试直接使用对象');
            return data.data;
          }
          console.error('配置数据格式不正确: 不是数组或对象', data.data);
          return {}; // 返回空对象
        }
      }
      throw new Error(data.msg || '获取所有配置失败');
    }
    throw new Error(`请求失败: ${res.statusCode}`);
  } catch (error) {
    console.error('获取所有配置失败:', error);
    throw error;
  }
}

/**
 * 获取配置并提供默认值（带本地缓存，默认24小时）
 * @param key 配置项的键名
 * @param defaultValue 默认值
 * @returns Promise<T> 配置项的值或默认值
 */
export async function getSettingWithDefault<T>(key: string, defaultValue: T): Promise<T> {
  const CACHE_KEY = `appSetting_${key}`;
  const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时

  try {
    // 1. 先查本地缓存
    const cache = wx.getStorageSync(CACHE_KEY);
    if (cache && typeof cache === 'object' && cache.value !== undefined && cache.timestamp) {
      const now = Date.now();
      if (now - cache.timestamp < CACHE_DURATION) {
        // 缓存有效，直接返回
        console.log(`[appSettings] 本地缓存命中: ${key}`);
        return cache.value;
      }
    }

    // 2. 缓存无效或不存在，请求远程
    const value = await getSetting<T>(key);
    console.log(`[appSettings] 远程加载: ${key}`);

    // 3. 写入本地缓存
    wx.setStorageSync(CACHE_KEY, {
      value,
      timestamp: Date.now()
    });

    return value;
  } catch (error) {
    // 4. 远程失败，尝试返回本地缓存（即使过期）
    try {
      const cache = wx.getStorageSync(CACHE_KEY);
      if (cache && typeof cache === 'object' && cache.value !== undefined) {
        console.warn(`[appSettings] 远程失败，使用本地缓存: ${key}`);
        return cache.value;
      }
    } catch (e) {
      // ignore
    }
    // 5. 本地缓存也无，返回默认值
    console.warn(`使用默认配置[${key}]:`, defaultValue);
    return defaultValue;
  }
}

/**
 * 获取分组配置并提供默认值（带本地缓存，默认24小时）
 * @param group 分组名称
 * @param defaultValues 默认值对象
 * @returns Promise<Record<string, any>> 合并后的配置
 */
export async function getGroupSettingsWithDefault<T extends Record<string, any>>(
  group: string, 
  defaultValues: T
): Promise<T> {
  const CACHE_KEY = `appGroupSetting_${group}`;
  const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时

  try {
    // 1. 先查本地缓存
    const cache = wx.getStorageSync(CACHE_KEY);
    if (cache && typeof cache === 'object' && cache.value !== undefined && cache.timestamp) {
      const now = Date.now();
      if (now - cache.timestamp < CACHE_DURATION) {
        // 缓存有效，直接返回
        console.log(`[appSettings] 本地分组缓存命中: ${group}`);
        return { ...defaultValues, ...cache.value };
      }
    }

    // 2. 缓存无效或不存在，请求远程
    const remoteSettings = await getGroupSettings(group);
    console.log(`[appSettings] 远程加载分组: ${group}`);

    // 3. 写入本地缓存
    wx.setStorageSync(CACHE_KEY, {
      value: remoteSettings,
      timestamp: Date.now()
    });

    return { ...defaultValues, ...remoteSettings };
  } catch (error) {
    // 4. 远程失败，尝试返回本地缓存（即使过期）
    try {
      const cache = wx.getStorageSync(CACHE_KEY);
      if (cache && typeof cache === 'object' && cache.value !== undefined) {
        console.warn(`[appSettings] 远程分组失败，使用本地缓存: ${group}`);
        return { ...defaultValues, ...cache.value };
      }
    } catch (e) {
      // ignore
    }
    // 5. 本地缓存也无，返回默认值
    console.warn(`使用默认分组配置[${group}]`);
    return defaultValues;
  }
} 
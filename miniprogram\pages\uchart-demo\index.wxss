.container {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  margin-top: 130rpx;
  margin-bottom: 120rpx;
}

.chart-container {
  width: 100%;
  background-color: #fff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  box-sizing: border-box;
  position: relative;
}

.chart-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 30rpx;
}

/* 图表类型选择器样式 */
.chart-type-selector {
  margin-bottom: 30rpx;
}

.selector-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.selector-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: flex-start;
}

.type-btn {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 30rpx;
  text-align: center;
  transition: all 0.3s;
  margin-bottom: 10rpx;
  min-width: 70rpx;
  flex-shrink: 0;
}

.type-btn.active {
  background: #1890FF;
  color: white;
}

.chart-wrapper {
  width: 100%;
  height: 300px;
  position: relative;
}

.chart-wrapper.loading {
  opacity: 0.6;
}

.loading-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 99;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.action-btn-container {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.update-btn {
  background-color: #1890FF;
  color: white;
  font-size: 28rpx;
  border-radius: 8rpx;
  padding: 15rpx 30rpx;
  border: none;
  width: 50%;
  text-align: center;
}

.chart-tips {
  margin-top: 40rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 40rpx;
  margin-bottom: 10rpx;
} 
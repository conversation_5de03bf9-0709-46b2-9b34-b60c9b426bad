/**index.wxss**/

.upload-section {
  margin: 20rpx 20rpx;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.upload-btn {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  color: #fff;
  padding: 24rpx 48rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(0, 195, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.upload-btn::before {
  content: "📷";
  font-size: 36rpx;
  margin-right: 8rpx;
}

.preview-section {
  margin: 20rpx 20rpx 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx 20rpx 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0rpx 0rpx 20rpx;
  position: relative;
}

.preview-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-left: 10rpx;
}

.preview-title-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.clear-annotation-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.2);
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.clear-annotation-btn .text {
  color: #ff3333;
  font-size: 24rpx;
}

.clear-icon {
  font-size: 20rpx !important;
}

.clear-annotation-btn:active {
  background: rgba(255, 0, 0, 0.2);
  transform: scale(0.95);
}

.grid-settings {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.settings-item:last-child {
  margin-bottom: 0;
}

.settings-item .text {
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
  width: 120rpx;
}

/* 添加 color-picker 的样式 */
.settings-item color-picker {
  flex: 1;
  width: 100%;
}

.number-control {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1px solid #eee;
  overflow: hidden;
}

.control-btn {
  width: 80rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.control-btn:active {
  background: #e0e0e0;
  opacity: 0.8;
}

.number-control input {
  flex: 1;
  height: 64rpx;
  text-align: center;
  border: none;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  min-width: 80rpx;
}

/* 禁用状态的输入框样式 */
.number-control input[disabled] {
  background: #fff;
  color: #333;
  opacity: 1;
  cursor: not-allowed;
}

.slider-item {
  padding-right: 20rpx;
}

.slider-item slider {
  flex: 1;
  margin: 0 20rpx;
}

.slider-item .unit {
  width: auto;
  margin-left: 10rpx;
  color: #666;
  font-size: 24rpx;
}

.preview-container {
  position: relative;
  width: 100%;
}

.preview-image {
  width: 100%;
  display: block;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.preview-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-coordinates {
  display: flex;
  width: 100%;
  position: absolute;
  z-index: 1;
}

.coordinate-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.vertical-coordinates {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 1;
}

.vertical-cell {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

/* 右对齐时的样式 */
.vertical-coordinates[style*="right: 0"] .vertical-cell {
  justify-content: flex-end;
}

/* 左对齐时的样式 */
.vertical-coordinates[style*="left: 0"] .vertical-cell {
  justify-content: flex-start;
}

/* 底部对齐时的样式 */
.grid-coordinates[style*="bottom: 0"] .coordinate-cell {
  align-items: flex-end;
  padding-bottom: 4rpx;
}

/* 顶部对齐时的样式 */
.grid-coordinates[style*="top: 0"] .coordinate-cell {
  align-items: flex-start;
  padding-top: 4rpx;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-line-horizontal {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
}

.grid-line-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
}

.color-list {
  flex: 1;
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.color-item {
  width: 50rpx;
  height: 50rpx;
  border-radius: 6rpx;
  border: 1px solid #ddd;
  transition: all 0.3s;
}

.color-item.active {
  transform: scale(1.1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
  border: 2px solid #07c160;
}

.grid-click-areas {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: auto;
}

.grid-cell {
  position: absolute;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.grid-cell:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.grid-cell.highlighted {
  background-color: rgba(0, 195, 255, 0.1);
}

.action-btn.active {
  background-color: #f0f0f0 !important;
}

.zoom-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.zoom-content {
  position: relative;
  width: 90vw;
  height: 90vw;
  display: flex;
  justify-content: center;
  align-items: center;
  touch-action: none;
  z-index: 1001;
}

.image-container {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  will-change: transform;
  touch-action: none;
}

.image-wrapper {
  position: relative;
  display: inline-block;
  will-change: transform;
  transform-origin: center;
}

.zoomed-image {
  display: block;
  width: 90vw;
  height: auto;
  object-fit: contain;
}

/* 放大图片的网格样式 */
.zoomed-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.zoomed-grid .grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.zoomed-grid .grid-line-horizontal {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
}

.zoomed-grid .grid-line-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
}

/* 放大图片的网格设置遮罩层 */
.zoom-grid-settings-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1100;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zoom-grid-settings-mask.show {
  opacity: 1;
  visibility: visible;
}

.zoom-grid-settings {
  width: 90vw;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
}

.zoom-grid-settings.show {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1);
  opacity: 1;
}

.zoom-grid-settings .settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.zoom-grid-settings .settings-header .text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.zoom-grid-settings .close-settings {
  font-size: 40rpx;
  color: #999;
  padding: 0 16rpx;
}

.zoom-grid-settings .settings-content {
  padding: 24rpx;
  max-height: 75vh;
  overflow-y: auto;
}

.zoom-grid-settings .settings-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 12rpx 0;
}

.zoom-grid-settings .settings-item .text {
  flex-shrink: 0;
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
}

.zoom-grid-settings .number-control {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 8rpx;
}

.zoom-grid-settings .control-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 6rpx;
  color: #666;
  font-size: 32rpx;
}

.zoom-grid-settings .number-control input {
  width: 80rpx;
  text-align: center;
  background: #fff;
  border-radius: 6rpx;
  padding: 8rpx 0;
}

.zoom-grid-settings .color-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  flex: 1;
}

.zoom-grid-settings .color-item {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.zoom-grid-settings .color-item.active {
  transform: scale(1.1);
  border-color: #07c160;
  box-shadow: 0 0 8rpx rgba(7, 193, 96, 0.3);
}

.zoom-grid-settings .slider-item {
  flex-wrap: wrap;
}

.zoom-grid-settings .slider-item slider {
  flex: 1;
  margin: 0 12rpx;
}

.zoom-grid-settings .unit {
  width: auto !important;
  margin-left: 8rpx;
}

/* 修改网格设置按钮样式 */
.grid-settings-btn {
  /* position: absolute;
  right: 40rpx;
  bottom: 190rpx; */
  display: flex;
  align-items: center;
  /* gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 20rpx; */
  color: #fff;
  font-size: 28rpx;
  /* z-index: 1001; */
}

.settings-icon {
  font-size: 32rpx;
}

.zoom-info {
  position: fixed;
  top: 60rpx;
  left: 40rpx;
  color: #fff;
  font-size: 36rpx;
  line-height: 1;
  padding: 20rpx 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 40rpx;
  z-index: 2;
}

.zoom-controls {
  position: absolute;
  left: 40rpx;
  right: 40rpx;
  bottom: 100rpx;
  /* background-color: rgba(0, 0, 0, 0.6); */
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
  border-radius: 20rpx;
  padding: 20rpx;
  z-index: 1001;
  display: grid;
  grid-template-columns: 2fr 1fr; /* 创建两列，平均分配宽度 */
  gap: 20rpx; /* 列与列之间的间距 */
}    

.zoom-slider {
  display: flex;
  align-items: center;
  /* padding: 0 20rpx; */
}

.zoom-slider slider {
  flex: 1;
  margin: 0;
}

.zoom-value {
  color: #fff;
  font-size: 28rpx;
  margin-left: 20rpx;
  text-align: right;
  min-width: 80rpx;
}

.zoom-close {
  position: fixed;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 48rpx;
  z-index: 2;
}

.download-btn {
  position: absolute;
  top: 10rpx;
  left: 40rpx;
  height: 80rpx;
  /* background-color: rgba(0, 0, 0, 0.6); */
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  color: #fff;
  font-size: 32rpx;
  z-index: 1001;
  transition: all 0.3s ease;
}

.download-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.8);
}

.download-icon {
  margin-left: 16rpx;
  font-size: 24rpx;
}
/* 关闭放大层 */
.zoom-closed-btn {
  position: absolute;
  top:10rpx;
  right: 40rpx;
  height: 80rpx;
  /* background-color: rgba(0, 0, 0, 0.6); */
  background: linear-gradient(90deg, rgba(46, 93, 248, 0.98), rgba(75, 116, 247, 0.98));
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  color: #fff;
  font-size: 32rpx;
  z-index: 1001;
  transition: all 0.3s ease;
}

.zoom-closed-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.8);
}

.zoom-closed-icon {
  font-size: 24rpx;
}
/* 关闭放大层结束 */
.download-buttons {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  padding: 30rpx 24rpx;
  margin: 30rpx 20rpx;
  position: relative;
}

.download-buttons .calc-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 10rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  transition: all 0.3s ease;
}

.download-buttons .calc-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.8);
}

.download-buttons .calc-btn .settings-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.download-buttons .calc-btn text:not(.settings-icon) {
  letter-spacing: 2rpx;
}

/* 优化动画效果 */
@keyframes buttonPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.download-buttons .calc-btn:hover {
  animation: buttonPulse 1.2s ease-in-out infinite;
}

.download-grid-btn,
.download-combined-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
  color: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

.download-grid-btn {
  background: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
}

.download-combined-btn {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.download-grid-btn:active,
.download-combined-btn:active {
  transform: scale(0.96) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.download-grid-btn::before,
.download-combined-btn::before {
  content: "⬇️";
  font-size: 36rpx;
  margin-right: 8rpx;
}

.download-grid-btn::after,
.download-combined-btn::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: downloadShine 3s infinite;
  pointer-events: none;
}

@keyframes downloadShine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* 在小屏幕上调整按钮为垂直排列 */
@media screen and (max-width: 320px) {
  .download-buttons {
    flex-direction: column;
  }
  
  .download-grid-btn,
  .download-combined-btn {
    width: 100%;
  }
}

/* 导出加载遮罩 */
.export-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.export-loading-content {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 12rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.export-loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  border: 6rpx solid #fff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading 0.8s linear infinite;
}

.export-loading-content .text {
  color: #fff;
  font-size: 28rpx;
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 操作选择浮层 */
.action-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  justify-content: center;
  align-items: center;
}

.action-popup.show {
  display: flex;
}

.action-content {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  width: 480rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.2s ease;
}

.action-popup.show .action-content {
  transform: scale(1);
  opacity: 1;
}

.action-info {
  padding: 24rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1rpx solid #eee;
}

.coordinate-text {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.action-buttons {
  display: flex;
  align-items: center;
  padding: 24rpx;
  gap: 24rpx;
}

.action-divider {
  width: 2rpx;
  height: 48rpx;
  background: #eee;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1.2;
  font-weight: normal;
}

.action-btn::after {
  display: none;
}

.action-icon {
  font-size: 48rpx;
}

.action-btn text:not(.action-icon) {
  font-size: 28rpx;
  color: #666;
}

.action-btn.active {
  color: #07c160;
}

.action-btn.active text:not(.action-icon) {
  color: #07c160;
}

.annotation-settings-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #00C3FF;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.annotation-settings-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.annotation-settings {
  position: fixed;
  right: -520rpx;
  top: 0;
  bottom: 0;
  width: 520rpx;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1001;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.annotation-settings.show {
  right: 0;
}

.annotation-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.annotation-settings-header .text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.annotation-settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.annotation-settings .settings-item {
  margin-bottom: 40rpx;
}

.annotation-settings .settings-item:last-child {
  margin-bottom: 0;
}

.annotation-settings .settings-item .text {
  color: #fff;
  width: 120rpx;
  font-size: 28rpx;
}

.annotation-settings .color-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
}

.annotation-settings .color-item {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.annotation-settings .color-item.active {
  transform: scale(1.1);
  border-color: #fff;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
}

.download-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

/* 设置按钮样式 */
.settings-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  box-shadow: 0 6rpx 16rpx rgba(0, 195, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.settings-btn::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: settingsShine 3s infinite;
  pointer-events: none;
}

@keyframes settingsShine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.settings-btn .text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.settings-icon {
  font-size: 36rpx !important;
  margin-right: 4rpx;
}

.settings-btn:active {
  transform: scale(0.96) translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 195, 255, 0.2);
  background: linear-gradient(135deg, #00B3FF 0%, #0078FF 100%);
}

/* 设置面板遮罩层 */
.settings-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.settings-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 主设置面板样式 */
.main-settings {
  position: fixed;
  left: 0;
  right: 0;
  top: -100%;
  width: 100%;
  height: auto;
  max-height: 85vh;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1001;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
}

.main-settings.show {
  top: 0;
  transform: translateY(0);
}

/* 设置内容区域 */
.settings-content {
  overflow-y: auto;
  overflow-x: hidden;
  margin: 20rpx;
  /* padding: 20rpx; */
  flex: 1; 
}

/* 优化设置组样式 */
.settings-group {
  /* background: #fff; */
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.settings-group:last-child {
  margin-bottom: 16rpx;
}

/* 设置项样式优化 */
.settings-item {
  margin-bottom: 20rpx;
}

.settings-item:last-child {
  margin-bottom: 0;
}

/* Tab导航样式 */
.settings-tabs {
  padding: 16rpx 30rpx;
  /* background: #fff; */
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-shrink: 0; /* 防止tab被压缩 */
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 0;
  color: #666;
  font-size: 26rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tab-icon {
  font-size: 36rpx;
  margin-bottom: 4rpx;
}

.tab-item.active {
  color: #07c160;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  top: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #07c160;
  border-radius: 2rpx;
}

/* 优化滑动条样式 */
.slider-item slider {
  margin: 0 20rpx;
}

/* 优化数字输入控件样式 */
.number-control {
  background: #f8f8f8;
  border: 1px solid #eee;
}

.control-btn {
  background: #f0f0f0;
  color: #666;
}

/* 优化颜色选择器样式 */
.color-list {
  gap: 16rpx;
  padding: 8rpx 0;
}

.color-item {
  width: 44rpx;
  height: 44rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.color-item.active {
  transform: scale(1.1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.position-picker {
  flex: 1;
  background: #f8f8f8;
  border-radius: 6rpx;
  border: 1px solid #ddd;
  overflow: hidden;
}

.picker-content {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

/* 在暗色主题下的样式调整 */
.zoom-settings .position-picker {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.zoom-settings .picker-content .text {
  color: #fff;
}

.zoom-settings .picker-arrow {
  color: rgba(255, 255, 255, 0.6);
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.calc-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #00C3FF 50%, #0086FF 100%);
  box-shadow: 0 6rpx 16rpx rgba(0, 195, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 尺寸计算器遮罩层 */
.size-calculator-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.size-calculator-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 尺寸计算器样式 */
.size-calculator {
  width:100%;
  background: #fff;
  border-radius: 24rpx;
  /* overflow: hidden; */
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.size-calculator.show {
  transform: scale(1);
  opacity: 1;
}

.calculator-header {
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.title-icon {
  font-size: 36rpx;
}

.close-calculator {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-calculator:active {
  opacity: 0.7;
}

.calculator-content {
  padding: 32rpx;
}

.paper-size-selector {
  margin-bottom: 32rpx;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  color: #666;
  font-size: 28rpx;
}

.label-icon {
  font-size: 32rpx;
}

.picker-content {
  padding: 20rpx 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.result-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
}

.result-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.result-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #666;
}

.result-value {
  color: #333;
  font-family: 'Roboto Mono', monospace;
}

.result-value.highlight {
  color: #07c160;
  font-weight: 500;
}

.config-button-group {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.config-section {
  margin: 0 20rpx;
  padding: 40rpx 0;
  background: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
  border-radius: 0 0 30rpx 30rpx;
}

.config-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.config-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}

.config-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.config-button-group {
  padding: 0 24rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.feature-btn {
  flex: 1;
  min-width: 280rpx;
  height: 100rpx;
  background: #ffffff;
  border-radius: 12rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid rgba(0,0,0,0.06);
}

.feature-btn.primary {
  width: 90%;
  height: 120rpx;
  background: #07c160;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(7,193,96,0.2);
}

.feature-btn .feature-text {
  color: #333;
  font-size: 28rpx;
  font-weight: 400;
}

.feature-btn.primary .feature-text {
  color: #fff;
  font-weight: 500;
}

.feature-btn .feature-icon {
  font-size: 36rpx;
  color: #666;
}

.feature-btn.primary .feature-icon {
  color: #fff;
}

.feature-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.05);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 0 16rpx;
  margin: 8rpx 0;
}

.title-line {
  height: 1px;
  width: 40rpx;
  background: rgba(0,0,0,0.1);
}

.title-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  overflow: hidden;
}

/* 图片容器 */
.image-container {
  flex: 1;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.placeholder-icon {
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.selected-image {
  width: 100%;
  height: 100%;
}

/* 网格线 */
.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.vertical-line {
  position: absolute;
  top: 0;
  width: 2rpx;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.3);
}

.horizontal-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.3);
}

/* 控制面板 */
.control-panel {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.control-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.grid-control {
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-size-control {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 8rpx;
}

.size-btn {
  width: 64rpx;
  height: 64rpx;
  padding: 0;
  margin: 0;
  line-height: 64rpx;
  text-align: center;
  font-size: 36rpx;
  color: #666;
  background: white;
  border: none;
  border-radius: 8rpx;
}

.size-btn::after {
  border: none;
}

.size-btn[disabled] {
  background: #f0f0f0;
  color: #ccc;
}

.size-text {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  min-width: 100rpx;
  text-align: center;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn::after {
  border: none;
}

.select-btn {
  background: #f5f5f5;
  color: #333;
}

.save-btn {
  background: #07c160;
  color: white;
}

.save-btn[disabled] {
  background: #a8e6c1;
  color: white;
}

/* 裁剪组件容器 */
.cropper-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 999;
}

.tool-intro {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 134, 255, 0.2);
}

.intro-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-content {
  margin-bottom: 40rpx;
}

.feature-section {
  margin-bottom: 36rpx;
}

.feature-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: flex-start;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-icon .text {
  color: #fff;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.usage-section {
  margin-top: 40rpx;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-section {
  margin-top: 40rpx;
}

.tip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
}

.tip-icon {
  color: #0086FF;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.feature-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.feature-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.feature-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.4s both; }

.step-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.step-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.step-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.step-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.tip-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.6s both; }

.grid-control-item {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
}

.grid-control-item.active {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
}

.grid-control-item:active {
  background: linear-gradient(135deg, #0086FF 0%, #00C3FF 100%);
}

.grid-action-button {
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
}

.grid-action-button:active {
  background: linear-gradient(135deg, #0086FF 0%, #00C3FF 100%);
}

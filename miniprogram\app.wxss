/**app.wxss**/
/* 全局变量 */
page {
  /* 颜色 */
  --primary-color: #07c160;
  --secondary-color: #999999;
  --background-color: #f7f7f7;

  /* 字体大小 */
  --font-size-small: 24rpx;
  --font-size-normal: 28rpx;
  --font-size-large: 32rpx;

  /* 间距 */
  --spacing-small: 8rpx;
  --spacing-normal: 16rpx;
  --spacing-large: 24rpx;

  /* 应用默认样式 */
  background-color: var(--background-color);
  font-size: var(--font-size-normal);
  color: #333333;
  position: relative;
}

/* 通用文本样式 */
.text-small {
  font-size: var(--font-size-small);
}

.text-normal {
  font-size: var(--font-size-normal);
}

.text-large {
  font-size: var(--font-size-large);
}

/* 通用间距类 */
.margin-small {
  margin: var(--spacing-small);
}

.margin-normal {
  margin: var(--spacing-normal);
}

.margin-large {
  margin: var(--spacing-large);
}

.padding-small {
  padding: var(--spacing-small);
}

.padding-normal {
  padding: var(--spacing-normal);
}

.padding-large {
  padding: var(--spacing-large);
}

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: var(--spacing-normal) var(--spacing-large);
  border-radius: 4rpx;
  font-size: var(--font-size-normal);
  text-align: center;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: #ffffff;
}

/* 通用卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: var(--spacing-normal);
  margin: var(--spacing-normal);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 通用列表样式 */
.list-item {
  padding: var(--spacing-normal);
  border-bottom: 1rpx solid #eee;
  background-color: #ffffff;
}

.list-item:last-child {
  border-bottom: none;
}

/* 通用输入框样式 */
.input {
  padding: var(--spacing-normal);
  border: 1rpx solid #eee;
  border-radius: 4rpx;
  font-size: var(--font-size-normal);
  background-color: #ffffff;
}

/* 通用图标样式 */
.icon {
  width: 32rpx;
  height: 32rpx;
  vertical-align: middle;
}

/* 通用flex布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

/* 通用状态类 */
.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.hidden {
  display: none !important;
}

/* 通用动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.fade-out {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 背景图片 */
.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* 容器样式 */
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 内容区域样式 */
.content-wrapper {
  width: 100%;
  position: relative;
  flex: 1;
  overflow: hidden;
}

.content-scroll {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* 工具说明样式 */
.tool-intro {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 134, 255, 0.2);
}

.intro-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-content {
  margin-bottom: 40rpx;
}

.feature-section {
  margin-bottom: 36rpx;
}

.feature-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: flex-start;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-icon .text {
  color: #fff;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.usage-section {
  margin-top: 40rpx;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-section {
  margin-top: 40rpx;
}

.tip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
}

.tip-icon {
  color: #0086FF;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.feature-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.feature-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.feature-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.4s both; }

.step-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.step-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.step-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.step-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.tip-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.6s both; }

// index.ts
import { layoutUtil } from '../../../../utils/layout';
import Api from '../../../../utils/api';
import eventBus from '../../../../utils/eventBus';
import { COMMON_ASSETS } from '../../../../utils/constants';

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    applications: [],
    filteredApplications: [], // 筛选后的申请列表
    loading: true,
    isEmpty: false,
    defaultAvatar: COMMON_ASSETS.DEFAULT_AVATAR, // 添加默认头像常量
    statusMap: {
      'pending': '审核中',
      'approved': '已通过',
      'rejected': '已驳回'
    },
    statusColorMap: {
      'pending': '#ff9800',
      'approved': '#4caf50',
      'rejected': '#f44336'
    },
    // 申请详情
    showDetail: false,
    currentApplication: null,
    // 状态计数
    pendingCount: 0,
    approvedCount: 0,
    rejectedCount: 0,
    // Tab筛选
    tabs: [
      { id: 'all', name: '全部' },
      { id: 'pending', name: '审核中' },
      { id: 'approved', name: '已通过' },
      { id: 'rejected', name: '已驳回' }
    ],
    activeTab: 'all'
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // 加载申请列表数据
      this.loadApplications();
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    // 加载申请列表数据
    async loadApplications() {
      this.setData({ loading: true });
      console.log('加载申请列表数据');
      
      try {
        // 从API获取数据
        const res = await Api.Status.getApplicationStatus();
        console.log('申请列表数据:', res);
        
        // 获取画种和画风列表数据
        const getStylesAndTypes = await Api.recruitment.getStylesAndTypes()
        const filteredTypes = (getStylesAndTypes.types.list || []).filter(item => item.code !== 'unlimited');
        const filteredStyles = (getStylesAndTypes.styles.list || []).filter(item => item.code !== 'unlimited');
        
        this.setData({ 
          artworkTypes: filteredTypes,
          artStyles: filteredStyles,
        });

        // 检查数据是否是数组且有值
        if (Array.isArray(res) && res.length > 0) {
          // 直接使用返回的数组数据
          this.setData({
            applications: res,
            isEmpty: false
          });
          
          // 计算各状态数量并筛选数据
          this.calculateStatusCounts(res);
          this.filterApplications();
        } 
        // 检查数据是否符合标准API响应格式
        else if (res && res.code === 1 && Array.isArray(res.data)) {
          // 使用标准API响应中的data字段
          this.setData({
            applications: res.data,
            isEmpty: res.data.length === 0
          });
          
          // 计算各状态数量并筛选数据
          this.calculateStatusCounts(res.data);
          this.filterApplications();
        } 
        else {
          // API返回错误或空数据
          this.setData({
            applications: [],
            filteredApplications: [],
            isEmpty: true
          });
          
          console.log('API返回数据格式不符合预期', res);
          
          // 显示错误提示
          wx.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取申请列表失败:', error);
        
        // 请求失败，设置为空数据
        this.setData({
          applications: [],
          filteredApplications: [],
          isEmpty: true
        });
        
        // 显示错误提示
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },
    
    // 计算各状态的申请数量
    calculateStatusCounts(applications) {
      let pendingCount = 0;
      let approvedCount = 0;
      let rejectedCount = 0;
      
      applications.forEach(item => {
        if (item.status === 'pending') pendingCount++;
        else if (item.status === 'approved') approvedCount++;
        else if (item.status === 'rejected') rejectedCount++;
      });
      
      this.setData({
        pendingCount,
        approvedCount,
        rejectedCount
      });
    },
    
    // 切换标签页
    switchTab(e) {
      const tabId = e.currentTarget.dataset.id;
      if (tabId !== this.data.activeTab) {
        this.setData({ activeTab: tabId });
        
        // 重新筛选数据，会调用转换ID为名称的逻辑
        this.filterApplications();
      }
    },
    
    // 根据当前选中的标签筛选数据
    filterApplications() {
      const { applications, activeTab, artworkTypes, artStyles } = this.data;
      
      // 如果应用列表为空，则设置筛选结果也为空
      if (!applications || applications.length === 0) {
        this.setData({
          filteredApplications: [],
          isEmpty: true
        });
        return;
      }
      
      let filteredApplications = [];
      
      if (activeTab === 'all') {
        filteredApplications = applications;
      } else {
        filteredApplications = applications.filter(item => item.status === activeTab);
      }
      
      // 转换artwork_types和art_styles的ID为名称
      if (artworkTypes && artworkTypes.length > 0 && artStyles && artStyles.length > 0) {
        filteredApplications = filteredApplications.map(item => {
          const processedItem = { ...item };
          
          // 处理artwork_types
          if (processedItem.artwork_types) {
            if (typeof processedItem.artwork_types === 'string' && processedItem.artwork_types.includes(',')) {
              // 如果是逗号分隔的ID字符串
              const typeIds = processedItem.artwork_types.split(',');
              processedItem.artwork_types = typeIds.map(id => {
                const type = artworkTypes.find(t => t.id == id || t.code == id);
                return type ? type.name : id;
              }).join(', ');
            } else if (!isNaN(Number(processedItem.artwork_types))) {
              // 如果是单个数字ID
              const type = artworkTypes.find(t => t.id == processedItem.artwork_types);
              if (type) {
                processedItem.artwork_types = type.name;
              }
            }
          }
          
          // 处理art_styles
          if (processedItem.art_styles) {
            if (typeof processedItem.art_styles === 'string' && processedItem.art_styles.includes(',')) {
              // 如果是逗号分隔的ID字符串
              const styleIds = processedItem.art_styles.split(',');
              processedItem.art_styles = styleIds.map(id => {
                const style = artStyles.find(s => s.id == id || s.code == id);
                return style ? style.name : id;
              }).join(', ');
            } else if (!isNaN(Number(processedItem.art_styles))) {
              // 如果是单个数字ID
              const style = artStyles.find(s => s.id == processedItem.art_styles);
              if (style) {
                processedItem.art_styles = style.name;
              }
            }
          }
          
          return processedItem;
        });
      }
      
      this.setData({
        filteredApplications,
        isEmpty: filteredApplications.length === 0
      });
      
      console.log('筛选后的数据:', filteredApplications);
    },
    
    // 查看申请详情
    viewApplicationDetail(e) {
      const id = e.currentTarget.dataset.id;
      const application = this.data.filteredApplications.find(item => item.id == id);
       
      if (application) {
        // 将artwork_types和art_styles的ID转换为名称
        const { artworkTypes, artStyles } = this.data;
        
        // 处理artwork_types
        let artwork_types = application.artwork_types;
        if (artwork_types && artworkTypes && artworkTypes.length > 0) {
          if (typeof artwork_types === 'string' && artwork_types.includes(',')) {
            // 如果是逗号分隔的ID字符串
            const typeIds = artwork_types.split(',');
            artwork_types = typeIds.map(id => {
              const type = artworkTypes.find(item => item.id == id || item.code == id);
              return type ? type.name : id;
            }).join(', ');
          } else if (!isNaN(Number(artwork_types))) {
            // 如果是单个数字ID
            const type = artworkTypes.find(item => item.id == artwork_types);
            artwork_types = type ? type.name : artwork_types;
          }
        }
        
        // 处理art_styles
        let art_styles = application.art_styles;
        if (art_styles && artStyles && artStyles.length > 0) {
          if (typeof art_styles === 'string' && art_styles.includes(',')) {
            // 如果是逗号分隔的ID字符串
            const styleIds = art_styles.split(',');
            art_styles = styleIds.map(id => {
              const style = artStyles.find(item => item.id == id || item.code == id);
              return style ? style.name : id;
            }).join(', ');
          } else if (!isNaN(Number(art_styles))) {
            // 如果是单个数字ID
            const style = artStyles.find(item => item.id == art_styles);
            art_styles = style ? style.name : art_styles;
          }
        }
        
        // 创建一个新对象，避免直接修改原始数据
        const applicationWithNames = {
          ...application,
          artwork_types,
          art_styles
        };
        
        this.setData({
          currentApplication: applicationWithNames,
          showDetail: true
        });
        
        // 滚动到顶部
        setTimeout(() => {
          const query = wx.createSelectorQuery().in(this);
          query.select('.detail-content').node().exec((res) => {
            if (res[0] && res[0].node) {
              res[0].node.scrollTop = 0;
            }
          });
        }, 100);
      } else {
        wx.showToast({
          title: '未找到申请信息',
          icon: 'none'
        });
      }
    },
    
    // 隐藏申请详情
    hideApplicationDetail() {
      this.setData({
        showDetail: false
      });
      
      // 延迟清空当前申请数据，保证动画流畅
      setTimeout(() => {
        this.setData({
          currentApplication: null
        });
      }, 300);
    },
    
    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '系列申请',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '系列申请',
        path: '/pages/about/studio/series/index'
      };
    },

    // 格式化日期 - 简化为直接返回原始值
    formatDate(dateString) {
      return dateString || '未知';
    },

    // 有条件的格式化日期
    formatDateIfNeeded(dateString) {
      // 如果是时间戳格式，则进行格式化
      if (typeof dateString === 'number' || (typeof dateString === 'string' && /^\d+$/.test(dateString))) {
        return this.formatDate(dateString);
      }
      // 否则返回原始值
      return dateString || '未知';
    },

    // 跳转到招募页面
    navigateToRecruitments() {
      wx.navigateTo({
        url: '/pages/recruitment/index'
      });
    }
  }
});

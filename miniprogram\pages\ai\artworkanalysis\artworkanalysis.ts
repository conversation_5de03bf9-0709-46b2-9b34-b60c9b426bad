// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
import {
  API_KEY,
  CHAT_COMPLETIONS_API_URL,
  requestZhipuAPI,
  showErrorMessage,
  updateProgress as updateProgressUtil,
  handleTabBarChange as handleTabBarChangeUtil,
  imageToBase64
} from '../../../utils/aiUtils';

interface AIResponse {
  choices: Array<{
    finish_reason: string;
    index: number;
    message: {
      content: string;
    }
  }>;
}

interface AnalysisResult {
  advantages: string;
  improvements: string;
  suggestions: string;
  originalityScore: number;
  originalityAnalysis: string;
  refinementScore: number;
  refinementAnalysis: string;
}

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    navMenus: [],
    tools: [],
    loading: true,
    isEmpty: false,
    tempImagePath: '',
    analyzing: false,
    analysisResult: null,
    progressPercent: 0,
    progressText: '',
    activeAnalysis: ''
  },

  lifetimes: {
    attached: function() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    detached: function() {
      // 清理临时文件
      if (this.data.tempImagePath && this.data.tempImagePath.startsWith(wx.env.USER_DATA_PATH)) {
        wx.getFileSystemManager().unlink({
          filePath: this.data.tempImagePath,
          fail: () => {
            // 忽略删除失败的错误
          }
        });
      }
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }    
  },

  methods: {
    // 使用通用函数处理TabBar变化
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      handleTabBarChangeUtil(this, data);
    },

    // 更新进度 - 使用通用函数
    updateProgress(percent: number, text: string) {
      updateProgressUtil(this, percent, text);
    },

    // 选择图片
    async chooseImage() {
      this.setData({
        analyzing: false,
        progressPercent: 0,
        progressText: '',
        analysisResult: null
      });

      try {
        this.updateProgress(10, '选择图片中...');
        const res = await wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera']
        });

        const tempFilePath = res.tempFiles[0].tempFilePath;

        try {
          this.updateProgress(20, '处理图片中...');
          // 使用通用函数将图片转为base64
          const base64Url = await imageToBase64(tempFilePath);
          
          this.setData({
            tempImagePath: base64Url,
            analyzing: true
          });

          // 选择图片后自动开始分析
          this.analyzeImage(tempFilePath);
        } catch (error) {
          wx.showToast({
            title: '图片处理失败',
            icon: 'none'
          });
        }
      } catch (error) {
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    },

    // 分析图片
    async analyzeImage(e?: any) {
      // 如果没有图片，直接返回
      if (!this.data.tempImagePath) {
        wx.showToast({
          title: '请先选择图片',
          icon: 'none'
        });
        return;
      }

      // 重置分析状态
      this.setData({
        analyzing: true,
        progressPercent: 0,
        progressText: '',
        analysisResult: null
      });

      if (!API_KEY) {
        wx.showToast({
          title: '请配置API密钥',
          icon: 'none'
        });
        return;
      }

      this.updateProgress(30, '准备分析...');

      try {
        // 获取图片路径
        const imagePath = this.data.tempImagePath;
        let base64Image;
        
        try {
          // 使用通用函数将图片转为base64
          base64Image = await imageToBase64(imagePath);
        } catch (error) {
          throw new Error('图片格式转换失败');
        }

        // 先判断是否为绘画作品
        this.updateProgress(45, '判断图片类型...');
        const requestDataForImageType = {
          model: "glm-4v-flash",
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: "这是一幅绘画作品吗？请只回答'是'或'否'，不要解释。如果是手绘、数字绘画、AI绘画等各类绘画作品回答'是'，如果是照片或其他类型的图片回答'否'。"
                },
                {
                  type: "image_url",
                  image_url: {
                    url: base64Image
                  }
                }
              ]
            }
          ],
          stream: false
        };

        // 使用通用API请求函数
        const typeCheckResponse = await requestZhipuAPI<AIResponse>(CHAT_COMPLETIONS_API_URL, requestDataForImageType);

        const isArtwork = typeCheckResponse 
          && typeCheckResponse.choices 
          && typeCheckResponse.choices[0] 
          && typeCheckResponse.choices[0].message 
          && typeCheckResponse.choices[0].message.content 
          && typeCheckResponse.choices[0].message.content.toLowerCase().includes('是');
        
        if (!isArtwork) {
          wx.showToast({
            title: '请上传绘画作品',
            icon: 'none',
            duration: 2000
          });
          this.setData({ 
            analyzing: false,
            progressPercent: 0,
            progressText: ''
          });
          return;
        }

        this.updateProgress(50, '构建分析请求...');
        const requestData = {
          model: "glm-4v-flash",
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: `作为一位严格的艺术作品分析师，请从以下几个方面仔细分析这幅作品(必须严格遵循以下格式)：

[优点]
- 构图：...
- 色彩：...
- 技法：...
（请用专业的艺术术语进行分析）

[改进]
- 构图方面：...
- 色彩方面：...
- 技法方面：...
（请指出具体需要改进的地方）

[建议]
- 具体可行的修改方案
- 建议使用的技法或工具
（请给出明确的操作建议）

[原创度]（必须严格执行以下评分标准）
1. 评分标准（倾向于更低的评分）：
   - 85-80%：完全原创，角色设计、场景、构图等都具有独特创新性
   - 79-70%：高度原创，但借鉴了某些艺术风格或表现手法
   - 69-50%：二次创作，包含明显的动漫/游戏角色或场景元素参考
   - 49-30%：高度相似，大量使用了已有作品的设计元素
   - 29-10%：临摹作品，与参考源高度相似
   - 9-0%：直接临摹，几乎完全复制原作

2. 临摹判定标准（若符合任一条件，必须降低评分）：
   - 人物五官、发型与已有角色相似：必须降至69%以下
   - 姿势、表情与已有作品相似：必须降至49%以下
   - 构图、场景布局高度相似：必须降至29%以下
   - 发现多处相似元素：必须进一步降低10-20%
   - 直接临摹痕迹：最高不得超过9%

3. 严格分析要求：
   - 必须详细对比每个设计元素
   - 列举所有可能的参考来源
   - 指出每个相似点的具体位置
   - 对原创部分进行逐一验证
   - 提供降分的具体理由

[精致度]（画面完成度评分）
1. 评分标准：
   - 95-100%：顶级专业完成度，完美的细节表现，无可挑剔的技法运用，整体和谐度极高
   - 90-94%：高级专业完成度，优秀的细节刻画，熟练的技法应用，轻微瑕疵
   - 85-89%：专业完成度，细节丰富，技法娴熟，个别不足
   - 75-84%：良好完成度，基本细节完整，技法运用稳定，有明显提升空间
   - 60-74%：基础完成度，细节欠缺，技法尚需提升，存在多处瑕疵
   - 40-59%：粗糙完成度，细节处理不足，技法生涩，需要大量改进
   - 0-39%：草稿完成度，细节缺失，技法欠佳，需要彻底重做

2. 评分细则（任一项不达标都需要相应减分）：
   - 线条质量（-5~-15分）：
     • 线条不够流畅或不稳定
     • 透视关系错误
     • 结构比例失调
   
   - 明暗层次（-5~-15分）：
     • 缺乏光影变化
     • 立体感不足
     • 空间感欠佳
   
   - 色彩表现（-5~-20分）：
     • 配色不够和谐
     • 色彩过度饱和或过于暗淡
     • 色调不统一
   
   - 细节完整度（-10~-25分）：
     • 重要部位细节缺失
     • 材质表现不到位
     • 装饰元素粗糙
   
   - 整体完成度（-5~-20分）：
     • 画面部分区域完成度不一致
     • 边角部分处理粗糙
     • 背景元素敷衍

3. 严格评估要求：
   - 必须逐项检查上述各个评分细则
   - 发现任何瑕疵必须扣分，不得放宽标准
   - 同类型问题多处出现需叠加扣分
   - 优秀作品也要仔细寻找改进空间
   - 评分必须有详细的分析说明和扣分依据

请按照以上标准进行严格分析，给出准确的评分和详细的分析报告。对于每项评分，都需要提供具体的判断依据和详细说明。`
                },
                {
                  type: "image_url",
                  image_url: {
                    url: base64Image
                  }
                }
              ]
            }
          ],
          stream: false
        };

        this.updateProgress(60, '发送分析请求...');

        // 使用通用API请求函数
        const response = await requestZhipuAPI<AIResponse>(CHAT_COMPLETIONS_API_URL, requestData);

        this.updateProgress(80, '接收分析结果...');
        console.log(response);
        
        if (!response 
            || !response.choices 
            || !response.choices[0] 
            || !response.choices[0].message 
            || !response.choices[0].message.content) {
          throw new Error('AI响应内容格式错误');
        }

        const result = this.parseAIResponse(response.choices[0].message.content);

        this.updateProgress(100, '分析完成');
        this.setData({
          analysisResult: result
        });
      } catch (error) {
        // 使用通用错误显示函数
        showErrorMessage(error, '分析失败');
      } finally {
        setTimeout(() => {
          this.setData({ 
            analyzing: false,
            progressPercent: 0,
            progressText: ''
          });
        }, 500);
      }
    },

    // 切换分析文本显示
    toggleAnalysis(e: any) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        activeAnalysis: this.data.activeAnalysis === type ? '' : type
      });
    },

    // 解析AI响应
    parseAIResponse(content: string): AnalysisResult {
      if (!content) {
        return {
          advantages: '暂无分析',
          improvements: '暂无分析',
          suggestions: '暂无建议',
          originalityScore: 0,
          originalityAnalysis: '暂无分析',
          refinementScore: 0,
          refinementAnalysis: '暂无分析'
        };
      }

      // 先统一处理格式
      content = content
        // 移除所有#号
        .replace(/#{1,4}\s*/g, '')
        // 统一分隔符格式
        .replace(/[【\[]/g, '')
        .replace(/[】\]]/g, '')
        // 处理换行
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        // 移除多余空行
        .replace(/\n{3,}/g, '\n\n')
        // 移除行尾空格
        .replace(/\s+$/gm, '')
        // 统一列表符号
        .replace(/^\s*[-•]\s*/gm, '• ');

      // 使用统一的分段标记进行分割
      const sections = content.split(/(?:优点[:：]?|改进[:：]?|建议[:：]?|原创度[:：]?|精致度[:：]?)/i);
      
      // 提取各部分内容
      const findSection = (keyword: string): string => {
        const index = content.indexOf(keyword);
        if (index === -1) return '';
        
        // 找到下一个关键词的位置
        const nextKeywordMatch = content.slice(index + keyword.length).match(/(?:优点[:：]?|改进[:：]?|建议[:：]?|原创度[:：]?|精致度[:：]?)/i);
        const endIndex = nextKeywordMatch ? index + keyword.length + nextKeywordMatch.index : content.length;
        
        let sectionText = content.slice(index + keyword.length, endIndex).trim();
        
        // 移除每行开头的数字序号
        sectionText = sectionText.replace(/^\d+\.\s*/gm, '');
        
        return sectionText;
      };

      // 清理文本内容
      const cleanText = (text: string): string => {
        if (!text) return '暂无分析';
        
        // 如果内容只包含标题或为空，返回默认值
        if (text.match(/^[\s\n]*(构图|色彩|技法)[\s\n]*$/)) {
          return '暂无分析';
        }

        return text
          .replace(/\*\*/g, '')
          .replace(/^\s*[-•]\s*/gm, '• ')
          .replace(/\n{3,}/g, '\n\n')
          .replace(/（/g, '(')
          .replace(/）/g, ')')
          .trim();
      };

      // 提取各部分内容
      const advantages = cleanText(findSection('优点'));
      const improvements = cleanText(findSection('改进'));
      const suggestions = cleanText(findSection('建议'));
      const originalityAnalysis = cleanText(findSection('原创度'));
      const refinementAnalysis = cleanText(findSection('精致度'));

      // 提取分数
      const extractScore = (text: string): number => {
        if (!text) return 0;
        
        // 尝试不同的分数格式
        const patterns = [
          /(\d+)%/,                    // 直接百分比
          /完成度大约为(\d+)%/,         // "完成度大约为xx%"
          /[约为](\d+)%/,              // "约xx%" 或 "为xx%"
          /评分[约为]*(\d+)%/,         // "评分xx%"
          /得分[约为]*(\d+)%/,         // "得分xx%"
          /分数[约为]*(\d+)%/          // "分数xx%"
        ];

        for (const pattern of patterns) {
          const match = text.match(pattern);
          if (match && match[1]) {
            return parseInt(match[1]);
          }
        }
        
        return 0;
      };

      const result = {
        advantages: advantages || '暂无分析',
        improvements: improvements || '暂无分析',
        suggestions: suggestions || '暂无建议',
        originalityScore: extractScore(originalityAnalysis),
        originalityAnalysis: originalityAnalysis || '暂无分析',
        refinementScore: extractScore(refinementAnalysis),
        refinementAnalysis: refinementAnalysis || '暂无分析'
      };

      return result;
    },

    // 处理图片加载错误
    handleImageError(e: any) {
      wx.showToast({
        title: '图片预览失败',
        icon: 'none'
      });
    },

    // 防止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: '作品分析',
        query: ''
      };
    },

    // 分享给朋友
    onShareAppMessage() {
      return {
        title: '作品分析',
        path: '/pages/apipage/aitest/aitest'
      };
    }
  }
});

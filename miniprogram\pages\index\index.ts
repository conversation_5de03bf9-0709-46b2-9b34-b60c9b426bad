// index.ts
import { layoutUtil } from '../../utils/layout';
import Api from '../../utils/api';
import { formatTime } from '../../utils/util';
import eventBus from '../../utils/eventBus';
import { STATIC_URL } from '../../utils/constants';
import SubscribeMessageService from '../../utils/subscribeMessage';

// 定义全部消息模板列表
const allTemplates = [
  { id: '4UaTnz9YkBUCJqL5xM8sYcE6K8g5c_ixQmEbvOjQMdk', title: '绘画成功通知', desc: '当您的绘画成功生成时，将收到通知' },
  { id: 'KdDI0fE6oBkxKP2gQaF52zH4Ge6TklV1Y-WnYgUAGz0', title: '绘画排队通知', desc: '当您的绘画排队等待时，收到位置提醒' },
  { id: 'znVcX5J5kFUgVZAvT46TTawAGR9l4r2p6QTcvRqeIzg', title: '绘画失败通知', desc: '当您的绘画生成失败时，将收到提醒' },
  { id: 'x6F0KOCdTz-CWsygLBYFTZPvbxH47QpzsDpmuihc7KQ', title: '系列开放通知', desc: '当新系列开放申请时，将收到提醒' },
  { id: '8H6w9lNBr_9e-6YHxQYlkFNQPbTqfDwF6_bFQdo5EQQ', title: '申请审核通知', desc: '当您的申请被审核时，将收到结果' },
  { id: 'cqlLx7cHWBYgP-vMrF3E6fY8rU84nkr4m9MrlrLk1u0', title: '作品上架通知', desc: '当您的作品成功上架时，将收到提醒' },
  { id: 'Cz-QCzbx-6WtWYkUGUK3R6Pg-FD6-Vc1E8kN7hQbhD4', title: '提现审核通知', desc: '当您的提现申请审核完成时收到通知' }
];

Component({
  options: {
    styleIsolation: 'shared'
  },

  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),  // 无安全区域的样式
    homeTools: [], // 工具列表
    categories: [], // 分类列表
    expandedCategories: {},  // 已展开的分类
    loading: true,
    navMenus: [],      // 导航菜单
    allMenus: [],      // 所有菜单（包括子菜单）
    // 添加艺术工具介绍
    artToolsIntro: {
      title: '艺术创作工具集',
      description: '为艺术创作者提供的专业工具集，涵盖图像处理、创作辅助、色彩管理、素材管理等多个方面。这些工具旨在提高工作效率、拓展创作可能性，适合不同阶段的艺术工作者使用。'
    },
    // 使用场景
    usageScenarios: [
      {
        title: '绘画创作',
        description: '提供色彩参考、构图辅助、透视网格等工具，帮助创作者更好地完成绘画作品。',
        tools: ['色彩参考', '构图工具', '透视辅助']
      },
      {
        title: '图像处理',
        description: '图像裁剪、缩放、滤镜调整和格式转换等功能，满足日常图像处理需求。',
        tools: ['图像编辑', '裁剪工具', '格式转换']
      },
      {
        title: '素材管理',
        description: '整理和管理创作素材，包括图像、参考资料、调色板等，提高工作流程效率。',
        tools: ['素材库', '收藏夹', '分类管理']
      }
    ],
    // 使用建议
    tips: [
      {
        title: '保持素材更新',
        content: '定期整理和更新素材库，删除不需要的文件，保持工作空间整洁。'
      },
      {
        title: '使用快捷键',
        content: '熟悉工具的快捷键操作，可以显著提高工作效率和创作流畅度。'
      },
      {
        title: '定期备份',
        content: '养成定期备份作品和素材的习惯，防止意外情况导致工作成果丢失。'
      }
    ],
    // 订阅消息相关
    showSubscribeModal: false, // 是否显示订阅弹窗
    subscribeTemplates: [], // 订阅模板列表
    currentDate: formatTime(new Date()).split(' ')[0], // 当前日期，只取日期部分
    currentBatchIndex: 0, // 当前批次索引
    isSubmitting: false // 是否正在提交订阅
  },


  lifetimes: {
    attached: function() {
      // 加载导航菜单
      this.loadNavMenus();
      
      // 监听事件
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      
      // 初始化订阅模板数据
      this.initSubscribeTemplates();
    },
    detached: function() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    }
  },

  methods: {
    handleTabBarChange: function(data: { 
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number 
    }) {
      this.setData({
        isTabBarCollapsed: data.isCollapsed,
        tabBarHeight: data.currentHeight
      });
    },
    
    async loadNavMenus() {
      try {
        this.setData({ loading: true });
        // 获取导航菜单数据
        const navMenus = await Api.nav.getMenuList();
        console.log('导航菜单:', navMenus);
        
        // 预处理菜单数据
        const processedMenus = this.processMenuData(navMenus);
        
        this.setData({
          navMenus: processedMenus,
          loading: false
        });
      } catch (error) {
        console.error('加载导航菜单失败:', error);
        this.setData({ loading: false });
        
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },
    
    // 处理菜单数据
    processMenuData(menuData: any[]) {
      if (!Array.isArray(menuData)) return [];
      
      // 过滤掉首页链接
      const filteredMenus = menuData.filter(menu => menu.url !== "/pages/index/index");
      
      // 根据排序字段排序
      const sortedMenus = [...filteredMenus].sort((a, b) => {
        return (a.sort_order || 0) - (b.sort_order || 0);
      });
      
      // 添加expanded字段
      return sortedMenus.map(menu => {
        // 对子菜单也进行排序
        if (menu.submenus && Array.isArray(menu.submenus)) {
          menu.submenus = [...menu.submenus].sort((a, b) => {
            return (a.sort_order || 0) - (b.sort_order || 0);
          });
        }
        
        return {
          ...menu,
          expanded: false // 默认折叠
        };
      });
    },
    
    // 切换分类展开状态
    toggleCategory(e: any) {
      const categoryId = e.currentTarget.dataset.categoryId;
      const navMenus = [...this.data.navMenus];
      
      // 查找对应的分类
      const categoryIndex = navMenus.findIndex(item => item.id === categoryId);
      if (categoryIndex !== -1) {
        // 切换展开状态
        navMenus[categoryIndex].expanded = !navMenus[categoryIndex].expanded;
        
        this.setData({ navMenus });
      }
    },
    
    // 导航到工具页面
    navigateToTool(e: any) {
      const url = e.currentTarget.dataset.url;
      if (!url) {
        wx.showToast({
          title: '功能正在开发中',
          icon: 'none'
        });
        return;
      }
      
      wx.navigateTo({
        url,
        fail: () => {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 点击登录按钮
    loginButtonClick() {
      eventBus.emit('loginModalEvent', {
        show: true,
        source: 'index'
      });
    },
    
    // 初始化订阅模板数据
    initSubscribeTemplates() {
      const templates = allTemplates.slice(0, 3); // 每批最多3个
      const templateData = templates.map(template => ({
        ...template,
        checked: true, // 默认选中
        sendTime: SubscribeMessageService.getFutureTime(0) // 默认发送时间为当天
      }));
      
      this.setData({
        subscribeTemplates: templateData
      });
    },
    
    // 处理模板选中状态变化
    handleTemplateChange(e: any) {
      const index = e.currentTarget.dataset.index;
      const templates = [...this.data.subscribeTemplates];
      templates[index].checked = !templates[index].checked;
      
      this.setData({
        subscribeTemplates: templates
      });
    },
    
    // 处理发送时间变化
    handleTimeChange(e: any) {
      const index = e.currentTarget.dataset.index;
      const value = e.detail.value;
      const templates = [...this.data.subscribeTemplates];
      templates[index].sendTime = value;
      
      this.setData({
        subscribeTemplates: templates
      });
    },
    
    // 显示订阅弹窗
    showSubscribeModal() {
      this.setData({
        showSubscribeModal: true
      });
    },
    
    // 关闭订阅弹窗
    closeSubscribeModal() {
      this.setData({
        showSubscribeModal: false
      });
    },
    
    // 确认订阅
    async confirmSubscribe() {
      if (this.data.isSubmitting) return;
      
      this.setData({
        isSubmitting: true
      });
      
      try {
        // 筛选已选中的模板
        const selectedTemplates = this.data.subscribeTemplates.filter(item => item.checked);
        if (selectedTemplates.length === 0) {
          wx.showToast({
            title: '请至少选择一个提醒',
            icon: 'none'
          });
          this.setData({ isSubmitting: false });
          return;
        }
        
        // 调用订阅消息API
        const templateIds = selectedTemplates.map(item => item.id);
        
        const subscribeRes = await wx.requestSubscribeMessage({
          tmplIds: templateIds
        });
        
        console.log('订阅结果:', subscribeRes);
        
        // 处理订阅结果，将用户订阅的模板发送到后端
        const acceptedTemplates = [];
        for (const [tmplId, res] of Object.entries(subscribeRes)) {
          if (res === 'accept') {
            const template = selectedTemplates.find(item => item.id === tmplId);
            if (template) {
              acceptedTemplates.push({
                templateId: tmplId,
                sendTime: template.sendTime || SubscribeMessageService.getFutureTime(0)
              });
            }
          }
        }
        
        // 如果有接受的模板，发送到后端
        if (acceptedTemplates.length > 0) {
          try {
            // 保存用户订阅信息到后端
            // 假设 Api.message 在实际代码中存在
            // 如果不存在，可以暂时注释掉或使用其他适当的 API
            // const saveRes = await Api.message.saveSubscribeInfo(acceptedTemplates);
            // console.log('保存订阅信息结果:', saveRes);
            
            wx.showToast({
              title: '订阅成功',
              icon: 'success'
            });
          } catch (err) {
            console.error('保存订阅信息失败:', err);
          }
        } else {
          wx.showToast({
            title: '您拒绝了所有订阅',
            icon: 'none'
          });
        }
        
        // 关闭弹窗并设置下一批订阅内容
        this.closeSubscribeModal();
        this.setNextBatchTemplates();
        
      } catch (error) {
        console.error('订阅消息失败:', error);
        wx.showToast({
          title: '订阅失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({
          isSubmitting: false
        });
      }
    },
    
    // 设置下一批订阅模板
    setNextBatchTemplates() {
      const nextBatchIndex = this.data.currentBatchIndex + 1;
      const startIndex = nextBatchIndex * 3;
      
      // 如果还有下一批
      if (startIndex < allTemplates.length) {
        const endIndex = Math.min(startIndex + 3, allTemplates.length);
        const nextBatch = allTemplates.slice(startIndex, endIndex);
        
        const templateData = nextBatch.map(template => ({
          ...template,
          checked: true,
          sendTime: SubscribeMessageService.getFutureTime(0)
        }));
        
        this.setData({
          currentBatchIndex: nextBatchIndex,
          subscribeTemplates: templateData
        });
      } else {
        // 已经是最后一批，重置为第一批
        this.setData({
          currentBatchIndex: 0
        });
        this.initSubscribeTemplates();
      }
    },
    
    // 测试批量订阅所有模板
    testAllSubscribe() {
      this.showSubscribeModal();
    },
    
    // 防止滚动穿透
    preventDefault() {
      return false;
    },
    
    // 登录成功回调
    onLoginSuccess(e: any) {
      console.log('登录成功:', e.detail);
    },
    
    // 测试页面跳转方法
    testjump(e: any) {
      const k = e.currentTarget.dataset.k;
      let testUrlParams = '';
      
      // 测试三种情况：无参数、加密参数、自定义参数
      if (k == 0) {
        testUrlParams = '';
      } else if (k == 1) {
        // 生成一个随机字符串，模拟加密数据
        const randomStr = Math.random().toString(36).substring(2, 15);
        testUrlParams = `?data=${randomStr}`;
      } else {
        testUrlParams = '?id=123&type=test&from=home';
      }
      
      const url = `/pages/test/test${testUrlParams}`;
      wx.navigateTo({
        url,
        success: () => {
          console.log('跳转成功:', url);
        },
        fail: (err) => {
          console.error('跳转失败:', err);
        }
      });
    }
  }
});



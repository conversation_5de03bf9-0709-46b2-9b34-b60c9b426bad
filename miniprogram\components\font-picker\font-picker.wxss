.fp-font-picker {
  /* 颜色变量 */
  --fp-primary-color: #1890ff;
  --fp-primary-color-light: #e6f7ff;
  --fp-text-color: #333;
  --fp-text-color-secondary: #666;
  --fp-text-color-placeholder: #999;
  --fp-border-color: #f0f0f0;
  --fp-background-color: #fff;
  --fp-mask-background: rgba(0, 0, 0, 0.5);
  
  /* 字体大小变量 */
  --fp-font-size-large: var(--font-size-large, 36rpx);
  --fp-font-size-medium: var(--font-size-medium, 28rpx);
  --fp-font-size-small: var(--font-size-small, 24rpx);
  --fp-font-size-mini: var(--font-size-mini, 22rpx);
  
  /* 间距变量 */
  --fp-spacing-large: var(--spacing-large, 30rpx);
  --fp-spacing-medium: var(--spacing-medium, 20rpx);
  --fp-spacing-small: var(--spacing-small, 16rpx);
  --fp-spacing-mini: var(--spacing-mini, 8rpx);
  
  /* 圆角变量 */
  --fp-border-radius-large: var(--border-radius-large, 16rpx);
  --fp-border-radius-medium: var(--border-radius-medium, 12rpx);
  --fp-border-radius-small: var(--border-radius-small, 8rpx);
  
  position: relative;
  width: 100%;
}

/* 触发按钮样式 */
.fp-trigger-button {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1px solid var(--fp-border-color);
  overflow: hidden;
  transition: all 0.3s ease;
}

.fp-trigger-button:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.02);
}

.fp-selected-font {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.fp-font-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.fp-font-preview {
  font-size: var(--fp-font-size-large);
  color: var(--fp-text-color);
  line-height: 1;
}

.fp-font-name {
  font-size: 28rpx;
  color: var(--fp-text-color);
  font-weight: 500;
}

.fp-font-tags {
  font-size: 24rpx;
  color: var(--fp-text-color-secondary);
}

.fp-select-area {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f5f7fa;
  border-radius: 4rpx;
  transition: all 0.3s ease;
}

.fp-select-area:active {
  background: #e6e8eb;
}

.fp-select-text {
  font-size: 24rpx;
  color: var(--fp-text-color-secondary);
}

.fp-arrow {
  width: 12rpx;
  height: 12rpx;
  border-right: 2rpx solid var(--fp-text-color-secondary);
  border-bottom: 2rpx solid var(--fp-text-color-secondary);
  transform: rotate(45deg);
  transition: transform 0.3s ease;
}

.fp-trigger-button.fp-active .fp-arrow {
  transform: rotate(-135deg);
}

/* 未选择状态 */
.fp-placeholder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.fp-placeholder-text {
  font-size: 28rpx;
  color: var(--fp-text-color-placeholder);
}

/* 添加装饰元素 */
.fp-trigger-button::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4rpx;
  height: 100%;
  background: linear-gradient(to bottom, var(--fp-primary-color), var(--fp-primary-color-dark, #0050c9));
  border-radius: 4rpx 0 0 4rpx;
  opacity: 0.8;
}

/* 添加悬停效果 */
.fp-trigger-button:hover {
  border-color: #1890ff20;
  background: linear-gradient(to right, #ffffff, #f0f7ff);
}

/* 弹出层样式 */
.fp-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--fp-mask-background);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.fp-popup-mask.fp-show {
  opacity: 1;
  visibility: visible;
}

.fp-popup-content {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--fp-background-color);
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  z-index: 1001;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.fp-popup-content.fp-show {
  transform: translateY(0);
}

.fp-popup-header {
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid var(--fp-border-color);
}

.fp-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--fp-text-color);
}

.fp-header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.fp-refresh-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.fp-refresh-btn image {
  width: 32rpx;
  height: 32rpx;
}

.fp-refresh-btn.fp-loading {
  animation: fp-spin 1s linear infinite;
}

.fp-popup-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f7fa;
}

.fp-close-icon {
  font-size: 32rpx;
  color: var(--fp-text-color-secondary);
  line-height: 1;
}

/* 分类标签样式 */
.fp-category-tabs {
  padding: 0 20rpx;
  white-space: nowrap;
  border-bottom: 1rpx solid var(--fp-border-color);
}

.fp-category-tabs-content {
  display: inline-flex;
  padding: 16rpx 0;
  gap: 24rpx;
}

.fp-category-tab {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 28rpx;
  color: var(--fp-text-color-secondary);
  background: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
}

.fp-category-icon {
  width: 32rpx;
  height: 32rpx;
}

.fp-category-tab.fp-active {
  color: var(--fp-primary-color);
  background: var(--fp-primary-color-light);
  font-weight: 500;
}

.fp-category-tab:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 字体列表样式 */
.fp-font-list-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.fp-font-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.fp-font-item {
  background: #f5f7fa;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.fp-font-item:active {
  transform: scale(0.98);
}

.fp-font-item.fp-active {
  background: var(--fp-primary-color-light);
  border-color: var(--fp-primary-color);
}

/* 加载遮罩 */
.fp-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fp-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.fp-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid var(--fp-primary-color);
  border-radius: 50%;
  animation: fp-spin 1s linear infinite;
}

.fp-loading-content .text {
  font-size: 28rpx;
  color: var(--fp-text-color-secondary);
}

@keyframes fp-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 字体信息展示样式 */
.fp-font-info {
  margin-bottom: 20rpx;
}

.fp-info-label, .fp-preview-label {
  font-size: 26rpx;
  color: #999;
  margin-right: 16rpx;
}

.fp-info-value {
  font-size: 28rpx;
  color: #333;
}

.fp-font-preview {
  margin-top: 16rpx;
}

.fp-preview-value {
  display: block;
  font-size: 36rpx;
  color: #333;
  text-align: center;
  padding: 20rpx 0;
}

/* 输入预览区域样式 */
.fp-input-preview-section {
  padding: 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.fp-preview-input {
  width: 100%;
  min-height: 80rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #f9f9f9;
  margin-bottom: 20rpx;
}

.fp-preview-display {
  min-height: 120rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.fp-preview-display.fp-has-font {
  background: #e6f7ff;
  border: 2rpx solid #1890ff;
}

.fp-preview-display .fp-preview-text {
  font-size: 36rpx;
  line-height: 1.5;
  color: #333;
  text-align: center;
  word-break: break-all;
  white-space: pre-wrap;
} 
.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 添加按钮样式 */
.auth-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.auth-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  background-color: #07c160;
  color: #fff;
  text-align: center;
  padding: 0;
}

.auth-btn::after {
  border: none;
}

.auth-btn-hide {
  background-color: #ff6347;
}

.auth-btn:active {
  opacity: 0.8;
}

.auth-btn-params {
  background-color: #1e90ff;
  width: 100%;
}

.test-c {
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.image-section {
  margin: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  max-height: calc(100vh - 400rpx);
  display: flex;
  flex-direction: column;
}

.image-container {
  width: 100%;
  min-height: 400rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container.empty {
  height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
}

.image-container image {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.process-canvas {
  position: absolute;
  left: -9999px;
  visibility: hidden;
}

.upload-hint {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  position: absolute;
  top: 0;
  left: 0;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.upload-text {
  font-size: 28rpx;
}

.tool-bar {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  background: #fff;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.tool-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.tool-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.tool-name {
  font-size: 24rpx;
  color: #666;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 998;
  pointer-events: none;
  transition: all 0.3s ease-out;
}

.mask.show {
  pointer-events: auto;
}

.adjust-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  z-index: 999;
  padding: 24rpx;
}

.adjust-panel.show {
  transform: translateY(0);
}

.adjust-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.adjust-header .text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.adjust-content {
  max-height: 45vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 10rpx;
}

/* 优化滚动条样式 */
.adjust-content::-webkit-scrollbar {
  width: 4rpx;
  background: transparent;
}

.adjust-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
}

.tonal-controls {
  padding: 0;
}

.control-item {
  margin-bottom: 20rpx;
}

.control-item:last-child {
  margin-bottom: 20rpx;
}

.control-item .text {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.adjust-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
  background: #fff;
}

.action-btn {
  min-width: 140rpx;
  height: 68rpx;
  line-height: 68rpx;
  text-align: center;
  border-radius: 34rpx;
  font-size: 26rpx;
  padding: 0 32rpx;
}

.action-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.action-btn.confirm {
  background: #007AFF;
  color: #fff;
}

/* 工具说明样式 */
.tool-intro {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.intro-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 134, 255, 0.2);
}

.intro-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.intro-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.intro-content {
  margin-bottom: 40rpx;
}

.feature-section {
  margin-bottom: 36rpx;
}

.feature-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: flex-start;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-icon .text {
  color: #fff;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.usage-section {
  margin-top: 40rpx;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00C3FF 0%, #0086FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-section {
  margin-top: 40rpx;
}

.tip-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1px solid rgba(37, 117, 252, 0.1);
  transition: all 0.3s ease;
}

.tip-icon {
  color: #0086FF;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.feature-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.feature-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }

.step-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.step-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.step-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.step-item:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.tip-item:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.tip-item:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.tip-item:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.5s both; }

.scene-list, .tip-list {
  margin-top: 16rpx;
}

.scene-item, .tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.scene-dot, .tip-dot {
  color: #007AFF;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.scene-text, .tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 裁剪组件样式 */
.cropper-component {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #000;
}

/* 确保paper-size-picker在裁剪器中正确显示 */
.cropper-title paper-size-picker {
  width: auto;
  min-width: 300rpx;
  display: inline-block;
}

/* 重新上传按钮样式 */
.reupload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #007AFF;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.reupload-btn:active {
  transform: scale(0.95);
}

.reupload-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
  filter: brightness(0) invert(1);
}

.reupload-text {
  font-size: 24rpx;
  color: #fff;
}

.operation-btns {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  margin: 20rpx 0;
}

.op-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.op-btn:active {
  transform: scale(0.95);
}

.op-btn.disabled {
  opacity: 0.5;
  background: #e0e0e0;
  pointer-events: none;
}

.op-btn.original-btn {
  background: #4CAF50;
}

.op-btn.original-btn .op-text {
  color: #fff;
}

.op-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
  filter: brightness(0) invert(1);
}

.op-text {
  font-size: 24rpx;
  color: #333;
}

.save-btn-wrapper {
  margin: 30rpx 20rpx;
  display: flex;
  justify-content: center;
}

.save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #34C759 0%, #30B956 100%);
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
  transition: all 0.3s ease;
  border: none;
  width: 80%;
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(52, 199, 89, 0.1);
}

.save-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  filter: brightness(0) invert(1);
}

.save-text {
  font-weight: 500;
}
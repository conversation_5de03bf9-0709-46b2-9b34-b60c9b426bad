<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="表情动作参考" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}">
      <!-- 内容区域开始 -->
      <view class="image-container">
        <block wx:for="{{currentDetail.images}}" wx:key="url">
          <view class="image-card">
            <view class="image-wrapper">
              <view class="image-number" wx:if="{{currentDetail.images.length > 1}}">{{index + 1}}</view>
              <image class="main-image" 
                     src="{{item.url}}" 
                     mode="widthFix" 
                     lazy-load="true"
                     bindload="onImageLoad"
                     data-index="{{index}}"
                     hidden="{{!item.isLoaded}}" />
              <view class="image-loading" hidden="{{item.isLoaded}}">
                <view class="loading-progress">
                  <view class="loading-step">步骤 {{loadingStep}}/{{loadingSteps.length}}</view>
                  <view class="progress-bar">
                    <view class="progress-inner" style="width: {{loadingProgress+'%'}}"></view>
                  </view>
                  <text class="progress-text">{{loadingProgress}}%</text>
                  <view class="loading-message">
                    <text class="loading-text">{{loadingText}}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="image-info">
              <view class="artist-info" wx:if="{{item.anime_name}}">
                <text class="label">动漫名称：</text>
                <text class="value">{{item.anime_name}}</text>
              </view>
              <view class="image-stats">
                <view class="stat-item">
                  <text class="label">类型：</text>
                  <text class="value">{{categories[selectedCategoryIndex].name}}</text>
                </view>
                <view class="stat-item">
                  <text class="label">格式：</text>
                  <text class="value">{{categories[selectedCategoryIndex].format === 'gif' ? '动态表情形体参考' : '静态表情形体参考'}}</text>
                </view>
              </view>
              <view class="links">
                <button class="link-btn download-btn" bindtap="downloadImage" data-url="{{item.url}}" data-index="{{index}}">
                  <text class="link-icon">💾</text>
                  <text>保存</text>
                </button>
              </view>
            </view>
            <view class="refresh-button-container">
              <button class="refresh-button {{globalCountdown > 0 ? 'counting' : ''}}" 
                      bindtap="refreshImage" 
                      data-index="{{index}}"
                      disabled="{{loading || globalCountdown > 0}}">
                <block wx:if="{{globalCountdown > 0}}">
                  <text class="countdown">{{globalCountdown}}s</text>
                </block>
                <block wx:else>
                  <text class="refresh-icon">🔄</text>
                  <text>换一张</text>
                </block>
              </button>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 控制面板 -->
      <view class="control-panel">
        <view class="selector-wrapper" bindtap="showCategorySelector">
          <view class="selector-item">
            <text class="selector-label">分类</text>
            <text class="selector-value">{{categories[selectedCategoryIndex].name}}</text>
            <text class="selector-arrow">▼</text>
          </view>
        </view>
        <view class="selector-wrapper">
          <picker bindchange="onAmountChange" value="{{selectedAmountIndex}}" range="{{amounts}}">
            <view class="selector-item">
              <text class="selector-label">数量</text>
              <text class="selector-value">{{amounts[selectedAmountIndex]}}张</text>
              <text class="selector-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>
      <!-- 说明开始 -->
      <view class="tool-intro">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="{{constants.STATIC_URL.ICON}}facialandbody.svg" mode="aspectFit"></image>
            </view>
            <text class="intro-title">表情动作参考</text>
          </view>

          <view class="intro-content">
            <view class="feature-section">
              <text class="section-title">主要功能</text>
              <view class="feature-list">
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">丰富素材</text>
                    <text class="feature-desc">提供大量优质的表情和动作参考素材</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">分类完善</text>
                    <text class="feature-desc">静态和动态素材分类清晰，方便查找</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">一键保存</text>
                    <text class="feature-desc">支持快速保存喜欢的参考素材</text>
                  </view>
                </view>
                <view class="feature-item">
                  <view class="feature-icon">
                    <text class="text">✦</text>
                  </view>
                  <view class="feature-content">
                    <text class="feature-name">随机切换</text>
                    <text class="feature-desc">支持快速切换不同的参考素材</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="usage-section">
              <text class="section-title">使用步骤</text>
              <view class="step-list">
                <view class="step-item">
                  <view class="step-number">1</view>
                  <view class="step-content">
                    <text class="step-title">选择分类</text>
                    <text class="step-desc">选择需要的表情或动作类型</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">2</view>
                  <view class="step-content">
                    <text class="step-title">设置数量</text>
                    <text class="step-desc">选择想要显示的参考图数量</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">3</view>
                  <view class="step-content">
                    <text class="step-title">浏览素材</text>
                    <text class="step-desc">查看参考图片，可随时切换新的素材</text>
                  </view>
                </view>
                <view class="step-item">
                  <view class="step-number">4</view>
                  <view class="step-content">
                    <text class="step-title">保存使用</text>
                    <text class="step-desc">保存喜欢的参考素材到本地</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tip-section">
              <text class="section-title">使用技巧</text>
              <view class="tip-list">
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">合理选择</text>
                  <text class="tip-text">根据创作需求选择合适的表情或动作类型</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">对比学习</text>
                  <text class="tip-text">同时查看多张参考图，理解表情动作的变化</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">动静结合</text>
                  <text class="tip-text">静态图片用于细节参考，动态图片用于理解动作流程</text>
                </view>
                <view class="tip-item">
                  <view class="tip-icon">💡</view>
                  <text class="tip-title">及时保存</text>
                  <text class="tip-text">遇到好的参考素材要及时保存，方便日后使用</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 说明结束 -->
      <!-- 内容区域结束 -->
    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 分类选择浮窗 -->
<view class="category-popup {{showCategorySelector ? 'show' : ''}}" catchtap="hideCategorySelector">
  <view class="popup-content" catchtap="stopPropagation">
    <view class="popup-header">
      <text class="popup-title">选择参考素材分类</text>
      <text class="popup-close" bindtap="hideCategorySelector">✕</text>
    </view>
    <view class="category-lists">
      <view class="category-column">
        <view class="column-title">静态表情形体</view>
        <scroll-view scroll-y class="category-scroll">
          <view 
            wx:for="{{categories}}" 
            wx:key="value" 
            wx:if="{{item.format === 'png'}}"
            class="category-item {{selectedCategory === item.value ? 'active' : ''}}"
            data-index="{{index}}"
            bindtap="selectCategory"
          >
            {{item.name}}
          </view>
        </scroll-view>
      </view>
      <view class="category-column">
        <view class="column-title">动态表情形体</view>
        <scroll-view scroll-y class="category-scroll">
          <view 
            wx:for="{{categories}}" 
            wx:key="value" 
            wx:if="{{item.format === 'gif'}}"
            class="category-item {{selectedCategory === item.value ? 'active' : ''}}"
            data-index="{{index}}"
            bindtap="selectCategory"
          >
            {{item.name}}
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</view>

<!-- 隐藏的画布 -->
<canvas type="2d" id="downloadCanvas" style="position:fixed;left:-9999px;top:-9999px;"></canvas>



